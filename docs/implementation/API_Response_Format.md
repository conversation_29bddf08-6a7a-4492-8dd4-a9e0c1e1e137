# Standardized API Response Format

## Overview
This document defines a standardized format for all API responses in the application. Consistent response formats improve frontend integration, error handling, and developer experience.

## Response Structure

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data goes here
  },
  "message": "Optional success message",
  "metadata": {
    // Optional metadata like pagination info
    "pagination": {
      "totalCount": 100,
      "totalPages": 10,
      "currentPage": 1,
      "limit": 10
    }
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": [
      // Optional array of detailed error information
      {
        "field": "fieldName",
        "message": "Validation error for this field"
      }
    ]
  }
}
```

## Status Codes

| Status Code | Description | Example Use Case |
|-------------|-------------|-----------------|
| 200 | OK | Successful GET, PUT, or DELETE |
| 201 | Created | Successful POST creating a new resource |
| 400 | Bad Request | Invalid input, validation errors |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Authenticated but not authorized |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource already exists |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server-side errors |

## Error Codes

| Error Code | Description | Example Use Case |
|------------|-------------|-----------------|
| VALIDATION_ERROR | Input validation failed | Invalid form data |
| RESOURCE_NOT_FOUND | Resource not found | Assembly ID not found |
| DUPLICATE_RESOURCE | Resource already exists | Assembly ID already exists |
| DATABASE_ERROR | Database operation failed | MongoDB connection error |
| INTERNAL_ERROR | Internal server error | Unexpected server error |
| AUTHENTICATION_ERROR | Authentication failed | Invalid credentials |
| AUTHORIZATION_ERROR | Authorization failed | Insufficient permissions |

## Implementation Guidelines

### Success Response Helper
```typescript
function successResponse(data: any, message?: string, metadata?: any) {
  return NextResponse.json({
    success: true,
    data,
    message,
    metadata
  }, {
    status: 200
  });
}
```

### Error Response Helper
```typescript
function errorResponse(code: string, message: string, details?: any[], statusCode: number = 400) {
  return NextResponse.json({
    success: false,
    error: {
      code,
      message,
      details
    }
  }, {
    status: statusCode
  });
}
```

### Validation Error Helper
```typescript
function validationErrorResponse(errors: { field: string, message: string }[]) {
  return errorResponse(
    'VALIDATION_ERROR',
    'Validation failed',
    errors,
    422
  );
}
```

## Example Usage

### GET /api/assemblies
```typescript
export async function GET(request: NextRequest) {
  try {
    const assemblies = await Assembly.find().populate('parts.partId');
    
    return successResponse(
      assemblies,
      'Assemblies retrieved successfully',
      {
        pagination: {
          totalCount: assemblies.length,
          totalPages: 1,
          currentPage: 1,
          limit: assemblies.length
        }
      }
    );
  } catch (error) {
    return errorResponse(
      'DATABASE_ERROR',
      'Failed to retrieve assemblies',
      [],
      500
    );
  }
}
```

### POST /api/assemblies
```typescript
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate input
    const validationErrors = validateAssemblyData(data);
    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }
    
    const assembly = new Assembly(data);
    const savedAssembly = await assembly.save();
    
    return successResponse(
      savedAssembly,
      'Assembly created successfully',
      null,
      201
    );
  } catch (error) {
    if (error.code === 11000) {
      return errorResponse(
        'DUPLICATE_RESOURCE',
        'An assembly with this ID already exists',
        [],
        409
      );
    }
    
    return errorResponse(
      'DATABASE_ERROR',
      'Failed to create assembly',
      [],
      500
    );
  }
}
```
