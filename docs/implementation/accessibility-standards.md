# Accessibility Standards and Guidelines

This document outlines the accessibility standards and guidelines for the Trend IMS application. Following these standards ensures that the application is usable by people with different abilities and complies with WCAG 2.1 AA standards.

## Table of Contents

1. [General Principles](#general-principles)
2. [Component-Specific Requirements](#component-specific-requirements)
3. [Implementation Approach](#implementation-approach)
4. [Testing Procedures](#testing-procedures)
5. [Resources and References](#resources-and-references)

## General Principles

### Color Contrast Requirements

- Text and interactive elements must have sufficient contrast ratios:
  - Normal text: 4.5:1 minimum contrast ratio
  - Large text (18pt or 14pt bold): 3:1 minimum contrast ratio
  - UI components and graphical objects: 3:1 minimum contrast ratio
- Use the theme context variables to ensure proper contrast in both light and dark modes
- Avoid using color alone to convey information

### Keyboard Accessibility

- All interactive elements must be operable with a keyboard
- Focus order should follow a logical sequence (typically the DOM order)
- Keyboard shortcuts should be documented and not conflict with browser or screen reader shortcuts
- Provide a visible focus indicator for all interactive elements
- Ensure that keyboard traps are avoided (except for modals with proper focus management)

### Focus Management

- All interactive elements must have a visible focus indicator
- Focus indicators should be high contrast and clearly visible
- Focus should move in a logical order through the page
- When content changes dynamically, focus should be managed appropriately
- Modals and dialogs should trap focus until closed
- After closing a modal, focus should return to the triggering element

### Screen Reader Support

- Use semantic HTML elements whenever possible
- Provide appropriate ARIA attributes when semantic HTML is not sufficient
- Ensure that all UI changes are announced to screen readers
- Use ARIA live regions for dynamic content updates
- Provide text alternatives for non-text content
- Ensure that form controls have associated labels

### Responsive Design

- Ensure that the application is usable at all screen sizes
- Support zoom up to 200% without loss of content or functionality
- Ensure that content reflows appropriately at different screen sizes
- Maintain appropriate touch target sizes for mobile devices

### Error Handling

- Error messages should be clear and descriptive
- Errors should be associated with the relevant form control
- Error messages should be announced to screen readers
- Provide suggestions for fixing errors when possible
- Allow users to resubmit forms with errors after corrections

## Component-Specific Requirements

### Interactive Elements (Buttons, Links, Icons)

#### ARIA Attributes

- Buttons should have appropriate `aria-label` when text is not descriptive
- Icon-only buttons must have an `aria-label` describing their function
- Links should have descriptive text that makes sense out of context
- Use `aria-expanded` for toggle buttons
- Use `aria-pressed` for toggle buttons that have a pressed state
- Use `aria-disabled` for disabled buttons that should still be focusable

#### Keyboard Interactions

- Buttons and links should be activatable with Enter key
- Buttons should also be activatable with Space key
- Links should navigate when activated with Enter key
- Avoid using `div` or `span` with click handlers; use proper button or link elements

#### Example Implementation

```tsx
// Good example
<Button 
  aria-label={text ? undefined : "Add new item"}
  aria-expanded={isExpanded}
  onClick={handleClick}
>
  {text || <PlusIcon />}
</Button>

// Bad example - avoid this
<div 
  className="button-like" 
  onClick={handleClick}
>
  <PlusIcon />
</div>
```

### Form Controls (Inputs, Checkboxes, Selects)

#### ARIA Attributes

- All form controls must have associated labels
- Use `aria-describedby` to associate error messages with form controls
- Use `aria-invalid` to indicate validation errors
- Use `aria-required` to indicate required fields
- Use appropriate roles if not using native form elements

#### Keyboard Interactions

- All form controls should be operable with keyboard
- Maintain standard keyboard interactions for form controls
- Custom form controls should mimic native keyboard behavior

#### Example Implementation

```tsx
<FormItem>
  <FormLabel htmlFor="name">Name</FormLabel>
  <FormControl>
    <Input 
      id="name" 
      aria-describedby="name-error"
      aria-invalid={!!errors.name}
      {...register("name")} 
    />
  </FormControl>
  {errors.name && (
    <FormMessage id="name-error">
      {errors.name.message}
    </FormMessage>
  )}
</FormItem>
```

### Navigation Components (Sidebar, Menus)

#### ARIA Attributes

- Use `aria-current` to indicate the current page in navigation
- Use `aria-expanded` for expandable navigation sections
- Use `aria-controls` to associate controls with the content they show/hide
- Use appropriate landmark roles (`nav`, `main`, `header`, etc.)

#### Keyboard Interactions

- Navigation menus should be navigable with arrow keys
- Dropdown menus should close with Escape key
- Provide a skip link to bypass navigation and go to main content

#### Example Implementation

```tsx
<nav aria-label="Main Navigation">
  <ul>
    <li>
      <Link 
        href="/" 
        aria-current={pathname === "/" ? "page" : undefined}
      >
        Dashboard
      </Link>
    </li>
    {/* Other navigation items */}
  </ul>
</nav>
```

### Content Containers (Cards, Tables, Lists)

#### ARIA Attributes

- Tables should have appropriate headers with `scope` attributes
- Use `aria-sort` for sortable table headers
- Lists should use proper list elements (`ul`, `ol`, `dl`)
- Cards should have appropriate headings for structure

#### Keyboard Interactions

- Ensure that all interactive elements within containers are keyboard accessible
- Provide keyboard shortcuts for common actions when appropriate
- Ensure proper focus management for expandable content

#### Example Implementation

```tsx
<Table>
  <TableHeader>
    <TableRow>
      <TableHead scope="col" aria-sort={sortDirection}>Name</TableHead>
      <TableHead scope="col">Status</TableHead>
      <TableHead scope="col">Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    {/* Table rows */}
  </TableBody>
</Table>
```

### Modals and Dialogs

#### ARIA Attributes

- Use `role="dialog"` or `role="alertdialog"` as appropriate
- Use `aria-labelledby` to associate a heading with the dialog
- Use `aria-describedby` for additional description if needed
- Use `aria-modal="true"` to indicate that the dialog is modal

#### Keyboard Interactions

- Focus should move to the dialog when opened
- Focus should be trapped within the dialog while open
- Dialog should close with Escape key
- Focus should return to the triggering element when closed

#### Example Implementation

```tsx
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogContent
    aria-labelledby="dialog-title"
    aria-describedby="dialog-description"
  >
    <DialogHeader>
      <DialogTitle id="dialog-title">Confirm Action</DialogTitle>
      <DialogDescription id="dialog-description">
        Are you sure you want to proceed?
      </DialogDescription>
    </DialogHeader>
    <DialogFooter>
      <Button variant="outline" onClick={() => setIsOpen(false)}>
        Cancel
      </Button>
      <Button onClick={handleConfirm}>Confirm</Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Dynamic Content (Loading States, Notifications)

#### ARIA Attributes

- Use `aria-live` regions for dynamic content updates
- Use `aria-busy` for loading states
- Use appropriate roles for notifications (`alert`, `status`, etc.)
- Use `aria-atomic` to control how updates are announced

#### Keyboard Interactions

- Ensure that focus is managed appropriately when content changes
- Provide keyboard shortcuts to dismiss notifications when appropriate
- Ensure that loading indicators are perceivable by screen readers

#### Example Implementation

```tsx
<div 
  role="status" 
  aria-live="polite" 
  aria-atomic="true"
>
  {isLoading ? (
    <p>Loading data...</p>
  ) : (
    <p>Data loaded successfully</p>
  )}
</div>
```

## Implementation Approach

### Configuration

Enable the `accessibilityMode` in the UI enhancement configuration:

```typescript
// app/config/ui-enhancement-plan.ts
globalSettings: {
  animationsEnabled: true,
  performanceMode: false,
  accessibilityMode: true // Enable accessibility mode
}
```

### Utility Functions and Hooks

Create reusable utility functions and hooks for common accessibility patterns:

#### Focus Trapping

```typescript
// app/hooks/useFocusTrap.ts
import { useEffect, useRef } from 'react';

export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    // Focus the first element when the trap is activated
    firstElement?.focus();
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;
      
      // Trap focus within the container
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive]);
  
  return containerRef;
}
```

#### Screen Reader Announcements

```typescript
// app/hooks/useAnnounce.ts
import { useEffect, useRef } from 'react';

export function useAnnounce(message: string, politeness: 'polite' | 'assertive' = 'polite') {
  const announceRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!message || !announceRef.current) return;
    
    announceRef.current.textContent = message;
  }, [message]);
  
  return (
    <div
      ref={announceRef}
      aria-live={politeness}
      aria-atomic="true"
      className="sr-only"
    />
  );
}
```

### Reusable Components

Create reusable components for common accessibility patterns:

#### SkipLink Component

```tsx
// app/components/accessibility/SkipLink.tsx
'use client';

import React from 'react';
import { cn } from '@/app/lib/utils';

export function SkipLink() {
  return (
    <a
      href="#main-content"
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",
        "focus:bg-background focus:p-4 focus:rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
      )}
    >
      Skip to main content
    </a>
  );
}
```

#### VisuallyHidden Component

```tsx
// app/components/accessibility/VisuallyHidden.tsx
import React from 'react';

interface VisuallyHiddenProps {
  children: React.ReactNode;
}

export function VisuallyHidden({ children }: VisuallyHiddenProps) {
  return (
    <span className="sr-only">
      {children}
    </span>
  );
}
```

## Testing Procedures

### Manual Testing

- Test all components with keyboard navigation
- Test with screen readers (NVDA, VoiceOver, JAWS)
- Test with high contrast mode
- Test with different zoom levels
- Test with different screen sizes

### Automated Testing

Integrate axe-core for automated accessibility testing:

```typescript
// __tests__/accessibility/components.test.tsx
import React from 'react';
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Button } from '@/app/components/ui/button';

expect.extend(toHaveNoViolations);

describe('Accessibility tests', () => {
  it('Button component should have no accessibility violations', async () => {
    const { container } = render(<Button>Test Button</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

## Resources and References

- [WCAG 2.1 Guidelines](https://www.w3.org/TR/WCAG21/)
- [WAI-ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [Inclusive Components](https://inclusive-components.design/)
- [A11y Project Checklist](https://www.a11yproject.com/checklist/)
- [React Accessibility](https://reactjs.org/docs/accessibility.html)
- [Next.js Accessibility](https://nextjs.org/docs/advanced-features/accessibility)
