# Assemblies API Documentation

This document provides detailed information about the Assemblies API endpoints, request/response formats, and usage examples.

## Base URL

All API endpoints are relative to the base URL of the application.

## Authentication

Authentication is handled by the Next.js API routes middleware. No additional authentication headers are required for these endpoints.

## Error Handling

All API endpoints return a consistent error format:

```json
{
  "success": false,
  "error": "Error message describing what went wrong"
}
```

## Endpoints

### Get All Assemblies

Retrieves a list of all assemblies.

- **URL**: `/api/assemblies`
- **Method**: `GET`
- **Query Parameters**:
  - `limit` (optional): Number of assemblies to return (default: 100)
  - `page` (optional): Page number for pagination (default: 1)
  - `sort` (optional): Field to sort by (default: "updatedAt")
  - `order` (optional): Sort order, "asc" or "desc" (default: "desc")

#### Success Response

```json
{
  "success": true,
  "data": [
    {
      "_id": "6800f5bc4e8ec95b6f4139b6", // Example ObjectId string
      "assemblyCode": "ASM-TF-01", // Updated field
      "name": "Tamping Frame Assemblies",
      "productId": null, // Added field (example value)
      "parentId": null, // Added field (example value)
      "isTopLevel": true, // Added field (example value)
      "status": "active", // Added field (example value)
      "partsRequired": [ // Updated field name
        {
          // Example showing populated part data (assuming API does this)
          // Note: Populated part structure should match the updated Part schema
          "partId": {
            "_id": "PART-001", // Part schema uses String _id
            "name": "Frame Support"
            // Other relevant Part fields...
          },
          "quantityRequired": 2
        }
      ],
      "createdAt": "2025-04-17T12:36:12.920Z",
      "updatedAt": "2025-04-17T12:36:12.920Z" // Assuming timestamps are still used
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 100,
  "totalPages": 1
}
```

### Get Assembly by ID

Retrieves a single assembly by its ID.

- **URL**: `/api/assemblies/:id`
- **Method**: `GET`
- **URL Parameters**:
  - `id`: The MongoDB ID of the assembly

#### Success Response

```json
{
  "success": true,
  "data": {
    "_id": "6800f5bc4e8ec95b6f4139b6", // Example ObjectId string
    "assemblyCode": "ASM-TF-01", // Updated field
    "name": "Tamping Frame Assemblies",
    "productId": null, // Added field (example value)
    "parentId": null, // Added field (example value)
    "isTopLevel": true, // Added field (example value)
    "status": "active", // Added field (example value)
    "partsRequired": [ // Updated field name
      {
        // Example showing populated part data
        "partId": {
          "_id": "PART-001", // Part schema uses String _id
          "name": "Frame Support"
          // Other relevant Part fields...
        },
        "quantityRequired": 2
      }
    ],
    "createdAt": "2025-04-17T12:36:12.920Z",
    "updatedAt": "2025-04-17T12:36:12.920Z" // Assuming timestamps are still used
  }
}
```

### Create Assembly

Creates a new assembly.

- **URL**: `/api/assemblies`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "assemblyCode": "ASM-TF-02", // Updated field
  "name": "Secondary Tamping Frame",
  "productId": null, // Added field
  "parentId": null, // Added field
  "isTopLevel": true, // Added field
  "status": "active", // Added field
  "partsRequired": [ // Updated field name
    {
      "partId": "PART-002", // Part ID (String)
      "quantityRequired": 1
    }
  ]
  // Removed description, assembly_stage
}
```

#### Success Response

```json
{
  "success": true,
  "data": {
    "_id": "6800f5bc4e8ec95b6f4139b8", // Example ObjectId string
    "assemblyCode": "ASM-TF-02", // Updated field
    "name": "Secondary Tamping Frame",
    "productId": null, // Added field
    "parentId": null, // Added field
    "isTopLevel": true, // Added field
    "status": "active", // Added field
    "partsRequired": [ // Updated field name
      {
        "partId": "PART-002", // Part ID (String)
        "quantityRequired": 1
      }
    ],
    "createdAt": "2025-04-22T12:36:12.920Z",
    "updatedAt": "2025-04-22T12:36:12.920Z" // Assuming timestamps are still used
  }
}
```

### Update Assembly

Updates an existing assembly.

- **URL**: `/api/assemblies/:id`
- **Method**: `PATCH`
- **Content-Type**: `application/json`
- **URL Parameters**:
  - `id`: The MongoDB ID of the assembly
- **Request Body**: Any fields that need to be updated

```json
{
  "name": "Updated Tamping Frame",
  "status": "inactive", // Example update
  "partsRequired": [ // Update partsRequired array
    {
      "partId": "PART-001", // Part ID (String)
      "quantityRequired": 3
    }
    // Add/remove other parts as needed
  ]
  // Removed description
}
```

#### Success Response

```json
{
  "success": true,
  "data": {
    "_id": "6800f5bc4e8ec95b6f4139b6", // Example ObjectId string
    "assemblyCode": "ASM-TF-01", // Updated field
    "name": "Updated Tamping Frame",
    "productId": null, // Added field
    "parentId": null, // Added field
    "isTopLevel": true, // Added field
    "status": "inactive", // Updated field
    "partsRequired": [ // Updated field name and content
      {
        "partId": "PART-001", // Part ID (String)
        "quantityRequired": 3
      }
    ],
    "createdAt": "2025-04-17T12:36:12.920Z",
    "updatedAt": "2025-04-22T12:36:12.920Z" // Assuming timestamps are still used
  }
}
```

### Delete Assembly

Deletes an assembly.

- **URL**: `/api/assemblies/:id`
- **Method**: `DELETE`
- **URL Parameters**:
  - `id`: The MongoDB ID of the assembly

#### Success Response

```json
{
  "success": true,
  "message": "Assembly deleted successfully"
}
```

## Data Models

### Assembly

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| assemblyCode | String | Unique code for the assembly |
| name | String | Name of the assembly |
| productId | ObjectId / Null | Reference to related product |
| parentId | ObjectId / Null | Reference to parent assembly |
| isTopLevel | Boolean | Indicates if this is a top-level assembly |
| status | String | Assembly status (e.g., "active", "inactive") |
| partsRequired | Array | Array of parts required for this assembly |
| partsRequired[].partId | String | Reference to the Part _id |
| partsRequired[].quantityRequired | Number | Quantity of the part required |
| createdAt | Date | Creation timestamp |
| updatedAt | Date | Last update timestamp (if timestamps enabled) |
// Removed: assembly_id, description, assembly_stage, parts

## Usage Examples

### Fetch All Assemblies

```javascript
const fetchAssemblies = async () => {
  const response = await fetch('/api/assemblies');
  const data = await response.json();
  
  if (response.ok) {
    return data.data;
  } else {
    throw new Error(data.error || 'Failed to fetch assemblies');
  }
};
```

### Create a New Assembly

```javascript
const createAssembly = async (assemblyData) => {
  const response = await fetch('/api/assemblies', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(assemblyData),
  });
  
  const data = await response.json();
  
  if (response.ok) {
    return data.data;
  } else {
    throw new Error(data.error || 'Failed to create assembly');
  }
};
```

### Update an Assembly

```javascript
const updateAssembly = async (id, updateData) => {
  const response = await fetch(`/api/assemblies/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(updateData),
  });
  
  const data = await response.json();
  
  if (response.ok) {
    return data.data;
  } else {
    throw new Error(data.error || 'Failed to update assembly');
  }
};
```

### Delete an Assembly

```javascript
const deleteAssembly = async (id) => {
  const response = await fetch(`/api/assemblies/${id}`, {
    method: 'DELETE',
  });
  
  const data = await response.json();
  
  if (response.ok) {
    return true;
  } else {
    throw new Error(data.error || 'Failed to delete assembly');
  }
};
```
