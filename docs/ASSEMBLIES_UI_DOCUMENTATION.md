# Assemblies UI Documentation

This document provides detailed information about the Assemblies UI components, their props, and usage examples.

## Table of Contents

1. [Status Components](#status-components)
   - [AssemblyStatusBadge](#assemblystatusbadge)
   - [PartsCountBadge](#partscountbadge)
2. [Action Components](#action-components)
   - [RefreshDataButton](#refreshdatabutton)
   - [DeleteAssemblyAction](#deleteassemblyaction)
   - [DuplicateAssemblyAction](#duplicateassemblyaction)
   - [QuickEditAssemblyAction](#quickeditassemblyaction)
3. [Table Components](#table-components)
   - [AssembliesTable](#assembliestable)
   - [ExpandableRow](#expandablerow)
4. [Form Components](#form-components)
   - [AssemblyFormContent](#assemblyformcontent)
5. [Context Providers](#context-providers)
   - [AssembliesProvider](#assembliesprovider)
   - [AssemblyFormProvider](#assemblyformprovider)

## Status Components

### AssemblyStatusBadge

A component that displays the status of an assembly based on its partsRequired array.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assembly | Assembly | The assembly object | Required |
| size | 'default' \| 'sm' | Size of the badge | 'default' |

#### Status Types

- **Complete**: Assembly has valid partsRequired with proper references
- **Needs Review**: Assembly has partsRequired but some have missing references
- **Incomplete**: Assembly has no partsRequired array or it is empty

#### Usage Example

```tsx
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';

// In your component
<AssemblyStatusBadge assembly={assembly} />
```

### PartsCountBadge

A component that displays the number of parts in an assembly's partsRequired array.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assembly | Assembly | The assembly object | Required |
| size | 'default' \| 'sm' | Size of the badge | 'default' |

#### Usage Example

```tsx
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';

// In your component
<PartsCountBadge assembly={assembly} />
```

## Action Components

### RefreshDataButton

A button component for refreshing data with loading state.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| onRefresh | () => Promise<void> | Function to call when refreshing | Required |
| label | string | Button label | undefined |
| className | string | Additional CSS classes | undefined |
| variant | 'default' \| 'destructive' \| 'outline' \| 'secondary' \| 'ghost' \| 'link' | Button variant | 'outline' |
| size | 'default' \| 'sm' \| 'lg' \| 'icon' | Button size | 'default' |
| showTooltip | boolean | Whether to show tooltip | true |
| tooltipText | string | Tooltip text | 'Refresh Data' |

#### Usage Example

```tsx
import { RefreshDataButton } from '@/app/components/actions/RefreshDataButton';

// In your component
<RefreshDataButton 
  onRefresh={refreshAssemblies}
  label="Refresh"
  variant="outline"
  size="sm"
/>
```

### DeleteAssemblyAction

A component for deleting an assembly with confirmation dialog.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assembly | Assembly | The assembly to delete | Required |
| variant | 'default' \| 'destructive' \| 'outline' \| 'secondary' \| 'ghost' \| 'link' \| 'icon' | Button variant | 'icon' |
| size | 'default' \| 'sm' \| 'lg' | Button size | 'sm' |
| onSuccess | () => void | Callback after successful deletion | undefined |

#### Usage Example

```tsx
import { DeleteAssemblyAction } from '@/app/components/actions/DeleteAssemblyAction';

// In your component
<DeleteAssemblyAction 
  assembly={assembly}
  onSuccess={() => refreshAssemblies()}
/>
```

### DuplicateAssemblyAction

A component for duplicating an assembly with confirmation dialog.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assembly | Assembly | The assembly to duplicate | Required |
| variant | 'default' \| 'destructive' \| 'outline' \| 'secondary' \| 'ghost' \| 'link' \| 'icon' | Button variant | 'icon' |
| size | 'default' \| 'sm' \| 'lg' | Button size | 'sm' |
| onSuccess | () => void | Callback after successful duplication | undefined |

#### Usage Example

```tsx
import { DuplicateAssemblyAction } from '@/app/components/actions/DuplicateAssemblyAction';

// In your component
<DuplicateAssemblyAction 
  assembly={assembly}
  onSuccess={() => refreshAssemblies()}
/>
```

### QuickEditAssemblyAction

A component for quick editing an assembly with a dialog form.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assembly | Assembly | The assembly to edit | Required |
| variant | 'default' \| 'destructive' \| 'outline' \| 'secondary' \| 'ghost' \| 'link' \| 'icon' | Button variant | 'icon' |
| size | 'default' \| 'sm' \| 'lg' | Button size | 'sm' |
| onSuccess | () => void | Callback after successful edit | undefined |

#### Usage Example

```tsx
import { QuickEditAssemblyAction } from '@/app/components/actions/QuickEditAssemblyAction';

// In your component
<QuickEditAssemblyAction 
  assembly={assembly}
  onSuccess={() => refreshAssemblies()}
/>
```

## Table Components

### AssembliesTable

A table component for displaying assemblies with expandable rows.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assemblies | Assembly[] | Array of assemblies to display | Required |
| simple | boolean | Whether to show a simplified version | false |

#### Usage Example

```tsx
import { AssembliesTable } from '@/app/components/tables/AssembliesTable';

// In your component
<AssembliesTable assemblies={assemblies} />
```

### ExpandableRow

A component for expandable rows in the assemblies table.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| assembly | Assembly | The assembly to display | Required |
| children | React.ReactNode | Content for the row | Required |
| colSpan | number | Number of columns to span in expanded view | Required |

#### Usage Example

```tsx
import { ExpandableRow } from '@/app/components/tables/AssembliesTable/ExpandableRow';

// In your component
<ExpandableRow 
  assembly={assembly} 
  colSpan={5}
>
  <TableCell>Cell content</TableCell>
  {/* More cells */}
</ExpandableRow>
```

## Form Components

### AssemblyFormContent

A component for creating or editing assemblies.

#### Props

None - Uses the AssemblyFormContext for state management.

#### Usage Example

```tsx
import AssemblyFormContent from '@/app/(main)/assemblies/[id]/edit/AssemblyFormContent';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';

// In your component
<AssemblyFormProvider assemblyId={id}>
  <AssemblyFormContent />
</AssemblyFormProvider>
```

## Context Providers

### AssembliesProvider

A context provider for managing assemblies data across components.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| children | React.ReactNode | Child components | Required |

#### Context Values

| Value | Type | Description |
|-------|------|-------------|
| assemblies | Assembly[] | Array of assemblies |
| isLoading | boolean | Whether assemblies are loading |
| error | string \| null | Error message if any |
| lastUpdated | Date \| null | When assemblies were last updated |
| isAutoRefreshEnabled | boolean | Whether auto-refresh is enabled |
| autoRefreshInterval | number | Auto-refresh interval in milliseconds |
| setAutoRefreshInterval | (interval: number) => void | Function to set auto-refresh interval |
| toggleAutoRefresh | () => void | Function to toggle auto-refresh |
| refreshAssemblies | () => Promise<void> | Function to refresh assemblies |
| getAssembly | (id: string) => Assembly \| undefined | Function to get an assembly by ID |
| deleteAssembly | (id: string) => Promise<boolean> | Function to delete an assembly |
| updateAssembly | (id: string, data: Partial<Assembly>) => Promise<boolean> | Function to update an assembly (using new Assembly structure) |
| createAssembly | (data: Omit<Assembly, '_id'>) => Promise<Assembly \| null> | Function to create an assembly (using new Assembly structure, omitting _id) |
| duplicateAssembly | (id: string) => Promise<Assembly \| null> | Function to duplicate an assembly |

#### Usage Example

```tsx
import { AssembliesProvider, useAssemblies } from '@/app/contexts/AssembliesContext';

// In your component
<AssembliesProvider>
  <YourComponent />
</AssembliesProvider>

// In YourComponent
const { assemblies, refreshAssemblies } = useAssemblies();
```

### AssemblyFormProvider

A context provider for managing assembly form state.

#### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| children | React.ReactNode | Child components | Required |
| assemblyId | string | ID of assembly to edit | undefined |

#### Context Values

| Value | Type | Description |
|-------|------|-------------|
| formData | Partial<Assembly> | Form data |
| isLoading | boolean | Whether form data is loading |
| isSaving | boolean | Whether form is saving |
| isEditing | boolean | Whether editing an existing assembly |
| isDirty | boolean | Whether form has unsaved changes |
| setFormData | (data: Partial<Assembly>) => void | Function to set form data |
| updateFormField | (field: string, value: any) => void | Function to update a form field |
| resetForm | () => void | Function to reset form |
| loadAssembly | (id: string) => Promise<void> | Function to load an assembly |
| saveAssembly | () => Promise<boolean> | Function to save form (using new Assembly structure) |
| addPart | (part: any) => void | Function to add a part to the partsRequired array |
| updatePart | (index: number, part: any) => void | Function to update a part in the partsRequired array |
| removePart | (index: number) => void | Function to remove a part from the partsRequired array |

#### Usage Example

```tsx
import { AssemblyFormProvider, useAssemblyForm } from '@/app/contexts/AssemblyFormContext';

// In your component
<AssemblyFormProvider assemblyId={id}>
  <YourFormComponent />
</AssemblyFormProvider>

// In YourFormComponent
const { formData, updateFormField, saveAssembly } = useAssemblyForm();
```
