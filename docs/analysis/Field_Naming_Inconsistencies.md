# Field Naming Inconsistencies

## Overview
This document identifies inconsistencies in field naming between the frontend components and backend models. These inconsistencies can lead to data transformation issues, validation errors, and bugs.

## Assembly Model vs. HierarchicalPartsForm

| Frontend (HierarchicalPartsForm) | Backend (Assembly Model) | Notes |
|----------------------------------|--------------------------|-------|
| `part_id` | `partId` | Different casing and separator style |
| `quantity` | `quantityRequired` | Completely different names |
| `children` | N/A | Frontend uses nested structure, backend uses flat structure with parent references |
| `id` | `_id` | Frontend uses UUID, backend uses MongoDB ObjectId |
| `description` | `description` | Consistent |
| `name` | `name` | Consistent |
| `assembly_id` | `assembly_id` | Consistent |

## Part Model Inconsistencies

| Field 1 | Field 2 | Notes |
|---------|---------|-------|
| `isAssembly` | `is_assembly` | Different casing and separator style |
| `unitOfMeasure` | `unit_of_measure` | Different casing and separator style |
| `part_id` (frontend) | `partNumber` (model) | Different naming convention |
| `sub_parts.part_id` | `parts.partId` | Inconsistent naming between related fields |

## Search Component Inconsistencies

| PartSearch Component | API Response | Notes |
|----------------------|--------------|-------|
| `part_id` | `partNumber` or `part_id` | Inconsistent field names in API responses |
| `is_assembly` | `isAssembly` | Different casing and separator style |

## Data Transformation Issues

1. **prepareFormData Function**: 
   - Transforms `part_id` to `partId`
   - Transforms `quantity` to `quantityRequired`
   - Flattens the hierarchical structure

2. **API Response Handling**:
   - Frontend needs to handle both `part_id` and `partNumber`
   - Frontend needs to handle both `isAssembly` and `is_assembly`

## Impact on CRUD Operations

1. **Create (POST)**:
   - Field names must be transformed before sending to API
   - Hierarchical structure must be flattened

2. **Read (GET)**:
   - API response fields must be mapped to frontend field names
   - Flat structure must be converted to hierarchical structure

3. **Update (PUT)**:
   - Same issues as Create operation
   - Additional complexity with handling existing data

4. **Delete (DELETE)**:
   - Less affected by field naming issues
   - May have issues with identifying the correct records to delete

## Recommendations

1. **Standardize Field Names**:
   - Choose one naming convention (camelCase or snake_case)
   - Update both frontend and backend to use the same convention

2. **Update Data Transformation**:
   - Modify `prepareFormData` to correctly transform all fields
   - Add validation to ensure all required fields are present

3. **Enhance API Endpoints**:
   - Standardize API response format
   - Ensure consistent field names in responses

4. **Update MongoDB Models**:
   - Remove duplicate fields
   - Use consistent naming conventions
   - Add middleware to handle backward compatibility if needed

5. **Improve Documentation**:
   - Document field mappings between frontend and backend
   - Add validation rules for each field
