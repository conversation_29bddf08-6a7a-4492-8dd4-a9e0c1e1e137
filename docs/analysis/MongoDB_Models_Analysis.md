# MongoDB Models Analysis

## Overview
The application uses MongoDB with Mongoose for data modeling. There are two primary models relevant to the assemblies functionality: `Assembly` and `Part`.

## Assembly Model

### Schema Definition
```typescript
const AssemblyPartSchema: Schema = new Schema({
  partId: {
    type: Schema.Types.ObjectId,
    ref: 'Part',
    required: [true, 'Part reference (partId) is required'],
  },
  quantityRequired: {
    type: Number,
    required: [true, 'Quantity required is required'],
    min: [1, 'Quantity required must be at least 1'],
  },
}, {
  _id: false // Prevent _id generation for subdocuments
});

const AssemblySchema: Schema = new Schema(
  {
    assembly_id: { type: String, required: true, unique: true, index: true },
    name: { type: String, required: true },
    description: { type: String },
    assembly_stage: { type: String, enum: ['Sub-Assembly', 'Final Assembly', 'SUB ASSEMBLY', 'FINAL ASSEMBLY'], index: true },
    assembly_code: { type: String },
    parts: [AssemblyPartSchema],
    assemblyDefinitionPartId: { type: Schema.Types.ObjectId, ref: 'Part', index: true },
    assemblyNumber: { type: String, index: true },
    workOrderId: { type: Schema.Types.ObjectId, ref: 'WorkOrder', index: true },
  },
  { timestamps: true }
);
```

### Key Fields
- `assembly_id`: Unique identifier for the assembly
- `name`: Name of the assembly
- `description`: Optional description
- `assembly_stage`: Stage of the assembly (Sub-Assembly or Final Assembly)
- `assembly_code`: Optional code for the assembly
- `parts`: Array of parts used in the assembly
  - `partId`: Reference to the Part model
  - `quantityRequired`: Quantity of the part needed

### Relationships
- References `Part` model through `partId` in the `parts` array
- Optional reference to `Part` model through `assemblyDefinitionPartId`
- Optional reference to `WorkOrder` model through `workOrderId`

## Part Model

### Schema Definition
```typescript
const SubPartSchema: Schema = new Schema({
  part_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'Part', 
    required: [true, 'Reference part ID is required for sub-part']
  },
  quantity: { 
    type: Number, 
    required: [true, 'Quantity is required for sub-part'], 
    min: [1, 'Quantity must be at least 1'],
    validate: {
      validator: Number.isInteger,
      message: 'Quantity must be a whole number'
    }
  }
});

const PartSchema: Schema = new Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  partNumber: { type: String, required: true, unique: true, trim: true },
  // ... other fields ...
  isAssembly: { type: Boolean, default: false },
  is_assembly: { type: Boolean, default: false },
  sub_parts: [SubPartSchema]
}, {
  timestamps: true,
  validateBeforeSave: true
});
```

### Key Fields
- `name`: Name of the part
- `description`: Optional description
- `partNumber`: Unique identifier for the part
- `isAssembly` / `is_assembly`: Boolean indicating if the part is an assembly
- `sub_parts`: Array of sub-parts if the part is an assembly
  - `part_id`: Reference to another Part
  - `quantity`: Quantity of the sub-part needed

### Relationships
- Self-referencing relationship through `sub_parts.part_id`
- Referenced by `Assembly` model through `parts.partId`

## Field Name Inconsistencies

### Assembly Model
- Frontend uses `part_id` while backend uses `partId`
- Frontend uses `quantity` while backend uses `quantityRequired`

### Part Model
- Duplicate fields: `isAssembly` and `is_assembly` (with middleware to sync them)
- Frontend sometimes uses `part_id` while the model uses `partNumber`
- Inconsistent naming between `sub_parts.part_id` and `parts.partId`

## Data Flow Issues

1. **Field Transformation**: The frontend needs to transform field names when sending data to the backend
2. **Data Validation**: Inconsistent validation between frontend and backend
3. **Duplicate Fields**: The model has duplicate fields with different naming conventions
4. **Reference Handling**: Potential issues with handling ObjectId references correctly

## Recommendations

1. **Standardize Field Names**: Use consistent naming across frontend and backend
2. **Consolidate Duplicate Fields**: Remove duplicate fields and use a single naming convention
3. **Improve Validation**: Ensure consistent validation rules between frontend and backend
4. **Enhance Reference Handling**: Improve handling of ObjectId references
5. **Update Data Transformation**: Update the `prepareFormData` function to correctly transform field names
