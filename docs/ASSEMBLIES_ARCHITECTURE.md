# Assemblies Module Architecture

This document provides an overview of the architecture for the Assemblies module, including the component structure, data flow, and design patterns.

## Architecture Overview

The Assemblies module follows a layered architecture with clear separation of concerns:

1. **UI Layer**: React components for displaying and interacting with assemblies
2. **State Management Layer**: Context providers for managing application state
3. **Data Access Layer**: API endpoints for CRUD operations on assemblies
4. **Database Layer**: MongoDB models and schemas for assemblies data

## Directory Structure

```
app/
├── (main)/
│   └── assemblies/
│       ├── [id]/
│       │   └── edit/
│       │       ├── AssemblyFormContent.tsx
│       │       ├── AssemblyFormWrapper.tsx
│       │       └── page.tsx
│       ├── create/
│       │   ├── AssemblyFormWrapper.tsx
│       │   └── page.tsx
│       ├── AssembliesPageContent.tsx
│       ├── AssembliesPageWrapper.tsx
│       └── page.tsx
├── api/
│   └── assemblies/
│       ├── [id]/
│       │   └── route.ts
│       └── route.ts
├── components/
│   ├── actions/
│   │   ├── DeleteAssemblyAction.tsx
│   │   ├── DuplicateAssemblyAction.tsx
│   │   ├── QuickEditAssemblyAction.tsx
│   │   └── RefreshDataButton.tsx
│   ├── controls/
│   │   └── AutoRefreshControl.tsx
│   ├── forms/
│   │   └── HierarchicalPartsForm.tsx
│   ├── status/
│   │   ├── AssemblyStatusBadge.tsx
│   │   └── PartsCountBadge.tsx
│   └── tables/
│       └── AssembliesTable/
│           ├── AssembliesTable.tsx
│           ├── AssembliesTableClient.tsx
│           ├── ExpandableRow.tsx
│           └── types.ts
├── contexts/
│   ├── AssembliesContext.tsx
│   └── AssemblyFormContext.tsx
├── lib/
│   └── utils.ts
└── models/
    └── Assembly.ts
```

## Component Hierarchy

```
AssembliesPageWrapper
└── AssembliesProvider
    └── AssembliesPageContent
        ├── AutoRefreshControl
        └── AssembliesTable
            └── ExpandableRow

AssemblyFormWrapper
└── AssemblyFormProvider
    └── AssemblyFormContent
        └── HierarchicalPartsForm
```

## Data Flow

1. **Data Fetching**:
   - AssembliesContext fetches assemblies data from the API
   - AssemblyFormContext fetches a single assembly for editing

2. **State Management**:
   - AssembliesContext manages the list of assemblies and CRUD operations
   - AssemblyFormContext manages the form state for creating/editing assemblies

3. **UI Updates**:
   - Components subscribe to context changes using the useAssemblies and useAssemblyForm hooks
   - Optimistic UI updates are applied for immediate feedback

4. **API Interactions**:
   - API calls are made through the context providers
   - Responses are handled and state is updated accordingly

## Design Patterns

### Context Provider Pattern

The module uses React Context for state management, with separate contexts for different concerns:

- **AssembliesContext**: Manages the list of assemblies and CRUD operations
- **AssemblyFormContext**: Manages the form state for creating/editing assemblies

### Optimistic UI Updates

The module implements optimistic UI updates for a better user experience:

1. Update the UI immediately with the expected result
2. Make the API call in the background
3. If the API call succeeds, keep the updated UI
4. If the API call fails, revert to the previous state

### Component Composition

The module uses component composition to create reusable and maintainable UI components:

- **Status Components**: AssemblyStatusBadge, PartsCountBadge
- **Action Components**: DeleteAssemblyAction, DuplicateAssemblyAction, QuickEditAssemblyAction
- **Table Components**: AssembliesTable, ExpandableRow

### Separation of Client and Server Components

The module follows Next.js best practices by separating client and server components:

- **Server Components**: Page components that fetch initial data
- **Client Components**: Interactive components that manage state and handle user interactions

## State Management

### AssembliesContext

The AssembliesContext provides the following state:

- **assemblies**: Array of assemblies
- **isLoading**: Whether assemblies are loading
- **error**: Error message if any
- **lastUpdated**: When assemblies were last updated
- **isAutoRefreshEnabled**: Whether auto-refresh is enabled
- **autoRefreshInterval**: Auto-refresh interval in milliseconds

And the following actions:

- **refreshAssemblies**: Function to refresh assemblies
- **getAssembly**: Function to get an assembly by ID
- **deleteAssembly**: Function to delete an assembly
- **updateAssembly**: Function to update an assembly
- **createAssembly**: Function to create an assembly
- **duplicateAssembly**: Function to duplicate an assembly

### AssemblyFormContext

The AssemblyFormContext provides the following state:

- **formData**: Form data
- **isLoading**: Whether form data is loading
- **isSaving**: Whether form is saving
- **isEditing**: Whether editing an existing assembly
- **isDirty**: Whether form has unsaved changes

And the following actions:

- **setFormData**: Function to set form data
- **updateFormField**: Function to update a form field
- **resetForm**: Function to reset form
- **loadAssembly**: Function to load an assembly
- **saveAssembly**: Function to save form
- **addPart**: Function to add a part
- **updatePart**: Function to update a part
- **removePart**: Function to remove a part

## API Endpoints

### GET /api/assemblies

Retrieves a list of all assemblies.

### GET /api/assemblies/:id

Retrieves a single assembly by its ID.

### POST /api/assemblies

Creates a new assembly.

### PATCH /api/assemblies/:id

Updates an existing assembly.

### DELETE /api/assemblies/:id

Deletes an assembly.

## Database Schema

The Assembly model has the following schema:

```typescript
{
  _id: ObjectId,
  assembly_id: String,
  name: String,
  description: String,
  assembly_stage: String,
  parts: [
    {
      partId: { type: ObjectId, ref: 'Part' },
      quantityRequired: Number
    }
  ],
  createdAt: Date,
  updatedAt: Date
}
```

## Testing Strategy

The module includes the following types of tests:

1. **Unit Tests**: Test individual components and functions
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test the complete user flow

### Test Files

- **__tests__/components/status/AssemblyStatusBadge.test.tsx**: Tests for the AssemblyStatusBadge component
- **__tests__/components/status/PartsCountBadge.test.tsx**: Tests for the PartsCountBadge component
- **__tests__/components/actions/RefreshDataButton.test.tsx**: Tests for the RefreshDataButton component
- **__tests__/contexts/AssembliesContext.test.tsx**: Tests for the AssembliesContext
- **__tests__/contexts/AssemblyFormContext.test.tsx**: Tests for the AssemblyFormContext

## Performance Considerations

1. **Optimistic UI Updates**: Provide immediate feedback to users
2. **Pagination**: Limit the number of assemblies fetched at once
3. **Memoization**: Use React.memo and useMemo to prevent unnecessary re-renders
4. **Debouncing**: Debounce user input to prevent excessive API calls
5. **Auto-Refresh**: Allow users to control auto-refresh settings

## Security Considerations

1. **Input Validation**: Validate user input on both client and server
2. **API Rate Limiting**: Limit the number of API calls from a single client
3. **Authentication**: Ensure only authenticated users can access the API
4. **Authorization**: Ensure users can only access assemblies they have permission to

## Future Improvements

1. **Real-time Updates**: Implement WebSockets for real-time updates
2. **Offline Support**: Add offline support with service workers
3. **Advanced Filtering**: Add more advanced filtering options
4. **Bulk Operations**: Add support for bulk operations (delete, update)
5. **Version History**: Track changes to assemblies over time
