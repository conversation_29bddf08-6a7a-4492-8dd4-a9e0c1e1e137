{"name": "inventory-management-system", "private": true, "version": "0.1.0", "scripts": {"setup-env": "node create-env.cjs", "dev": "node create-env.cjs && next dev --turbopack -p 5174", "build": "next build", "start": "next start", "lint": "eslint . --config eslint.config.js", "test-db": "node test-db-connection.cjs", "test-ssl": "node test-ssl-connection.cjs", "diagnose-db": "node diagnose-mongodb.cjs", "verify-db-data": "node verify-mongodb-data.cjs", "migrate-data": "node init-mongodb.cjs", "migrate-assemblies": "node scripts/migrate-assemblies.cjs", "init-assemblies": "node scripts/init-assemblies.js", "check-env": "node check-env.cjs", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "mcp-postman": "node mcp-postman-runner.js", "run-postman-tests": "node run-all-api-tests.js", "test:api": "node run-all-api-tests.js", "test:api:single": "node run-single-collection.js", "test:inventory": "jest __tests__/api/inventory/route.test.js", "test:transactions": "jest __tests__/api/inventory-transactions/route.test.js", "test:parts-id": "jest __tests__/api/parts/\\[id\\]/route.test.js", "test:batch-tracking": "jest __tests__/api/batch-tracking/route.test.js", "test:assemblies": "jest __tests__/api/assemblies/route.test.js", "test:assemblies:playwright": "npx playwright test tests/playwright/assembly-crud.test.js --headed", "test:create-assembly": "npx playwright test tests/playwright/create-assembly.test.js --headed", "test:update-assembly": "npx playwright test tests/playwright/update-assembly.test.js --headed", "test:delete-assembly": "npx playwright test tests/playwright/delete-assembly.test.js --headed", "test:view-assembly": "npx playwright test tests/playwright/view-assembly.test.js --headed", "test:assemblies:all": "npx playwright test tests/playwright/*-assembly.test.js --headed", "test:analytics": "jest __tests__/api/analytics/route.test.js", "test:categories": "jest __tests__/api/categories/route.test.js", "test:hierarchical-part-entry": "jest __tests__/api/hierarchical-part-entry/route.test.js", "test:hierarchical-builder": "jest __tests__/api/hierarchical-builder/route.test.js", "test:batches-logs": "jest __tests__/api/batches/logs.test.js", "test:batches-inventory": "jest __tests__/api/batches/inventory.test.js", "mcp:postman": "node mcp-postman-runner.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.12", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.5", "@radix-ui/react-slider": "^1.3.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@sentry/nextjs": "^9.19.0", "@types/lodash.debounce": "^4.0.9", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.3.1", "dotenv": "^16.4.7", "framer-motion": "^11.0.8", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.503.0", "mongodb": "^6.15.0", "mongoose": "^8.13.1", "next": "^15.2.4", "react": "^19.1.0", "react-confirm-alert": "^3.0.6", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "recharts": "^2.12.2", "sonner": "^2.0.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@next/bundle-analyzer": "^15.3.2", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@types/supertest": "^6.0.3", "autoprefixer": "^10.4.18", "eslint": "^8", "eslint-config-next": "^15.2.4", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mongodb-memory-server": "^10.1.4", "newman": "^6.2.1", "postcss": "^8.4.35", "supertest": "^7.1.0", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-eslint": "^8.3.0"}, "overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.1"}, "description": "Trend IMS is a web-based application designed to manage inventory, track products, assemblies, purchase orders, and provide insights into stock levels and production status. It features a dashboard for quick overview and actions, detailed views for products and assemblies, and utilizes a MongoDB database for data persistence.", "main": "capture-console-errors.js", "directories": {"doc": "docs", "lib": "lib"}, "keywords": [], "author": "", "license": "ISC"}