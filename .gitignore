# Dependencies
/node_modules/
/.pnp
.pnp.js
.yarn/
.pnpm/
web_modules/
jspm_packages/
bower_components/

# Build and Production
/build/
/dist/
/.next/
/out/
/.swc/
/.vercel/
/.cache/
/.turbo/
/.turbo-cache/
/.webpack/
/.parcel-cache/
.fusebox/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Development Environments
/.clinerules/
.clinerules
/.trae/
/.augment.json
/.augmentignore
/.context-include
/.sentrycliignore
@.roo/
*.roo
.grid

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local
.env.sentry-build-plugin

# Logs and Debugging
/logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
debug-*.js
capture-console-errors.js
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Testing
/coverage/
/.nyc_output/
/__tests__/
/__mocks__/
/__snapshots__/
*.lcov
jest.config.js
jest.setup.js
diagnose-mongodb.cjs
test-db-connection.cjs
test-ssl-connection.cjs
test-mcp-api.js
run-all-api-tests.js
run-single-collection.js
run-tests.js
verify-mongodb-data.cjs

# IDE and Editor Settings
/.idea/
/.vscode/
*.swp
*.swo
.cursor/
*.cursor
.cursorignore
*.sublime*
.project
.classpath
.c9/
*.launch
.settings/

# Database and MongoDB
*.sqlite
*.sqlite3
*.db
*.db3
mcp-postman/
mcp-postman-client.js
mcp-postman-config.json
mcp-postman-runner.js
postman-collections/
init-mongodb.cjs
#create-env.cjs
#check-env.cjs
mongodb.config.json
app/services/mongodb.ts.debug

# Instrumentation and Monitoring
sentry-mcp/
instrumentation.ts
instrumentation-client.ts
sentry.edge.config.ts
sentry.server.config.ts
mcp-taskmanager/

# TypeScript and JavaScript
*.tsbuildinfo
next-env.d.ts
tsconfig.app.json
tsconfig.node.json
*.chunk.js
*.chunk.css
*.js.map
*.css.map

# System Files
.DS_Store
**/.DS_Store
Thumbs.db
*.stackdump
Desktop.ini
$RECYCLE.BIN/
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Backup and Temporary Files
*.tmp
*.temp
.git-backup/
/backup/
/js_backup/
/sql_backup/
/split_sql/
*_backup/
*.bak
*.backup
*.old
migration-audit-report.json
migration-audit-report.md
assemblies.html

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Documentation Files
*.md
!README.md
!REQUIREMENTS.md
!database_schema_updated.md
!schema_refactor_checklist.md
!api-test-summary.md
!task_list_testing/*

# Specific Project Directories
/tasks/
/task_list_testing/

# Generated Content
storybook-static/
build-storybook.log
*.generated.*
.dynamodb/

/test-results/
/tests/
/test-output/
/playwright-report/
/playwright.config.js

/postman-test/