// This is a Server Component by default (no "use client" directive)
import { ErrorDisplayProps } from './types';
import ErrorDisplayClient from './ErrorDisplayClient';

/**
 * ErrorDisplay component following Next.js best practices
 * 
 * Server component that checks for interactivity needs and delegates
 * to the client component when necessary.
 */
export default function ErrorDisplay(props: ErrorDisplayProps) {
  const hasInteractivity = !!props.onRetry;
  
  // If no retry functionality is needed, we could render a static version
  // For simplicity and since animations are used, we'll use the client for now
  // In a future update, we could implement a static server version
  
  return <ErrorDisplayClient {...props} />;
}
