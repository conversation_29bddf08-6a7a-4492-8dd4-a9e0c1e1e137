"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Tag, ArrowUp, ArrowDown, AlertTriangle, Plus } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Product data structure for the ProductCard component
 */
interface ProductCardProduct {
  /** Unique identifier for the product */
  id: string;
  /** Name of the product */
  name: string;
  /** Optional description of the product */
  description?: string | null | undefined;
  /** Optional URL to the product image */
  imageUrl?: string | null | undefined;
  /** Current stock quantity */
  currentStock: number;
  /** Minimum stock level before reordering */
  reorderLevel: number;
  /** Optional product category */
  category?: string | null | undefined;
  /** Optional product price */
  price?: number;
  /** Optional total value of on-hand inventory */
  onHandValue?: number | null | undefined;
  /** Optional demand level classification */
  demand?: 'High' | 'Medium' | 'Low' | null;
}

/**
 * Props for the ProductCard component
 */
interface ProductCardProps {
  /** Product data to display */
  product: ProductCardProduct;
  /** Whether this is a placeholder card */
  isPlaceholder?: boolean;
  /** Whether this card should be featured/highlighted */
  isFeatured?: boolean;
}

/**
 * Card component for displaying product information
 * Shows product details, stock status, and pricing information
 */
const ProductCard: React.FC<ProductCardProps> = ({ product, isPlaceholder = false, isFeatured = false }) => {
  const { theme } = useTheme();

  /**
   * Determines the stock status based on current stock and reorder level
   * @returns Object containing color, label, and icon for the stock status
   */
  const getStockStatus = () => {
    if (product.currentStock === 0) return { color: 'red', label: 'Out of Stock', icon: <AlertTriangle size={14} /> };
    if (product.currentStock < product.reorderLevel) return { color: 'yellow', label: 'Low Stock', icon: <AlertTriangle size={14} /> };
    return { color: 'green', label: 'In Stock', icon: <ArrowUp size={14} /> };
  };

  const getDemandLabel = () => {
    if (!product.demand) return null;

    switch (product.demand) {
      case 'High':
        return { color: 'red', label: 'High Demand', icon: <ArrowUp size={14} /> };
      case 'Medium':
        return { color: 'yellow', label: 'Medium Demand', icon: <ArrowUp size={14} /> };
      case 'Low':
        return { color: 'blue', label: 'Low Demand', icon: <ArrowDown size={14} /> };
      default:
        return null;
    }
  };

  const stockStatus = getStockStatus();
  const demandLabel = getDemandLabel();

  // CSS classes for status colors
  const getStatusClasses = (type: string, color: string) => {
    if (type === 'bg') {
      return `${type}-${color}-100 dark:${type}-${color}-900/30`;
    }
    return `${type}-${color}-600 dark:${type}-${color}-400`;
  };

  return (
    <motion.div
      whileHover={{ y: -5, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' }}
      className={`${theme === 'dark' ? 'bg-dark-800/60 backdrop-blur-md shadow-lg' : 'bg-white/80 backdrop-blur-md shadow-md'} rounded-xl overflow-hidden h-full cursor-pointer border border-transparent hover:border-gray-200 dark:hover:border-dark-700 transition-all duration-300`}
    >
      {/* Product Image */}
      <div className="relative w-full h-40 bg-gray-100 dark:bg-dark-700">
        {product.imageUrl ? (
          <img
            src={product.imageUrl}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            {isPlaceholder ? (
              <div className="text-center">
                <Plus size={24} className="mx-auto mb-2 text-gray-400 dark:text-gray-600" />
                <span className="text-sm text-gray-500 dark:text-gray-400">Add New Product</span>
              </div>
            ) : (
              <Tag size={24} className="text-gray-400 dark:text-gray-600" />
            )}
          </div>
        )}

        {/* Stock Status Tag */}
        {!isPlaceholder && stockStatus && (
          <div className={`absolute top-2 right-2 px-2 py-1 text-xs rounded-full ${getStatusClasses('bg', stockStatus.color)} ${getStatusClasses('text', stockStatus.color)} flex items-center`}>
            {stockStatus.icon}
            <span className="ml-1">{stockStatus.label}</span>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-medium text-gray-900 dark:text-white truncate">{product.name}</h3>

        {product.description && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 h-10 overflow-hidden">
            {product.description}
          </p>
        )}

        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center">
            {product.category && (
              <span className="text-xs bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded">
                {product.category}
              </span>
            )}
          </div>

          {demandLabel && !isPlaceholder && (
            <span className={`text-xs ${getStatusClasses('bg', demandLabel.color)} ${getStatusClasses('text', demandLabel.color)} px-2 py-1 rounded-full flex items-center`}>
              {demandLabel.icon}
              <span className="ml-1">{demandLabel.label}</span>
            </span>
          )}
        </div>

        {!isPlaceholder && (
          <div className="mt-4 pt-3 border-t border-gray-100 dark:border-dark-700">
            <div className="flex justify-between items-center">
              <div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Stock</div>
                <div className="font-medium">{product.currentStock} units</div>
              </div>

              {product.onHandValue !== undefined && product.onHandValue !== null && (
                <div className="text-right">
                  <div className="text-xs text-gray-500 dark:text-gray-400">Value</div>
                  <div className="font-medium">${product.onHandValue.toLocaleString()}</div>
                </div>
              )}
            </div>
          </div>
        )}

        {isPlaceholder && (
          <div className="mt-4 pt-3 border-t border-gray-100 dark:border-dark-700">
            <div className="flex justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded flex items-center"
              >
                <Plus size={14} className="mr-1" />
                <span>Add New Product</span>
              </motion.button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ProductCard;
