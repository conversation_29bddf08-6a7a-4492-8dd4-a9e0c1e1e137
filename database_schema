### MongoDB Schema Definition (IMS Database) - Revised

This schema definition reflects the updated structure of the Inventory Management System (IMS) database, incorporating best practices for referencing and data integrity, notably the use of `ObjectId` for part references. Each collection description is followed by a sample JSON document.

---

**1. `parts` Collection**

Stores detailed information about individual parts.

* `_id`: `ObjectId` (Primary Key, unique MongoDB identifier)
* `partNumber`: `String` (Unique business identifier for the part, e.g., "U.21.3217P/NU2220-C3". **Should be indexed.**)
* `name`: `String` (Name of the part)
* `description`: `String` (Optional description)
* `technicalSpecs`: `String | Null` (Optional technical specifications)
* `isManufactured`: `Boolean` (Indicates if the part is manufactured in-house or purchased)
* `reorderLevel`: `Int32 | Null` (Stock level at which reordering is triggered)
* `status`: `String` (e.g., "active", "inactive", "obsolete")
* `inventory`: `Object`
    * `currentStock`: `Int32` (Current quantity on hand)
    * `warehouseId`: `ObjectId` (Reference to `warehouses._id` indicating where the stock is located)
    * `safetyStockLevel`: `Int32` (Minimum stock to avoid stockouts)
    * `maximumStockLevel`: `Int32` (Maximum desired stock level)
    * `averageDailyUsage`: `Double` (Estimated daily consumption rate)
    * `abcClassification`: `String` (ABC analysis category, e.g., "A", "B", "C")
    * `lastStockUpdate`: `Date | Null` (Timestamp of the last inventory update for this part)
* `supplierId`: `ObjectId | Null` (Reference to `suppliers._id` if this part is typically purchased from a specific supplier)
* `unitOfMeasure`: `String` (e.g., "pcs", "kg", "meter")
* `costPrice`: `Double` (Cost of acquiring/manufacturing one unit of the part)
* `categoryId`: `ObjectId | Null` (Reference to `categories._id`)
* `createdAt`: `Date` (Timestamp of part record creation)
* `updatedAt`: `Date` (Timestamp of last part record update)

**Sample Data:**
```json
{
  "_id": { 
    "$oid": "6640f0a0a1b2c3d4e5f6a00a" 
  }, 
  "partNumber": "DL23.108", 
  "name": "Spacer  Ring", 
  "description": "", 
  "technicalSpecs": "", 
  "isManufactured": true, 
  "reorderLevel": 20, 
  "status": "active", 
  "inventory": { 
    "currentStock": 169, 
    "warehouseId": { 
      "$oid": "65f000000000000000000001" 
    }, 
    "safetyStockLevel": 10, 
    "maximumStockLevel": 60, 
    "averageDailyUsage": 0.5, 
    "abcClassification": "A", 
    "lastStockUpdate": { 
      "$date": "2025-02-28T21:20:28.000Z" 
    } 
  }, 
  "supplierId": { 
    "$oid": "681f796ad6a21248b8ec7600" 
  }, 
  "unitOfMeasure": "pcs", 
  "costPrice": 8.9, 
  "categoryId": { 
    "$oid": "65f000020000000000000001" 
  }, 
  "createdAt": { 
    "$date": "2025-02-28T21:20:28.000Z" 
  }, 
  "updatedAt": { 
    "$date": "2025-02-28T21:20:28.000Z" 
  } 
}
```

---

**2. `warehouses` Collection**

Details about storage locations.

* `_id`: `ObjectId` (Primary Key)
* `location_id`: `String` (Unique business identifier for the warehouse/location, e.g., "WH-PF-01")
* `name`: `String` (Human-readable name of the warehouse/location)
* `location`: `String` (Descriptive location details, e.g., "Main Building, Ground Floor")
* `capacity`: `Int32` (Storage capacity, unit depends on context)
* `manager`: `String` (Name of the manager responsible)
* `contact`: `String` (Contact information for the warehouse)
* `isBinTracked`: `Boolean` (Indicates if the warehouse uses bin locations for parts)
* `createdAt`: `Date` (Timestamp of warehouse record creation)
* `updatedAt`: `Date` (Timestamp of last warehouse record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000000000000000000001" },
  "location_id": "WH-PF-01",
  "name": "Production Floor",
  "location": "Main Building, Ground Floor",
  "capacity": 1000,
  "manager": "Sarah Miller",
  "contact": "<EMAIL>",
  "isBinTracked": true,
  "createdAt": { "$date": "2023-01-01T10:00:00.000Z" },
  "updatedAt": { "$date": "2023-01-01T10:00:00.000Z" }
}
```

---

**3. `suppliers` Collection**

Information about part suppliers.

* `_id`: `ObjectId` (Primary Key)
* `supplier_id`: `String` (Unique business identifier for the supplier, e.g., "SUP001")
* `name`: `String` (Name of the supplier company)
* `contactPerson`: `String` (Name of the primary contact at the supplier)
* `email`: `String` (Supplier contact email)
* `phone`: `String` (Supplier contact phone number)
* `address`: `String` (Supplier's physical address)
* `specialty`: `Array<String>` (List of product types or part categories the supplier specializes in)
* `rating`: `Double | Int32` (Supplier performance rating)
* `payment_terms`: `String | Null` (e.g., "Net 30", "Net 60")
* `delivery_terms`: `String | Null` (e.g., "FOB Destination", "EXW")
* `is_active`: `Boolean` (Indicates if the supplier is currently active)
* `createdAt`: `Date` (Timestamp of supplier record creation)
* `updatedAt`: `Date` (Timestamp of last supplier record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "681f796ad6a21248b8ec75ff" },
  "supplier_id": "SUP001",
  "name": "Tamping Systems",
  "contactPerson": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "address": "123 Main St, Anytown, USA",
  "specialty": ["Tamping Units", "Railway Maintenance Equipment"],
  "rating": 4.5,
  "payment_terms": "Net 30",
  "delivery_terms": "FOB Destination",
  "is_active": true,
  "createdAt": { "$date": "2025-05-10T16:06:02.256Z" },
  "updatedAt": { "$date": "2025-05-10T16:06:02.256Z" }
}
```

---

**4. `users` Collection**

User accounts for the IMS.

* `_id`: `ObjectId` (Primary Key)
* `username`: `String` (Unique login username)
* `email`: `String` (User's email address, should be unique)
* `fullName`: `String` (User's full name)
* `role`: `String` (User role, e.g., "admin", "manager", "staff")
* `passwordHash`: `String` (Securely hashed password)
* `isActive`: `Boolean` (Indicates if the user account is active)
* `lastLoginAt`: `Date | Null` (Timestamp of the user's last login)
* `createdAt`: `Date` (Timestamp of user record creation)
* `updatedAt`: `Date` (Timestamp of last user record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000010000000000000001" },
  "username": "admin_user",
  "email": "<EMAIL>",
  "fullName": "Administrator",
  "role": "admin",
  "passwordHash": "$2b$12$abcdefghijklmnopqrstuvwxyzABCDEFG",
  "isActive": true,
  "lastLoginAt": { "$date": "2025-05-10T10:00:00.000Z" },
  "createdAt": { "$date": "2023-01-01T09:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-10T10:00:00.000Z" }
}
```

---

**5. `categories` Collection**

Hierarchical categorization for parts and products.

* `_id`: `ObjectId` (Primary Key)
* `name`: `String` (Name of the category, unique within its level)
* `description`: `String` (Optional description of the category)
* `parentCategory`: `ObjectId | Null` (Reference to `categories._id` of the parent category, null for top-level categories)
* `createdAt`: `Date` (Timestamp of category record creation)
* `updatedAt`: `Date` (Timestamp of last category record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000020000000000000001" },
  "name": "Bearings",
  "description": "All types of bearings.",
  "parentCategory": null,
  "createdAt": { "$date": "2023-01-05T10:00:00.000Z" },
  "updatedAt": { "$date": "2023-01-05T10:00:00.000Z" }
}
```

---

**6. `products` Collection**

Information about finished goods that can be sold. Products can be manufactured from assemblies or be direct resales of parts.

* `_id`: `ObjectId` (Primary Key)
* `productCode`: `String` (Unique business code for the product, e.g., "PROD-TAMP-UNIT-DUO")
* `name`: `String` (Name of the product)
* `description`: `String` (Description of the product)
* `categoryId`: `ObjectId` (Reference to `categories._id`)
* `status`: `String` (e.g., "active", "discontinued", "in_development")
* `sellingPrice`: `Double` (Price at which the product is sold)
* `assemblyId`: `ObjectId | Null` (Reference to `assemblies._id` if this product is an assembly)
* `partId`: `ObjectId | Null` (Reference to `parts._id` if this product is a direct resale of a part)
* `createdAt`: `Date` (Timestamp of product record creation)
* `updatedAt`: `Date` (Timestamp of last product record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "6628c5f0a1b2c3d4e5f6a7b2" },
  "productCode": "PROD-TAMP-UNIT-DUO",
  "name": "Complete Tamping Unit (Duomatic Model)",
  "description": "Fully assembled tamping unit for Duomatic machines.",
  "categoryId": { "$oid": "65f000020000000000000005" },
  "status": "active",
  "sellingPrice": 62000.00,
  "assemblyId": null,
  "partId": null,
  "createdAt": { "$date": "2024-02-11T11:00:00.000Z" },
  "updatedAt": { "$date": "2024-02-11T11:00:00.000Z" }
}
```

---

**7. `assemblies` Collection**

Defines Bills of Materials (BOMs) for manufactured items.

* `_id`: `ObjectId` (Primary Key)
* `assemblyCode`: `String` (Unique business code for the assembly, e.g., "ASM-TA-100")
* `name`: `String` (Name of the assembly)
* `productId`: `ObjectId | Null` (Reference to `products._id` if this assembly is sold as a specific product)
* `parentId`: `ObjectId | Null` (Reference to another `assemblies._id` if this is a sub-assembly)
* `isTopLevel`: `Boolean` (Indicates if this is a top-level assembly, not part of another assembly)
* `partsRequired`: `Array<Object>` (List of component parts and their quantities)
    * `partId`: `ObjectId` (Reference to `parts._id` of the component part)
    * `quantityRequired`: `Int32`
    * `unitOfMeasure`: `String` (Should ideally match the UoM of the referenced part)
* `status`: `String` (Assembly definition status, e.g., "active", "pending_review", "obsolete")
* `version`: `Int32` (Version number of the assembly definition)
* `manufacturingInstructions`: `String | Null` (Link to or text of manufacturing SOPs)
* `estimatedBuildTime`: `String | Null` (e.g., "1.5 hours", "30 minutes")
* `createdAt`: `Date` (Timestamp of assembly definition creation)
* `updatedAt`: `Date` (Timestamp of last assembly definition update)

**Sample Data:**
```json
{
  "_id": { "$oid": "681f796bd6a21248b8ec7640" },
  "assemblyCode": "ASM-TA-100",
  "name": "Tamping Arm Assembly",
  "productId": null,
  "parentId": null,
  "isTopLevel": false,
  "partsRequired": [
    { "partId": { "$oid": "6640f1000000000000000001" }, "quantityRequired": 2, "unitOfMeasure": "pcs" },
    { "partId": { "$oid": "6640f1000000000000000002" }, "quantityRequired": 8, "unitOfMeasure": "pcs" }
  ],
  "status": "active",
  "version": 1,
  "manufacturingInstructions": "Follow SOP-ASM-100 for assembly.",
  "estimatedBuildTime": "1.5 hours",
  "createdAt": { "$date": "2025-05-10T16:06:03.385Z" },
  "updatedAt": { "$date": "2025-05-11T10:00:00.000Z" }
}
```

---

**8. `transactions` Collection**

Records all inventory movements.

* `_id`: `ObjectId` (Primary Key)
* `partId`: `ObjectId` (Reference to `parts._id` for which the transaction occurred)
* `warehouseId`: `ObjectId` (Reference to `warehouses._id` where the transaction took place)
* `transactionType`: `String` (e.g., "stock_in_purchase", "stock_out_production", "adjustment_cycle_count", "stock_in_production", "transfer_out", "transfer_in", "sales_shipment")
* `quantity`: `Int32` (Quantity involved; positive for stock in, negative for stock out)
* `previousStock`: `Int32` (Stock level of the part in the warehouse before this transaction)
* `newStock`: `Int32` (Stock level of the part in the warehouse after this transaction)
* `transactionDate`: `Date` (Date and time the transaction occurred)
* `referenceNumber`: `String | Null` (Reference to related documents, e.g., PO number, WO number, SO number, Adjustment ID)
* `referenceType`: `String | Null` (Type of the reference document, e.g., "PurchaseOrder", "WorkOrder", "SalesOrder", "StockAdjustment")
* `userId`: `ObjectId` (Reference to `users._id` of the user who performed or initiated the transaction)
* `notes`: `String` (Additional notes or reasons for the transaction)
* `createdAt`: `Date` (Timestamp of transaction record creation)

**Sample Data:**
```json
{
  "_id": { "$oid": "6809e615f6450d12271ec00f" },
  "partId": { "$oid": "6640f0a0a1b2c3d4e5f6a001" },
  "warehouseId": { "$oid": "65f000000000000000000001" },
  "transactionType": "stock_in_purchase",
  "quantity": 50,
  "previousStock": 52,
  "newStock": 102,
  "transactionDate": { "$date": "2025-03-01T10:00:00.000Z" },
  "referenceNumber": "PO-001",
  "referenceType": "PurchaseOrder",
  "userId": { "$oid": "65f000010000000000000003" },
  "notes": "Received from Reliable Bearings Co.",
  "createdAt": { "$date": "2025-03-01T10:05:00.000Z" }
}
```

---

**9. `purchaseorders` Collection**

Orders placed with suppliers for acquiring parts.

* `_id`: `ObjectId` (Primary Key)
* `poNumber`: `String` (Unique purchase order number, e.g., "PO-2025-001")
* `supplierId`: `ObjectId` (Reference to `suppliers._id`)
* `orderDate`: `Date` (Date the order was placed)
* `expectedDeliveryDate`: `Date` (Expected delivery date from the supplier)
* `items`: `Array<Object>` (List of parts being ordered)
    * `partId`: `ObjectId` (Reference to `parts._id`)
    * `description`: `String` (Part description, can be copied from part record at time of PO creation)
    * `quantity`: `Int32` (Quantity ordered)
    * `unitPrice`: `Double` (Price per unit at the time of order)
    * `lineTotal`: `Double` (Calculated as quantity * unitPrice)
    * `receivedQuantity`: `Int32` (Quantity received against this line item so far)
* `totalAmount`: `Double` (Total cost of the order)
* `status`: `String` (Order status, e.g., "pending_approval", "ordered", "partially_received", "fully_received", "cancelled")
* `notes`: `String` (Additional notes for the PO)
* `shippingAddress`: `String` (Address where the goods are to be shipped)
* `billingAddress`: `String` (Address for billing purposes)
* `termsAndConditions`: `String` (Specific terms related to this PO)
* `createdBy`: `ObjectId` (Reference to `users._id` of the user who created the PO)
* `approvedBy`: `ObjectId | Null` (Reference to `users._id` of the user who approved the PO)
* `approvalDate`: `Date | Null` (Timestamp of PO approval)
* `createdAt`: `Date` (Timestamp of PO record creation)
* `updatedAt`: `Date` (Timestamp of last PO record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000050000000000000001" },
  "poNumber": "PO-2025-001",
  "supplierId": { "$oid": "681f796ad6a21248b8ec7600" },
  "orderDate": { "$date": "2025-05-01T10:00:00.000Z" },
  "expectedDeliveryDate": { "$date": "2025-05-15T10:00:00.000Z" },
  "items": [
    {
      "partId": { "$oid": "6640f0a0a1b2c3d4e5f6a001" },
      "description": "Roller bearing Plasser & Theurer/SKF/FAG make",
      "quantity": 50,
      "unitPrice": 70.00,
      "lineTotal": 3500.00,
      "receivedQuantity": 0
    }
  ],
  "totalAmount": 3590.00,
  "status": "ordered",
  "notes": "Standard order for stock replenishment.",
  "shippingAddress": "123 Manufacturing Lane, InduCity, ST 54321",
  "billingAddress": "123 Manufacturing Lane, InduCity, ST 54321",
  "termsAndConditions": "Net 30 days. Delivery by May 15th.",
  "createdBy": { "$oid": "65f000010000000000000002" },
  "approvedBy": { "$oid": "65f000010000000000000001" },
  "approvalDate": { "$date": "2025-05-01T11:00:00.000Z" },
  "createdAt": { "$date": "2025-05-01T10:05:00.000Z" },
  "updatedAt": { "$date": "2025-05-01T11:05:00.000Z" }
}
```

---

**10. `workorders` Collection**

Orders to manufacture parts or assemble products.

* `_id`: `ObjectId` (Primary Key)
* `woNumber`: `String` (Unique work order number, e.g., "WO-2025-001")
* `assemblyId`: `ObjectId | Null` (Reference to `assemblies._id` if producing an assembly)
* `partIdToManufacture`: `ObjectId | Null` (Reference to `parts._id` if manufacturing a specific part not defined as an assembly)
* `productId`: `ObjectId | Null` (Reference to `products._id` if the WO is to produce a saleable product)
* `quantity`: `Int32` (Quantity to produce)
* `status`: `String` (Order status, e.g., "planned", "released", "in_progress", "completed", "on_hold", "cancelled")
* `priority`: `String` (Priority level, e.g., "low", "medium", "high", "urgent")
* `dueDate`: `Date` (Date by which the work order should be completed)
* `startDate`: `Date | Null` (Actual start date of production)
* `completedAt`: `Date | Null` (Actual completion timestamp)
* `assignedTo`: `ObjectId | Null` (Reference to `users._id` or a team/department responsible)
* `notes`: `String` (Additional notes for the work order)
* `sourceDemand`: `String | Null` (Origin of the demand, e.g., "SalesOrder-SO2025-105", "StockReplenishment", "Project-XYZ")
* `createdAt`: `Date` (Creation timestamp)
* `updatedAt`: `Date` (Last update timestamp)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000060000000000000001" },
  "woNumber": "WO-2025-001",
  "assemblyId": { "$oid": "681f796bd6a21248b8ec7640" },
  "partIdToManufacture": null,
  "productId": null,
  "quantity": 10,
  "status": "planned",
  "priority": "medium",
  "dueDate": { "$date": "2025-05-25T17:00:00.000Z" },
  "startDate": null,
  "completedAt": null,
  "assignedTo": { "$oid": "65f000010000000000000003" },
  "notes": "Standard production run for Tamping Arm Assembly.",
  "sourceDemand": "StockReplenishment",
  "createdAt": { "$date": "2025-05-12T09:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-12T09:00:00.000Z" }
}
```

---

**11. `batches` Collection**

Tracks production runs, often as sub-units of work orders.

* `_id`: `ObjectId` (Primary Key)
* `batchCode`: `String` (Unique code for the batch, e.g., "BATCH-TA-20250513-001")
* `workOrderId`: `ObjectId` (Reference to the parent `workorders._id`)
* `partId`: `ObjectId | Null` (Reference to `parts._id` being produced, if not an assembly)
* `assemblyId`: `ObjectId | Null` (Reference to `assemblies._id` being produced)
* `quantityPlanned`: `Int32` (Planned quantity for this batch)
* `quantityProduced`: `Int32` (Actual quantity successfully produced in this batch)
* `quantityScrapped`: `Int32` (Quantity scrapped during this batch production)
* `startDate`: `Date | Null` (Actual production start date for this batch)
* `endDate`: `Date | Null` (Actual production end date for this batch)
* `status`: `String` (Batch status, e.g., "pending", "in_progress", "completed", "paused")
* `notes`: `String` (Additional notes specific to this batch)
* `assignedMachine`: `String | Null` (Identifier for the machine or workstation used)
* `createdAt`: `Date` (Timestamp of batch record creation)
* `updatedAt`: `Date` (Timestamp of last batch record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000070000000000000001" },
  "batchCode": "BATCH-TA-20250513-001",
  "workOrderId": { "$oid": "65f000060000000000000001" },
  "partId": null,
  "assemblyId": { "$oid": "681f796bd6a21248b8ec7640" },
  "quantityPlanned": 10,
  "quantityProduced": 0,
  "quantityScrapped": 0,
  "startDate": null,
  "endDate": null,
  "status": "pending",
  "notes": "First batch for WO-2025-001.",
  "assignedMachine": "ASM-LINE-01",
  "createdAt": { "$date": "2025-05-13T09:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-13T09:00:00.000Z" }
}
```

---

**12. `batchlogs` Collection**

Logs events and activities related to specific production batches.

* `_id`: `ObjectId` (Primary Key)
* `batchId`: `ObjectId` (Reference to `batches._id`)
* `timestamp`: `Date` (Time of the log entry)
* `event`: `String` (Description of the event, e.g., "Material Issued", "QC Checkpoint Passed", "Machine Stoppage")
* `userId`: `ObjectId` (Reference to `users._id` of the user who logged or is associated with the event)
* `details`: `String` (Additional textual details about the event)
* `data`: `Object | Null` (Flexible field for structured data related to the event, e.g., `{ "material_codes": [...], "checkpoint": "QC1" }`)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000080000000000000001" },
  "batchId": { "$oid": "65f000070000000000000002" },
  "timestamp": { "$date": "2025-05-13T10:05:00.000Z" },
  "event": "Material Issued for 5 units",
  "userId": { "$oid": "65f000010000000000000003" },
  "details": "Issued: Raw Steel Bar x5, Keys x10, Washers x10",
  "data": { "material_codes": ["RAW-STL-001", "KEY-007", "WSH-016"] }
}
```

---

**13. `deliveries` Collection**

Tracks inbound (from suppliers) and outbound (to customers) shipments.

* `_id`: `ObjectId` (Primary Key)
* `deliveryId`: `String` (Unique delivery identifier, e.g., "DEL-2025-001-IN")
* `referenceType`: `String` (Type of reference, e.g., "PurchaseOrder", "SalesOrder", "TransferOrder")
* `referenceId`: `ObjectId` (Reference to the related document in another collection)
* `supplierId`: `ObjectId | Null` (Reference to `suppliers._id` for inbound deliveries)
* `customerId`: `ObjectId | Null` (Reference to an implied `customers` collection for outbound deliveries - **Note: `customers` collection schema not provided**)
* `status`: `String` (Delivery status, e.g., "scheduled", "in_transit", "delivered", "partially_delivered", "delayed", "cancelled")
* `scheduledDate`: `Date` (Scheduled date for delivery/shipment)
* `actualDate`: `Date | Null` (Actual date of delivery/shipment)
* `trackingNumber`: `String | Null` (Carrier tracking number)
* `carrier`: `String | Null` (Name of the shipping carrier, e.g., "FedEx", "UPS")
* `notes`: `String` (Additional notes regarding the delivery)
* `receivedBy`: `ObjectId | Null` (Reference to `users._id` who received/confirmed an inbound delivery)
* `itemsDelivered`: `Array<Object>` (List of items in the delivery)
    * `partId`: `ObjectId | Null` (Reference to `parts._id`. For "CSM" example, this was mapped to a placeholder part. Consider if this should be `productId` or a more flexible `itemId` with `itemType` if products are directly delivered.)
    * `productId`: `ObjectId | Null` (Consider adding for product deliveries)
    * `quantity`: `Int32`
* `createdAt`: `Date` (Creation timestamp)
* `updatedAt`: `Date` (Last update timestamp)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000090000000000000001" },
  "deliveryId": "DEL-2025-001-IN",
  "referenceType": "PurchaseOrder",
  "referenceId": { "$oid": "65f000050000000000000001" },
  "supplierId": { "$oid": "681f796ad6a21248b8ec7600" },
  "customerId": null,
  "status": "scheduled",
  "scheduledDate": { "$date": "2025-05-15T10:00:00.000Z" },
  "actualDate": null,
  "trackingNumber": "TRK123456789",
  "carrier": "FedEx",
  "notes": "Awaiting delivery of PO-2025-001.",
  "receivedBy": null,
  "itemsDelivered": [],
  "createdAt": { "$date": "2025-05-12T11:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-12T11:00:00.000Z" }
}
```

---

**14. `settings` Collection**

System-wide configurations and parameters.

* `_id`: `ObjectId` (Primary Key)
* `key`: `String` (Unique setting key, e.g., "default_notification_email")
* `value`: `String` (Setting value; actual data type depends on `dataType` field)
* `description`: `String` (Description of the setting)
* `dataType`: `String` (Data type of the value, e.g., "string", "integer", "boolean", "json")
* `group`: `String` (Setting group for organization, e.g., "Notifications", "Inventory", "System")
* `lastModifiedBy`: `ObjectId` (Reference to `users._id` who last modified the setting)
* `lastModifiedAt`: `Date` (Last modification timestamp)
* `isSystemEditableOnly`: `Boolean` (If true, setting can only be changed by system processes, not through UI by regular admins)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f0000c0000000000000001" },
  "key": "default_notification_email",
  "value": "<EMAIL>",
  "description": "Default email address for system notifications.",
  "dataType": "string",
  "group": "Notifications",
  "lastModifiedBy": { "$oid": "65f000010000000000000001" },
  "lastModifiedAt": { "$date": "2023-01-01T12:00:00.000Z" },
  "isSystemEditableOnly": false
}
```

---

**15. `IMS_TEJ` Collection** (System Transaction Event Journal / Logs)

Comprehensive logs for system events, errors, and important transactions.

* `_id`: `ObjectId` (Primary Key)
* `timestamp`: `Date` (Log timestamp)
* `eventType`: `String` (Type of event, e.g., "UserLogin", "InventoryUpdateFailure", "ApiRequest")
* `level`: `String` (Log level, e.g., "INFO", "WARN", "ERROR", "DEBUG", "FATAL")
* `message`: `String` (Log message)
* `source`: `String` (Source of the log, e.g., "API-AuthModule", "BackgroundJob-StockSync", "WorkOrderService")
* `details`: `Object` (Additional structured details, e.g., `{ "ip_address": "...", "user_agent": "...", "error_code": "..." }`)
* `userId`: `ObjectId | Null` (Reference to `users._id` if the event is associated with a user)
* `correlationId`: `String | Null` (ID to correlate logs related to a single operation or request flow)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f0000d0000000000000001" },
  "timestamp": { "$date": "2025-05-14T10:05:00.000Z" },
  "eventType": "UserLogin",
  "level": "INFO",
  "message": "User admin_user logged in successfully.",
  "source": "API-AuthModule",
  "details": { "ip_address": "*************", "user_agent": "Chrome/100.0" },
  "userId": { "$oid": "65f000010000000000000001" },
  "correlationId": "corr-login-xyz123"
}
```

---

## Schema Design Recommendations

Based on MongoDB best practices:

1.  **Embedded Documents**: The schema makes good use of embedded documents (e.g., `inventory` in `parts`, `items` in `purchaseorders`), which is efficient for data that is frequently accessed together.
2.  **References (`ObjectId`)**: References between collections are appropriately implemented using `ObjectId`s. This is crucial for data integrity and performance, especially with the `parts` collection now using `ObjectId` as `_id` and a separate `partNumber` for the business key.
3.  **Schema Versioning**: Consider adding a `schemaVersion` field to documents (e.g., in a central configuration or on each document if large-scale migrations are common) to help manage future schema evolutions.
4.  **Indexing**:
    * Ensure `_id` fields (default index) are leveraged for primary lookups.
    * Create indexes on fields frequently used in queries, sorts, and joins (`$lookup`).
    * **Crucially, index `parts.partNumber` as it will be a common lookup field.**
    * Other candidates for indexing include: `warehouses.location_id`, `suppliers.supplier_id`, `users.username`, `users.email`, `products.productCode`, `assemblies.assemblyCode`, `transactions.transactionDate`, `transactions.partId`, `purchaseorders.poNumber`, `purchaseorders.supplierId`, `workorders.woNumber`, `workorders.status`, `workorders.dueDate`, `batches.batchCode`, `batches.workOrderId`, `deliveries.deliveryId`, `IMS_TEJ.timestamp`, `IMS_TEJ.eventType`.
5.  **Data Duplication (Denormalization) Strategy**: The current schema maintains a good balance. Small, relatively static pieces of information (like a part name or product code) might be duplicated in transaction-like records if it simplifies queries significantly and the overhead of updating duplicated data is low. However, with `ObjectId` references, `$lookup` is efficient for retrieving related data.
6.  **Data Integrity Considerations**:
    * While MongoDB is schema-flexible, application-level validation is critical to ensure data consistency (e.g., valid `ObjectId` references, correct enum values for `status` fields).
    * For fields like `status` or `transactionType`, consider using consistent string enums.
7.  **Review `deliveries.itemsDelivered`**: The structure for `itemsDelivered` currently uses `partId`. If products are also delivered, consider a more flexible structure like `[{ itemId: ObjectId, itemType: String, quantity: Int32 }]` where `itemType` could be "Part" or "Product", and `itemId` references the respective collection.

This revised schema provides a more robust foundation for your Inventory Management System.
