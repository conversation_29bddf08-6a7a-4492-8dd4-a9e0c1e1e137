/* Import theme variables */
@import './styles/theme-variables.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-yellow: #FFEB3B;
  --primary-orange: #FF9800;
  --primary-black: #212121;
  --primary-blue: #1274F3;
  --primary-pink: #EC3A76;
  --primary-mint: #4BFFB2;

  /* Updated Dark Theme Variables */
  --T-bg-sidebar: #2D2D2D;   /* Sidebar background - Modern grey */
  --T-bg-primary: #1E1E1E;   /* Main content area background - Very dark */
  --T-bg-card: #333333;      /* Card background - Slightly lighter than sidebar */
  --T-text-primary: #F0F0F0; /* Primary text with high contrast - WCAG AA compliant */
  --T-text-secondary: #A0A0A0; /* Secondary text - WCAG AA compliant */
  --T-text-headings: #FFFFFF; /* Heading text - Maximum contrast */
  --T-accent-primary: #FFFFFF; /* Primary accent - Neutral white */
  --T-accent-active: #E0E0E0; /* Active accent - Light grey */
  --T-border-color: #444444; /* Border color - Medium grey */
  --T-border-subtle: #383838; /* Subtle border - Darker grey */
  --T-focus-ring: rgba(224, 224, 224, 0.4); /* Focus ring for accessibility */
  --T-hover-overlay: rgba(255, 255, 255, 0.05); /* Hover state overlay */

  --dark-bg: var(--T-bg-primary);
  --dark-card: var(--T-bg-card);
  --dark-element: var(--T-bg-sidebar);
  --dark-hover: #3E3E3E; /* Hover state background */
  --dark-accent: var(--T-accent-primary);
  --dark-accent-muted: var(--T-accent-active);
  --dark-border: var(--T-border-subtle);
  --dark-text-primary: var(--T-text-primary);
  --dark-text-secondary: var(--T-text-secondary);

  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 40 96% 54%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;
  --ring: 215 20.2% 65.1%;
  --radius: 0.5rem;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.5s ease, color 0.5s ease;
}

.dark body {
  background-color: var(--dark-bg);
  color: var(--dark-text-primary);
  background-attachment: fixed;
  --background: 0 0% 12%;
  --foreground: 0 0% 94%;
  --muted: 0 0% 15%;
  --muted-foreground: 0 0% 63%;
  --accent: 0 0% 100%;
  --accent-foreground: 0 0% 7%;
  --popover: 0 0% 15%;
  --popover-foreground: 0 0% 94%;
  --border: 0 0% 27%;
  --input: 0 0% 27%;
  --card: 0 0% 20%;
  --card-foreground: 0 0% 94%;
  --primary: 40 96% 54%;
  --primary-foreground: 0 0% 7%;
  --secondary: 0 0% 15%;
  --secondary-foreground: 0 0% 94%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 0 0% 94%;
  --ring: 0 0% 27%;
}

/* Glass morphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(180, 180, 180, 0.05);
}

.dark .glass {
  background: rgba(45, 45, 45, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(68, 68, 68, 0.2);
}

/* Floating animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: transparent;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--T-border-color);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Custom sidebar scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 224, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(160, 174, 192, 0.5);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(68, 68, 68, 0.7);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(85, 85, 85, 0.9);
}

/* Custom dark theme */
.dark .bg-gray-900 {
  background-color: var(--T-bg-primary);
}

.dark .bg-gray-800 {
  background-color: var(--T-bg-card);
}

.dark .bg-gray-750 {
  background-color: var(--T-bg-sidebar);
}

.dark .bg-gray-700 {
  background-color: var(--T-bg-sidebar);
}

.dark .hover\:bg-gray-700:hover {
  background-color: var(--dark-hover);
}

.dark .border-gray-700 {
  border-color: var(--T-border-color);
}

/* Updated for better contrast on interactive elements */
.dark .focus\:ring-2:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: var(--T-accent-primary);
}

.dark .hover\:bg-gray-600:hover {
  background-color: #444444;
}

/* Accent colors for status indicators */
.dark .text-green-600 {
  color: var(--primary-mint);
}

.dark .text-blue-600 {
  color: var(--T-accent-primary); /* Changed to white for accent */
}

.dark .text-red-600 {
  color: var(--primary-pink);
}

/* Enhanced text colors for better readability */
.dark .text-gray-400 {
  color: var(--T-text-secondary);
}

.dark .text-gray-500 {
  color: var(--T-text-secondary);
}

.dark .text-muted-foreground {
  color: var(--T-text-secondary);
}

/* Gradient backgrounds */
.dark .bg-gradient-primary {
  background: linear-gradient(135deg, var(--T-bg-card) 0%, var(--T-bg-sidebar) 100%);
}

.dark .bg-gradient-accent {
  background: linear-gradient(135deg, #404040 0%, #333333 100%);
}

/* Improved gradient for dark theme */
.dark .bg-gradient-dark {
  background: linear-gradient(135deg, var(--T-bg-primary) 0%, var(--T-bg-sidebar) 100%);
}

/* Card styling */
.card {
  @apply rounded-xl overflow-hidden transition-all duration-300;
}

.dark .card {
  background-color: var(--T-bg-card);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid var(--T-border-color);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
  color: var(--T-text-primary);
}

.card-hover {
  @apply transition-all duration-300;
}

.card-hover:hover {
  transform: translateY(-5px);
}

.dark .card-hover:hover {
  background-color: var(--dark-hover);
}

/* Button styling */
.btn {
  @apply rounded-xl py-2 px-4 transition-all duration-300 transform hover:translate-y-[-2px] focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* Enhanced button styles for better visibility */
.dark .btn-primary {
  background-color: var(--T-accent-primary);
  color: var(--dark-bg);
  box-shadow: 0 4px 10px rgba(224, 224, 224, 0.25);
}

.dark .btn-primary:hover {
  background-color: var(--T-accent-active);
  box-shadow: 0 6px 15px rgba(255, 255, 255, 0.3);
}

.dark .btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(224, 224, 224, 0.4);
}

.dark .btn-secondary {
  background-color: var(--dark-element);
  color: var(--dark-text-primary);
  box-shadow: 0 4px 10px rgba(40, 40, 40, 0.35);
}

.dark .btn-secondary:hover {
  background-color: var(--dark-hover);
  box-shadow: 0 6px 15px rgba(51, 51, 51, 0.45);
}

.dark .btn-secondary:focus {
  box-shadow: 0 0 0 3px rgba(51, 51, 51, 0.6);
}

.dark .btn-accent {
  background-color: var(--T-bg-card);
  color: var(--T-text-primary);
  box-shadow: 0 4px 10px rgba(64, 64, 64, 0.35);
}

.dark .btn-accent:hover {
  background-color: var(--dark-hover);
  box-shadow: 0 6px 15px rgba(80, 80, 80, 0.45);
}

.dark .btn-accent:focus {
  box-shadow: 0 0 0 3px rgba(80, 80, 80, 0.6);
}

/* Form controls with better focus states */
.dark input, .dark textarea, .dark select {
  border-color: var(--dark-border);
  background-color: rgba(40, 40, 40, 0.6);
  color: var(--T-text-primary);
}

.dark input::placeholder, .dark textarea::placeholder {
  color: rgba(160, 160, 160, 0.8);
}

.dark input:focus, .dark textarea:focus, .dark select:focus {
  border-color: var(--T-accent-primary);
  box-shadow: 0 0 0 2px var(--T-focus-ring);
  outline: none;
}

.dark input:disabled, .dark textarea:disabled, .dark select:disabled {
  background-color: rgba(30, 30, 30, 0.4);
  color: rgba(160, 160, 160, 0.5);
  cursor: not-allowed;
}

/* Improved form controls for dark theme */
.dark .form-input,
.dark .form-select,
.dark .form-textarea {
  background-color: var(--T-bg-primary);
  border-color: var(--T-border-color);
  color: var(--T-text-primary);
}

.dark .form-input:focus,
.dark .form-select:focus,
.dark .form-textarea:focus {
  border-color: var(--T-accent-primary);
  box-shadow: 0 0 0 2px var(--T-focus-ring);
}

/* Interactive elements with improved hover states */
.dark button:not([disabled]):hover,
.dark .btn:not([disabled]):hover,
.dark .clickable:hover {
  filter: brightness(1.15);
  transform: translateY(-2px);
}

/* Add focus outlines for keyboard navigation */
.dark *:focus-visible {
  outline: 2px solid var(--T-accent-primary);
  outline-offset: 3px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Additional dark theme card elements */
.dark .bg-card,
.dark .card-background,
.dark .content-card {
  background-color: var(--dark-card);
  border-color: var(--dark-border);
}

/* Ensure main content areas use the right background */
.dark .content-area,
.dark .main-content,
.dark .dashboard-content {
  background-color: var(--dark-bg);
}

/* Ensure buttons have the right hover state */
.dark .btn:hover,
.dark .button:hover,
.dark button:hover {
  background-color: var(--dark-hover);
}

/* Apply theme colors to page containers */
.dark [class*="page-container"],
.dark [class*="content-container"] {
  background-color: var(--dark-bg);
  color: var(--dark-text-primary);
}

/* Apply updated colors to all cards and panels */
.dark [class*="card"],
.dark [class*="panel"],
.dark [class*="Box"],
.dark [class*="Container"] {
  background-color: var(--dark-card);
  border-color: var(--dark-border);
  color: var(--T-text-primary);
}

/* Ensure consistent styling for data tables */
/* Light mode table styles */
table {
  background-color: white;
  color: #374151;
  border-color: #e5e7eb;
}

th {
  background-color: #f9fafb;
  color: #4b5563;
  border-color: #e5e7eb;
  font-weight: 600;
}

td {
  border-color: #e5e7eb;
  color: #111827;
}

tr:hover {
  background-color: #f9fafb;
}

/* Dark mode table styles */
.dark table {
  background-color: var(--T-bg-card);
  color: var(--T-text-primary);
  border-color: var(--T-border-subtle);
}

.dark th {
  background-color: var(--T-bg-sidebar);
  color: var(--T-text-headings);
  border-color: var(--T-border-color);
}

.dark td {
  border-color: var(--T-border-subtle);
  color: var(--T-text-primary);
}

.dark tr:hover {
  background-color: var(--dark-hover);
}

/* Ensure consistent styling for charts and data visualization */
.dark .recharts-cartesian-grid-horizontal line,
.dark .recharts-cartesian-grid-vertical line {
  stroke: var(--T-border-subtle);
}

.dark .recharts-tooltip-wrapper {
  background-color: var(--T-bg-card) !important;
  border-color: var(--T-border-color) !important;
  color: var(--T-text-primary) !important;
}

/* Ensure consistent styling for modals and dialogs */
.dark .modal,
.dark .dialog,
.dark [role="dialog"] {
  background-color: var(--T-bg-card);
  border-color: var(--T-border-color);
  color: var(--T-text-primary);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Theme-consistent gradients */
.theme-gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r;
}

.dark .theme-gradient-text {
  @apply from-primary-blue via-primary-blue/90 to-primary-pink/90;
}

.theme-gradient-text {
  @apply from-primary-blue/90 via-primary-blue/80 to-primary-pink/80;
}

.theme-gradient-bg {
  @apply bg-gradient-to-r;
}

.dark .theme-gradient-bg {
  @apply from-primary-blue/30 to-primary-pink/30;
}

.theme-gradient-bg {
  @apply from-primary-blue/10 to-primary-pink/10;
}

.dark .theme-gradient-border {
  @apply from-primary-blue/50 to-primary-pink/50;
}

.theme-gradient-border {
  @apply from-primary-blue/20 to-primary-pink/20;
}

/* Light mode specific card styles */
.light-mode-card {
  @apply bg-white border border-gray-200 shadow-sm;
}

.light-mode-form {
  @apply bg-white border border-gray-100 shadow-md;
}