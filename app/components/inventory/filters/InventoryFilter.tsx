'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Slider } from '@/app/components/ui/slider';
import { Input } from '@/app/components/ui/input';
import { Checkbox } from '@/app/components/ui/checkbox';
import { Label } from '@/app/components/ui/label';
import { Badge } from '@/app/components/ui/badge';
import { Button } from '@/app/components/ui/button';
import { Switch } from '@/app/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { useTheme } from '@/app/context/ThemeContext';
import { ShimmerButton, RippleButton } from '@/app/components/ui/magic-ui';
import { FilterState, DEFAULT_FILTER_STATE, saveFiltersToStorage } from './types';
import { debounce } from '@/lib/utils';

interface InventoryFilterProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  onReset: () => void;
  suppliers: string[];
  categories: { id: string; name: string }[]; // Changed to object array
  locations: string[];
}

/**
 * Inventory filter component with range sliders, toggles, and multi-select dropdowns
 */
export const InventoryFilter: React.FC<InventoryFilterProps> = ({
  filters,
  onFiltersChange,
  onReset,
  suppliers,
  categories,
  locations,
}) => {
  const { theme } = useTheme();

  // Stock quantity quick presets
  const stockPresets = [
    { label: 'Low (0-10)', min: 0, max: 10 },
    { label: 'Medium (11-50)', min: 11, max: 50 },
    { label: 'High (51+)', min: 51, max: 1000 },
  ];

  // Debounced save to localStorage
  const debouncedSave = useCallback(
    debounce((newFilters: FilterState) => {
      saveFiltersToStorage(newFilters);
    }, 150),
    []
  );

  // Update filters and trigger the callback
  const updateFilters = useCallback((newFilters: FilterState) => {
    onFiltersChange(newFilters);
    debouncedSave(newFilters);
  }, [onFiltersChange, debouncedSave]);

  // Handle stock quantity range change
  const handleStockRangeChange = (values: number[]) => {
    if (values.length === 2) {
      updateFilters({
        ...filters,
        stockQuantity: {
          ...filters.stockQuantity,
          min: values[0],
          max: values[1],
        },
      });
    }
  };

  // Handle stock quantity preset selection
  const handleStockPresetChange = (preset: { min: number; max: number }) => {
    updateFilters({
      ...filters,
      stockQuantity: {
        ...filters.stockQuantity,
        min: preset.min,
        max: preset.max,
        enabled: true,
      },
    });
  };

  // Handle stock quantity toggle
  const handleStockToggle = (checked: boolean) => {
    updateFilters({
      ...filters,
      stockQuantity: {
        ...filters.stockQuantity,
        enabled: checked,
      },
    });
  };

  // Handle reorder level change
  const handleReorderLevelChange = (value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 0) {
      updateFilters({
        ...filters,
        reorderLevel: {
          ...filters.reorderLevel,
          value: numValue,
        },
      });
    }
  };

  // Handle reorder level below only toggle
  const handleBelowOnlyToggle = (checked: boolean) => {
    updateFilters({
      ...filters,
      reorderLevel: {
        ...filters.reorderLevel,
        belowOnly: checked,
      },
    });
  };

  // Handle reorder level toggle
  const handleReorderToggle = (checked: boolean) => {
    updateFilters({
      ...filters,
      reorderLevel: {
        ...filters.reorderLevel,
        enabled: checked,
      },
    });
  };

  // Handle multi-select filter change
  const handleMultiSelectChange = (
    filterName: 'supplier' | 'category' | 'location',
    values: string[]
  ) => {
    updateFilters({
      ...filters,
      [filterName]: {
        ...filters[filterName],
        values,
        enabled: values.length > 0,
      },
    });
  };

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      className="bg-card dark:bg-dark-card rounded-lg shadow-md p-5 mb-6 border border-input/20 dark:border-dark-border/20"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Stock Quantity Filter */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium text-foreground dark:text-dark-text-primary">
              Stock Quantity
            </Label>
            <Switch
              checked={filters.stockQuantity.enabled}
              onCheckedChange={handleStockToggle}
              aria-label="Enable stock quantity filter"
            />
          </div>

          <div className={filters.stockQuantity.enabled ? 'opacity-100' : 'opacity-50 pointer-events-none'}>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-xs text-muted-foreground dark:text-dark-text-secondary">
                Range: {filters.stockQuantity.min} - {filters.stockQuantity.max === 1000 ? '1000+' : filters.stockQuantity.max}
              </span>
            </div>

            <Slider
              value={[filters.stockQuantity.min, filters.stockQuantity.max]}
              min={0}
              max={1000}
              step={1}
              onValueChange={handleStockRangeChange}
              className="mb-4"
            />

            <div className="flex flex-wrap gap-2">
              {stockPresets.map((preset) => (
                <Badge
                  key={preset.label}
                  variant="outline"
                  className={`cursor-pointer hover:bg-primary/10 dark:hover:bg-primary-blue/10 transition-colors ${
                    filters.stockQuantity.min === preset.min && filters.stockQuantity.max === preset.max
                      ? 'bg-primary/20 dark:bg-primary-blue/20 border-primary/30 dark:border-primary-blue/30'
                      : ''
                  }`}
                  onClick={() => handleStockPresetChange(preset)}
                >
                  {preset.label}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        {/* Reorder Level Filter */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium text-foreground dark:text-dark-text-primary">
              Reorder Level
            </Label>
            <Switch
              checked={filters.reorderLevel.enabled}
              onCheckedChange={handleReorderToggle}
              aria-label="Enable reorder level filter"
            />
          </div>

          <div className={filters.reorderLevel.enabled ? 'opacity-100' : 'opacity-50 pointer-events-none'}>
            <div className="flex items-center space-x-2 mb-2">
              <Input
                type="number"
                min={0}
                value={filters.reorderLevel.value}
                onChange={(e) => handleReorderLevelChange(e.target.value)}
                className="w-20 h-8"
              />

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="belowOnly"
                  checked={filters.reorderLevel.belowOnly}
                  onCheckedChange={handleBelowOnlyToggle}
                />
                <Label htmlFor="belowOnly" className="text-sm cursor-pointer">
                  Below only
                </Label>
              </div>
            </div>

            <div className="text-xs text-muted-foreground dark:text-dark-text-secondary">
              {filters.reorderLevel.belowOnly
                ? `Show items with stock below reorder level (${filters.reorderLevel.value})`
                : `Show items with reorder level of ${filters.reorderLevel.value}`}
            </div>
          </div>
        </div>

        {/* Supplier Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-foreground dark:text-dark-text-primary">
            Supplier
          </Label>
          <Select
            value="multiple"
            onValueChange={() => {}}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select suppliers">
                {filters.supplier.values.length > 0
                  ? `${filters.supplier.values.length} supplier${filters.supplier.values.length > 1 ? 's' : ''} selected`
                  : 'All suppliers'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {suppliers.map((supplier) => (
                <div key={supplier} className="flex items-center space-x-2 p-2">
                  <Checkbox
                    id={`supplier-${supplier}`}
                    checked={filters.supplier.values.includes(supplier)}
                    onCheckedChange={(checked) => {
                      const newValues = checked
                        ? [...filters.supplier.values, supplier]
                        : filters.supplier.values.filter((s) => s !== supplier);
                      handleMultiSelectChange('supplier', newValues);
                    }}
                  />
                  <Label htmlFor={`supplier-${supplier}`} className="text-sm cursor-pointer">
                    {supplier}
                  </Label>
                </div>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-foreground dark:text-dark-text-primary">
            Category
          </Label>
          <Select
            value="multiple"
            onValueChange={() => {}}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select categories">
                {filters.category.values.length > 0
                  ? `${filters.category.values.length} categor${filters.category.values.length > 1 ? 'ies' : 'y'} selected`
                  : 'All categories'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2 p-2">
                  <Checkbox
                    id={`category-${category.id}`}
                    checked={filters.category.values.includes(category.id)} // Use category.id for checking
                    onCheckedChange={(checked) => {
                      const newValues = checked
                        ? [...filters.category.values, category.id] // Store category.id
                        : filters.category.values.filter((c) => c !== category.id);
                      handleMultiSelectChange('category', newValues);
                    }}
                  />
                  <Label htmlFor={`category-${category.id}`} className="text-sm cursor-pointer">
                    {category.name} {/* Display category.name */}
                  </Label>
                </div>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Location Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-foreground dark:text-dark-text-primary">
            Location
          </Label>
          <Select
            value="multiple"
            onValueChange={() => {}}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select locations">
                {filters.location.values.length > 0
                  ? `${filters.location.values.length} location${filters.location.values.length > 1 ? 's' : ''} selected`
                  : 'All locations'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {locations.map((location) => (
                <div key={location} className="flex items-center space-x-2 p-2">
                  <Checkbox
                    id={`location-${location}`}
                    checked={filters.location.values.includes(location)}
                    onCheckedChange={(checked) => {
                      const newValues = checked
                        ? [...filters.location.values, location]
                        : filters.location.values.filter((l) => l !== location);
                      handleMultiSelectChange('location', newValues);
                    }}
                  />
                  <Label htmlFor={`location-${location}`} className="text-sm cursor-pointer">
                    {location}
                  </Label>
                </div>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-6 flex justify-end space-x-3">
        <ShimmerButton
          className="px-4 py-2 rounded-md"
          shimmerColor={theme === 'dark' ? "#4B5563" : "#E5E7EB"}
          background={theme === 'dark' ? "rgba(18, 116, 243, 0.8)" : "rgba(59, 130, 246, 0.8)"}
          onClick={() => {
            // Apply filters is handled automatically through state changes
          }}
        >
          Apply Filters
        </ShimmerButton>

        <RippleButton
          className="px-4 py-2 rounded-md bg-secondary dark:bg-dark-element hover:bg-secondary/80 dark:hover:bg-dark-hover text-secondary-foreground dark:text-dark-text-primary"
          onClick={onReset}
          rippleColor={theme === 'dark' ? "#4B5563" : "#E5E7EB"}
        >
          Reset All
        </RippleButton>
      </div>
    </motion.div>
  );
};
