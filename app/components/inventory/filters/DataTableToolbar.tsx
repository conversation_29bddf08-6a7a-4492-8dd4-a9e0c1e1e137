'use client';

import React, { useEffect, useRef } from 'react';
import { Filter, X } from 'lucide-react';
import { Button } from '@/app/components/ui/button';
import { Badge } from '@/app/components/ui/badge';
import { useTheme } from '@/app/context/ThemeContext';
import { RippleButton } from '@/app/components/ui/magic-ui';
import { FilterState, countActiveFilters } from './types';

interface DataTableToolbarProps {
  filters: FilterState;
  showFilters: boolean;
  onToggleFilters: () => void;
  onResetFilters: () => void;
}

/**
 * Toolbar component for the data table with filter controls
 */
export const DataTableToolbar: React.FC<DataTableToolbarProps> = ({
  filters,
  showFilters,
  onToggleFilters,
  onResetFilters,
}) => {
  const { theme } = useTheme();
  const activeFiltersCount = countActiveFilters(filters);
  const badgeRef = useRef<HTMLSpanElement>(null);

  // Add pulse animation to the badge when it mounts or count changes
  useEffect(() => {
    if (badgeRef.current && activeFiltersCount > 0) {
      badgeRef.current.classList.add('animate-ping-once');
      
      const timer = setTimeout(() => {
        if (badgeRef.current) {
          badgeRef.current.classList.remove('animate-ping-once');
        }
      }, 800);
      
      return () => clearTimeout(timer);
    }
  }, [activeFiltersCount]);

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <RippleButton
          className={`flex items-center ${
            showFilters
              ? 'bg-primary/10 dark:bg-primary-blue/10 text-primary dark:text-primary-blue'
              : 'bg-secondary dark:bg-dark-element text-secondary-foreground dark:text-dark-text-primary'
          } hover:bg-secondary/80 dark:hover:bg-dark-hover rounded-full px-3 py-2`}
          onClick={onToggleFilters}
          rippleColor={theme === 'dark' ? "#4B5563" : "#E5E7EB"}
        >
          <Filter size={16} className="mr-2" />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <Badge 
              ref={badgeRef}
              variant="default" 
              className="ml-2 bg-primary dark:bg-primary-blue text-white h-5 w-5 flex items-center justify-center p-0 rounded-full"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </RippleButton>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onResetFilters}
            className="h-8 gap-1 text-xs text-muted-foreground dark:text-dark-text-secondary hover:text-foreground dark:hover:text-dark-text-primary"
          >
            <X size={14} />
            <span>Clear all</span>
          </Button>
        )}
      </div>
    </div>
  );
};

// Add the custom animation to the global styles
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ping-once {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.5);
        opacity: 0.5;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }
    
    .animate-ping-once {
      animation: ping-once 0.8s cubic-bezier(0, 0, 0.2, 1);
    }
  `;
  document.head.appendChild(style);
}
