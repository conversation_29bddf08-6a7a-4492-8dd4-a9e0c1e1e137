import { describe, it, expect } from 'vitest';
import { applyFilters, extractUniqueValues } from '../utils';
import { FilterState, DEFAULT_FILTER_STATE } from '../types';
import { Product } from '@/app/types';

describe('Filter Utils', () => {
  // Sample products for testing
  const sampleProducts: Product[] = [
    {
      id: '1',
      name: 'Product 1',
      currentStock: 5,
      reorderLevel: 10,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
      supplierManufacturer: 'Supplier A',
      category: 'Category X Name',
      categoryId: 'catX_id',
      location: 'Warehouse 1'
    },
    {
      id: '2',
      name: 'Product 2',
      currentStock: 20,
      reorderLevel: 15,
      createdAt: '2023-01-02',
      updatedAt: '2023-01-02',
      supplierManufacturer: 'Supplier B',
      category: 'Category Y Name',
      categoryId: 'catY_id',
      location: 'Warehouse 2'
    },
    {
      id: '3',
      name: 'Product 3',
      currentStock: 50,
      reorderLevel: 30,
      createdAt: '2023-01-03',
      updatedAt: '2023-01-03',
      supplierManufacturer: 'Supplier A',
      category: 'Category X Name',
      categoryId: 'catX_id',
      location: 'Warehouse 1'
    },
    {
      id: '4',
      name: 'Product 4',
      currentStock: 0,
      reorderLevel: 5,
      createdAt: '2023-01-04',
      updatedAt: '2023-01-04',
      supplierManufacturer: 'Supplier C',
      category: 'Category Z Name',
      categoryId: 'catZ_id',
      location: 'Warehouse 3'
    }
  ];

  describe('applyFilters', () => {
    it('should return all products when no filters are enabled', () => {
      const filters: FilterState = { ...DEFAULT_FILTER_STATE };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(4);
    });

    it('should filter by stock quantity range', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          min: 10,
          max: 40,
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('2');
    });

    it('should filter by reorder level (below only)', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        reorderLevel: {
          value: 10,
          belowOnly: true,
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(2);
      expect(result.map(p => p.id)).toContain('1');
      expect(result.map(p => p.id)).toContain('4');
    });

    it('should filter by reorder level (exact match)', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        reorderLevel: {
          value: 10,
          belowOnly: false,
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });

    it('should filter by supplier', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        supplier: {
          values: ['Supplier A'],
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(2);
      expect(result.map(p => p.id)).toContain('1');
      expect(result.map(p => p.id)).toContain('3');
    });

    it('should filter by categoryId', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        category: {
          values: ['catX_id'], // Filter by categoryId
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(2);
      expect(result.map(p => p.id)).toContain('1');
      expect(result.map(p => p.id)).toContain('3');
    });

    it('should filter by location', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        location: {
          values: ['Warehouse 1'],
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(2);
      expect(result.map(p => p.id)).toContain('1');
      expect(result.map(p => p.id)).toContain('3');
    });

    it('should combine multiple filters', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          min: 0,
          max: 30,
          enabled: true
        },
        supplier: {
          values: ['Supplier A'],
          enabled: true
        }
      };
      const result = applyFilters(sampleProducts, filters);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('1');
    });

    it('should handle empty products array', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          min: 10,
          max: 40,
          enabled: true
        }
      };
      const result = applyFilters([], filters);
      expect(result).toHaveLength(0);
    });

    it('should handle edge case with very large stock quantity', () => {
      const largeStockProduct: Product = {
        id: '5',
        name: 'Product 5',
        currentStock: 10000,
        reorderLevel: 100,
        createdAt: '2023-01-05',
        updatedAt: '2023-01-05'
      };
      
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          min: 5000,
          max: 20000,
          enabled: true
        }
      };
      
      const result = applyFilters([...sampleProducts, largeStockProduct], filters);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('5');
    });
  });

  describe('extractUniqueValues', () => {
    it('should extract unique supplier values', () => {
      const result = extractUniqueValues(sampleProducts, 'supplierManufacturer');
      expect(result).toHaveLength(3);
      expect(result).toContain('Supplier A');
      expect(result).toContain('Supplier B');
      expect(result).toContain('Supplier C');
    });

    it('should extract unique category names (if still used for display purposes)', () => {
      // This test verifies extractUniqueValues for the 'category' (name) field.
      // In the main app, unique categories for filtering are now derived differently (id/name pairs).
      const result = extractUniqueValues(sampleProducts, 'category');
      expect(result).toHaveLength(3);
      expect(result).toContain('Category X Name');
      expect(result).toContain('Category Y Name');
      expect(result).toContain('Category Z Name');
    });

    it('should extract unique location values', () => {
      const result = extractUniqueValues(sampleProducts, 'location');
      expect(result).toHaveLength(3);
      expect(result).toContain('Warehouse 1');
      expect(result).toContain('Warehouse 2');
      expect(result).toContain('Warehouse 3');
    });

    it('should handle empty products array', () => {
      const result = extractUniqueValues([], 'supplierManufacturer');
      expect(result).toHaveLength(0);
    });

    it('should handle missing values', () => {
      const productsWithMissingValues: Product[] = [
        {
          id: '1',
          name: 'Product 1',
          currentStock: 5,
          reorderLevel: 10,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
          supplierManufacturer: 'Supplier A'
        },
        {
          id: '2',
          name: 'Product 2',
          currentStock: 20,
          reorderLevel: 15,
          createdAt: '2023-01-02',
          updatedAt: '2023-01-02'
          // Missing supplierManufacturer
        }
      ];
      
      const result = extractUniqueValues(productsWithMissingValues, 'supplierManufacturer');
      expect(result).toHaveLength(1);
      expect(result).toContain('Supplier A');
    });
  });
});
