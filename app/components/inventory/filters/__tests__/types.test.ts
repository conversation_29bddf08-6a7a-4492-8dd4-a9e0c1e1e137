import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  FilterState,
  DEFAULT_FILTER_STATE,
  countActiveFilters,
  hasActiveFilters,
  resetFilters,
  getFilterStorageKey,
  saveFiltersToStorage,
  loadFiltersFromStorage
} from '../types';

describe('Filter Types', () => {
  describe('countActiveFilters', () => {
    it('should return 0 when no filters are active', () => {
      const filters: FilterState = { ...DEFAULT_FILTER_STATE };
      expect(countActiveFilters(filters)).toBe(0);
    });

    it('should count stock quantity filter when enabled', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          ...DEFAULT_FILTER_STATE.stockQuantity,
          enabled: true
        }
      };
      expect(countActiveFilters(filters)).toBe(1);
    });

    it('should count reorder level filter when enabled', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        reorderLevel: {
          ...DEFAULT_FILTER_STATE.reorderLevel,
          enabled: true
        }
      };
      expect(countActiveFilters(filters)).toBe(1);
    });

    it('should count supplier filter when enabled and has values', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        supplier: {
          values: ['Supplier A'],
          enabled: true
        }
      };
      expect(countActiveFilters(filters)).toBe(1);
    });

    it('should not count supplier filter when enabled but has no values', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        supplier: {
          values: [],
          enabled: true
        }
      };
      expect(countActiveFilters(filters)).toBe(0);
    });

    it('should count multiple active filters', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          ...DEFAULT_FILTER_STATE.stockQuantity,
          enabled: true
        },
        reorderLevel: {
          ...DEFAULT_FILTER_STATE.reorderLevel,
          enabled: true
        },
        supplier: {
          values: ['Supplier A'],
          enabled: true
        }
      };
      expect(countActiveFilters(filters)).toBe(3);
    });
  });

  describe('hasActiveFilters', () => {
    it('should return false when no filters are active', () => {
      const filters: FilterState = { ...DEFAULT_FILTER_STATE };
      expect(hasActiveFilters(filters)).toBe(false);
    });

    it('should return true when at least one filter is active', () => {
      const filters: FilterState = {
        ...DEFAULT_FILTER_STATE,
        stockQuantity: {
          ...DEFAULT_FILTER_STATE.stockQuantity,
          enabled: true
        }
      };
      expect(hasActiveFilters(filters)).toBe(true);
    });
  });

  describe('resetFilters', () => {
    it('should return the default filter state', () => {
      const result = resetFilters();
      expect(result).toEqual(DEFAULT_FILTER_STATE);
    });

    it('should return a new object, not a reference to DEFAULT_FILTER_STATE', () => {
      const result = resetFilters();
      expect(result).not.toBe(DEFAULT_FILTER_STATE);
    });
  });

  describe('localStorage functions', () => {
    // Mock localStorage
    let localStorageMock: { [key: string]: string } = {};
    
    beforeEach(() => {
      localStorageMock = {};
      
      // Mock localStorage methods
      global.localStorage = {
        getItem: vi.fn((key) => localStorageMock[key] || null),
        setItem: vi.fn((key, value) => {
          localStorageMock[key] = value.toString();
        }),
        removeItem: vi.fn((key) => {
          delete localStorageMock[key];
        }),
        clear: vi.fn(() => {
          localStorageMock = {};
        }),
        length: 0,
        key: vi.fn((index) => ''),
      };
    });

    afterEach(() => {
      vi.clearAllMocks();
    });

    describe('getFilterStorageKey', () => {
      it('should return the correct key with default userId', () => {
        expect(getFilterStorageKey()).toBe('inventory_filters_default');
      });

      it('should return the correct key with custom userId', () => {
        expect(getFilterStorageKey('user123')).toBe('inventory_filters_user123');
      });
    });

    describe('saveFiltersToStorage', () => {
      it('should save filters to localStorage', () => {
        const filters: FilterState = {
          ...DEFAULT_FILTER_STATE,
          stockQuantity: {
            ...DEFAULT_FILTER_STATE.stockQuantity,
            enabled: true
          }
        };
        
        saveFiltersToStorage(filters);
        
        expect(localStorage.setItem).toHaveBeenCalledWith(
          'inventory_filters_default',
          JSON.stringify(filters)
        );
      });

      it('should use custom userId when provided', () => {
        const filters: FilterState = { ...DEFAULT_FILTER_STATE };
        const userId = 'user123';
        
        saveFiltersToStorage(filters, userId);
        
        expect(localStorage.setItem).toHaveBeenCalledWith(
          'inventory_filters_user123',
          JSON.stringify(filters)
        );
      });

      it('should handle localStorage errors', () => {
        const filters: FilterState = { ...DEFAULT_FILTER_STATE };
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        
        // Make localStorage.setItem throw an error
        (localStorage.setItem as any).mockImplementation(() => {
          throw new Error('localStorage error');
        });
        
        // This should not throw
        saveFiltersToStorage(filters);
        
        expect(consoleErrorSpy).toHaveBeenCalled();
        consoleErrorSpy.mockRestore();
      });
    });

    describe('loadFiltersFromStorage', () => {
      it('should load filters from localStorage', () => {
        const filters: FilterState = {
          ...DEFAULT_FILTER_STATE,
          stockQuantity: {
            ...DEFAULT_FILTER_STATE.stockQuantity,
            enabled: true
          }
        };
        
        // Set up localStorage mock to return our filters
        localStorageMock['inventory_filters_default'] = JSON.stringify(filters);
        
        const result = loadFiltersFromStorage();
        
        expect(localStorage.getItem).toHaveBeenCalledWith('inventory_filters_default');
        expect(result).toEqual(filters);
      });

      it('should use custom userId when provided', () => {
        const filters: FilterState = { ...DEFAULT_FILTER_STATE };
        const userId = 'user123';
        
        // Set up localStorage mock to return our filters
        localStorageMock['inventory_filters_user123'] = JSON.stringify(filters);
        
        loadFiltersFromStorage(userId);
        
        expect(localStorage.getItem).toHaveBeenCalledWith('inventory_filters_user123');
      });

      it('should return null when no filters are found', () => {
        const result = loadFiltersFromStorage();
        
        expect(localStorage.getItem).toHaveBeenCalledWith('inventory_filters_default');
        expect(result).toBeNull();
      });

      it('should handle localStorage errors', () => {
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        
        // Make localStorage.getItem throw an error
        (localStorage.getItem as any).mockImplementation(() => {
          throw new Error('localStorage error');
        });
        
        const result = loadFiltersFromStorage();
        
        expect(consoleErrorSpy).toHaveBeenCalled();
        expect(result).toBeNull();
        consoleErrorSpy.mockRestore();
      });

      it('should handle invalid JSON', () => {
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        
        // Set up localStorage mock to return invalid JSON
        localStorageMock['inventory_filters_default'] = 'invalid json';
        
        const result = loadFiltersFromStorage();
        
        expect(consoleErrorSpy).toHaveBeenCalled();
        expect(result).toBeNull();
        consoleErrorSpy.mockRestore();
      });
    });
  });
});
