'use client';

import React from 'react';
import {
  Table,
  TableHeader,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from '@/app/components/ui/table';
import { Badge } from '@/app/components/ui/badge';
import { Button } from '@/app/components/ui/button';
import {
  Boxes,
  PackageOpen,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Interface for inventory items displayed in the table.
 * This is a hybrid interface that combines data from both the Part model
 * and the InventoryLevel model to display in the UI.
 */
interface InventoryItem {
  // Part fields
  _id: string;
  id: string; // For backward compatibility
  partNumber: string;
  name: string;
  description: string;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: string;
  unitOfMeasure: string;
  cost: number;
  technicalSpecs?: string;

  // Supplier fields
  supplierId?: { _id: string; name: string } | string | null;
  supplierManufacturer?: string;
  supplierManufacturerId?: string | null;
  supplier?: { _id: string; name: string } | null; // Direct supplier object from API
  supplierName?: string; // Additional supplier name field

  // Category fields
  categoryId?: { _id: string; name: string; description?: string } | string | null;
  categoryName?: string;

  // Warehouse fields
  warehouseId?: string | null;
  warehouseName?: string;

  // Embedded inventory data from Part model
  inventory?: {
    currentStock: number;
    // Modified: warehouseId can be either a string or an object with _id, name, location
    warehouseId?: { _id: string; name: string; location?: string } | string;
    // Modified: Add warehouse as it's included in the API response
    warehouse?: { _id: string; name: string; location?: string } | null;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate: Date | null;
  };

  // Additional fields from InventoryLevel model
  location_in_warehouse?: string;
  quantity_on_hand?: number;
  quantity_allocated?: number;
  quantity_available?: number;
  safety_stock_level?: number;
  maximum_stock_level?: number;
  notes?: string;

  // For backward compatibility
  location?: string;
  currentStock?: number;

  // Timestamps
  createdAt: string;
  updatedAt: string;
}

interface InventoryTableProps {
  inventoryItems: InventoryItem[];
  onView?: (item: InventoryItem) => void;
  onEdit?: (item: InventoryItem) => void;
  onDelete?: (item: InventoryItem) => void;
}

export function InventoryTable({
  inventoryItems,
  onView,
  onEdit,
  onDelete,
}: InventoryTableProps) {
  const { theme } = useTheme();

  console.log('[DEBUG] InventoryTable - Received items count:', inventoryItems.length);

  // Log the first item in detail to help diagnose issues
  if (inventoryItems.length > 0) {
    const firstItem = inventoryItems[0];
    console.log('[DEBUG] First item details:', {
      _id: firstItem._id,
      partNumber: firstItem.partNumber,
      name: firstItem.name,
      currentStock: firstItem.inventory?.currentStock ?? firstItem.currentStock ?? 0,
      supplier: firstItem.supplier,
      supplierId: firstItem.supplierId,
      supplierManufacturer: firstItem.supplierManufacturer,
      supplierName: firstItem.supplierName,
      reorderLevel: firstItem.reorderLevel ?? firstItem.inventory?.safetyStockLevel ?? 0,
      status: firstItem.status
    });
  }

  function getStockStatusBadge(item: InventoryItem) {
    console.log(`[DEBUG] getStockStatusBadge - Item: ${item.name} (${item._id})`);
    console.log(`[DEBUG] Stock data:`, {
      inventoryStock: item.inventory?.currentStock,
      topLevelStock: item.currentStock,
      reorderLevel: item.reorderLevel,
      safetyLevel: item.inventory?.safetyStockLevel
    });

    // Use currentStock from inventory object or from top-level property as fallback
    const stock = item.inventory?.currentStock ?? item.currentStock ?? 0;

    // Use reorderLevel or safetyStockLevel from inventory as fallback
    const reorder = item.reorderLevel ?? item.inventory?.safetyStockLevel ?? -1;

    // Detailed logging of stock status condition
    console.log(`[DEBUG] Stock calculation: stock=${stock}, reorder=${reorder}, condition=${stock <= 0 ? 'Out of Stock' :
      (reorder >= 0 && stock <= reorder ? 'Low Stock' : 'In Stock')}`);

    // No inventory data
    if (!item.inventory && item.currentStock === undefined) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          No Data
        </Badge>
      );
    }

    if (stock <= 0) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Out of Stock
        </Badge>
      );
    }

    if (reorder >= 0 && stock <= reorder) {
      return (
        <Badge variant="warning" className="flex items-center gap-1 bg-yellow-500 hover:bg-yellow-600">
          <AlertCircle className="h-3 w-3" />
          Low Stock
        </Badge>
      );
    }

    return (
      <Badge variant="success" className="flex items-center gap-1 bg-green-500 hover:bg-green-600">
        <Boxes className="h-3 w-3" />
        In Stock
      </Badge>
    );
  }

  // Helper function to format the date
  const formatDate = (dateString: string | Date) => {
    if (!dateString) return '—';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="border rounded-md overflow-hidden">
      <Table>
        <TableHeader className="bg-muted/50 dark:bg-muted/20">
          <TableRow>
            <TableHead className="font-medium">Part Number</TableHead>
            <TableHead className="font-medium">Name</TableHead>
            <TableHead className="font-medium">Current Stock</TableHead>
            <TableHead className="font-medium">Supplier</TableHead>
            <TableHead className="font-medium">Reorder Level</TableHead>
            <TableHead className="font-medium">Status</TableHead>
            <TableHead className="text-right font-medium">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {inventoryItems && inventoryItems.length > 0 ? (
            inventoryItems.map((item) => (
              <TableRow
                key={item._id || item.id}
                className="hover:bg-muted/30 dark:hover:bg-muted/10 transition-colors"
              >
                <TableCell className="font-mono text-xs">
                  <div className="inline-block px-2 py-1 bg-primary/5 dark:bg-primary/10 rounded border border-primary/10 dark:border-primary/20">
                    {item.partNumber}
                  </div>
                </TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell className="text-center">
                  <span className={(item.inventory?.currentStock ?? item.currentStock ?? 0) > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                    {item.inventory?.currentStock ?? item.currentStock ?? 0} {item.unitOfMeasure || 'pcs'}
                  </span>
                </TableCell>
                <TableCell>
                  {(() => {
                    // Log supplier data for debugging
                    console.log(`[DEBUG] Supplier data for ${item.name}:`, {
                      supplier: item.supplier,
                      supplierId: item.supplierId,
                      supplierManufacturer: item.supplierManufacturer,
                      supplierName: item.supplierName
                    });

                    // Check if supplier exists directly on the item
                    if (item.supplier && typeof item.supplier === 'object' && item.supplier !== null && 'name' in item.supplier && item.supplier.name) {
                      return item.supplier.name;
                    }
                    // Check if supplierId is an object with a name property
                    else if (item.supplierId && typeof item.supplierId === 'object' && item.supplierId !== null && 'name' in item.supplierId && item.supplierId.name) {
                      return item.supplierId.name;
                    }
                    // Fall back to supplierManufacturer or supplierName
                    else if (item.supplierManufacturer) {
                      return item.supplierManufacturer;
                    }
                    else if (item.supplierName) {
                      return item.supplierName;
                    }
                    // Use 'N/A' as a last resort
                    else {
                      return 'N/A';
                    }
                  })()}
                </TableCell>
                <TableCell className="text-center">
                  {item.reorderLevel ?? item.inventory?.safetyStockLevel ?? 0}
                </TableCell>
                <TableCell>{getStockStatusBadge(item)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    {onView && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onView(item)}
                        className="h-8 w-8 hover:bg-primary/10 dark:hover:bg-primary/20"
                      >
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View</span>
                      </Button>
                    )}
                    {onEdit && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEdit(item)}
                        className="h-8 w-8 hover:bg-primary/10 dark:hover:bg-primary/20"
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                    )}
                    {onDelete && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDelete(item)}
                        className="h-8 w-8 text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-10">
                <PackageOpen className="mx-auto h-12 w-12 text-muted-foreground" />
                <p className="mt-2 text-sm text-muted-foreground">
                  No inventory items found.
                </p>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}