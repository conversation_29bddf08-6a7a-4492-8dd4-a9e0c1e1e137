'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Layers,
  ChevronRight
} from 'lucide-react';

import { DeleteAssemblyAction } from '@/app/components/actions/DeleteAssemblyAction';
import { QuickEditAssemblyAction } from '@/app/components/actions/QuickEditAssemblyAction';
import { DuplicateAssemblyAction } from '@/app/components/actions/DuplicateAssemblyAction';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';
import { AssemblyEditButton } from '@/app/components/modals/AssemblyFormModal';

import { Badge } from '@/app/components/ui/badge';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/app/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import { Assembly, Part } from '@/app/components/tables/AssembliesTable/types';
import { cn } from '@/app/lib/utils';

// Magic UI styles
import { useTheme } from '@/app/context/ThemeContext';

interface EnhancedAssemblyCardProps {
  assembly: Assembly;
  onRefresh?: () => void;
}

/**
 * Enhanced Card component for displaying assembly information with Magic UI effects
 */
const EnhancedAssemblyCard = ({ assembly, onRefresh }: EnhancedAssemblyCardProps) => {
  const { theme } = useTheme();
  // Define these variables for use in the component
  const hasValidParts = !!(assembly && assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0);
  const partsCount = (assembly && assembly.partsRequired && Array.isArray(assembly.partsRequired)) ? assembly.partsRequired.length : 0;

  // Check if we have full part details (populated from API)
  const hasDetailedParts = !!(assembly && 
    assembly.partsRequired && 
    Array.isArray(assembly.partsRequired) &&
    assembly.partsRequired.some(part =>
      part && // Ensure part itself is not null/undefined
      typeof part.partId === 'object' && part.partId !== null && 'name' in part.partId
    ));

  return (
    <div className="group h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -5, transition: { duration: 0.2 } }}
        className="h-full relative"
      >
        {/* Shine border effect with theme-based colors */}
        <div className={cn(
          "absolute -inset-0.5 rounded-xl opacity-0 group-hover:opacity-100 blur transition duration-1000 group-hover:duration-200",
          "bg-gradient-to-r theme-gradient-border"
        )}></div>

        <Card className={cn(
          "h-full flex flex-col relative bg-background border border-border z-10",
          "before:absolute before:inset-0 before:rounded-xl before:bg-[radial-gradient(800px_circle_at_var(--x,_0px)_var(--y,_0px),rgba(255,255,255,0.06),transparent_40%)] before:opacity-0 before:transition-opacity before:duration-500 group-hover:before:opacity-100",
          "hover:shadow-xl transition-all duration-300",
          theme === 'light' ? "shadow-sm bg-white/95 border-gray-200" : ""
        )}
        onMouseMove={(e) => {
          const rect = e.currentTarget.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          e.currentTarget.style.setProperty('--x', `${x}px`);
          e.currentTarget.style.setProperty('--y', `${y}px`);
        }}>
          <CardHeader className="pb-2 flex flex-row justify-between items-start">
            <div>
              <motion.h3
                className={cn(
                  "text-lg font-semibold line-clamp-1",
                  theme === 'light' ? "text-gray-800" : "theme-gradient-text"
                )}
              >
                {assembly.name}
              </motion.h3>
              <Badge variant="outline" className={cn("font-mono mt-1", theme === 'light' ? "border-gray-300 bg-gray-50 text-gray-700" : "")}>
                {assembly.assemblyCode}
              </Badge>
            </div>
            <div className="flex flex-col items-end gap-2">
              <AssemblyStatusBadge assembly={assembly} size="sm" />
            </div>
          </CardHeader>

          <CardContent className="py-2 flex-grow">
            <div className="flex items-center gap-2 mb-3">
              <PartsCountBadge assembly={assembly} size="sm" />
            </div>

            {/* Parts preview */}
            {hasValidParts && (
              <div className="mt-2 space-y-1">
                <p className={cn("text-sm font-medium mb-1",
                  theme === 'light' ? "text-gray-700" : "text-muted-foreground"
                )}>Parts:</p>
                <ul className="text-sm space-y-1 max-h-24 overflow-y-auto pr-1">
                  {(assembly.partsRequired || []).slice(0, 3).map((part, index) => {
                    // Check if we have detailed part information (populated)
                    const partDetail = hasDetailedParts && part && typeof part.partId === 'object' ? part.partId as Part : null;
                    const partName = partDetail?.name || partDetail?.partNumber || (part && typeof part.partId === 'string' ? part.partId : 'Unknown Part');
                    const hasInventory = !!(partDetail?.inventory && partDetail.inventory.currentStock !== undefined);

                    return part && part.partId ? (
                      <li key={index} className="flex items-center justify-between text-xs group">
                        <div className="flex items-center">
                          <span className="w-4 inline-block">•</span>
                          <span className={cn("font-medium",
                            theme === 'light' ? "text-gray-800" : ""
                          )}>{partName}</span>
                          <span className={cn("ml-1",
                            theme === 'light' ? "text-gray-600" : "text-muted-foreground"
                          )}>
                            ({part.quantityRequired}x)
                          </span>
                        </div>

                        {/* Inventory information if available */}
                        {hasInventory && partDetail?.inventory && (
                          <div className="text-xs opacity-70 group-hover:opacity-100">
                            <Badge variant={partDetail.inventory.currentStock < part.quantityRequired ? "destructive" : "outline"} className="ml-1 text-xs">
                              Stock: {partDetail.inventory.currentStock}
                            </Badge>
                          </div>
                        )}
                      </li>
                    ) : (
                      <li key={index} className="flex items-center text-xs text-red-500">
                        <span className="w-4 inline-block">•</span>
                        <span>Missing part reference</span>
                      </li>
                    );
                  })}
                  {partsCount > 3 && (
                    <li className={cn("text-xs",
                      theme === 'light' ? "text-gray-500" : "text-muted-foreground"
                    )}>
                      <span className="w-4 inline-block">•</span>
                      <span>+{partsCount - 3} more parts</span>
                    </li>
                  )}
                </ul>
              </div>
            )}

            {!hasValidParts && (
              <div className="flex flex-col items-center justify-center py-4">
                <Layers className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">No parts defined</p>
              </div>
            )}
          </CardContent>

          <CardFooter className="pt-2 flex justify-between items-center border-t overflow-hidden">
            {/* Left side actions */}
            <div className="flex gap-1 flex-shrink-0">
              <DeleteAssemblyAction
                assembly={assembly}
                variant="icon"
                size="sm"
                onSuccess={onRefresh}
              />
              <DuplicateAssemblyAction
                assembly={assembly}
                variant="icon"
                size="sm"
                onSuccess={onRefresh}
              />
            </div>

            {/* Right side actions - wrapped for better spacing */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <QuickEditAssemblyAction
                assembly={assembly}
                variant="icon"
                size="sm"
                onSuccess={onRefresh}
              />
              <AssemblyEditButton
                assemblyId={assembly._id}
                onSuccess={onRefresh}
              />
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="default" size="sm" asChild className="group/btn relative overflow-hidden">
                      <Link href={`/assemblies/${assembly.assemblyCode}`}>
                        <span className="relative z-10">View</span>
                        <ChevronRight size={16} className="ml-1 relative z-10" />
                        <div className={cn(
                          "absolute inset-0 translate-x-[-100%] group-hover/btn:translate-x-[100%] transition-transform duration-300",
                          "bg-gradient-to-r theme-gradient-bg"
                        )}></div>
                      </Link>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View Assembly Details</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}

export default EnhancedAssemblyCard;