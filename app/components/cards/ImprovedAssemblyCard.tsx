'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Layers,
  ChevronRight,
  BarChart4,
  AlertCircle
} from 'lucide-react';

import { DeleteAssemblyAction } from '@/app/components/actions/DeleteAssemblyAction';
import { QuickEditAssemblyAction } from '@/app/components/actions/QuickEditAssemblyAction';
import { DuplicateAssemblyAction } from '@/app/components/actions/DuplicateAssemblyAction';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';

import { Badge } from '@/app/components/ui/badge';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/app/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import { Assembly, Part } from '@/app/components/tables/AssembliesTable/types';
import { cn } from '@/app/lib/utils';
import { useTheme } from '@/app/context/ThemeContext';
import { Progress } from '@/app/components/ui/progress';

interface ImprovedAssemblyCardProps {
  assembly: Assembly;
  onRefresh?: () => void;
}

/**
 * Improved Card component for displaying assembly information
 * Combines the best of AssemblyCard and EnhancedAssemblyCard with optimized performance
 */
export function ImprovedAssemblyCard({ assembly, onRefresh }: ImprovedAssemblyCardProps) {
  const { theme } = useTheme();

  // Define these variables for use in the component with stronger type checking
  const hasValidParts = useMemo(() => (
    assembly &&
    assembly.partsRequired &&
    Array.isArray(assembly.partsRequired) &&
    assembly.partsRequired.length > 0
  ), [assembly]);

  const partsCount = useMemo(() => (
    hasValidParts ? assembly.partsRequired.length : 0
  ), [hasValidParts, assembly]);

  // Check if we have full part details (populated from API)
  const hasDetailedParts = useMemo(() => (
    hasValidParts &&
    assembly.partsRequired.some(part =>
      part &&
      typeof part.partId === 'object' &&
      part.partId !== null &&
      'name' in part.partId
    )
  ), [hasValidParts, assembly]);

  // Calculate inventory status
  const inventoryStatus = useMemo(() => {
    if (!hasDetailedParts || !hasValidParts) return { status: 'unknown', percent: 0 };
    
    let availableParts = 0;
    let totalPartsNeeded = 0;
    
    assembly.partsRequired.forEach(part => {
      if (!part || typeof part.partId !== 'object' || !part.partId) return;
      
      const partDetail = part.partId as Part;
      const quantity = part.quantity || 1;
      const available = partDetail?.inventory?.currentStock || 0;
      
      totalPartsNeeded += quantity;
      availableParts += Math.min(available, quantity);
    });
    
    if (totalPartsNeeded === 0) return { status: 'unknown', percent: 0 };
    
    const percent = Math.round((availableParts / totalPartsNeeded) * 100);
    
    if (percent >= 100) return { status: 'complete', percent: 100 };
    if (percent >= 75) return { status: 'good', percent };
    if (percent >= 30) return { status: 'warning', percent };
    return { status: 'critical', percent };
  }, [hasDetailedParts, hasValidParts, assembly]);

  // Get critical parts (low stock)
  const criticalParts = useMemo(() => {
    if (!hasDetailedParts || !hasValidParts) return [];
    
    return assembly.partsRequired
      .filter(part => {
        if (!part || typeof part.partId !== 'object' || !part.partId) return false;
        
        const partDetail = part.partId as Part;
        const quantity = part.quantity || 1;
        const available = partDetail?.inventory?.currentStock || 0;
        
        return available < quantity;
      })
      .map(part => {
        const partDetail = part.partId as Part;
        const quantity = part.quantity || 1;
        const available = partDetail?.inventory?.currentStock || 0;
        
        return {
          name: partDetail.name,
          needed: quantity,
          available,
          shortage: quantity - available
        };
      })
      .sort((a, b) => (b.shortage / b.needed) - (a.shortage / a.needed));
  }, [hasDetailedParts, hasValidParts, assembly]);

  return (
    <div className="group h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -5, transition: { duration: 0.2 } }}
        className="h-full relative"
      >
        {/* Shine border effect with theme-based colors */}
        <div className={cn(
          "absolute -inset-0.5 rounded-xl opacity-0 group-hover:opacity-100 blur transition duration-1000 group-hover:duration-200",
          "bg-gradient-to-r theme-gradient-border"
        )}></div>

        <Card className={cn(
          "h-full flex flex-col relative bg-background border border-border z-10",
          "before:absolute before:inset-0 before:rounded-xl before:bg-[radial-gradient(800px_circle_at_var(--x,_0px)_var(--y,_0px),rgba(255,255,255,0.06),transparent_40%)] before:opacity-0 before:transition-opacity before:duration-500 group-hover:before:opacity-100",
          "hover:shadow-xl transition-all duration-300",
          theme === 'light' ? "shadow-sm bg-white/95 border-gray-200" : ""
        )}
        onMouseMove={(e) => {
          const rect = e.currentTarget.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          e.currentTarget.style.setProperty('--x', `${x}px`);
          e.currentTarget.style.setProperty('--y', `${y}px`);
        }}>
          <CardHeader className="pb-2 flex flex-row justify-between items-start">
            <div>
              <motion.h3
                className={cn(
                  "text-lg font-semibold line-clamp-1",
                  theme === 'light' ? "text-gray-800" : "theme-gradient-text"
                )}
              >
                {assembly.name}
              </motion.h3>
              <Badge variant="outline" className={cn("font-mono mt-1", theme === 'light' ? "border-gray-300 bg-gray-50 text-gray-700" : "")}>
                {assembly.assemblyCode}
              </Badge>
            </div>
            <div className="flex flex-col items-end gap-2">
              <AssemblyStatusBadge assembly={assembly} size="sm" />
            </div>
          </CardHeader>

          <CardContent className="py-2 flex-grow">
            <div className="flex items-center justify-between gap-2 mb-3">
              <PartsCountBadge assembly={assembly} size="sm" />
              
              {/* Inventory Status */}
              <div className="flex flex-col">
                <div className="flex items-center gap-1 text-xs font-medium">
                  <span className={cn(
                    inventoryStatus.status === 'complete' ? "text-green-500" :
                    inventoryStatus.status === 'good' ? "text-blue-500" :
                    inventoryStatus.status === 'warning' ? "text-amber-500" :
                    inventoryStatus.status === 'critical' ? "text-red-500" : "text-muted-foreground"
                  )}>
                    {inventoryStatus.status === 'complete' ? "All Parts Available" :
                    inventoryStatus.status === 'good' ? "Most Parts Available" :
                    inventoryStatus.status === 'warning' ? "Some Parts Missing" :
                    inventoryStatus.status === 'critical' ? "Critical Parts Missing" : "Unknown Status"}
                  </span>
                </div>
                <div className="w-full mt-1">
                  <Progress 
                    value={inventoryStatus.percent} 
                    className="h-1.5" 
                    color={
                      inventoryStatus.status === 'complete' ? "bg-green-500" :
                      inventoryStatus.status === 'good' ? "bg-blue-500" :
                      inventoryStatus.status === 'warning' ? "bg-amber-500" :
                      inventoryStatus.status === 'critical' ? "bg-red-500" : ""
                    }
                  />
                </div>
              </div>
            </div>

            {/* Parts preview */}
            {hasValidParts && (
              <div className="mt-2 space-y-1">
                <p className={cn("text-sm font-medium mb-1",
                  theme === 'light' ? "text-gray-700" : "text-muted-foreground"
                )}>Parts:</p>
                <ul className="text-sm space-y-1 max-h-24 overflow-y-auto pr-1 custom-scrollbar">
                  {criticalParts.length > 0 ? (
                    // Show critical parts first
                    criticalParts.slice(0, 2).map((part, index) => (
                      <li key={`critical-part-${index}`} className="flex items-center text-xs text-red-500">
                        <AlertCircle className="w-3 h-3 mr-1 flex-shrink-0" />
                        <span className="truncate">{part.name}</span>
                        <span className="ml-auto whitespace-nowrap">{part.available}/{part.needed}</span>
                      </li>
                    ))
                  ) : (
                    // Show regular parts if no critical parts
                    assembly.partsRequired.slice(0, 3).map((part, index) => {
                      // Verify part exists before accessing properties
                      if (!part) {
                        return (
                          <li key={`missing-part-${index}`} className="flex items-center text-xs text-red-500">
                            <span className="w-4 inline-block">•</span>
                            <span>Invalid part entry</span>
                          </li>
                        );
                      }

                      // Check if we have detailed part information (populated)
                      const partDetail = hasDetailedParts && typeof part.partId === 'object' ? part.partId as Part : null;
                      const partName = partDetail?.name || (typeof part.partId === 'string' ? part.partId : 'Unknown Part');
                      const hasInventory = partDetail?.inventory && partDetail.inventory.currentStock !== undefined;
                      const quantity = part.quantity || 1;
                      const stock = hasInventory ? partDetail?.inventory?.currentStock || 0 : null;
                      
                      return (
                        <li key={`part-${index}`} className={cn("flex items-center text-xs justify-between",
                          theme === 'light' ? "text-gray-700" : "text-muted-foreground"
                        )}>
                          <span className="w-4 inline-block">•</span>
                          <span className="truncate">{partName}</span>
                          <span className="ml-auto flex items-center whitespace-nowrap">
                            {quantity}× 
                            {hasInventory && (
                              <span className={cn("ml-1",
                                stock !== null && stock >= quantity ? "text-green-500" : "text-red-500"
                              )}>
                                ({stock}/{quantity})
                              </span>
                            )}
                          </span>
                        </li>
                      );
                    })
                  )}
                  
                  {partsCount > 3 && (
                    <li className={cn("text-xs",
                      theme === 'light' ? "text-gray-500" : "text-muted-foreground"
                    )}>
                      <span className="w-4 inline-block">•</span>
                      <span>+{partsCount - 3} more parts</span>
                    </li>
                  )}
                </ul>
              </div>
            )}

            {!hasValidParts && (
              <div className="flex flex-col items-center justify-center py-4">
                <Layers className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">No parts defined</p>
              </div>
            )}
          </CardContent>

          <CardFooter className="pt-2 px-4 flex justify-between items-center border-t overflow-hidden relative">
            <div className="flex gap-1 flex-shrink-0 relative z-20">
              <DeleteAssemblyAction
                assembly={assembly}
                variant="icon"
                size="sm"
                onSuccess={onRefresh}
                className="relative z-20"
                id={`delete-${assembly._id}`}
              />

              <DuplicateAssemblyAction
                assembly={assembly}
                variant="icon"
                size="sm"
                onSuccess={onRefresh}
                className="relative z-20"
                id={`duplicate-${assembly._id}`}
              />
            </div>

            <div className="flex gap-2 flex-shrink-0 relative z-20">
              <QuickEditAssemblyAction
                assembly={assembly}
                variant="icon"
                size="sm"
                onSuccess={onRefresh}
                className="relative z-20"
                id={`quick-edit-${assembly._id}`}
              />

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="default"
                      size="sm"
                      asChild
                      className="group/btn relative overflow-hidden z-10"
                    >
                      <Link href={`/assemblies/${assembly.assemblyCode || assembly._id}`}>
                        <span className="relative z-10 flex items-center">
                          View
                          <ChevronRight size={16} className="ml-1 relative z-10" />
                        </span>
                        <div className={cn(
                          "absolute inset-0 translate-x-[-100%] group-hover/btn:translate-x-[100%] transition-transform duration-300",
                          "bg-gradient-to-r theme-gradient-bg"
                        )}></div>
                      </Link>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View Assembly Details</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
} 