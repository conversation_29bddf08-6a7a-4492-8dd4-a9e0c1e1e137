'use client';

import React, { useState } from 'react';
import { Plus, Layers, LayoutGrid } from 'lucide-react';
import { Button } from '@/app/components/ui/button';
import { LazyUnifiedAssemblyForm } from '@/app/components/forms/LazyUnifiedAssemblyForm';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import { ShimmerButton, RippleButton } from '@/app/components/ui/magic-ui';
import { useTheme } from '@/app/context/ThemeContext';

interface AssemblyFormModalProps {
  onSuccess?: () => void;
}

/**
 * Assembly Form Modal Component
 * Provides buttons to open the unified assembly form modal
 * and manages the modal state
 */
export function AssemblyFormModal({ onSuccess }: AssemblyFormModalProps) {
  const { theme } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [assemblyId, setAssemblyId] = useState<string | undefined>(undefined);

  // Open modal for creating a new assembly
  const openCreateModal = () => {
    setModalMode('create');
    setAssemblyId(undefined);
    setIsModalOpen(true);
  };

  // Open modal for editing an existing assembly
  const openEditModal = (id: string) => {
    setModalMode('edit');
    setAssemblyId(id);
    setIsModalOpen(true);
  };

  // Close the modal
  const closeModal = () => {
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      {/* Create Assembly Buttons */}
      <div className="space-x-2">
        <RippleButton
          variant="secondary"
          className={`rounded-full ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-200'}`}
          onClick={openCreateModal}
          rippleColor={theme === 'dark' ? '#ffffff20' : '#00000020'}
        >
          <Plus size={16} className="mr-2" />
          <span>Standard Assembly</span>
        </RippleButton>

        {/* Hierarchical Part Entry */}
        <ShimmerButton
          onClick={openCreateModal}
          className="rounded-full px-4 py-2 text-sm font-medium"
          shimmerColor={theme === 'dark' ? '#ffffff30' : '#ffffff60'}
          background={theme === 'dark' ? 'rgba(16, 24, 39, 0.8)' : 'rgba(79, 70, 229, 1)'}
        >
          <Layers size={16} className="mr-2" />
          <span>Hierarchical Assembly</span>
        </ShimmerButton>

        {/* Visual Builder */}
        <RippleButton
          variant="outline"
          className={`rounded-full ${theme === 'dark' ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-100'}`}
          onClick={openCreateModal}
          rippleColor={theme === 'dark' ? '#ffffff20' : '#00000020'}
        >
          <LayoutGrid size={16} className="mr-2" />
          <span>Visual Builder</span>
        </RippleButton>
      </div>

      {/* Assembly Form Modal - Lazy Loaded */}
      <LazyUnifiedAssemblyForm
        isOpen={isModalOpen}
        onClose={closeModal}
        assemblyId={assemblyId}
        mode={modalMode}
      />
    </>
  );
}

/**
 * Assembly Edit Button Component
 * Standalone button to edit a specific assembly
 */
export function AssemblyEditButton({ assemblyId, onSuccess }: { assemblyId: string, onSuccess?: () => void }) {
  const { theme } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Enhanced openEditModal function with console logging for debugging
  const openEditModal = () => {
    console.log('Opening edit modal for assembly:', assemblyId);
    // Force any existing modals to close first
    document.body.click();
    setTimeout(() => {
      setIsModalOpen(true);
    }, 50);
  };

  const closeModal = () => {
    console.log('Closing edit modal for assembly:', assemblyId);
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      {/* Button with appropriate z-index and flex constraints */}
      <RippleButton
        variant="outline"
        size="sm"
        onClick={openEditModal}
        className={`relative z-10 flex-shrink-0 ${theme === 'dark' ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-100'}`}
        rippleColor={theme === 'dark' ? '#ffffff20' : '#00000020'}
      >
        Edit
      </RippleButton>

      {/* Modal with higher z-index - rendered at the root level */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <LazyUnifiedAssemblyForm
          isOpen={isModalOpen}
          onClose={closeModal}
          assemblyId={assemblyId}
          mode="edit"
        />
      </div>
    </>
  );
}