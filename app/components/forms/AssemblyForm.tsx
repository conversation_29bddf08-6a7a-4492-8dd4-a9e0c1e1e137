'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider } from 'react-hook-form';
import { toast } from 'sonner';
import { Save, ArrowLeft, Loader2 } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Input } from '@/app/components/ui/input';
import { Button } from '@/app/components/ui/button';
import { Textarea } from '@/app/components/ui/textarea';
import { Switch } from '@/app/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/app/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';

import { PartSelector } from './PartSelector';
import { PartRequirementsList } from './PartRequirementsList';
import { StockSummary } from './StockSummary';
import { useAssemblyForm } from './AssemblyFormWrapper';
import { 
  assemblyFormSchema, 
  partialAssemblyFormSchema, 
  defaultValues, 
  assemblyStatusOptions, 
  PartialAssemblyFormValues 
} from './schema';

interface AssemblyFormProps {
  className?: string;
}

export function AssemblyForm({ className }: AssemblyFormProps) {
  const router = useRouter();
  const { 
    formData, 
    isLoading, 
    isSaving, 
    isEditing, 
    isDirty,
    updateFormField,
    resetForm,
    saveAssembly
  } = useAssemblyForm();
  
  // Setup react-hook-form with zod validation
  const form = useForm<PartialAssemblyFormValues>({
    resolver: zodResolver(partialAssemblyFormSchema),
    defaultValues: formData,
    mode: 'onChange'
  });
  
  // Update form when formData changes
  useEffect(() => {
    if (!isLoading) {
      form.reset(formData);
    }
  }, [form, formData, isLoading]);
  
  // Update context when form fields change
  const handleFieldChange = <K extends keyof PartialAssemblyFormValues>(
    field: K, 
    value: PartialAssemblyFormValues[K]
  ) => {
    form.setValue(field, value);
    updateFormField(field, value);
  };
  
  // Handle form submission
  const onSubmit = async () => {
    try {
      // Validate the form with the stricter schema when submitting
      const result = assemblyFormSchema.safeParse(formData);
      
      if (!result.success) {
        // Show validation errors
        const fieldErrors = result.error.flatten().fieldErrors;
        
        for (const [field, errors] of Object.entries(fieldErrors)) {
          if (errors && errors.length > 0) {
            toast.error(`${field}: ${errors[0]}`);
          }
        }
        
        return;
      }
      
      // Save the assembly
      const success = await saveAssembly();
      
      if (success) {
        // Navigate back to assemblies list
        router.push('/assemblies');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to save assembly');
    }
  };
  
  // Handle cancel button click
  const handleCancel = () => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        resetForm();
        router.push('/assemblies');
      }
    } else {
      router.push('/assemblies');
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading assembly data...</span>
      </div>
    );
  }
  
  return (
    <div className={className}>
      {/* Header with actions */}
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={handleCancel} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Assemblies
        </Button>
        
        <Button 
          onClick={onSubmit}
          disabled={isSaving || (!isDirty && isEditing)}
          className="flex items-center gap-2"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Assembly
            </>
          )}
        </Button>
      </div>
      
      <FormProvider {...form}>
        <Form {...form}>
          <form className="space-y-6">
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="w-full justify-start mb-4">
                <TabsTrigger value="details" className="flex-1 max-w-[200px]">Assembly Details</TabsTrigger>
                <TabsTrigger value="parts" className="flex-1 max-w-[200px]">Parts Required</TabsTrigger>
                <TabsTrigger value="stock" className="flex-1 max-w-[200px]">Stock Status</TabsTrigger>
              </TabsList>
              
              {/* Assembly Details Tab */}
              <TabsContent value="details" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      {/* Assembly Code */}
                      <FormField
                        control={form.control}
                        name="assemblyCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assembly Code</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter assembly code" 
                                {...field} 
                                value={field.value || ''}
                                onChange={(e) => handleFieldChange('assemblyCode', e.target.value)}
                                disabled={isEditing} // Cannot change code in edit mode
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      {/* Assembly Name */}
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assembly Name</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter assembly name" 
                                {...field} 
                                value={field.value || ''}
                                onChange={(e) => handleFieldChange('name', e.target.value)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    {/* Description */}
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter assembly description" 
                              {...field} 
                              value={field.value || ''}
                              onChange={(e) => handleFieldChange('description', e.target.value)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="grid grid-cols-2 gap-4">
                      {/* Status */}
                      <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={(value) => handleFieldChange('status', value as any)}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {assemblyStatusOptions.map((status) => (
                                  <SelectItem key={status} value={status}>
                                    {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      {/* Version */}
                      <FormField
                        control={form.control}
                        name="version"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Version</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={1}
                                {...field}
                                value={field.value || 1}
                                onChange={(e) => handleFieldChange('version', parseInt(e.target.value) || 1)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    {/* Is Top Level Assembly */}
                    <FormField
                      control={form.control}
                      name="isTopLevel"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Top Level Assembly</FormLabel>
                            <FormDescription>
                              This is a standalone assembly, not part of another assembly
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => handleFieldChange('isTopLevel', checked)}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Manufacturing Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Manufacturing Instructions */}
                    <FormField
                      control={form.control}
                      name="manufacturingInstructions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Manufacturing Instructions</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter manufacturing instructions (optional)" 
                              {...field} 
                              value={field.value || ''}
                              onChange={(e) => handleFieldChange('manufacturingInstructions', e.target.value)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Estimated Build Time */}
                    <FormField
                      control={form.control}
                      name="estimatedBuildTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Build Time</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="e.g. 2 hours (optional)" 
                              {...field} 
                              value={field.value || ''}
                              onChange={(e) => handleFieldChange('estimatedBuildTime', e.target.value)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
              
              {/* Parts Required Tab */}
              <TabsContent value="parts" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Add Parts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <PartSelector />
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Parts Required</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <PartRequirementsList />
                  </CardContent>
                </Card>
              </TabsContent>
              
              {/* Stock Status Tab */}
              <TabsContent value="stock" className="space-y-6">
                <StockSummary />
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </FormProvider>
    </div>
  );
} 