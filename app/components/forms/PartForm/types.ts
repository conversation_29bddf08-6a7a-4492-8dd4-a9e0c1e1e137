import * as z from 'zod';
// import { PartDocument } from '@/app/types/inventory/part'; // Commented out if not directly used for PartFormData

/**
 * Interface for PartForm props
 */
export interface PartFormProps {
  /** Submit handler function */
  onSubmit: (data: PartFormData) => void;
  /** Close handler function */
  onClose: () => void;
  /** Initial data for the form (for editing) */
  initialData?: PartFormData;
  /** Whether the form is for editing or adding a part */
  isEdit?: boolean;
  /** Title to display on the form */
  title?: string;
}

/**
 * Schema for validating part form data
 * Uses Zod to define the validation rules
 */
export const partFormSchema = z.object({
  _id: z.string(),
  name: z.string().min(1, 'Name is required'),
  partNumber: z.string().optional(),
  description: z.string().optional(),
  technicalSpecs: z.string().optional(),
  isManufactured: z.boolean().default(false),
  reorderLevel: z.number().nullable(),
  status: z.enum(['active', 'inactive', 'obsolete']).default('active'),
  isAssembly: z.boolean().default(false),
  schemaVersion: z.number().optional(),
  subParts: z.array(
    z.object({
      partId: z.string(),
      quantity: z.number().min(1)
    })
  ).optional(),
  supplierId: z.string().optional(),
  unitOfMeasure: z.string().optional(),
  costPrice: z.number().optional(),
  categoryId: z.string().optional(),
  inventory: z.object({
    currentStock: z.number(),
    location: z.string().optional(),
    lastStockUpdate: z.date().nullable().optional(),
    warehouseId: z.string().optional(),
    safetyStockLevel: z.number().optional(),
    maximumStockLevel: z.number().optional(),
    averageDailyUsage: z.number().optional(),
    abcClassification: z.enum(['A', 'B', 'C']).optional()
  })
});

/**
 * Interface for the internal form data structure, derived from the Zod schema
 */
export type PartFormData = z.infer<typeof partFormSchema>;

// Explicitly define SubPartData to match Zod schema and usage
export interface SubPartData {
  partId: string;
  quantity: number;
}