"use client";

import React, { useState, useEffect } from 'react';
import { AlertCircle, Package, Database, Settings, Plus, Trash2, Edit2 } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Switch } from '@/app/components/ui/switch';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/app/components/ui/tabs';
import { Card, CardContent } from '@/app/components/ui/card';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

// Import enhanced form components
import {
  EnhancedFormContainer,
  EnhancedFormField,
  EnhancedFormItem,
  EnhancedFormLabel,
  EnhancedFormControl,
  EnhancedFormMessage,
  FormSubmitButton,
  useAutosave,
  AutosaveIndicator
} from '@/app/components/ui/enhanced-form';

import {
  PartFormProps,
  PartFormData,
  partFormSchema
} from './types';

// Helper function to generate a unique part ID
const generatePartId = () => {
  return `P-${uuidv4().substring(0, 8).toUpperCase()}`;
};

// Form error component
const FormError = ({ name, message }: { name: string; message?: string }) => {
  if (!message) return null;
  return (
    <div className="text-destructive text-sm mt-1 flex items-center">
      <AlertCircle className="h-3 w-3 mr-1" />
      <span>{message}</span>
    </div>
  );
};

/**
 * Enhanced client component implementation of PartForm
 * Includes inline validation, autosave, and improved loading states
 */
export default function EnhancedPartFormClient({
  onSubmit,
  onClose,
  initialData,
  isEdit = false,
  title
}: PartFormProps) {
  const { theme } = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const [availableParts, setAvailableParts] = useState<Array<{ _id: string, name: string }>>([]);
  const [suppliers, setSuppliers] = useState<Array<{ _id: string, name: string }>>([]);
  const [isLoadingParts, setIsLoadingParts] = useState(false);
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false);
  const [showSubPartForm, setShowSubPartForm] = useState(false);
  const [editingSubPartIndex, setEditingSubPartIndex] = useState<number | null>(null);
  const [formSubmitSuccess, setFormSubmitSuccess] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<PartFormData>({
    resolver: zodResolver(partFormSchema),
    defaultValues: {
      _id: initialData?._id || generatePartId(),
      name: initialData?.name || '',
      description: initialData?.description || '',
      technical_specs: initialData?.technical_specs || '',
      is_manufactured: initialData?.is_manufactured ?? false,
      reorder_level: initialData?.reorder_level ?? 0,
      status: initialData?.status || 'active',
      inventory: {
        current_stock: initialData?.inventory?.current_stock ?? 0,
        location: initialData?.inventory?.location || '',
        lastCountDate: initialData?.inventory?.lastCountDate || null,
      },
      isAssembly: initialData?.isAssembly ?? false,
      sub_parts: initialData?.sub_parts || [],
      schemaVersion: initialData?.schemaVersion || 1,
      supplier_id: initialData?.supplier_id || undefined,
    },
    mode: 'onChange' // Enable validation on change for immediate feedback
  });

  // Set up autosave functionality
  const { status: autosaveStatus, lastSaved, error: autosaveError } = useAutosave({
    form,
    onSave: async (data) => {
      // Only autosave if we're editing an existing part
      if (isEdit && initialData?._id) {
        // Store in localStorage only, don't submit to API
        localStorage.setItem(`part-form-autosave-${initialData._id}`, JSON.stringify(data));
      }
    },
    delay: 2000, // 2 seconds delay
    enabled: isEdit, // Only enable autosave for editing existing parts
    localStorageKey: isEdit && initialData?._id ? `part-form-autosave-${initialData._id}` : undefined
  });

  // Watch for changes to isAssembly to show/hide sub-parts section
  const isAssembly = form.watch('isAssembly');

  // Use field array to manage sub-parts
  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: 'sub_parts',
  });

  // Fetch available parts for sub-part selection
  useEffect(() => {
    const fetchParts = async () => {
      if (isAssembly) {
        try {
          setIsLoadingParts(true);
          const response = await fetch('/api/parts');
          if (!response.ok) {
            throw new Error('Failed to fetch parts');
          }
          const data = await response.json();
          // Filter out the current part if editing
          const filteredParts = data.data.filter((part: any) =>
            !isEdit || part._id !== initialData?._id
          );
          setAvailableParts(filteredParts);
        } catch (error) {
          console.error('Error fetching parts:', error);
          toast.error('Failed to load parts for assembly');
        } finally {
          setIsLoadingParts(false);
        }
      }
    };

    fetchParts();
  }, [isAssembly, isEdit, initialData?._id]);

  // Fetch suppliers for supplier selection
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setIsLoadingSuppliers(true);
        const response = await fetch('/api/suppliers');
        if (!response.ok) {
          throw new Error('Failed to fetch suppliers');
        }
        const data = await response.json();
        if (data.data) {
          setSuppliers(data.data);
        }
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        // Don't show error toast as suppliers might not be critical
      } finally {
        setIsLoadingSuppliers(false);
      }
    };

    fetchSuppliers();
  }, []);

  /**
   * Handles adding a new sub-part to the assembly
   */
  const handleAddSubPart = (partId: string, quantity: number) => {
    // Find the part in availableParts
    const part = availableParts.find(p => p._id === partId);
    if (!part) {
      toast.error('Selected part not found');
      return;
    }

    if (editingSubPartIndex !== null) {
      // Update existing sub-part
      update(editingSubPartIndex, { part_id: partId, quantity });
      setEditingSubPartIndex(null);
    } else {
      // Add new sub-part
      append({ part_id: partId, quantity });
    }

    setShowSubPartForm(false);
  };

  /**
   * Handles editing an existing sub-part
   */
  const handleEditSubPart = (index: number) => {
    setEditingSubPartIndex(index);
    setShowSubPartForm(true);
  };

  /**
   * Handles removing a sub-part from the assembly
   */
  const handleRemoveSubPart = (index: number) => {
    remove(index);
  };

  /**
   * Handles form submission
   * Submits data to parent component and closes the form on success
   */
  const onFormSubmit = form.handleSubmit(async (data) => {
    try {
      setIsSubmitting(true);
      setFormError(null);
      setFormSubmitSuccess(false);

      // Validate that assemblies have at least one sub-part
      if (data.isAssembly && (!data.sub_parts || data.sub_parts.length === 0)) {
        setFormError('An assembly must have at least one sub-part');
        setActiveTab('advanced'); // Switch to advanced tab to show the error
        setIsSubmitting(false);
        return;
      }

      // Format the data to match the MongoDB schema
      const formattedData: PartFormData = {
        ...data,
        inventory: {
          current_stock: data.inventory.current_stock,
          location: data.inventory.location || '',
          lastCountDate: data.inventory.lastCountDate || null,
        },
        status: data.status || 'active',
        schemaVersion: data.schemaVersion || 1,
        isAssembly: data.isAssembly || false,
        sub_parts: data.sub_parts || [],
        supplier_id: data.supplier_id,
      };

      // Remove any duplicate top-level fields that should only be in the inventory object
      delete (formattedData as any).currentStock;
      delete (formattedData as any).location;

      await onSubmit(formattedData);
      setFormSubmitSuccess(true);

      // Short delay to show success state before closing
      setTimeout(() => {
        onClose();
      }, 1000);
    } catch (error) {
      console.error('Error submitting form:', error);
      setFormError('Failed to submit form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  });

  // Sub-part form component
  const SubPartForm = () => {
    const [selectedPartId, setSelectedPartId] = useState<string>(
      editingSubPartIndex !== null && fields[editingSubPartIndex]
        ? fields[editingSubPartIndex].part_id
        : ''
    );
    const [quantity, setQuantity] = useState<number>(
      editingSubPartIndex !== null && fields[editingSubPartIndex]
        ? fields[editingSubPartIndex].quantity
        : 1
    );

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (!selectedPartId) {
        toast.error('Please select a part');
        return;
      }
      if (quantity < 1) {
        toast.error('Quantity must be at least 1');
        return;
      }
      handleAddSubPart(selectedPartId, quantity);
    };

    return (
      <Card className="mb-4 border border-input dark:border-dark-border">
        <CardContent className="p-4">
          <h3 className="text-lg font-medium mb-4">
            {editingSubPartIndex !== null ? 'Edit Sub-Part' : 'Add Sub-Part'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="part_id">Select Part</Label>
              <select
                id="part_id"
                value={selectedPartId}
                onChange={(e) => setSelectedPartId(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                required
                aria-label="Select a part for this assembly"
              >
                <option value="">Select a part...</option>
                {availableParts.map((part) => (
                  <option key={part._id} value={part._id}>
                    {part.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity</Label>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 bg-secondary dark:bg-dark-element rounded-l-md border border-input dark:border-dark-border"
                  aria-label="Decrease quantity"
                >
                  -
                </button>
                <input
                  id="quantity"
                  type="number"
                  min="1"
                  value={quantity}
                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  className="flex h-10 w-20 text-center border-y border-input dark:border-dark-border bg-background px-3 py-2 text-sm focus-visible:outline-none dark:bg-dark-element/60 dark:text-dark-text-primary"
                  required
                  aria-label="Quantity of this part needed in the assembly"
                />
                <button
                  type="button"
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 bg-secondary dark:bg-dark-element rounded-r-md border border-input dark:border-dark-border"
                  aria-label="Increase quantity"
                >
                  +
                </button>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowSubPartForm(false);
                  setEditingSubPartIndex(null);
                }}
              >
                Cancel
              </Button>
              <Button type="submit">
                {editingSubPartIndex !== null ? 'Update' : 'Add'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    );
  };

  // Form footer with cancel and submit buttons
  const formFooter = (
    <>
      <Button
        variant="outline"
        onClick={onClose}
        type="button"
      >
        Cancel
      </Button>
      <FormSubmitButton
        type="submit"
        form="part-form"
        isLoading={isSubmitting}
        isSuccess={formSubmitSuccess}
        loadingText={isEdit ? "Updating..." : "Creating..."}
        successText={isEdit ? "Updated!" : "Created!"}
      >
        {isEdit ? 'Update Part' : 'Create Part'}
      </FormSubmitButton>
    </>
  );

  return (
    <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50 p-4">
      <EnhancedFormContainer
        title={title || (isEdit ? 'Edit Part' : 'Add New Part')}
        isLoading={isSubmitting}
        error={formError}
        animate={true}
        footer={formFooter}
        className="max-w-3xl mx-auto"
        autosaveStatus={autosaveStatus}
        lastSaved={lastSaved}
        autosaveError={autosaveError}
        showAutosaveIndicator={isEdit}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Package size={16} />
              <span>General</span>
            </TabsTrigger>
            <TabsTrigger value="inventory" className="flex items-center gap-2">
              <Database size={16} />
              <span>Inventory</span>
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Settings size={16} />
              <span>Advanced</span>
            </TabsTrigger>
          </TabsList>

          <form id="part-form" onSubmit={onFormSubmit} className="space-y-6">
            {/* General Tab Content */}
            <TabsContent value="general" className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Part ID Field */}
                  <EnhancedFormItem>
                    <EnhancedFormLabel htmlFor="_id">Part ID</EnhancedFormLabel>
                    <EnhancedFormField
                      name="_id"
                      control={form.control}
                      render={({ field }) => (
                        <EnhancedFormControl>
                          <Input
                            {...field}
                            id="_id"
                            readOnly={isEdit}
                            className={form.formState.errors._id ? "border-destructive" : ""}
                          />
                        </EnhancedFormControl>
                      )}
                    />
                    <EnhancedFormMessage />
                  </EnhancedFormItem>

                  {/* Status Field */}
                  <EnhancedFormItem>
                    <EnhancedFormLabel htmlFor="status">Status</EnhancedFormLabel>
                    <EnhancedFormField
                      name="status"
                      control={form.control}
                      render={({ field }) => (
                        <EnhancedFormControl>
                          <select
                            {...field}
                            id="status"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="obsolete">Obsolete</option>
                          </select>
                        </EnhancedFormControl>
                      )}
                    />
                    <EnhancedFormMessage />
                  </EnhancedFormItem>
                </div>

                {/* Name Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="name">Name</EnhancedFormLabel>
                  <EnhancedFormField
                    name="name"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <Input
                          {...field}
                          id="name"
                          placeholder="Enter part name"
                          className={form.formState.errors.name ? "border-destructive" : ""}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Description Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="description">Description</EnhancedFormLabel>
                  <EnhancedFormField
                    name="description"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <textarea
                          {...field}
                          id="description"
                          placeholder="Enter part description"
                          className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                          value={field.value || ''}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Technical Specs Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="technical_specs">Technical Specifications</EnhancedFormLabel>
                  <EnhancedFormField
                    name="technical_specs"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <textarea
                          {...field}
                          id="technical_specs"
                          placeholder="Enter technical specifications"
                          className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                          value={field.value || ''}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Supplier Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="supplier_id">Supplier</EnhancedFormLabel>
                  <EnhancedFormField
                    name="supplier_id"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <select
                          {...field}
                          id="supplier_id"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                          value={field.value || ''}
                        >
                          <option value="">Select a supplier...</option>
                          {suppliers.map((supplier) => (
                            <option key={supplier._id} value={supplier._id}>
                              {supplier.name}
                            </option>
                          ))}
                        </select>
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Is Manufactured Field */}
                <EnhancedFormItem>
                  <div className="flex items-center space-x-2">
                    <EnhancedFormField
                      name="is_manufactured"
                      control={form.control}
                      render={({ field }) => (
                        <EnhancedFormControl>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="is_manufactured"
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                            <EnhancedFormLabel htmlFor="is_manufactured" className="cursor-pointer">
                              Manufactured In-House
                            </EnhancedFormLabel>
                          </div>
                        </EnhancedFormControl>
                      )}
                    />
                  </div>
                  <EnhancedFormMessage />
                </EnhancedFormItem>
              </div>
            </TabsContent>

            {/* Inventory Tab Content */}
            <TabsContent value="inventory" className="space-y-6">
              <div className="space-y-4">
                {/* Current Stock Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="inventory.current_stock">Current Stock</EnhancedFormLabel>
                  <EnhancedFormField
                    name="inventory.current_stock"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <Input
                          {...field}
                          id="inventory.current_stock"
                          type="number"
                          min="0"
                          onChange={(e) => field.onChange(Number(e.target.value))}
                          className={form.formState.errors.inventory?.current_stock ? "border-destructive" : ""}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Location Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="inventory.location">Storage Location</EnhancedFormLabel>
                  <EnhancedFormField
                    name="inventory.location"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <Input
                          {...field}
                          id="inventory.location"
                          placeholder="Enter storage location"
                          className={form.formState.errors.inventory?.location ? "border-destructive" : ""}
                          value={field.value || ''}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Reorder Level Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="reorder_level">Reorder Level</EnhancedFormLabel>
                  <EnhancedFormField
                    name="reorder_level"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <Input
                          {...field}
                          id="reorder_level"
                          type="number"
                          min="0"
                          onChange={(e) => field.onChange(Number(e.target.value) || null)}
                          className={form.formState.errors.reorder_level ? "border-destructive" : ""}
                          value={field.value === null ? '' : field.value}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                  <p className="text-xs text-muted-foreground mt-1">
                    Set the minimum stock level at which to reorder this part
                  </p>
                </EnhancedFormItem>

                {/* Last Count Date Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="inventory.lastCountDate">Last Inventory Count</EnhancedFormLabel>
                  <EnhancedFormField
                    name="inventory.lastCountDate"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <Input
                          {...field}
                          id="inventory.lastCountDate"
                          type="date"
                          value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                          onChange={(e) => {
                            const date = e.target.value ? new Date(e.target.value) : null;
                            field.onChange(date);
                          }}
                          className={form.formState.errors.inventory?.lastCountDate ? "border-destructive" : ""}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                </EnhancedFormItem>
              </div>
            </TabsContent>

            {/* Advanced Tab Content */}
            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-4">
                {/* Is Assembly Field */}
                <EnhancedFormItem>
                  <div className="flex items-center space-x-2">
                    <EnhancedFormField
                      name="isAssembly"
                      control={form.control}
                      render={({ field }) => (
                        <EnhancedFormControl>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="isAssembly"
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                                // If turning off assembly, clear sub-parts
                                if (!checked && fields.length > 0) {
                                  if (confirm('Removing assembly status will delete all sub-parts. Continue?')) {
                                    // Remove all sub-parts
                                    fields.forEach((_, index) => remove(index));
                                  } else {
                                    // Keep assembly status if user cancels
                                    field.onChange(true);
                                  }
                                }
                              }}
                              aria-label="Toggle assembly status"
                            />
                            <EnhancedFormLabel htmlFor="isAssembly" className="cursor-pointer">
                              This is an Assembly
                            </EnhancedFormLabel>
                          </div>
                        </EnhancedFormControl>
                      )}
                    />
                  </div>
                  <EnhancedFormMessage />
                </EnhancedFormItem>

                {/* Sub-parts section - only visible when isAssembly is true */}
                {isAssembly && (
                  <div className="mt-6 space-y-4 border border-input dark:border-dark-border rounded-md p-4 bg-secondary/10 dark:bg-dark-element/20">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">Sub-Parts</h3>
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => {
                          setEditingSubPartIndex(null);
                          setShowSubPartForm(true);
                        }}
                        className="flex items-center"
                        aria-label="Add a sub-part to this assembly"
                      >
                        <Plus size={16} className="mr-1" />
                        Add Sub-Part
                      </Button>
                    </div>

                    {showSubPartForm && <SubPartForm />}

                    {fields.length === 0 ? (
                      <div className="text-center py-6 text-muted-foreground">
                        <Package className="h-12 w-12 mx-auto mb-2 opacity-20" />
                        <p>No sub-parts added yet.</p>
                        <p className="text-sm">Click "Add Sub-Part" to add components to this assembly.</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {fields.map((field, index) => {
                          // Find the part details from availableParts
                          const part = availableParts.find(p => p._id === field.part_id);

                          return (
                            <div
                              key={field.id}
                              className="flex items-center justify-between p-3 bg-background dark:bg-dark-element/40 rounded-md border border-input dark:border-dark-border"
                            >
                              <div className="flex-1">
                                <div className="font-medium">{part?.name || 'Unknown Part'}</div>
                                <div className="text-sm text-muted-foreground">
                                  Quantity: {field.quantity}
                                </div>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditSubPart(index)}
                                  aria-label={`Edit ${part?.name || 'part'}`}
                                >
                                  <Edit2 className="h-4 w-4" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveSubPart(index)}
                                  aria-label={`Remove ${part?.name || 'part'}`}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                )}

                {/* Schema Version Field */}
                <EnhancedFormItem>
                  <EnhancedFormLabel htmlFor="schemaVersion">Schema Version</EnhancedFormLabel>
                  <EnhancedFormField
                    name="schemaVersion"
                    control={form.control}
                    render={({ field }) => (
                      <EnhancedFormControl>
                        <Input
                          {...field}
                          id="schemaVersion"
                          type="number"
                          min="1"
                          readOnly
                          onChange={(e) => field.onChange(Number(e.target.value))}
                          className={form.formState.errors.schemaVersion ? "border-destructive" : ""}
                        />
                      </EnhancedFormControl>
                    )}
                  />
                  <EnhancedFormMessage />
                  <p className="text-xs text-muted-foreground mt-1">
                    This field is managed automatically by the system
                  </p>
                </EnhancedFormItem>
              </div>
            </TabsContent>
          </form>
        </Tabs>
      </EnhancedFormContainer>
    </div>
  );
}
