"use client";

import React from "react";
import { useRouter } from "next/navigation";

/**
 * Temporary simplified version of ProductFormClient
 * This is a placeholder until we can fix the build issues
 */
export function ProductFormClient({ initialData, mode = "create" }) {
  const router = useRouter();

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">
        {mode === "create" ? "Create New Product" : "Edit Product"}
      </h1>
      <p className="text-gray-500 dark:text-gray-400 mb-6">
        Enter product details and associated components
      </p>

      <div className="space-y-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
          <p className="text-center text-gray-500 dark:text-gray-400">
            This form is temporarily unavailable during maintenance.
            We're working to fix it as soon as possible.
          </p>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            onClick={() => router.push("/products")}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
          >
            Cancel
          </button>
          <button
            onClick={() => router.push("/products")}
            className="px-4 py-2 bg-blue-500 text-white rounded-md"
          >
            {mode === "create" ? "Create Product" : "Update Product"}
          </button>
        </div>
      </div>
    </div>
  );
}