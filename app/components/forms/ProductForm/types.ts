/**
 * Simplified types for the ProductForm component
 */

/**
 * Props for the ProductForm component
 */
export interface ProductFormProps {
  /**
   * Initial data for the form. If provided, form will be pre-filled with this data.
   */
  initialData?: any;

  /**
   * Mode of the form. 'create' for creating new products, 'edit' for editing existing ones.
   * @default "create"
   */
  mode?: "create" | "edit";
}

/**
 * Props for the ProductFormClient component, extends ProductFormProps
 */
export interface ProductFormClientProps extends ProductFormProps {
  // Any additional props specific to the client component can be added here
}