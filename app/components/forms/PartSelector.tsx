'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, Loader2 } from 'lucide-react';
import { Input } from '@/app/components/ui/input';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent } from '@/app/components/ui/card';
import { Label } from '@/app/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Badge } from '@/app/components/ui/badge';
import { PartRequirement, unitOfMeasureOptions } from './schema';
import { useAssemblyForm } from './AssemblyFormWrapper';
import { PartSearchResult } from './schema';

interface PartSelectorProps {
  onSelect?: (part: PartRequirement) => void;
}

export function PartSelector({ onSelect }: PartSelectorProps) {
  const { addPart } = useAssemblyForm();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<PartSearchResult[]>([]);
  const [selectedPart, setSelectedPart] = useState<PartSearchResult | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [unitOfMeasure, setUnitOfMeasure] = useState<string>('ea');
  const [error, setError] = useState<string | null>(null);
  
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Debounced search function
  const performSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/parts/search?q=${encodeURIComponent(query)}`);
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }
      
      const data = await response.json();
      setSearchResults(data.results || []);
    } catch (error) {
      console.error('Error searching parts:', error);
      setError('Failed to search parts. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);
  
  // Handle search input changes with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    if (searchQuery.trim()) {
      searchTimeoutRef.current = setTimeout(() => {
        performSearch(searchQuery);
      }, 300); // 300ms debounce
    } else {
      setSearchResults([]);
    }
    
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery, performSearch]);
  
  // Handle part selection
  const handlePartSelect = (part: PartSearchResult) => {
    setSelectedPart(part);
    setSearchQuery(''); // Clear search after selection
    setSearchResults([]); // Clear results
  };
  
  // Add the selected part to the assembly
  const handleAddPart = () => {
    if (!selectedPart) {
      setError('Please select a part first');
      return;
    }
    
    if (quantity <= 0) {
      setError('Quantity must be greater than zero');
      return;
    }
    
    // Create the part requirement object
    const partRequirement: PartRequirement = {
      partId: selectedPart._id,
      name: selectedPart.name,
      description: selectedPart.description || '',
      partNumber: selectedPart.partNumber || selectedPart.assemblyCode || '',
      quantityRequired: quantity,
      unitOfMeasure: unitOfMeasure as any, // Type assertion to satisfy TS
      isAssembly: selectedPart.isAssembly || false,
      currentStock: selectedPart.current_stock || 0,
      stockStatus: selectedPart.current_stock >= quantity ? 'sufficient' : 'insufficient'
    };
    
    // Add to the assembly
    addPart(partRequirement);
    
    // Call the onSelect callback if provided
    if (onSelect) {
      onSelect(partRequirement);
    }
    
    // Reset the form
    setSelectedPart(null);
    setQuantity(1);
    setUnitOfMeasure('ea');
    setError(null);
  };
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Add Part to Assembly</h3>
      
      {/* Part Search */}
      <div className="relative">
        <Label htmlFor="part-search">Search for a part</Label>
        <div className="relative mt-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            id="part-search"
            placeholder="Search by part name, number, or description"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {isSearching && (
            <Loader2 className="absolute right-2 top-2.5 h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>
        
        {/* Search Results Dropdown */}
        {searchResults.length > 0 && (
          <Card className="absolute z-10 w-full mt-1 max-h-72 overflow-auto">
            <CardContent className="p-0">
              <ul className="py-1">
                {searchResults.map((part) => (
                  <li
                    key={part._id}
                    className="px-3 py-2 hover:bg-muted cursor-pointer"
                    onClick={() => handlePartSelect(part)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">{part.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {part.partNumber || part.assemblyCode || part._id}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {part.isAssembly && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
                            Assembly
                          </Badge>
                        )}
                        <Badge 
                          variant={part.current_stock > 0 ? "success" : "destructive"}
                          className="text-xs"
                        >
                          Stock: {part.current_stock || 0}
                        </Badge>
                      </div>
                    </div>
                    {part.description && (
                      <div className="text-xs text-muted-foreground mt-1 truncate">
                        {part.description}
                      </div>
                    )}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* Selected Part */}
      {selectedPart && (
        <div className="p-3 border rounded-md bg-muted/20">
          <div className="flex justify-between">
            <div>
              <h4 className="font-medium">{selectedPart.name}</h4>
              <div className="text-sm text-muted-foreground">
                {selectedPart.partNumber || selectedPart.assemblyCode || selectedPart._id}
              </div>
            </div>
            <Badge 
              variant={selectedPart.current_stock > 0 ? "success" : "destructive"}
            >
              Stock: {selectedPart.current_stock || 0}
            </Badge>
          </div>
        </div>
      )}
      
      {/* Quantity and UOM */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="quantity">Quantity Required</Label>
          <Input
            id="quantity"
            type="number"
            min="1"
            value={quantity}
            onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="uom">Unit of Measure</Label>
          <Select
            value={unitOfMeasure}
            onValueChange={setUnitOfMeasure}
          >
            <SelectTrigger id="uom" className="mt-1">
              <SelectValue placeholder="Select unit" />
            </SelectTrigger>
            <SelectContent>
              {unitOfMeasureOptions.map((unit) => (
                <SelectItem key={unit} value={unit}>
                  {unit}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="text-sm text-destructive">
          {error}
        </div>
      )}
      
      {/* Add Button */}
      <Button
        onClick={handleAddPart}
        disabled={!selectedPart}
        className="w-full"
      >
        Add Part to Assembly
      </Button>
    </div>
  );
} 