"use client";

import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Package, Plus, Truck, Database, Gift, Search, Loader } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import { searchParts } from '@/app/lib/supabase';
import debounce from 'lodash.debounce';
import { Database as DatabaseTypes } from '@/app/types/supabase';
import { FormContainer } from '@/app/components/ui/form-container';
import { Button } from '@/app/components/ui/button';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/app/components/ui/tabs';

interface FormData {
  part?: {
    id?: string;
    name?: string;
    description?: string | null;
    is_external?: boolean;
    assemblyStage?: string;
    supplier_id?: number | null;
    manufacturer_part_number?: string | null;
    technical_specs?: string | null;
    barcode_sku?: string | null;
    unit_of_measure?: string | null;
    minimum_order_quantity?: number | null;
    lead_time_days?: number | null;
  };
  inventory?: {
    currentStock?: number;
    reorderLevel?: number;
    reorderQuantity?: number;
    unitCost?: number;
    location?: string | null;
    last_inventory_count_date?: string | null;
    average_daily_usage?: number | null;
    safety_stock_level?: number | null;
    maximum_stock_level?: number | null;
    abc_classification?: string | null;
  };
  childParts?: {
    partId: string;
    quantity: number;
    position?: string | null;
    notes?: string | null;
  }[];
  additionalDetails?: {
    dimensions?: string;
    weight?: string;
    material?: string;
    notes?: string;
  };
}

interface EnhancedPartFormProps {
  onSubmit: (data: FormData) => void;
  onClose: () => void;
  initialData?: FormData;
  isEdit?: boolean;
  title?: string;
}

interface SearchResult {
  part_id: string;
  name: string;
  description: string | null;
  inventory: {
    current_stock: number;
    reorder_level: number;
    location: string | null;
  } | null;
  [key: string]: any;
}

const EnhancedPartForm: React.FC<EnhancedPartFormProps> = ({
  onSubmit,
  onClose,
  initialData,
  isEdit = false,
  title = 'Add New Part',
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const [formData, setFormData] = useState<FormData>({
    part: {
      id: '',
      name: '',
      description: '',
      assemblyStage: 'Component',
      supplier_id: null,
      manufacturer_part_number: '',
      technical_specs: '',
      barcode_sku: '',
      unit_of_measure: '',
      minimum_order_quantity: null,
      lead_time_days: null,
    },
    inventory: {
      currentStock: 0,
      reorderLevel: 0,
      reorderQuantity: 10,
      unitCost: 0,
      location: 'Main Warehouse',
    },
    additionalDetails: {
      dimensions: '',
      weight: '',
      material: '',
      notes: '',
    },
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        part: {
          ...formData.part,
          ...initialData.part,
        },
        inventory: {
          ...formData.inventory,
          ...initialData.inventory,
        },
        childParts: initialData.childParts || [],
        additionalDetails: {
          ...formData.additionalDetails,
          ...initialData.additionalDetails,
        },
      });
    }
  }, [initialData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const debouncedSearch = useRef(
    debounce(async (query: string) => {
      if (query.length < 2) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      try {
        setIsSearching(true);
        const results = await searchParts(query);
        setSearchResults(results || []);
        setShowResults(true);
      } catch (error) {
        console.error('Error searching parts:', error);
      } finally {
        setIsSearching(false);
      }
    }, 300)
  ).current;

  useEffect(() => {
    debouncedSearch(searchQuery);

    // Cleanup debounced function on component unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handlePartSelect = (result: SearchResult) => {
    setFormData({
      ...formData,
      part: {
        ...formData.part,
        id: result.part_id,
        name: result.name,
        description: result.description || '',
        supplier_id: result.supplier_id ? Number(result.supplier_id) : null,
        manufacturer_part_number: result.manufacturer_part_number || '',
        // Map other part properties as needed
      },
      inventory: {
        ...formData.inventory,
        currentStock: result.inventory?.current_stock || 0,
        // Remove reorderLevel since it doesn't exist in the schema
        location: result.inventory?.location || '',
      },
    });

    setSearchQuery('');
    setShowResults(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('inventory.')) {
      const inventoryField = name.split('.')[1];
      setFormData({
        ...formData,
        inventory: {
          ...formData.inventory,
          [inventoryField]: value,
        },
      });
    } else if (name.startsWith('additionalDetails.')) {
      const detailsField = name.split('.')[1];
      setFormData({
        ...formData,
        additionalDetails: {
          ...formData.additionalDetails,
          [detailsField]: value,
        },
      });
    } else {
      // Map UI field names to database field names
      const fieldMap: { [key: string]: string } = {
        'manufacturerPartNumber': 'manufacturer_part_number',
        'leadTimeWeeks': 'lead_time_days',
        'supplier': 'supplier_id',
        // Add other mappings as needed
      };

      const fieldName = fieldMap[name] || name;

      setFormData({
        ...formData,
        part: {
          ...formData.part,
          [fieldName]: value,
        },
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormError(null);
    
    try {
      onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
      setFormError('Failed to submit the form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Form footer with action buttons
  const formFooter = (
    <>
      <Button variant="outline" onClick={onClose} type="button">
        Cancel
      </Button>
      <Button type="submit" form="enhanced-part-form" disabled={isSubmitting}>
        {isEdit ? 'Update Part' : 'Create Part'}
      </Button>
    </>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
      <FormContainer
        title={title}
        isLoading={isSubmitting}
        error={formError}
        animate={true}
        footer={formFooter}
        className="max-w-4xl mx-auto"
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Package size={16} />
              <span>General</span>
            </TabsTrigger>
            <TabsTrigger value="inventory" className="flex items-center gap-2">
              <Database size={16} />
              <span>Inventory</span>
            </TabsTrigger>
            <TabsTrigger value="details" className="flex items-center gap-2">
              <Gift size={16} />
              <span>Details</span>
            </TabsTrigger>
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search size={16} />
              <span>Search Part</span>
            </TabsTrigger>
          </TabsList>

          <form id="enhanced-part-form" onSubmit={handleSubmit}>
            {/* General Info Tab */}
            {activeTab === 'general' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Part Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.part?.name || ''}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Assembly Stage
                    </label>
                    <select
                      name="assemblyStage"
                      value={formData.part?.assemblyStage || 'Component'}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    >
                      <option value="Component">Component</option>
                      <option value="Sub-Assembly">Sub-Assembly</option>
                      <option value="Final Assembly">Final Assembly</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.part?.description || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Manufacturer Part Number
                    </label>
                    <input
                      type="text"
                      name="manufacturerPartNumber"
                      value={formData.part?.manufacturer_part_number || ''}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Barcode / SKU
                    </label>
                    <input
                      type="text"
                      name="barcode_sku"
                      value={formData.part?.barcode_sku || ''}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Technical Specifications
                  </label>
                  <textarea
                    name="technical_specs"
                    value={formData.part?.technical_specs || ''}
                    onChange={handleChange}
                    rows={3}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                  />
                </div>
              </div>
            )}

            {/* Inventory Tab */}
            {activeTab === 'inventory' && (
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Current Stock
                    </label>
                    <input
                      type="number"
                      name="inventory.currentStock"
                      value={formData.inventory?.currentStock || 0}
                      onChange={handleChange}
                      min="0"
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Reorder Level
                    </label>
                    <input
                      type="number"
                      name="inventory.reorderLevel"
                      value={formData.inventory?.reorderLevel || 0}
                      onChange={handleChange}
                      min="0"
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Reorder Quantity
                    </label>
                    <input
                      type="number"
                      name="inventory.reorderQuantity"
                      value={formData.inventory?.reorderQuantity || 10}
                      onChange={handleChange}
                      min="0"
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Unit Cost
                    </label>
                    <input
                      type="number"
                      name="inventory.unitCost"
                      value={formData.inventory?.unitCost || 0}
                      onChange={handleChange}
                      min="0"
                      step="0.01"
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Location
                    </label>
                    <input
                      type="text"
                      name="inventory.location"
                      value={formData.inventory?.location || ''}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Lead Time (Days)
                    </label>
                    <input
                      type="number"
                      name="lead_time_days"
                      value={formData.part?.lead_time_days || ''}
                      onChange={handleChange}
                      min="0"
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Minimum Order Quantity
                    </label>
                    <input
                      type="number"
                      name="minimum_order_quantity"
                      value={formData.part?.minimum_order_quantity || ''}
                      onChange={handleChange}
                      min="0"
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Additional Details Tab */}
            {activeTab === 'details' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Dimensions
                    </label>
                    <input
                      type="text"
                      name="additionalDetails.dimensions"
                      value={formData.additionalDetails?.dimensions || ''}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                      placeholder="e.g. 10cm x 5cm x 2cm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Weight
                    </label>
                    <input
                      type="text"
                      name="additionalDetails.weight"
                      value={formData.additionalDetails?.weight || ''}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                      placeholder="e.g. 500g"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Material
                  </label>
                  <input
                    type="text"
                    name="additionalDetails.material"
                    value={formData.additionalDetails?.material || ''}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    placeholder="e.g. Stainless Steel, Aluminum, Plastic"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Notes
                  </label>
                  <textarea
                    name="additionalDetails.notes"
                    value={formData.additionalDetails?.notes || ''}
                    onChange={handleChange}
                    rows={4}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                    placeholder="Any additional information about this part..."
                  />
                </div>
              </div>
            )}

            {/* Search for existing parts */}
            {activeTab === 'search' && (
              <div ref={searchContainerRef} className="mb-6 relative">
                <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-2">
                  <Search size={18} className="text-gray-500 dark:text-gray-400 mr-2" />
                  <input
                    type="text"
                    placeholder="Search for existing parts..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="bg-transparent border-none outline-none w-full text-gray-700 dark:text-gray-200"
                  />
                  {isSearching && <Loader size={18} className="text-blue-500 animate-spin" />}
                </div>

                {/* Search results dropdown */}
                <AnimatePresence>
                  {showResults && searchResults.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md overflow-hidden border border-gray-200 dark:border-gray-700"
                    >
                      <div className="max-h-60 overflow-y-auto">
                        {searchResults.map((result) => (
                          <div
                            key={result.part_id}
                            className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700"
                            onClick={() => handlePartSelect(result)}
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">
                              {result.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              ID: {result.part_id} • Stock: {result.inventory?.current_stock || 0}
                            </div>
                          </div>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </form>
        </Tabs>
      </FormContainer>
    </div>
  );
};

export default EnhancedPartForm;