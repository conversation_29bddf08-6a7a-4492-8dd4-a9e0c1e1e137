'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { X, Save, Loader2, Plus, Edit, Trash, Eye, Layers } from 'lucide-react';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';
import { cn } from '@/app/lib/utils';

import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/app/components/ui/card';
import { HierarchicalPartsForm } from '@/app/components/forms/HierarchicalPartsForm';
import { useAssemblyForm } from '@/app/contexts/AssemblyFormContext';
import { ShimmerButton, MagicCard, RippleButton, BorderBeam } from '@/app/components/ui/magic-ui';
import { useTheme } from '@/app/context/ThemeContext';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { Input } from '@/app/components/ui/input';
import { PartSearch, PartSearchResult } from '@/app/components/search/PartSearch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";

interface UnifiedAssemblyFormProps {
  isOpen: boolean;
  onClose: () => void;
  assemblyId?: string;
  mode?: 'create' | 'edit';
}

/**
 * Unified Assembly Form component - appears as a popup modal
 * Used for both creating and editing assemblies
 */
export function UnifiedAssemblyForm({ isOpen, onClose, assemblyId, mode = 'create' }: UnifiedAssemblyFormProps) {
  const hierarchicalPartsFormRef = useRef<{ triggerSubmit: () => Promise<void> }>(null);
  const router = useRouter();
  const { theme } = useTheme();
  const {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    saveAssembly,
    resetForm,
    loadAssembly,
    setFormData,
    updateFormField
  } = useAssemblyForm();

  const [showConfirmClose, setShowConfirmClose] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [showPartsTable, setShowPartsTable] = useState(mode === 'edit');
  const [selectedPart, setSelectedPart] = useState<any>(null);
  const [showPartDetails, setShowPartDetails] = useState(false);
  const [showPartSearch, setShowPartSearch] = useState(false);
  const [editPartQuantity, setEditPartQuantity] = useState<{index: number, quantity: number} | null>(null);

  // Create a ref for the portal container
  const portalRef = useRef<HTMLDivElement | null>(null);

  // Initialize portal container on mount
  useEffect(() => {
    setIsMounted(true);

    // Create portal container if it doesn't exist
    let portalContainer = document.getElementById('assembly-form-portal');
    if (!portalContainer) {
      portalContainer = document.createElement('div');
      portalContainer.id = 'assembly-form-portal';
      document.body.appendChild(portalContainer);
    }
    portalRef.current = portalContainer as HTMLDivElement;

    return () => {
      // Safer cleanup - only remove if it exists and we're the last component using it
      const container = document.getElementById('assembly-form-portal');
      if (container && container.childNodes.length === 0 && container.parentNode === document.body) {
        document.body.removeChild(container);
      }
    };
  }, []);

  const prevAssemblyIdRef = useRef<string | undefined>(undefined);
  const prevIsOpenRef = useRef<boolean>(false);

  // Load assembly data if in edit mode
  useEffect(() => {
    const assemblyIdActuallyChanged = assemblyId !== prevAssemblyIdRef.current;
    const formJustOpened = isOpen && !prevIsOpenRef.current;

    if (isOpen && assemblyId && mode === 'edit') {
      // Only load if assemblyId actually changed, or if the form just opened with a valid assemblyId.
      // This helps prevent re-loading if other dependencies cause re-renders 
      // but these key conditions haven't meaningfully changed for a reload.
      if (assemblyIdActuallyChanged || formJustOpened) {
        console.log('UnifiedAssemblyForm: Conditions met (ID changed or form opened), loading assembly:', assemblyId);
        loadAssembly(assemblyId);
      } else {
        // console.log('UnifiedAssemblyForm: Conditions for load not met or already loaded for current state.');
      }
    }

    // Update refs for the next render to track changes accurately
    prevAssemblyIdRef.current = assemblyId;
    prevIsOpenRef.current = isOpen;
  }, [isOpen, assemblyId, mode, loadAssembly]); // Keep original dependencies for correctness

  // Memoize formatted assembly data for the form to prevent unnecessary re-renders
  const initialAssemblyData = useMemo(() => {
    if (!formData) return { partsRequired: [] };

    // Recursive function to transform parts and maintain hierarchy
    const mapPartsRecursively = (parts: any[]): any[] => {
      return parts.map((part: any) => {
        // The 'part' object here comes from formData.partsRequired, which should already be processed by AssemblyFormContext
        // to include partDisplayIdentifier, name, description, etc., directly or via a populated partId object.

        // Get quantity from either quantityRequired (new schema) or quantity (old schema)
        const quantity = part?.quantityRequired || part?.quantity || 0;
        const unitOfMeasure = part?.unitOfMeasure || part?.unit_of_measure || 'ea';

        // Create the formatted part with all required fields for HierarchicalPartsForm
        const formattedPart: {
          id: any; // This is the BOM item's _id or a generated UUID for new items
          partId: string; // This is the actual Part's _id
          partDisplayIdentifier: string; // This is the human-readable identifier (partNumber, assemblyCode)
          name: string;
          description: string;
          quantity: number; // Keep as quantity for UI consistency in HierarchicalPartsForm
          quantityRequired?: number; // Store for DB compatibility and internal logic
          isExpanded: boolean;
          category?: string;
          currentStock?: number;
          reorderLevel?: number;
          technicalSpecs?: string;
          unitOfMeasure?: string; // Canonical field name
          children?: any[];
        } = {
          // Ensure stable ID: Use existing _id (from DB) or id (from form state).
          // Random ID generation here caused instability and re-render loops.
          // If an ID is missing, it's an upstream data issue to be addressed.
          id: part?._id || part?.id,
          partId: part?.partId, // This should be the actual Part's _id
          partDisplayIdentifier: part?.partDisplayIdentifier || part?.partId, // Use the pre-processed display identifier, fallback to partId
          name: part?.name || 'Unknown Part',
          description: part?.description || '',
          quantity: Number(quantity),
          quantityRequired: Number(quantity), // Store for DB compatibility
          isExpanded: part?.isExpanded !== undefined ? part.isExpanded : true,
          category: part?.category || '',

          currentStock: part?.currentStock || part?.inventory?.currentStock || 0,
          reorderLevel: part?.reorderLevel || 0,
          technicalSpecs: part?.technicalSpecs || '',
          unitOfMeasure: unitOfMeasure
        };

        // If this part has children, recursively map them too
        if (part.children && Array.isArray(part.children) && part.children.length > 0) {
          formattedPart.children = mapPartsRecursively(part.children);
        }

        return formattedPart;
      });
    };

    // Transform assembly parts data to match HierarchicalPartsForm expected format
    const formattedParts = formData.partsRequired && Array.isArray(formData.partsRequired)
      ? mapPartsRecursively(formData.partsRequired)
      : [];

    console.log('Formatted parts with hierarchy:', formattedParts);

    // Map legacy status values to canonical status values if needed
    let statusValue = formData?.status || 'active';
    // Remove 'in_production' and map to 'active'
    if (statusValue === 'in_production') statusValue = 'active';
    // If using legacy status values, map them to canonical values
    if ((statusValue as any) === 'design_phase' || (statusValue as any) === 'design_complete') {
      statusValue = statusValue;
    }

    // For backward compatibility, map assembly_stage to status if needed
    // This is a legacy field that should be mapped to the canonical status field
    // Cast to any to access potential legacy fields
    const legacyStage = (formData as any)?.assembly_stage as string | undefined;
    if (legacyStage && !formData?.status) {
      switch (legacyStage) {
        case 'Final Assembly':
        case 'FINAL ASSEMBLY':
          statusValue = 'active';
          break;
        case 'Sub-Assembly':
        case 'SUB ASSEMBLY':
          statusValue = 'pending_review';
          break;
      }
    }

    // Return data in the format expected by HierarchicalPartsForm
    return {
      assemblyCode: formData?.assemblyCode || '',
      name: formData?.name || '',
      description: formData?.description || '',
      status: statusValue, // Only allowed values
      productId: formData?.productId || '', // Always a string for Select
      parentId: formData?.parentId || '', // Always a string for Select
      isTopLevel: formData?.isTopLevel === undefined ? true : formData.isTopLevel,
      version: formData?.version || 1,
      manufacturingInstructions: formData?.manufacturingInstructions || null,
      estimatedBuildTime: formData?.estimatedBuildTime || null,
      partsRequired: formattedParts
    };
  }, [formData]);

  // Log initialAssemblyData before passing to HierarchicalPartsForm
  useEffect(() => {
    if (isOpen) {
      console.log('[UnifiedAssemblyForm] initialAssemblyData for HierarchicalPartsForm:', JSON.stringify(initialAssemblyData, null, 2));
    }
  }, [initialAssemblyData, isOpen]);

  // Handle save button click
  const handleSave = async () => {
    if (hierarchicalPartsFormRef.current) {
        try {
            // This will call HierarchicalPartsForm.onSubmit, which in turn calls
            // the onFormSubmit prop defined below. The onFormSubmit prop now handles saving.
            await hierarchicalPartsFormRef.current.triggerSubmit();
        } catch (error) {
            // Catch errors from react-hook-form's handleSubmit or from onFormSubmit if it throws
            console.error("Error during form submission trigger in UnifiedAssemblyForm:", error);
            // Toasting for validation errors is usually handled by the form itself or the onFormSubmit callback.
            // If it's a generic error not caught by onFormSubmit's try/catch, a toast here might be appropriate.
            // However, most validation errors should be caught and toasted within onFormSubmit.
            if (!(error instanceof Error && (error.message.includes('Assembly') || error.message.includes('part')))) {
                 toast.error("An unexpected error occurred during submission. Please check form details or console.");
            }
        }
    } else {
      console.warn("HierarchicalPartsForm ref not available, cannot save.");
      toast.error("Cannot save assembly. Form component is missing or not ready.");
    }
  };

  // Handle close with confirmation if form is dirty
  const handleClose = () => {
    if (isDirty) {
      setShowConfirmClose(true);
    } else {
      resetForm();
      onClose();
    }
  };

  // Confirm close and discard changes
  const confirmClose = () => {
    resetForm();
    setShowConfirmClose(false);
    onClose();
  };

  // Cancel close
  const cancelClose = () => {
    setShowConfirmClose(false);
  };

  // Log modal state for debugging
  useEffect(() => {
    console.log('Modal state changed:', { isOpen, assemblyId, mode });
  }, [isOpen, assemblyId, mode]);

  // Add a function to view part details
  const handleViewPart = (part: any) => {
    setSelectedPart(part);
    setShowPartDetails(true);
  };

  // Add a function to edit part
  const handleEditPart = (part: any, index: number) => {
    // Set up quantity editing
    setEditPartQuantity({
      index,
      quantity: part.quantity
    });
    toast.info(`Editing quantity for part ${typeof part.partId === 'object' ? part.partId.name : 'Unknown Part'}`);
  };

  // Save the updated part quantity
  const savePartQuantity = () => {
    if (editPartQuantity) {
      const { index, quantity } = editPartQuantity;

      // Create a copy of the parts array
      const updatedParts = [...(formData.partsRequired || [])];

      // Update the quantity for the specific part - ensure we follow DB schema
      updatedParts[index] = {
        ...updatedParts[index],
        // Only include valid fields as per our schema
        partId: updatedParts[index].partId,
        quantityRequired: quantity,
        unitOfMeasure: updatedParts[index].unitOfMeasure || 'ea'
      };

      // Update form data with the updated parts
      setFormData({
        ...formData,
        partsRequired: updatedParts
      });

      toast.success('Quantity updated');
      setEditPartQuantity(null);
    }
  };

  // Add a function to delete part
  const handleDeletePart = (part: any, index: number) => {
    if (confirm(`Are you sure you want to delete part ${part.name || part.partId}?`)) {
      // Create a copy of form data to modify
      const updatedParts = [...(formData.partsRequired || [])];
      updatedParts.splice(index, 1);

      // Update form data with the part removed
      setFormData({
        ...formData,
        partsRequired: updatedParts
      });

      toast.success(`Part removed from assembly`);
    }
  };

  // Add state for products and assemblies
  const [products, setProducts] = useState<any[]>([]);
  const [assemblies, setAssemblies] = useState<any[]>([]);

  useEffect(() => {
    // Fetch products
    fetch('/api/products?page=1&limit=100')
      .then(res => res.json())
      .then(data => setProducts(data.data || []));
    // Fetch assemblies (exclude current in edit mode)
    fetch('/api/assemblies?page=1&limit=100')
      .then(res => res.json())
      .then(data => setAssemblies(data.data || []));
  }, [assemblyId]);

  if (!isOpen || !isMounted) return null;

  // Modal content to be rendered in the portal
  const modalContent = (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center pointer-events-auto">
          {/* Backdrop with blur effect */}
          <motion.div
            className={`absolute inset-0 backdrop-blur-sm ${theme === 'dark' ? 'bg-black/70' : 'bg-black/50'}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            className="relative z-[9999] w-full max-w-4xl max-h-[90vh] overflow-hidden rounded-xl shadow-2xl pointer-events-auto"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 10 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            <MagicCard className="overflow-hidden p-0 border-0">
              <CardHeader className={cn(
                `border-b border-border flex flex-row items-center justify-between`,
                theme === 'dark'
                  ? "bg-dark-card/90"
                  : "bg-white shadow-sm"
              )}>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <CardTitle className="text-xl font-bold">
                    {mode === 'edit' ? 'Edit Assembly' : 'Create New Assembly'}
                  </CardTitle>
                </motion.div>
                <RippleButton
                  className="rounded-full h-8 w-8 p-0 flex items-center justify-center"
                  onClick={handleClose}
                  rippleColor={theme === 'dark' ? '#ffffff20' : '#00000020'}
                >
                  <X className="h-4 w-4" />
                </RippleButton>
              </CardHeader>

              {isLoading ? (
                <CardContent className={cn(
                  "p-8 flex items-center justify-center",
                  theme === 'dark'
                    ? "bg-dark-card/90"
                    : "bg-white"
                )}>
                  <motion.div
                    className="flex flex-col items-center gap-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    <div className="relative">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <div className="absolute inset-0 h-8 w-8 rounded-full animate-ping bg-primary/20"></div>
                    </div>
                    <p className="text-muted-foreground font-medium">Loading assembly data...</p>
                  </motion.div>
                </CardContent>
              ) : (
                <>
                  <CardContent className={cn(
                    "p-6 overflow-y-auto max-h-[calc(90vh-10rem)]",
                    theme === 'dark'
                      ? "bg-dark-card/95 scrollbar-thin scrollbar-track-dark-card/50"
                      : "bg-white scrollbar-thin scrollbar-track-gray-100/50"
                  )}>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <label className="mb-2 block text-sm font-medium">Assembly Code</label>
                          <Input
                            placeholder="Enter assembly code"
                            value={formData?.assemblyCode || ''}
                            onChange={(e) => updateFormField('assemblyCode', e.target.value)}
                            className="w-full"
                            disabled={isLoading || mode === 'edit'} // Can't edit assembly code in edit mode
                          />
                        </div>

                        <div>
                          <label className="mb-2 block text-sm font-medium">Status</label>
                          <Select
                            value={formData?.status || 'active'}
                            onValueChange={(value) => updateFormField('status', value)}
                            disabled={isLoading}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="pending_review">Pending Review</SelectItem>
                              <SelectItem value="design_phase">Design Phase</SelectItem>
                              <SelectItem value="design_complete">Design Complete</SelectItem>
                              <SelectItem value="obsolete">Obsolete</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <label htmlFor="assemblyName" className="mb-2 block text-sm font-medium">Assembly Name</label>
                          <Input
                            id="assemblyName"
                            placeholder="Enter assembly name"
                            value={formData?.name || ''}
                            onChange={(e) => updateFormField('name', e.target.value)}
                            disabled={isSaving}
                          />
                        </div>
                      </div>
                      <div className="md:col-span-2">
                        <label htmlFor="assemblyDescription" className="mb-2 block text-sm font-medium">Description</label>
                        <Input
                          id="assemblyDescription"
                          placeholder="Enter assembly description"
                          value={formData?.description || ''}
                          onChange={(e) => updateFormField('description', e.target.value)}
                          className="w-full"
                          disabled={isSaving}
                        />
                      </div>

                      {/* New fields based on schema */}
                      <div className="grid gap-4 md:grid-cols-2 mt-4">
                        <div>
                          <label htmlFor="assemblyVersion" className="mb-2 block text-sm font-medium">Version</label>
                          <Input
                            id="assemblyVersion"
                            type="number"
                            placeholder="Enter assembly version"
                            value={formData?.version || 1}
                            onChange={(e) => updateFormField('version', parseInt(e.target.value, 10) || 1)}
                            disabled={isSaving}
                          />
                        </div>
                        <div>
                          <label htmlFor="isTopLevel" className="mb-2 block text-sm font-medium">Is Top Level Assembly?</label>
                          <Select
                            value={formData?.isTopLevel === true ? 'true' : 'false'}
                            onValueChange={(value) => updateFormField('isTopLevel', value === 'true')}
                            disabled={isSaving}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select if top level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="true">Yes</SelectItem>
                              <SelectItem value="false">No (Sub-assembly)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid gap-4 md:grid-cols-2 mt-4">
                        <div>
                          <label htmlFor="productId" className="mb-2 block text-sm font-medium">Product (Optional)</label>
                          <Select
                            value={formData?.productId ? formData.productId : 'none'}
                            onValueChange={val => updateFormField('productId', val === 'none' ? null : val)}
                            disabled={isSaving}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select a product" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {products.map((product) => (
                                <SelectItem key={product._id} value={product._id}>
                                  {product.name} ({product.productCode || product._id})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <label htmlFor="parentId" className="mb-2 block text-sm font-medium">Parent Assembly (Optional)</label>
                          <Select
                            value={formData?.parentId ? formData.parentId : 'none'}
                            onValueChange={val => updateFormField('parentId', val === 'none' ? null : val)}
                            disabled={isSaving || formData?.isTopLevel === true}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select a parent assembly" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {assemblies
                                .filter(a => !assemblyId || a._id !== assemblyId)
                                .map((assembly) => (
                                  <SelectItem key={assembly._id} value={assembly._id}>
                                    {assembly.name} ({assembly.assemblyCode || assembly._id})
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="mt-4">
                        <label htmlFor="manufacturingInstructions" className="mb-2 block text-sm font-medium">Manufacturing Instructions (Optional)</label>
                        <Input
                          id="manufacturingInstructions"
                          placeholder="Enter manufacturing instructions or link to SOP"
                          value={formData?.manufacturingInstructions || ''}
                          onChange={(e) => updateFormField('manufacturingInstructions', e.target.value)}
                          disabled={isSaving}
                        />
                      </div>

                      <div className="mt-4">
                        <label htmlFor="estimatedBuildTime" className="mb-2 block text-sm font-medium">Estimated Build Time (Optional)</label>
                        <Input
                          id="estimatedBuildTime"
                          placeholder="e.g., 1.5 hours, 30 minutes"
                          value={formData?.estimatedBuildTime || ''}
                          onChange={(e) => updateFormField('estimatedBuildTime', e.target.value)}
                          disabled={isSaving}
                        />
                      </div>

                    </motion.div>

                  <HierarchicalPartsForm
                    ref={hierarchicalPartsFormRef}
                    initialData={initialAssemblyData} // Pass the memoized data here
                    mode={mode}
                    onFormSubmit={async (data) => {
                      // This function is called by HierarchicalPartsForm after its internal validation and submission.
                      // It's assumed that HierarchicalPartsForm has updated the context's formData if necessary.
                      console.log('HierarchicalPartsForm submitted data to UnifiedAssemblyForm:', data);
                      // Now, trigger the saveAssembly from the context, which uses its own formData.
                      const success = await saveAssembly(); 
                      if (success) {
                        toast.success(`Assembly ${mode === 'edit' ? 'updated' : 'created'} successfully`);
                        resetForm();
                        onClose();
                        router.refresh();
                      } else {
                        // Error handling is likely within saveAssembly or HierarchicalPartsForm itself
                        console.error('Failed to save assembly via context from UnifiedAssemblyForm');
                      }
                    }}
                  />
                  </CardContent>

                  <CardFooter className={cn(
                    "flex justify-end gap-3 p-4 border-t border-border",
                    theme === 'dark'
                      ? "bg-dark-card/90"
                      : "bg-gray-50"
                  )}>
                    <RippleButton
                      onClick={handleClose}
                      className={cn(
                        "px-4 py-2 rounded-md",
                        theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 text-gray-200' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                      )}
                      rippleColor={theme === 'dark' ? '#ffffff20' : '#00000020'}
                    >
                      Cancel
                    </RippleButton>
                    <ShimmerButton
                      onClick={handleSave}
                      disabled={isSaving || isLoading}
                      shimmerColor={theme === 'dark' ? '#4ade8030' : '#22c55e60'}
                      background={theme === 'dark' ? 'rgba(34, 197, 94, 0.8)' : 'rgba(34, 197, 94, 1)'}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Assembly
                        </>
                      )}
                    </ShimmerButton>
                  </CardFooter>

                  {/* Visual separator between form sections */}
                  <div className="relative">
                    <div className="absolute inset-x-0 h-px bg-gradient-to-r from-transparent via-gray-400/50 dark:via-gray-600/50 to-transparent"></div>
                  </div>

                  {/* Part Search Modal (existing code needed by HierarchicalPartsForm potentially - keep for now, but maybe move logic) */}
                  {showPartSearch && (
                    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center">
                      {/* ... PartSearch Modal Content ... */}
                    </div>
                  )}

                  {/* Part Details Modal (existing code - keep for now) */}
                  {showPartDetails && selectedPart && (
                    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center">
                      {/* ... Part Details Modal Content ... */}
                    </div>
                  )}
                </>
              )}
            </MagicCard>
          </motion.div>


          {/* Confirmation Dialog */}
          <AnimatePresence>
            {showConfirmClose && (
              <motion.div
                className="fixed inset-0 z-[10000] flex items-center justify-center pointer-events-auto"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <motion.div
                  className={`absolute inset-0 backdrop-blur-sm ${theme === 'dark' ? 'bg-black/80' : 'bg-black/60'}`}
                  onClick={cancelClose}
                />
                <motion.div
                  className="relative z-[10000] max-w-md w-full mx-4 pointer-events-auto"
                  initial={{ scale: 0.9, opacity: 0, y: 20 }}
                  animate={{ scale: 1, opacity: 1, y: 0 }}
                  exit={{ scale: 0.9, opacity: 0, y: 10 }}
                  transition={{ type: 'spring', damping: 25, stiffness: 300 }}
                >
                  <BorderBeam
                    className={`${theme === 'dark' ? 'bg-gray-900' : 'bg-white'} rounded-xl shadow-xl p-0`}
                    beamColor={theme === 'dark' ? '#ff555580' : '#ff555560'}
                    beamDuration="4s"
                    beamOpacity="0.6"
                    beamSpread="180deg"
                    background={theme === 'dark' ? 'rgba(17, 24, 39, 0.8)' : 'rgba(255, 255, 255, 0.9)'}
                  >
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-2">Discard changes?</h3>
                      <p className="text-muted-foreground mb-4">
                        You have unsaved changes that will be lost if you close this form.
                      </p>
                      <div className="flex justify-end gap-2">
                        <RippleButton
                          onClick={cancelClose}
                          className={`border ${theme === 'dark' ? 'border-gray-700 text-gray-300 hover:bg-gray-800' : 'border-gray-200 hover:bg-gray-100'} px-4 py-2 rounded-md`}
                          rippleColor={theme === 'dark' ? '#ffffff20' : '#00000020'}
                        >
                          Continue Editing
                        </RippleButton>
                        <ShimmerButton
                          onClick={confirmClose}
                          shimmerColor={theme === 'dark' ? '#ff555530' : '#ff555560'}
                          background={theme === 'dark' ? 'rgba(220, 38, 38, 0.8)' : 'rgba(220, 38, 38, 1)'}
                        >
                          Discard Changes
                        </ShimmerButton>
                      </div>
                    </div>
                  </BorderBeam>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </AnimatePresence>
  );

  // Render the modal content in the portal
  // Only create portal if component is mounted and portal container exists
  return (isMounted && portalRef.current) ? createPortal(modalContent, portalRef.current) : null;
}