'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@/app/context/ThemeContext';
import { cn } from '@/app/lib/utils';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';

// Dynamically import the UnifiedAssemblyForm component
const DynamicUnifiedAssemblyForm = dynamic(
  () => import('./UnifiedAssemblyForm').then(mod => ({ default: mod.UnifiedAssemblyForm })),
  {
    loading: () => <LoadingFallback />,
    ssr: false,
  }
);

// Loading fallback component
function LoadingFallback() {
  const { theme } = useTheme();
  
  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center",
      theme === 'dark' ? 'bg-black/70' : 'bg-black/50'
    )}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={cn(
          "rounded-lg p-8 shadow-xl",
          theme === 'dark' ? 'bg-gray-900' : 'bg-white'
        )}
      >
        <div className="flex flex-col items-center gap-4">
          <div className="relative">
            <Loader2 className="h-10 w-10 animate-spin text-primary" />
            <div className="absolute inset-0 h-10 w-10 rounded-full animate-ping bg-primary/20"></div>
          </div>
          <p className="text-lg font-medium">Loading Assembly Form...</p>
        </div>
      </motion.div>
    </div>
  );
}

// Props interface
interface LazyUnifiedAssemblyFormProps {
  isOpen: boolean;
  onClose: () => void;
  assemblyId?: string;
  mode?: 'create' | 'edit';
}

/**
 * Lazy-loaded wrapper for UnifiedAssemblyForm
 * This component dynamically imports the actual form component only when needed
 */
export function LazyUnifiedAssemblyForm({ isOpen, onClose, assemblyId, mode = 'create' }: LazyUnifiedAssemblyFormProps) {
  // Only render the form when isOpen is true
  if (!isOpen) return null;
  
  return (
    <AssemblyFormProvider assemblyId={assemblyId}>
      <DynamicUnifiedAssemblyForm
        isOpen={isOpen}
        onClose={onClose}
        assemblyId={assemblyId}
        mode={mode}
      />
    </AssemblyFormProvider>
  );
}
