'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { Loader2, AlertCircle, Info } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Textarea } from '@/app/components/ui/textarea';
import { Label } from '@/app/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/app/components/ui/Form/Form';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/app/components/ui/tooltip';
import { Badge } from '@/app/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Card, CardContent } from '@/app/components/ui/card';
import { useTheme } from '@/app/context/ThemeContext';

// Define batch statuses
export const BATCH_STATUSES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  ON_HOLD: 'on_hold',
  QUALITY_CHECK: 'quality_check'
};

// Define valid status transitions
export const VALID_STATUS_TRANSITIONS = {
  [BATCH_STATUSES.PENDING]: [BATCH_STATUSES.IN_PROGRESS, BATCH_STATUSES.CANCELLED, BATCH_STATUSES.ON_HOLD],
  [BATCH_STATUSES.IN_PROGRESS]: [BATCH_STATUSES.QUALITY_CHECK, BATCH_STATUSES.ON_HOLD, BATCH_STATUSES.CANCELLED],
  [BATCH_STATUSES.QUALITY_CHECK]: [BATCH_STATUSES.COMPLETED, BATCH_STATUSES.IN_PROGRESS, BATCH_STATUSES.ON_HOLD],
  [BATCH_STATUSES.ON_HOLD]: [BATCH_STATUSES.IN_PROGRESS, BATCH_STATUSES.CANCELLED],
  [BATCH_STATUSES.COMPLETED]: [], // Terminal state, no transitions allowed
  [BATCH_STATUSES.CANCELLED]: [] // Terminal state, no transitions allowed
};

// Define interfaces for data
interface Part {
  _id: string;
  name: string;
  description?: string;
}

interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
}

interface WorkOrder {
  _id: string;
  woNumber: string;
  status: string;
}

interface Batch {
  _id: string;
  batchCode: string;
  partId?: string | Part;
  assemblyId?: string | Assembly;
  quantityPlanned: number;
  quantityProduced?: number;
  startDate: string | Date;
  endDate?: string | Date;
  status: string;
  notes?: string;
  workOrderId: string | WorkOrder;
  createdAt: string | Date;
  updatedAt: string | Date;
}

// Define form schema with enhanced validation
const batchFormSchema = z.object({
  batchCode: z.string().optional(),
  partId: z.string().optional(),
  assemblyId: z.string().optional(),
  quantityPlanned: z.number().min(1, "Quantity must be at least 1"),
  quantityProduced: z.number().min(0, "Quantity produced cannot be negative").optional(),
  startDate: z.string().refine(date => {
    try {
      return !isNaN(new Date(date).getTime());
    } catch {
      return false;
    }
  }, { message: "Invalid start date" }),
  endDate: z.string().optional().refine(date => {
    if (!date) return true;
    try {
      return !isNaN(new Date(date).getTime());
    } catch {
      return false;
    }
  }, { message: "Invalid end date" }),
  status: z.string().refine(status => 
    Object.values(BATCH_STATUSES).includes(status as any), 
    { message: "Invalid status" }
  ),
  notes: z.string().optional(),
  workOrderId: z.string().min(1, "Work order is required")
}).refine(data => {
  // Either partId or assemblyId must be provided, but not both
  return (!!data.partId && !data.assemblyId) || (!data.partId && !!data.assemblyId);
}, {
  message: "Either a part or an assembly must be selected, but not both",
  path: ["partId"]
}).refine(data => {
  // If endDate is provided, it must be after startDate
  if (data.endDate && data.startDate) {
    return new Date(data.endDate) >= new Date(data.startDate);
  }
  return true;
}, {
  message: "End date must be after start date",
  path: ["endDate"]
}).refine(data => {
  // If status is completed, endDate must be provided
  if (data.status === BATCH_STATUSES.COMPLETED && !data.endDate) {
    return false;
  }
  return true;
}, {
  message: "End date is required for completed batches",
  path: ["endDate"]
});

interface BatchFormProps {
  initialData?: Batch | null;
  workOrders: WorkOrder[];
  parts: Part[];
  assemblies: Assembly[];
  onSubmit: (data: z.infer<typeof batchFormSchema>) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export default function BatchFormClient({
  initialData,
  workOrders,
  parts,
  assemblies,
  onSubmit,
  onCancel,
  isSubmitting = false
}: BatchFormProps) {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<string>("part");
  const [currentStatus, setCurrentStatus] = useState<string>(initialData?.status || BATCH_STATUSES.PENDING);
  const isEditing = !!initialData;

  // Initialize form with default values or existing batch data
  const form = useForm<z.infer<typeof batchFormSchema>>({
    resolver: zodResolver(batchFormSchema),
    defaultValues: {
      batchCode: initialData?.batchCode || '',
      partId: typeof initialData?.partId === 'string' 
        ? initialData.partId 
        : initialData?.partId?._id || '',
      assemblyId: typeof initialData?.assemblyId === 'string' 
        ? initialData.assemblyId 
        : initialData?.assemblyId?._id || '',
      quantityPlanned: initialData?.quantityPlanned || 1,
      quantityProduced: initialData?.quantityProduced || 0,
      startDate: initialData?.startDate 
        ? format(new Date(initialData.startDate), 'yyyy-MM-dd')
        : format(new Date(), 'yyyy-MM-dd'),
      endDate: initialData?.endDate 
        ? format(new Date(initialData.endDate), 'yyyy-MM-dd')
        : undefined,
      status: initialData?.status || BATCH_STATUSES.PENDING,
      notes: initialData?.notes || '',
      workOrderId: typeof initialData?.workOrderId === 'string' 
        ? initialData.workOrderId 
        : initialData?.workOrderId?._id || ''
    }
  });

  // Set active tab based on initial data
  useEffect(() => {
    if (initialData) {
      if (initialData.partId) {
        setActiveTab("part");
      } else if (initialData.assemblyId) {
        setActiveTab("assembly");
      }
    }
  }, [initialData]);

  // Watch for status changes to update available transitions
  const watchedStatus = form.watch("status");
  useEffect(() => {
    setCurrentStatus(watchedStatus);
  }, [watchedStatus]);

  // Get valid status transitions for the current status
  const getValidStatusTransitions = (currentStatus: string) => {
    if (!isEditing) {
      // For new batches, only allow pending status
      return [BATCH_STATUSES.PENDING];
    }
    
    // For existing batches, get valid transitions based on current status
    return [
      currentStatus, // Always include current status
      ...(VALID_STATUS_TRANSITIONS[currentStatus as keyof typeof VALID_STATUS_TRANSITIONS] || [])
    ];
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    
    // Clear the other field when switching tabs
    if (value === "part") {
      form.setValue("assemblyId", "");
    } else {
      form.setValue("partId", "");
    }
  };

  // Handle form submission
  const handleSubmit = async (data: z.infer<typeof batchFormSchema>) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Error submitting batch form:", error);
      toast.error("Failed to save batch");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Batch Code */}
          <FormField
            control={form.control}
            name="batchCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Batch Code</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    placeholder="Auto-generated if empty" 
                    disabled={isEditing}
                  />
                </FormControl>
                <FormDescription>
                  A unique identifier for this batch
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Work Order */}
          <FormField
            control={form.control}
            name="workOrderId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Work Order</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isEditing}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a work order" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {workOrders.map((wo) => (
                      <SelectItem key={wo._id} value={wo._id}>
                        {wo.woNumber}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  The work order this batch is associated with
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Part or Assembly Selection */}
        <Tabs value={activeTab} onValueChange={handleTabChange} disabled={isEditing}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="part">Part</TabsTrigger>
            <TabsTrigger value="assembly">Assembly</TabsTrigger>
          </TabsList>
          <TabsContent value="part">
            <Card>
              <CardContent className="pt-6">
                <FormField
                  control={form.control}
                  name="partId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Part</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isEditing}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a part" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {parts.map((part) => (
                            <SelectItem key={part._id} value={part._id}>
                              {part.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The part to be manufactured in this batch
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="assembly">
            <Card>
              <CardContent className="pt-6">
                <FormField
                  control={form.control}
                  name="assemblyId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assembly</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isEditing}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an assembly" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {assemblies.map((assembly) => (
                            <SelectItem key={assembly._id} value={assembly._id}>
                              {assembly.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The assembly to be built in this batch
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Quantity Planned */}
          <FormField
            control={form.control}
            name="quantityPlanned"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity Planned</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    min={1}
                    disabled={isEditing}
                  />
                </FormControl>
                <FormDescription>
                  The target quantity for this batch
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Quantity Produced */}
          <FormField
            control={form.control}
            name="quantityProduced"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity Produced</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    min={0}
                  />
                </FormControl>
                <FormDescription>
                  The actual quantity produced so far
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Start Date */}
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Start Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormDescription>
                  When the batch production started
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* End Date */}
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>End Date</FormLabel>
                <FormControl>
                  <Input 
                    type="date" 
                    {...field} 
                    value={field.value || ''} 
                  />
                </FormControl>
                <FormDescription>
                  When the batch production was completed
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Status */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {getValidStatusTransitions(currentStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription className="flex items-center">
                <Info className="h-4 w-4 mr-1" />
                Status transitions are restricted based on the current status
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Notes */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea 
                  {...field} 
                  placeholder="Add any additional information about this batch"
                  rows={4}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              isEditing ? 'Update Batch' : 'Create Batch'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
