"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';

interface ProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (product: any) => void;
  initialProduct?: any; // Optional existing product for editing
}

const ProductModal: React.FC<ProductModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialProduct,
}) => {
  // Initialize state with existing product or empty values
  const [product, setProduct] = useState({
    productCode: initialProduct?.productCode || '',
    name: initialProduct?.name || '',
    description: initialProduct?.description || '',
    categoryId: initialProduct?.categoryId || '',
    status: initialProduct?.status || 'active',
    sellingPrice: initialProduct?.sellingPrice || 0,
    assemblyId: initialProduct?.assemblyId || '',
    partId: initialProduct?.partId || '',
  });

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setProduct((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(product);
    onClose();
  };

  // If not open, return null
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-md relative"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
        >
          <X size={24} />
        </button>

        {/* Modal Title */}
        <h2 className="text-2xl font-bold mb-6 text-center">
          {initialProduct ? 'Edit Product' : 'Add New Product'}
        </h2>

        {/* Product Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Name
            </label>
            <input
              type="text"
              name="name"
              value={product.name}
              onChange={handleChange}
              required
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
              placeholder="Enter product name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <input
              type="text"
              name="description"
              value={product.description}
              onChange={handleChange}
              required
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
              placeholder="Enter product description"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category ID
            </label>
            <input
              type="text"
              name="categoryId"
              value={product.categoryId}
              onChange={handleChange}
              required
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
              placeholder="Enter category ID"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <input
              type="text"
              name="status"
              value={product.status}
              onChange={handleChange}
              required
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
              placeholder="Enter status"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Selling Price
            </label>
            <input
              type="number"
              name="sellingPrice"
              value={product.sellingPrice}
              onChange={handleChange}
              required
              min="0"
              step="0.01"
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assembly ID
            </label>
            <input
              type="text"
              name="assemblyId"
              value={product.assemblyId}
              onChange={handleChange}
              required
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
              placeholder="Enter assembly ID"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Part ID
            </label>
            <input
              type="text"
              name="partId"
              value={product.partId}
              onChange={handleChange}
              required
              className="w-full bg-gray-100 border border-gray-200 rounded-lg px-3 py-2"
              placeholder="Enter part ID"
            />
          </div>

          {/* Submit Button */}
          <div className="mt-6">
            <button
              type="submit"
              className="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-800 font-bold py-2 px-4 rounded-full transition duration-300 ease-in-out transform hover:scale-105"
            >
              {initialProduct ? 'Update Product' : 'Add Product'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default ProductModal; 