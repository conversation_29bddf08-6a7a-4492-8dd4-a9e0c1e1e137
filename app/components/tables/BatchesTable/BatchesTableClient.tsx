"use client";

import React, { useState, useEffect, useMemo } from "react";
import { format } from "date-fns";
import {
  Eye,
  Pencil,
  Trash2,
  MoreHorizontal,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Layers
} from "lucide-react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import { Batch, BatchesTableProps } from "./types";
import {
  EnhancedTable,
  ColumnDef,
  SortState,
  PaginationState,
  FilterState
} from "@/app/components/ui/enhanced-table";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import Link from "next/link";

/**
 * Status badge component for batch status
 */
function StatusBadge({ status }: { status: string }) {
  switch (status.toLowerCase()) {
    case "pending":
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-500 dark:border-yellow-800">Pending</Badge>;
    case "in_progress":
    case "in progress":
      return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-500 dark:border-blue-800">In Progress</Badge>;
    case "completed":
      return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-500 dark:border-green-800">Completed</Badge>;
    case "cancelled":
      return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-500 dark:border-red-800">Cancelled</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
}

/**
 * BatchesTableClient component
 * Client component for displaying batches in a table
 */
export default function BatchesTableClient({
  simple = false,
  className,
  initialData,
  fetchData = true,
  woNumber,
  onBatchClick,
  onBatchEdit,
  onBatchDelete,
  onBatchCreate
}: BatchesTableProps) {
  const { theme } = useTheme();
  const [batches, setBatches] = useState<Batch[]>(initialData || []);
  const [isLoading, setIsLoading] = useState<boolean>(fetchData && !initialData);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);

  // Pagination state
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex: 1,
    pageSize: 10
  });

  // Sort state
  const [sortState, setSortState] = useState<SortState>({
    id: "createdAt",
    direction: "desc"
  });

  // Filter state
  const [filterState, setFilterState] = useState<FilterState[]>([]);

  // Helper function to get the name of the item (part or assembly)
  const getItemName = (batch: Batch): string => {
    if (batch.partId) {
      return typeof batch.partId === 'string' 
        ? 'Unknown Part' 
        : batch.partId.name;
    }
    if (batch.assemblyId) {
      return typeof batch.assemblyId === 'string'
        ? 'Unknown Assembly'
        : batch.assemblyId.name;
    }
    return 'N/A';
  };

  // Helper function to get the work order number
  const getWorkOrderNumber = (batch: Batch): string => {
    if (batch.workOrderId) {
      return typeof batch.workOrderId === 'string'
        ? batch.workOrderId
        : batch.workOrderId.woNumber;
    }
    return 'N/A';
  };

  // Fetch batches from API
  const fetchBatchesData = async () => {
    if (!fetchData) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Construct the API URL with query parameters
      let url = woNumber 
        ? `/api/work-orders/${woNumber}/batches` 
        : '/api/batches';
      
      // Add pagination, sorting, and filtering parameters
      url += `?page=${paginationState.pageIndex}&limit=${paginationState.pageSize}`;
      
      if (sortState) {
        url += `&sort=${sortState.id}&order=${sortState.direction}`;
      }
      
      if (filterState.length > 0) {
        const filterParams = filterState
          .map(filter => `${filter.id}=${encodeURIComponent(filter.value)}`)
          .join('&');
        url += `&${filterParams}`;
      }
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch batches');
      }
      
      setBatches(result.data || []);
      setTotalItems(result.pagination?.totalCount || 0);
    } catch (err: any) {
      console.error('Error fetching batches:', err);
      setError(err.message || 'Failed to fetch batches');
      setBatches([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch batches when pagination, sorting, or filtering changes
  useEffect(() => {
    if (fetchData) {
      fetchBatchesData();
    }
  }, [fetchData, paginationState, sortState, filterState, woNumber]);

  // Update batches when initialData changes
  useEffect(() => {
    if (initialData) {
      setBatches(initialData);
      setTotalItems(initialData.length);
    }
  }, [initialData]);

  // Handle sort change
  const handleSortChange = (newSortState: SortState) => {
    setSortState(newSortState);
  };

  // Handle filter change
  const handleFilterChange = (newFilterState: FilterState[]) => {
    setFilterState(newFilterState);
  };

  // Handle pagination change
  const handlePaginationChange = (newPaginationState: PaginationState) => {
    setPaginationState(newPaginationState);
  };

  // Handle batch click
  const handleBatchClick = (batch: Batch) => {
    if (onBatchClick) {
      onBatchClick(batch);
    }
  };

  // Define table columns
  const columns = useMemo<ColumnDef<Batch>[]>(() => [
    {
      id: "batchCode",
      header: "Batch Code",
      accessorFn: (row) => row.batchCode,
      cell: (value) => <span className="font-medium">{value}</span>,
      sortable: true,
      filterable: true
    },
    {
      id: "itemName",
      header: "Item",
      accessorFn: (row) => getItemName(row),
      cell: (value, row) => (
        <div>
          <div>{value}</div>
          <div className="text-xs text-muted-foreground">
            {row.partId ? 'Part' : row.assemblyId ? 'Assembly' : 'N/A'}
          </div>
        </div>
      ),
      sortable: true,
      filterable: true
    },
    {
      id: "quantity",
      header: "Quantity",
      accessorFn: (row) => row.quantityProduced !== undefined ? `${row.quantityProduced}/${row.quantityPlanned}` : row.quantityPlanned,
      cell: (value) => <span className="font-mono">{value}</span>,
      sortable: true
    },
    {
      id: "status",
      header: "Status",
      accessorFn: (row) => row.status,
      cell: (value) => <StatusBadge status={value} />,
      sortable: true,
      filterable: true
    },
    {
      id: "startDate",
      header: "Start Date",
      accessorFn: (row) => row.startDate,
      cell: (value) => format(new Date(value), "MMM d, yyyy"),
      sortable: true,
      hideOnMobile: true
    },
    {
      id: "endDate",
      header: "End Date",
      accessorFn: (row) => row.endDate,
      cell: (value) => value ? format(new Date(value), "MMM d, yyyy") : "N/A",
      sortable: true,
      hideOnMobile: true
    }
  ], []);

  // Render row actions
  const renderRowActions = (batch: Batch) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            if (onBatchClick) onBatchClick(batch);
          }}
          className="cursor-pointer"
        >
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            if (onBatchEdit) onBatchEdit(batch);
          }}
          className="cursor-pointer"
        >
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation();
            if (onBatchDelete) onBatchDelete(batch);
          }}
          className="cursor-pointer text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // Render table actions
  const renderTableActions = () => (
    <div className="flex items-center space-x-2">
      {onBatchCreate && (
        <Button
          onClick={onBatchCreate}
          size="sm"
          className="bg-primary hover:bg-primary/90"
        >
          Create Batch
        </Button>
      )}
    </div>
  );

  // Render empty state
  const renderEmptyState = () => (
    <div className="p-8 text-center">
      <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
      <h3 className="text-lg font-medium mb-2">No Batches Found</h3>
      <p className="text-sm text-muted-foreground mb-4">
        {woNumber 
          ? `There are no batches associated with work order ${woNumber}.`
          : 'There are no batches in the system yet.'}
      </p>
      {onBatchCreate && (
        <Button onClick={onBatchCreate}>
          Create Batch
        </Button>
      )}
    </div>
  );

  return (
    <div className={className}>
      <EnhancedTable
        data={batches}
        columns={columns}
        enableSorting={true}
        enableFiltering={!simple}
        enablePagination={true}
        initialSortState={sortState}
        initialFilterState={filterState}
        initialPaginationState={paginationState}
        totalItems={totalItems}
        isLoading={isLoading}
        onSortChange={handleSortChange}
        onFilterChange={handleFilterChange}
        onPaginationChange={handlePaginationChange}
        onRowClick={handleBatchClick}
        renderRowActions={renderRowActions}
        renderTableActions={onBatchCreate ? renderTableActions : undefined}
        renderEmptyState={renderEmptyState}
        error={error ? new Error(error) : null}
        compact={simple}
        striped={true}
        highlightOnHover={true}
        bordered={true}
        stickyHeader={true}
        dense={simple}
        hover={true}
        shadow={true}
        rounded={true}
        backdropBlur={true}
      />
    </div>
  );
}
