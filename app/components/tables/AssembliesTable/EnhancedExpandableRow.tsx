'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Layers, AlertCircle, CheckCircle2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/app/components/ui/button';
import { TableCell, TableRow } from '@/app/components/ui/table';
import { Badge } from '@/app/components/ui/badge';
import { cn } from '@/app/lib/utils';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';
import { useTheme } from '@/app/context/ThemeContext';

interface EnhancedExpandableRowProps {
  assembly: Assembly;
  children: React.ReactNode;
  colSpan: number;
}

/**
 * Enhanced expandable row component for the assemblies table with Magic UI effects
 */
export function EnhancedExpandableRow({ assembly, children, colSpan }: EnhancedExpandableRowProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { theme } = useTheme();
  
  // Check if assembly has valid parts
  const hasValidParts = assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0;
  
  return (
    <>
      <TableRow 
        className={cn(
          "cursor-pointer transition-all duration-300 relative overflow-hidden",
          isExpanded ? "bg-muted/50 border-b-0" : "hover:bg-muted/30"
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {children}
      </TableRow>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.tr
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="bg-muted/30 overflow-hidden"
          >
            <TableCell colSpan={colSpan} className="p-0 overflow-hidden">
              <motion.div 
                initial={{ y: -20 }}
                animate={{ y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className={cn(
                  "p-4 border-t border-border/50 relative overflow-hidden",
                  "after:absolute after:inset-0 after:bg-[radial-gradient(1000px_circle_at_80%_20%,rgba(99,102,241,0.1),transparent_40%)] after:opacity-70",
                )}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative z-10">
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <span className={cn(
                        "bg-clip-text text-transparent bg-gradient-to-r",
                        theme === 'dark' 
                          ? "from-indigo-200 to-purple-300" 
                          : "from-indigo-600 to-purple-700"
                      )}>Assembly Details</span>
                    </h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-muted-foreground w-24">ID:</span>
                        <Badge variant="outline" className="font-mono">
                          {assembly._id}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-muted-foreground w-24">Code:</span>
                        <Badge variant="outline">
                          {assembly.assemblyCode || "N/A"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-muted-foreground w-24">Status:</span>
                        <AssemblyStatusBadge assembly={assembly} size="sm" />
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-muted-foreground w-24">Parts:</span>
                        <PartsCountBadge assembly={assembly} size="sm" />
                      </div>
                      {assembly.description && (
                        <div className="flex gap-2">
                          <span className="text-xs font-medium text-muted-foreground w-24">Description:</span>
                          <span className="text-xs">{assembly.description}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <span className={cn(
                        "bg-clip-text text-transparent bg-gradient-to-r",
                        theme === 'dark' 
                          ? "from-indigo-200 to-purple-300" 
                          : "from-indigo-600 to-purple-700"
                      )}>Parts List</span>
                    </h4>
                    {hasValidParts ? (
                      <div className="max-h-40 overflow-y-auto pr-2 rounded-md backdrop-blur-sm bg-background/30 border border-border/50">
                        <table className="w-full text-xs">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left font-medium py-1 px-2">Part Name</th>
                              <th className="text-center font-medium py-1 px-2 w-20">Quantity</th>
                              <th className="text-center font-medium py-1 px-2 w-20">Status</th>
                            </tr>
                          </thead>
                          <tbody>
                            {assembly.partsRequired.map((part, index) => {
                              const partData = typeof part.partId === 'object' ? part.partId : null;
                              const quantity = part.quantity || 1;
                              const hasStock = partData && partData.inventory && partData.inventory.currentStock >= quantity;
                              
                              return (
                                <motion.tr 
                                  key={index} 
                                  className="border-b border-border/50"
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.2, delay: index * 0.05 }}
                                  whileHover={{ backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }}
                                >
                                  {partData ? (
                                    <>
                                      <td className="py-1 px-2">{partData.name}</td>
                                      <td className="py-1 px-2 text-center">{quantity}x</td>
                                      <td className="py-1 px-2 text-center">
                                        {hasStock ? (
                                          <span className="inline-flex items-center text-green-500">
                                            <CheckCircle2 size={12} className="mr-1" />
                                            In Stock
                                          </span>
                                        ) : (
                                          <span className="inline-flex items-center text-red-500">
                                            <AlertCircle size={12} className="mr-1" />
                                            Low Stock
                                          </span>
                                        )}
                                      </td>
                                    </>
                                  ) : (
                                    <>
                                      <td className="py-1 px-2 text-red-500">Missing Part Reference</td>
                                      <td className="py-1 px-2 text-center">{quantity}x</td>
                                      <td className="py-1 px-2 text-center">
                                        <span className="inline-flex items-center text-red-500">
                                          <AlertCircle size={12} className="mr-1" />
                                          Error
                                        </span>
                                      </td>
                                    </>
                                  )}
                                </motion.tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-4 rounded-md backdrop-blur-sm bg-background/30 border border-border/50">
                        <Layers className="h-8 w-8 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">No parts defined</p>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            </TableCell>
          </motion.tr>
        )}
      </AnimatePresence>
    </>
  );
}