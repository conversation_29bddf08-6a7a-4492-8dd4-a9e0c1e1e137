'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Layers, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Button } from '@/app/components/ui/button';
import { TableCell, TableRow } from '@/app/components/ui/table';
import { Badge } from '@/app/components/ui/badge';
import { cn } from '@/app/lib/utils';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';

interface ExpandableRowProps {
  assembly: Assembly;
  children: React.ReactNode;
  colSpan: number;
}

/**
 * Expandable row component for the assemblies table
 */
export function ExpandableRow({ assembly, children, colSpan }: ExpandableRowProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Check if assembly has valid parts
  const hasValidParts = assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0; // Changed from assembly.parts
  
  return (
    <>
      <TableRow 
        className={cn(
          "cursor-pointer hover:bg-muted/50 transition-colors",
          isExpanded && "bg-muted/50 border-b-0"
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {children}
      </TableRow>
      
      {isExpanded && (
        <TableRow className="bg-muted/30">
          <TableCell colSpan={colSpan} className="p-0">
            <div className="p-4 border-t border-border/50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Assembly Details</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">ID:</span>
                      <Badge variant="outline" className="font-mono">
                        {assembly.assemblyCode} {/* Changed from assembly.assembly_id */}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">Stage:</span>
                      <Badge variant="outline">
                        {assembly.assemblyStage || "Standard"} {/* Changed from assembly.assembly_stage */}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">Status:</span>
                      <AssemblyStatusBadge assembly={assembly} size="sm" />
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">Parts:</span>
                      <PartsCountBadge assembly={assembly} size="sm" />
                    </div>
                    {assembly.description && (
                      <div className="flex gap-2">
                        <span className="text-xs font-medium text-muted-foreground w-24">Description:</span>
                        <span className="text-xs">{assembly.description}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Parts List</h4>
                  {hasValidParts ? (
                    <div className="max-h-40 overflow-y-auto pr-2">
                      <table className="w-full text-xs">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left font-medium py-1 px-2">Part Name</th>
                            <th className="text-center font-medium py-1 px-2 w-20">Quantity</th>
                            <th className="text-center font-medium py-1 px-2 w-20">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {assembly.partsRequired.map((part, index) => { // Changed from assembly.parts
                            const partData = part.partId as Part; // Directly use partId, assuming it's populated or string
                            const quantity = part.quantity || 1; // Changed from quantityRequired/quantity_required
                            
                            return (
                              <tr key={index} className="border-b border-border/50">
                                {partData && typeof partData === 'object' ? (
                                  <>
                                    <td className="py-1 px-2">{partData.name}</td>
                                    <td className="py-1 px-2 text-center">{quantity}x</td>
                                    <td className="py-1 px-2 text-center">
                                      {(partData.inventory && partData.inventory.currentStock > 0) ? ( // Check inventory stock
                                        <span className="inline-flex items-center text-green-600">
                                          <CheckCircle2 size={12} className="mr-1" />
                                          Available
                                        </span>
                                      ) : (
                                        <span className="inline-flex items-center text-amber-600">
                                          <AlertCircle size={12} className="mr-1" />
                                          Unavailable
                                        </span>
                                      )}
                                    </td>
                                  </>
                                ) : (
                                  <>
                                    <td className="py-1 px-2 text-red-500">{typeof partData === 'string' ? `Part ID: ${partData}` : 'Missing part data'}</td>
                                    <td className="py-1 px-2 text-center">{quantity}x</td>
                                    <td className="py-1 px-2 text-center">
                                      <span className="inline-flex items-center text-red-600">
                                        <AlertCircle size={12} className="mr-1" />
                                        Invalid
                                      </span>
                                    </td>
                                  </>
                                )}
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-4 text-muted-foreground">
                      <Layers size={24} className="mb-2" />
                      <p className="text-xs">No parts defined</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TableCell>
        </TableRow>
      )}
    </>
  );
}
