import { WorkOrder } from "@/app/types/orders";

export interface WorkOrdersTableProps {
  /**
   * Whether to use a simplified version of the table
   */
  simple?: boolean;
  
  /**
   * Optional className for styling
   */
  className?: string;
  
  /**
   * Optional initial data for the table
   */
  initialData?: WorkOrder[];
  
  /**
   * Whether to fetch data from the API
   */
  fetchData?: boolean;
  
  /**
   * Optional callback for when a work order is clicked
   */
  onWorkOrderClick?: (workOrder: WorkOrder) => void;
  
  /**
   * Optional callback for when a work order is edited
   */
  onWorkOrderEdit?: (workOrder: WorkOrder) => void;
  
  /**
   * Optional callback for when a work order is deleted
   */
  onWorkOrderDelete?: (workOrder: WorkOrder) => void;
  
  /**
   * Optional callback for when a work order is created
   */
  onWorkOrderCreate?: () => void;
}
