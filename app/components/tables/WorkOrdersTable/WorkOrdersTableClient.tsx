"use client";

import React, { useState, useEffect, useMemo } from "react";
import { format } from "date-fns";
import {
  Eye,
  Pencil,
  Trash2,
  MoreHorizontal,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import { WorkOrder } from "@/app/types/orders";
import { User } from "@/app/types/users";
import { WorkOrdersTableProps } from "./types";
import {
  EnhancedTable,
  ColumnDef,
  SortState,
  PaginationState,
  FilterState
} from "@/app/components/ui/enhanced-table";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { confirmAlert } from "react-confirm-alert";
import "react-confirm-alert/src/react-confirm-alert.css";

/**
 * WorkOrdersTableClient component
 * Client component for displaying work orders in a table
 */
export function WorkOrdersTableClient({
  simple = false,
  className,
  initialData,
  fetchData = true,
  onWorkOrderClick,
  onWorkOrderEdit,
  onWorkOrderDelete,
  onWorkOrderCreate
}: WorkOrdersTableProps) {
  const { theme } = useTheme();
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>(initialData || []);
  const [isLoading, setIsLoading] = useState<boolean>(fetchData && !initialData);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex: 1,
    pageSize: 10
  });

  // Sort state
  const [sortState, setSortState] = useState<SortState>({
    sortBy: "createdAt",
    sortDirection: "desc"
  });

  // Filter state
  const [filterState, setFilterState] = useState<FilterState[]>([]);

  // Total items for pagination
  const [totalItems, setTotalItems] = useState<number>(0);

  // Fetch work orders from API
  useEffect(() => {
    if (!fetchData) return;

    const fetchWorkOrders = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append("page", paginationState.pageIndex.toString());
        params.append("limit", paginationState.pageSize.toString());
        params.append("sortField", sortState.sortBy);
        params.append("sortOrder", sortState.sortDirection);

        // Add filters
        filterState.forEach(filter => {
          if (filter.value) {
            params.append(filter.id, filter.value.toString());
          }
        });

        console.log("Fetching work orders with params:", params.toString());

        const response = await fetch("/api/work-orders?" + params.toString(), {
          // Add cache control headers to prevent caching issues
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (!response.ok) {
          throw new Error("Error fetching work orders: " + response.statusText);
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        // Validate the data structure
        if (!Array.isArray(data.data)) {
          console.warn("API returned unexpected data format:", data);
          setWorkOrders([]);
          setTotalItems(0);
        } else {
          console.log("Received work orders:", data.data.length);
          setWorkOrders(data.data || []);
          setTotalItems(data.pagination?.totalCount || 0);
        }
      } catch (err: any) {
        setError(err.message || "Failed to fetch work orders");
        console.error("Error fetching work orders:", err);
        // Set empty data on error to prevent displaying stale data
        setWorkOrders([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkOrders();
  }, [fetchData, paginationState, sortState, filterState]);

  // Handle sort change
  const handleSortChange = (newSortState: SortState) => {
    setSortState(newSortState);
  };

  // Handle pagination change
  const handlePaginationChange = (newPaginationState: PaginationState) => {
    setPaginationState(newPaginationState);
  };

  // Handle filter change
  const handleFilterChange = (newFilterState: FilterState[]) => {
    setFilterState(newFilterState);
  };

  // Handle work order click
  const handleWorkOrderClick = (workOrder: WorkOrder) => {
    try {
      if (onWorkOrderClick && workOrder && workOrder.woNumber) {
        // Make sure we have a valid work order with at least the woNumber property
        onWorkOrderClick(workOrder);
      } else {
        console.warn("Invalid work order or missing callback:", workOrder);
      }
    } catch (err) {
      console.error("Error handling work order click:", err);
      setError("Error viewing work order details. Please try again.");
    }
  };

  // Handle work order edit
  const handleWorkOrderEdit = (workOrder: WorkOrder) => {
    try {
      if (onWorkOrderEdit && workOrder && workOrder.woNumber) {
        // Make sure we have a valid work order with at least the woNumber property
        onWorkOrderEdit(workOrder);
      } else {
        console.warn("Invalid work order or missing callback for edit:", workOrder);
      }
    } catch (err) {
      console.error("Error handling work order edit:", err);
      setError("Error editing work order. Please try again.");
    }
  };

  // Handle work order delete
  const handleWorkOrderDelete = (workOrder: WorkOrder) => {
    try {
      if (!workOrder || !workOrder.woNumber) {
        console.warn("Invalid work order for deletion:", workOrder);
        return;
      }

      confirmAlert({
        title: "Confirm Deletion",
        message: "Are you sure you want to delete work order " + workOrder.woNumber + "?",
        buttons: [
          {
            label: "Yes",
            onClick: async () => {
              try {
                const response = await fetch("/api/work-orders/" + workOrder.woNumber, {
                  method: "DELETE"
                });

                if (!response.ok) {
                  throw new Error("Error deleting work order: " + response.statusText);
                }

                const data = await response.json();

                if (data.error) {
                  throw new Error(data.error);
                }

                // Remove the deleted work order from the state
                setWorkOrders(workOrders.filter(wo => wo.woNumber !== workOrder.woNumber));

                // Call the callback if provided
                if (onWorkOrderDelete) {
                  onWorkOrderDelete(workOrder);
                }
              } catch (err: any) {
                setError(err.message || "Failed to delete work order");
                console.error("Error deleting work order:", err);
              }
            }
          },
          {
            label: "No",
            onClick: () => {}
          }
        ]
      });
    } catch (err) {
      console.error("Error handling work order delete:", err);
      setError("Error preparing to delete work order. Please try again.");
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    let variant: "default" | "secondary" | "destructive" | "outline" | "success" | "warning" = "default";
    let icon = null;

    // Ensure status is a valid string
    const safeStatus = typeof status === 'string' ? status : 'pending';

    switch (safeStatus) {
      case "pending":
        variant = "warning";
        icon = <Clock className="h-3 w-3 mr-1" />;
        break;
      case "in_progress":
        variant = "default";
        icon = <AlertCircle className="h-3 w-3 mr-1" />;
        break;
      case "completed":
        variant = "success";
        icon = <CheckCircle className="h-3 w-3 mr-1" />;
        break;
      case "on_hold":
        variant = "secondary";
        icon = <AlertTriangle className="h-3 w-3 mr-1" />;
        break;
      case "cancelled":
        variant = "destructive";
        icon = <XCircle className="h-3 w-3 mr-1" />;
        break;
      default:
        variant = "outline";
        icon = <AlertCircle className="h-3 w-3 mr-1" />;
        console.warn(`Unknown status value: ${safeStatus}`);
    }

    return (
      <Badge variant={variant} className="flex items-center">
        {icon}
        <span className="capitalize">{safeStatus.replace(/_/g, " ")}</span>
      </Badge>
    );
  };

  // Priority badge component
  const PriorityBadge = ({ priority }: { priority: string }) => {
    let variant: "default" | "secondary" | "destructive" | "outline" | "success" | "warning" = "default";

    // Ensure priority is a valid string
    const safePriority = typeof priority === 'string' ? priority : 'medium';

    switch (safePriority) {
      case "low":
        variant = "success";
        break;
      case "medium":
        variant = "warning";
        break;
      case "high":
        variant = "destructive";
        break;
      default:
        variant = "outline";
        console.warn(`Unknown priority value: ${safePriority}`);
    }

    return (
      <Badge variant={variant} className="capitalize">
        {safePriority}
      </Badge>
    );
  };

  // Define table columns
  const columns = useMemo<ColumnDef<WorkOrder>[]>(() => [
    {
      id: "woNumber",
      header: "WO Number",
      accessorFn: (row) => row.woNumber || "",
      cell: (value) => <span className="font-medium">{value}</span>,
      sortable: true,
      filterable: true
    },
    {
      id: "status",
      header: "Status",
      accessorFn: (row) => row.status || "pending",
      cell: (value) => <StatusBadge status={value} />,
      sortable: true,
      filterable: true
    },
    {
      id: "priority",
      header: "Priority",
      accessorFn: (row) => row.priority || "medium",
      cell: (value) => <PriorityBadge priority={value} />,
      sortable: true,
      filterable: true
    },
    {
      id: "quantity",
      header: "Quantity",
      accessorFn: (row) => row.quantity || 0,
      sortable: true
    },
    {
      id: "assignedTo",
      header: "Assigned To",
      accessorFn: (row) => row.assignee?.fullName || row.assignedTo || "Unassigned",
      sortable: true,
      filterable: true,
      hideOnMobile: !simple
    },
    {
      id: "dueDate",
      header: "Due Date",
      accessorFn: (row) => row.dueDate,
      cell: (value) => {
        if (!value) return "N/A";
        try {
          return format(new Date(value), "MMM d, yyyy");
        } catch (err) {
          console.error("Error formatting date:", err);
          return "Invalid Date";
        }
      },
      sortable: true,
      hideOnMobile: true
    },
    {
      id: "createdAt",
      header: "Created",
      accessorFn: (row) => row.createdAt,
      cell: (value) => {
        if (!value) return "N/A";
        try {
          return format(new Date(value), "MMM d, yyyy");
        } catch (err) {
          console.error("Error formatting date:", err);
          return "Invalid Date";
        }
      },
      sortable: true,
      hideOnMobile: true
    }
  ], []);

  // Render row actions
  const renderRowActions = (row: WorkOrder) => (
    <div className="flex justify-end space-x-1">
      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          e.stopPropagation();
          handleWorkOrderClick(row);
        }}
        title="View"
      >
        <Eye className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          e.stopPropagation();
          handleWorkOrderEdit(row);
        }}
        title="Edit"
      >
        <Pencil className="h-4 w-4" />
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => e.stopPropagation()}
            title="More options"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              handleWorkOrderDelete(row);
            }}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  return (
    <div className={className}>
      <EnhancedTable
        data={workOrders}
        columns={columns}
        enableSorting={true}
        enableFiltering={!simple}
        enablePagination={true}
        initialSortState={sortState}
        initialFilterState={filterState}
        initialPaginationState={paginationState}
        totalItems={totalItems}
        isLoading={isLoading}
        onSortChange={handleSortChange}
        onFilterChange={handleFilterChange}
        onPaginationChange={handlePaginationChange}
        onRowClick={handleWorkOrderClick}
        renderRowActions={renderRowActions}
        error={error ? new Error(error) : null}
        renderErrorState={(err) => (
          <div className="p-4 text-center">
            <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
            <p className="text-destructive font-medium">Error loading work orders</p>
            <p className="text-muted-foreground text-sm mt-1">{err.message}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={() => {
                setError(null);
                setPaginationState({ pageIndex: 1, pageSize: 10 });
              }}
            >
              Try Again
            </Button>
          </div>
        )}
        renderEmptyState={() => (
          <div className="p-8 text-center">
            <Clipboard className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">No work orders found</p>
            {onWorkOrderCreate && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={onWorkOrderCreate}
              >
                Create Work Order
              </Button>
            )}
          </div>
        )}
        compact={simple}
        striped={true}
        highlightOnHover={true}
        bordered={true}
        stickyHeader={true}
        dense={simple}
        hover={true}
        shadow={true}
        rounded={true}
        backdropBlur={true}
      />

      {error && (
        <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-md text-destructive">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <span>{error}</span>
          </div>
        </div>
      )}
    </div>
  );
}
