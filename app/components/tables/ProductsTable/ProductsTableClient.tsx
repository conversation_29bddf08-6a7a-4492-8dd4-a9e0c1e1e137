"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { MoreHorizontal, PencilIcon, Eye, Trash2 } from "lucide-react";
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';
import { cn } from "@/app/lib/utils";
import { useTheme } from '@/app/context/ThemeContext';

// Import part action components
import { ViewPartAction, EditPartAction, DeletePartAction } from '@/app/components/actions/parts';

import { ProductsTableProps, Product } from './types';

/**
 * Client component implementation of ProductsTable
 * Handles all interactive logic and rendering
 */
export default function ProductsTableClient({ products, simple = false, onDeletePart }: ProductsTableProps) {
  const router = useRouter();
  const { theme } = useTheme();
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});

  /**
   * Handle refresh after actions
   */
  const handleRefresh = () => {
    router.refresh();
  };

  const handleDelete = async (product: Product) => {
    confirmAlert({
      title: "Confirm Deletion",
      message: `Are you sure you want to delete product "${product.name}" (${product.product_id})?`,
      buttons: [
        {
          label: 'Yes',
          onClick: async () => {
            try {
              setIsDeleting(prev => ({ ...prev, [product._id]: true }));

              const response = await fetch(`/api/products/${product._id}`, {
                method: 'DELETE',
              });

              if (response.ok) {
                toast.success(`Product ${product.product_id} deleted successfully`);
                router.refresh();
              } else {
                const data = await response.json();
                throw new Error(data.error || "Failed to delete product");
              }
            } catch (error) {
              toast.error(error instanceof Error ? error.message : "An error occurred while deleting the product");
              console.error("Delete error:", error);
            } finally {
              setIsDeleting(prev => ({ ...prev, [product._id]: false }));
            }
          }
        },
        {
          label: 'No',
          onClick: () => {}
        }
      ]
    });
  };

  if (products.length === 0) {
    return null;
  }

  return (
    <div className="relative rounded-lg overflow-hidden">
      {/* Grid pattern background for enhanced styling */}
      <div className="absolute inset-0 pointer-events-none opacity-[0.03] dark:opacity-[0.07]">
        <div className="absolute inset-0 bg-grid-pattern-gray-400/30 [mask-image:linear-gradient(to_bottom,white,transparent)] bg-[length:20px_20px]" />
      </div>

      <div className={cn(
        "rounded-lg border backdrop-blur-[2px]",
        theme === 'light'
          ? "bg-white border-gray-200 shadow-sm"
          : "bg-[var(--T-bg-card)] border-border"
      )}>
        <Table>
          <TableHeader>
            <TableRow className={cn(
              theme === 'light'
                ? "bg-gray-50/80 border-gray-200"
                : "bg-[var(--T-bg-sidebar)] border-[var(--T-border-color)]"
            )}>
              <TableHead>Name</TableHead>
              {simple ? (
                <>
                  <TableHead>ID</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Reorder Level</TableHead>
                  <TableHead className="w-[120px] text-right">Actions</TableHead>
                </>
              ) : (
                <>
                  <TableHead>Product ID</TableHead>
                  <TableHead>Main Assembly</TableHead>
                  <TableHead>Components</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody className={cn(
            theme === 'light' ? "bg-white" : ""
          )}>
          {products.map((product) => (
            <TableRow
              key={product._id || product.id}
              className={cn(
                theme === 'light'
                  ? "hover:bg-gray-50 border-gray-200"
                  : "hover:bg-[var(--dark-hover)] border-[var(--T-border-subtle)]"
              )}>
              <TableCell className={cn("font-medium", theme === 'light' ? "text-gray-900" : "")}>
                {simple ? (
                  product.name
                ) : (
                  <Link href={`/products/${product._id}`} className="hover:underline">
                    {product.name}
                  </Link>
                )}
              </TableCell>

              {simple ? (
                <>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>
                    <Badge variant="outline" className="font-mono">
                      {product.id || product.product_id}
                    </Badge>
                  </TableCell>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>{product.currentStock ?? '—'}</TableCell>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>{product.supplierManufacturer || '—'}</TableCell>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>{product.reorderLevel ?? '—'}</TableCell>
                  <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center justify-end gap-1">
                      <ViewPartAction
                        partId={product._id || product.id || ''} // Use _id if available, otherwise fall back to id
                        variant="icon"
                        size="sm"
                        id={`view-part-${product._id || product.id || ''}`}
                      />

                      <EditPartAction
                        partId={product._id || product.id || ''} // Use _id if available, otherwise fall back to id
                        variant="icon"
                        size="sm"
                        onSuccess={handleRefresh}
                        id={`edit-part-${product._id || product.id || ''}`}
                      />

                      <DeletePartAction
                        partId={product._id || product.id || ''} // Use _id if available, otherwise fall back to id
                        partName={product.name}
                        variant="icon"
                        size="sm"
                        onSuccess={() => {
                          // Call the onDeletePart callback if provided
                          if (onDeletePart) {
                            // Use _id if available, otherwise fall back to id
                            const idToDelete = product._id || product.id || '';
                            console.log('[ProductsTableClient] Calling onDeletePart with ID:', idToDelete);
                            onDeletePart(idToDelete).catch(error => {
                              console.error('Error in onDeletePart callback:', error);
                            });
                          }
                          // Always refresh the UI
                          handleRefresh();
                        }}
                        id={`delete-part-${product._id || product.id || ''}`}
                      />
                    </div>
                  </TableCell>
                </>
              ) : (
                <>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>
                    <Badge variant="outline" className="font-mono">
                      {product.product_id}
                    </Badge>
                  </TableCell>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>
                    {product.main_assembly_id ? (
                      <div className="flex flex-col">
                        <span>{product.main_assembly_id.name}</span>
                        <span className={cn("text-xs", theme === 'light' ? "text-gray-500" : "text-muted-foreground")}>
                          {product.main_assembly_id.part_id}
                        </span>
                      </div>
                    ) : (
                      <span className={theme === 'light' ? "text-gray-500" : "text-muted-foreground"}>—</span>
                    )}
                  </TableCell>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>
                    <Badge>
                      {product.components?.length || 0} parts
                    </Badge>
                  </TableCell>
                  <TableCell className={theme === 'light' ? "text-gray-900" : ""}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" disabled={isDeleting[product._id || '']}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href={`/products/${product._id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/products/${product._id}/edit`}>
                            <PencilIcon className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive"
                          onClick={() => handleDelete(product)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
    </div>
  );
}
