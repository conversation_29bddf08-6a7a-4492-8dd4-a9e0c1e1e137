/**
 * Type definitions for ProductsTable component
 */

/**
 * Interface for Product data in the table
 */
export interface Product {
  _id: string;
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  assemblyId?: string | null;
  partId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Props for the ProductsTable component
 */
export interface ProductsTableProps {
  products: Product[];
  simple?: boolean; // For simple mode without dropdown actions
  onDeletePart?: (id: string) => Promise<void>; // Optional callback for when a part is deleted
}