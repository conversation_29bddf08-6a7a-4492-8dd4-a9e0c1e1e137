'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Layers } from 'lucide-react';

import { Button } from '@/app/components/ui/button';
import Link from 'next/link';
import { ImprovedAssemblyCard } from '@/app/components/cards/ImprovedAssemblyCard';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';

interface AssembliesGridProps {
  assemblies: Assembly[];
}

/**
 * Grid view for assemblies
 */
export function AssembliesGrid({ assemblies }: AssembliesGridProps) {
  const router = useRouter();

  /**
   * Handle refresh after actions
   */
  const handleRefresh = () => {
    router.refresh();
  };

  if (assemblies.length === 0) {
    return (
      <div className="p-8 text-center">
        <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">No Assemblies Found</h3>
        <p className="text-sm text-muted-foreground mb-4">
          There are no assemblies in the system yet.
        </p>
        <Button asChild>
          <Link href="/assemblies/create">Create Assembly</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {assemblies.map((assembly) => (
        <ImprovedAssemblyCard
          key={assembly._id}
          assembly={assembly}
          onRefresh={handleRefresh}
        />
      ))}
    </div>
  );
}
