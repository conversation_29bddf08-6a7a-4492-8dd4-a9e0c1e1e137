import React from 'react';
import dynamic from 'next/dynamic';
import { HeaderRightControlsProps } from './types';

// Use dynamic import to load the client component with SSR disabled
const HeaderRightControlsClient = dynamic(() => import('./HeaderRightControlsClient'), {
  ssr: false
});

/**
 * Server component for header right controls
 * Delegates rendering to the client component
 */
const HeaderRightControls: React.FC<HeaderRightControlsProps> = (props) => {
  return <HeaderRightControlsClient {...props} />;
};

export default HeaderRightControls; 