"use client";

import React from 'react';
import { HeaderRightControlsProps } from './types';
import OriginalHeaderRightControls from '@/app/components/layout/HeaderRightControls';

/**
 * Client component for header right controls
 * This is a wrapper around the original HeaderRightControls
 * component to support the new client/server pattern
 */
const HeaderRightControlsClient: React.FC<HeaderRightControlsProps> = ({ className }) => {
  return (
    <div className={className}>
      <OriginalHeaderRightControls />
    </div>
  );
};

export default HeaderRightControlsClient; 