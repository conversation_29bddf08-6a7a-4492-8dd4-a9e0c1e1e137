import React from 'react';

/**
 * Interface for notification items
 */
export interface Notification {
  /** Unique identifier for the notification */
  id: number;
  /** Type of notification for styling */
  type: 'alert' | 'info' | 'success';
  /** Notification message content */
  message: string;
  /** Whether the notification has been read */
  read: boolean;
  /** Time string for when the notification was created */
  time: string;
}

/**
 * Interface for tracking popup visibility states
 */
export interface PopupState {
  /** Whether the quick actions popup is visible */
  quickActions: boolean;
  /** Whether the notifications popup is visible */
  notifications: boolean;
  /** Whether the user menu popup is visible */
  userMenu: boolean;
  /** Whether the help menu popup is visible */
  helpMenu: boolean;
  /** Whether the view options popup is visible */
  viewOptions: boolean;
  /** Whether the search popup is visible */
  search: boolean;
}

/**
 * Props for the HeaderRightControls component
 */
export interface HeaderRightControlsProps {
  /** Optional className to apply custom styles */
  className?: string;
} 