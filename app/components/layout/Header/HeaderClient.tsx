"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu } from 'lucide-react';
import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';
import HeaderRightControls from './HeaderRightControls';
import { HeaderProps } from './types';

/**
 * Client component for Header
 * Handles all interactive elements, animations, and state
 */
const HeaderClient: React.FC<HeaderProps> = ({ title, children }) => {
  const { sidebarExpanded, setSidebarExpanded } = useAppContext();
  const { theme } = useTheme();
  const [isMobile, setIsMobile] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  /**
   * Check window size and update mobile state accordingly
   */
  useEffect(() => {
    const checkWindowSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkWindowSize();

    // Add event listener
    window.addEventListener('resize', checkWindowSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkWindowSize);
  }, []);

  /**
   * Handle scroll effects to update header background
   */
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  /**
   * Toggle the sidebar expanded state
   */
  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  return (
    <motion.div
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`sticky top-0 z-40 flex justify-between items-center py-5 px-5 md:px-8 backdrop-blur-lg ${
        theme === 'dark'
          ? scrolled
            ? 'bg-dark-800/80 border-b border-dark-border-subtle'
            : 'bg-transparent'
          : scrolled
            ? 'bg-white/80'
            : 'bg-transparent'
      } transition-all duration-300`}
    >
      <div className="flex items-center">
        {isMobile && (
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleSidebar}
            className="mr-3 p-2 rounded-full hover:bg-gray-100/80 dark:hover:bg-dark-600 cursor-pointer backdrop-blur-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-dark-focus-ring"
          >
            <Menu size={24} className="text-gray-600 dark:text-gray-300" />
          </motion.div>
        )}
        <motion.h1
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className={`text-2xl md:text-4xl font-light truncate ${
            theme === 'dark'
              ? 'text-white'
              : scrolled ? 'text-gray-800' : 'text-gray-900'
          }`}
        >
          {title}
        </motion.h1>
      </div>

      <motion.div
        className="flex items-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {children}
        <HeaderRightControls />
      </motion.div>
    </motion.div>
  );
};

export default HeaderClient; 