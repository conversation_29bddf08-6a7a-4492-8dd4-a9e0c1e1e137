import React from 'react';
import dynamic from 'next/dynamic';
import { HeaderProps } from './types';

// Use dynamic import to load the client component with SSR disabled
const HeaderClient = dynamic(() => import('./HeaderClient'), {
  ssr: false
});

/**
 * Header component for navigation and page identification
 * Server component that delegates rendering to the client component
 * 
 * @param props - Props to pass to the client component
 */
const Header: React.FC<HeaderProps> = (props) => {
  return <HeaderClient {...props} />;
};

export default Header; 