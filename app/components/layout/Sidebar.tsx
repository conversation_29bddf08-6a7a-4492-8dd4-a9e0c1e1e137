"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  Package,
  Truck,
  FileText,
  Menu,
  LayoutGrid,
  Settings,
  LogOut,
  Users,
  BarChart2,
  ShoppingCart,
  Clipboard,
  RotateCcw,
  UserCog,
  Warehouse,
  Layers,
  Upload,
  Database,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronsRight,
  Box
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Interface for navigation items in the sidebar
 */
interface NavItem {
  /** Icon component to display */
  icon: React.ReactNode;
  /** URL path for the navigation link */
  path: string;
  /** Display label for the navigation item */
  label: string;
  /** Optional badge count to display */
  badge?: number;
  /** Optional category for grouping navigation items */
  category?: string;
}

/**
 * Sidebar component for main navigation
 * Provides links to all major sections of the application
 * Supports collapsible categories and responsive design
 */
const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const { sidebarExpanded, setSidebarExpanded } = useAppContext();
  const { theme } = useTheme();
  const [isMobile, setIsMobile] = useState(false);
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [allExpanded, setAllExpanded] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Check window size on mount and when it changes
  useEffect(() => {
    const checkWindowSize = () => {
      setIsMobile(window.innerWidth < 768);

      // Auto-collapse sidebar on mobile
      if (window.innerWidth < 768 && sidebarExpanded) {
        setSidebarExpanded(false);
      }
    };

    // Initial check
    checkWindowSize();

    // Add event listener
    window.addEventListener('resize', checkWindowSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkWindowSize);
  }, [sidebarExpanded, setSidebarExpanded]);

  // Categorized nav items with badges for important sections
  const navItems: NavItem[] = [
    { icon: <Home size={20} />, path: '/', label: 'Dashboard', category: 'Overview' },
    { icon: <BarChart2 size={20} />, path: '/analytics', label: 'Analytics', category: 'Overview' },

    { icon: <Package size={20} />, path: '/inventory', label: 'Inventory', badge: 5, category: 'Inventory' },
    { icon: <Database size={20} />, path: '/hierarchical-part-entry', label: 'Part Entry', category: 'Inventory' },
    { icon: <Layers size={20} />, path: '/hierarchical-builder', label: 'Assembly Builder', category: 'Inventory' },
    { icon: <Box size={20} />, path: '/assemblies', label: 'Assemblies', category: 'Inventory' },
    { icon: <Upload size={20} />, path: '/product-import', label: 'Import Products', category: 'Inventory' },
    { icon: <Layers size={20} />, path: '/products', label: 'Products', category: 'Inventory' },
    { icon: <LayoutGrid size={20} />, path: '/categories', label: 'Categories', category: 'Inventory' },

    { icon: <ShoppingCart size={20} />, path: '/purchase-orders', label: 'Purchase Orders', badge: 2, category: 'Orders' },
    { icon: <Clipboard size={20} />, path: '/work-orders', label: 'Work Orders', category: 'Orders' },

    { icon: <RotateCcw size={20} />, path: '/inventory-transactions', label: 'Transactions', category: 'Operations' },
    { icon: <Truck size={20} />, path: '/logistics', label: 'Logistics', category: 'Operations' },
    { icon: <Warehouse size={20} />, path: '/warehouses', label: 'Warehouses', category: 'Operations' },
    { icon: <Layers size={20} />, path: '/batch-tracking', label: 'Batch Tracking', category: 'Operations' },

    { icon: <FileText size={20} />, path: '/reports', label: 'Reports', category: 'Administration' },
    { icon: <Users size={20} />, path: '/suppliers', label: 'Suppliers', category: 'Administration' },
    { icon: <UserCog size={20} />, path: '/user-management', label: 'Users', category: 'Administration' }
  ];

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  const toggleCategory = (category: string, e: React.MouseEvent) => {
    // If Shift key is pressed, toggle all categories
    if (e.shiftKey) {
      setAllExpanded(!allExpanded);
      setExpandedCategory(null);
      return;
    }

    // Otherwise, toggle just this category
    if (expandedCategory === category) {
      setExpandedCategory(null);
      setAllExpanded(false);
    } else {
      setExpandedCategory(category);
      setAllExpanded(false);
    }
  };

  // Get all unique categories
  const categories = Array.from(new Set(navItems.map(item => item.category)));

  // Show tooltip for 3 seconds when sidebar is first expanded
  useEffect(() => {
    if (sidebarExpanded) {
      setShowTooltip(true);
      const timer = setTimeout(() => {
        setShowTooltip(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sidebarExpanded]);

  return (
    <motion.div
      initial={{ x: -100, opacity: 0 }}
      animate={{
        x: 0,
        opacity: 1,
        width: sidebarExpanded ? (isMobile ? '85%' : 240) : 68
      }}
      transition={{ duration: 0.3 }}
      className={`h-screen ${
        theme === 'dark'
          ? 'bg-dark-800/95 border-r border-dark-border-subtle'
          : 'bg-white/95'
      } flex flex-col py-6 shadow-lg overflow-hidden ${isMobile && sidebarExpanded ? 'fixed z-50' : 'relative'}`}
    >
      <div className={`flex justify-between w-full ${sidebarExpanded ? 'px-4 mb-6' : 'px-3 mb-6'}`}>
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.2 }}
          className="flex items-center"
        >
          {/* Logo with floating animation */}
          <motion.div
            whileHover={{ rotate: 180 }}
            animate={{ y: [0, -5, 0] }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className={`w-10 h-10 rounded-full flex items-center justify-center ${
              theme === 'dark'
                ? 'bg-gradient-to-br from-dark-700 to-dark-800'
                : 'bg-gradient-to-br from-gray-700 to-gray-800'
            }`}
          >
            <div className="w-6 h-3 bg-white dark:bg-dark-900 rounded-sm"></div>
          </motion.div>
          <AnimatePresence>
            {sidebarExpanded && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="ml-3 text-gray-900 dark:text-white font-semibold text-lg"
              >
                Inventory
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Subtle collapse button when expanded */}
        {sidebarExpanded && (
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleSidebar}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer"
            aria-label="Collapse sidebar"
          >
            <ChevronLeft size={16} />
          </motion.div>
        )}
      </div>

      {/* Subtle expand button for collapsed state - only show when collapsed */}
      {!sidebarExpanded && (
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleSidebar}
          className="mb-4 w-10 h-7 mx-auto flex items-center justify-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer"
          aria-label="Expand sidebar"
        >
          <ChevronsRight size={16} />
        </motion.div>
      )}

      <div className={`flex-1 w-full ${sidebarExpanded ? 'px-4' : 'px-3'} overflow-y-auto overflow-x-hidden custom-scrollbar`}>
        {/* Tooltip to show shortcut hint */}
        {sidebarExpanded && showTooltip && (
          <div className="text-xs text-center text-gray-500 dark:text-gray-400 mb-2 px-2 py-1 bg-gray-100 dark:bg-dark-700 rounded-md">
            <span className="font-medium">Tip:</span> Hold <kbd className="px-1 py-0.5 text-xs font-semibold bg-gray-200 dark:bg-dark-600 rounded">Shift</kbd> + click on any category to expand/collapse all
          </div>
        )}
        
        <div className={`flex flex-col ${sidebarExpanded ? 'space-y-1' : 'space-y-3'}`}>          
          {/* Show categorized navigation when expanded */}
          {sidebarExpanded ? (
            categories.map((category) => (
              <div key={category} className="w-full">
                <motion.div
                  whileHover={{ backgroundColor: theme === 'dark' ? 'rgba(39, 48, 88, 0.5)' : 'rgba(243, 244, 246, 0.6)' }}
                  initial={{ backgroundColor: 'transparent' }}
                  transition={{ duration: 0.2 }}
                  onClick={(e) => toggleCategory(category!, e)}
                  className="flex items-center justify-between px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase cursor-pointer rounded-xl"
                  role="button"
                  aria-expanded={expandedCategory === category || allExpanded}
                  aria-controls={`category-${category}`}
                  title="Shift+click to expand/collapse all categories"
                >
                  <span>{category}</span>
                  <motion.div
                    animate={{ rotate: (expandedCategory === category || allExpanded) ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ChevronDown size={14} />
                  </motion.div>
                </motion.div>

                <AnimatePresence>
                  {(expandedCategory === category || allExpanded) && (
                    <motion.div
                      id={`category-${category}`}
                      initial={{ height: 0, opacity: 0 }}
                      animate={{
                        height: (expandedCategory === category || allExpanded) ? "auto" : 0,
                        opacity: (expandedCategory === category || allExpanded) ? 1 : 0,
                        marginBottom: (expandedCategory === category || allExpanded) ? 8 : 0
                      }}
                      exit={{ height: 0, opacity: 0, marginBottom: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-1 ml-3 flex flex-col space-y-1">
                        {navItems
                          .filter(item => item.category === category)
                          .map((item) => (
                            <Link href={item.path} key={item.path} className="block w-full">
                              <motion.div
                                whileHover={{
                                  backgroundColor: pathname === item.path
                                    ? theme === 'dark'
                                      ? 'rgba(64, 64, 64, 0.3)'
                                      : 'rgba(224, 224, 224, 0.5)'
                                    : theme === 'dark'
                                      ? 'rgba(44, 44, 44, 0.3)'
                                      : 'rgba(243, 244, 246, 0.6)'
                                }}
                                transition={{ duration: 0.2 }}
                                className={`flex items-center justify-between py-2 px-3 rounded-lg text-sm font-medium ${
                                  pathname === item.path
                                    ? theme === 'dark'
                                      ? 'bg-dark-600/30 text-white'
                                      : 'bg-gray-200 text-gray-900'
                                    : theme === 'dark'
                                      ? 'text-gray-300 hover:text-white'
                                      : 'text-gray-700 hover:text-gray-900'
                                }`}
                              >
                                <div className="flex items-center">
                                  <div className="w-5 h-5 flex items-center justify-center">
                                    {item.icon}
                                  </div>
                                  <span className="ml-3 text-sm font-medium">{item.label}</span>
                                </div>

                                {item.badge && (
                                  <span className={`px-2 py-0.5 text-xs rounded-full ${
                                    theme === 'dark'
                                      ? 'bg-gray-200 text-gray-800'
                                      : 'bg-gray-100/50 text-gray-600'
                                  }`}>
                                    {item.badge}
                                  </span>
                                )}
                              </motion.div>
                            </Link>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))
          ) : (
            // Show just icons when collapsed
            navItems.map((item) => (
              <Link href={item.path} key={item.path} className="block w-full">
                <motion.div
                  whileHover={{
                    scale: 1.02,
                    backgroundColor: pathname === item.path
                      ? theme === 'dark'
                        ? 'var(--dark-hover)'
                        : 'rgba(224, 224, 224, 0.5)'
                      : theme === 'dark'
                        ? 'var(--T-hover-overlay)'
                        : 'rgba(243, 244, 246, 0.6)'
                  }}
                  whileTap={{ scale: 0.98 }}
                  className={`mt-2 flex py-2 px-3 rounded-xl ${
                    theme === 'dark'
                      ? 'hover:bg-gray-700/20 text-gray-300'
                      : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <div className="w-5 h-5 flex items-center justify-center">
                    {item.icon}
                  </div>

                  {/* Badge indicator */}
                  {item.badge && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 flex items-center justify-center">
                      <span className={`w-4 h-4 flex items-center justify-center text-[10px] rounded-full ${
                        theme === 'dark'
                          ? 'bg-gray-200 text-gray-800'
                          : 'bg-gray-100/50 text-gray-600'
                      }`}>
                        {item.badge}
                      </span>
                    </div>
                  )}
                </motion.div>

                {/* Tooltip for nav items when collapsed */}
                <div className="absolute left-16 mt-[-32px] px-2 py-1 w-auto bg-gray-900 text-white text-xs font-medium rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-50 pointer-events-none">
                  {item.label}
                </div>
              </Link>
            ))
          )}
        </div>
      </div>

      {/* Footer actions */}
      <div className={`mt-auto ${sidebarExpanded ? 'px-4' : 'px-3'}`}>
        <Link href="/settings" className="block w-full">
          <motion.div
            whileHover={{
              backgroundColor: theme === 'dark' ? 'rgba(39, 48, 88, 0.8)' : 'rgba(243, 244, 246, 0.8)',
              scale: 1.02
            }}
            whileTap={{ scale: 0.98 }}
            className={`flex items-center ${sidebarExpanded ? 'justify-start px-3' : 'justify-center'} py-2 rounded-xl mb-2 ${
              pathname === '/settings'
                ? theme === 'dark'
                  ? 'bg-dark-700/80 text-white'
                  : 'bg-gray-200 text-gray-800'
                : theme === 'dark'
                  ? 'text-gray-300 hover:text-white'
                  : 'text-gray-700 hover:text-gray-900'
            }`}
          >
            <Settings size={18} />
            <AnimatePresence>
              {sidebarExpanded && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                  className="ml-3 text-sm font-medium"
                >
                  Settings
                </motion.span>
              )}
            </AnimatePresence>
          </motion.div>
        </Link>
      </div>
    </motion.div>
  );
};

export default Sidebar;