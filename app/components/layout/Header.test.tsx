"use client";

import React from 'react';
import { render, screen } from '@testing-library/react';
import Header from './Header';

// Mock the AppContext
jest.mock('@/app/context/AppContext', () => ({
  useAppContext: () => ({
    sidebarExpanded: true,
    setSidebarExpanded: jest.fn(),
  }),
}));

// Mock the ThemeContext
jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({
    theme: 'light',
  }),
}));

// Mock the HeaderRightControls component
jest.mock('@/app/components/layout/HeaderRightControls', () => {
  return function MockHeaderRightControls() {
    return <div data-testid="header-right-controls">Header Right Controls</div>;
  };
});

// Mock the usePathname hook
jest.mock('next/navigation', () => ({
  usePathname: () => '/dashboard',
}));

describe('Header', () => {
  // Default props for the component
  const defaultProps = {
    title: 'Dashboard',
  };

  // Setup function to render the component with props
  const setup = (props = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return render(<Header {...mergedProps} />);
  };

  it('renders the title correctly', () => {
    setup();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  it('renders the HeaderRightControls component', () => {
    setup();
    expect(screen.getByTestId('header-right-controls')).toBeInTheDocument();
  });

  it('renders children when provided', () => {
    setup({
      children: <div data-testid="header-children">Header Children</div>,
    });
    expect(screen.getByTestId('header-children')).toBeInTheDocument();
  });

  it('renders with a custom title', () => {
    setup({ title: 'Custom Title' });
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
  });

  // Note: Testing the mobile sidebar toggle would require more complex setup
  // with window resize events and click handlers
});
