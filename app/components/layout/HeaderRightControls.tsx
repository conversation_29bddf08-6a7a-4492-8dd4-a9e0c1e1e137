"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Settings,
  Bell,
  Plus,
  HelpCircle,
  User,
  Database,
  Grid,
  List,
  BookOpen,
  FileText,
  ShoppingCart,
  Package,
  BarChart2,
  Download,
  Upload,
  AlertCircle,
  Check,
  MessageSquare,
  ExternalLink,
  LogOut
} from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import ThemeToggle from '@/app/components/ui/ThemeToggle';
import { format } from 'date-fns'; // Import format if needed for consistent server/client initial render

/**
 * Interface for notification items
 */
interface Notification {
  /** Unique identifier for the notification */
  id: number;
  /** Type of notification for styling */
  type: 'alert' | 'info' | 'success';
  /** Notification message content */
  message: string;
  /** Whether the notification has been read */
  read: boolean;
  /** Time string for when the notification was created */
  time: string;
}

/**
 * Interface for tracking popup visibility states
 */
interface PopupState {
  /** Whether the quick actions popup is visible */
  quickActions: boolean;
  /** Whether the notifications popup is visible */
  notifications: boolean;
  /** Whether the user menu popup is visible */
  userMenu: boolean;
  /** Whether the help menu popup is visible */
  helpMenu: boolean;
  /** Whether the view options popup is visible */
  viewOptions: boolean;
  /** Whether the search popup is visible */
  search: boolean;
  /** Whether the system status popup is visible */
  systemStatus: boolean;
}

/**
 * Component for the right side of the header
 * Includes notifications, user menu, theme toggle, and other controls
 */
const HeaderRightControls: React.FC = () => {
  const { theme } = useTheme();

  // Create a unified popup state
  const [popupState, setPopupState] = useState<PopupState>({
    quickActions: false,
    notifications: false,
    userMenu: false,
    helpMenu: false,
    viewOptions: false,
    search: false,
    systemStatus: false
  });

  // Refs for all popups
  const popupRefs = {
    quickActions: useRef<HTMLDivElement>(null),
    notifications: useRef<HTMLDivElement>(null),
    userMenu: useRef<HTMLDivElement>(null),
    helpMenu: useRef<HTMLDivElement>(null),
    viewOptions: useRef<HTMLDivElement>(null),
    search: useRef<HTMLDivElement>(null),
    systemStatus: useRef<HTMLDivElement>(null)
  };

  // Button refs for focus management
  const buttonRefs = {
    quickActions: useRef<HTMLDivElement>(null),
    notifications: useRef<HTMLDivElement>(null),
    userMenu: useRef<HTMLDivElement>(null),
    helpMenu: useRef<HTMLDivElement>(null),
    viewOptions: useRef<HTMLDivElement>(null),
    search: useRef<HTMLDivElement>(null),
    systemStatus: useRef<HTMLDivElement>(null)
  };

  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentView, setCurrentView] = useState('list');
  const [isMounted, setIsMounted] = useState(false); // State to track client-side mount

  // Notification sample data
  const [notifications, setNotifications] = useState<Notification[]>([
    { id: 1, type: 'alert', message: 'Low stock for Tamping frame', read: false, time: '10m ago' },
    { id: 2, type: 'info', message: 'Purchase order #12345 approved', read: false, time: '25m ago' },
    { id: 3, type: 'success', message: 'Data sync completed successfully', read: true, time: '1h ago' },
    { id: 4, type: 'alert', message: 'System maintenance scheduled for tonight', read: true, time: '3h ago' },
  ]);

  // System status
  const [systemStatus, setSystemStatus] = useState({
    connected: true,
    lastSync: new Date(),
    dbStatus: 'Healthy',
    performance: 'Good'
  });

  // Toggle a specific popup
  const togglePopup = (popupName: keyof PopupState) => {
    // Close all other popups first
    const newState = Object.keys(popupState).reduce((acc, key) => {
      acc[key as keyof PopupState] = false;
      return acc;
    }, {} as PopupState);

    // Toggle the target popup
    newState[popupName] = !popupState[popupName];
    setPopupState(newState);

    // Focus the popup if opening
    if (newState[popupName] && popupName === 'search' && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  };

  // Close all popups
  const closeAllPopups = () => {
    setPopupState({
      quickActions: false,
      notifications: false,
      userMenu: false,
      helpMenu: false,
      viewOptions: false,
      search: false,
      systemStatus: false
    });
  };

  // Close popups when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      let clickedInsidePopup = false;

      // Check if clicked inside any popup
      Object.entries(popupRefs).forEach(([key, ref]) => {
        const typedKey = key as keyof PopupState;
        if (
          popupState[typedKey] &&
          ref.current &&
          (ref.current.contains(event.target as Node) ||
           buttonRefs[typedKey]?.current?.contains(event.target as Node))
        ) {
          clickedInsidePopup = true;
        }
      });

      if (!clickedInsidePopup) {
        closeAllPopups();
      }
    };

    // Handle escape key for closing popups
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        const anyPopupOpen = Object.values(popupState).some(isOpen => isOpen);
        if (anyPopupOpen) {
          closeAllPopups();

          // Return focus to the last active popup button
          for (const key of Object.keys(popupState) as Array<keyof PopupState>) {
            if (popupState[key] && buttonRefs[key]?.current) {
              buttonRefs[key].current?.focus();
              break;
            }
          }
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [popupState]); // Dependency array includes popupState

  // Set mounted state after initial render on client
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Mark notification as read
  const markAsRead = (id: number) => {
    setNotifications(notifications.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
  };

  // Count unread notifications
  const unreadCount = notifications.filter(notification => !notification.read).length;

  return (
    <div className="flex items-center space-x-2 md:space-x-3">
      {/* 1. Global Search */}
      <div ref={popupRefs.search} className="relative">
        <motion.div
          ref={buttonRefs.search}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('search')}
          className={`w-10 h-10 ${popupState.search ? 'bg-gray-200 dark:bg-gray-700' : 'bg-gray-100 dark:bg-gray-800'} rounded-full flex items-center justify-center cursor-pointer transition-colors`}
          aria-expanded={popupState.search}
          aria-controls="search-popup"
          tabIndex={0}
          role="button"
          aria-label="Search"
        >
          <Search size={18} className="text-gray-600 dark:text-gray-400" />
        </motion.div>

        <AnimatePresence>
          {popupState.search && (
            <motion.div
              id="search-popup"
              initial={{ opacity: 0, y: -10, width: '200px' }}
              animate={{ opacity: 1, y: 0, width: '300px' }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10 overflow-hidden"
            >
              <div className="p-3">
                <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2">
                  <Search size={16} className="text-gray-500 dark:text-gray-400" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search for products, orders..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="ml-2 bg-transparent border-none outline-none w-full text-sm text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
                {searchQuery && (
                  <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    Press Enter to search for "{searchQuery}"
                  </div>
                )}
                <div className="mt-3">
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">RECENT SEARCHES</div>
                  <div className="space-y-1">
                    <div className="flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                      <span className="text-sm text-gray-800 dark:text-gray-200">Tamping frame</span>
                    </div>
                    <div className="flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                      <span className="text-sm text-gray-800 dark:text-gray-200">Purchase order #12345</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 2. Quick Actions Button */}
      <div ref={popupRefs.quickActions} className="relative">
        <motion.div
          ref={buttonRefs.quickActions}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('quickActions')}
          className={`w-10 h-10 ${popupState.quickActions ? 'bg-blue-600 dark:bg-blue-700' : 'bg-blue-500 dark:bg-blue-600'} rounded-full flex items-center justify-center cursor-pointer transition-colors`}
          aria-expanded={popupState.quickActions}
          aria-controls="quick-actions-popup"
          tabIndex={0}
          role="button"
          aria-label="Quick actions"
        >
          <Plus size={18} className="text-white" />
        </motion.div>

        <AnimatePresence>
          {popupState.quickActions && (
            <motion.div
              id="quick-actions-popup"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 w-64 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10"
            >
              <div className="p-2">
                <div className="text-sm font-medium text-gray-800 dark:text-gray-200 px-3 py-2">Quick Actions</div>

                <div className="space-y-1">
                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <Package size={16} className="text-blue-500 dark:text-blue-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Add New Product</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <ShoppingCart size={16} className="text-green-500 dark:text-green-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Create Purchase Order</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <BarChart2 size={16} className="text-purple-500 dark:text-purple-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Generate Report</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <Upload size={16} className="text-orange-500 dark:text-orange-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Import Data</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <Download size={16} className="text-indigo-500 dark:text-indigo-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Export Data</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 3. System Status */}
      <div ref={popupRefs.systemStatus} className="relative">
        <motion.div
          ref={buttonRefs.systemStatus}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('systemStatus')}
          className={`w-10 h-10 ${popupState.systemStatus ? 'bg-gray-200 dark:bg-gray-700' : systemStatus.connected ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'} rounded-full flex items-center justify-center cursor-pointer transition-colors`}
          aria-expanded={popupState.systemStatus}
          aria-controls="system-status-popup"
          tabIndex={0}
          role="button"
          aria-label="System status"
        >
          <Database size={18} className={systemStatus.connected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'} />
        </motion.div>

        <AnimatePresence>
          {popupState.systemStatus && (
            <motion.div
              id="system-status-popup"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 w-64 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10 p-3"
            >
              <div className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">System Status</div>

              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Database:</span>
                  <span className={`font-medium ${systemStatus.connected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {systemStatus.connected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Last Sync:</span>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {/* Only render locale time string after mounting */}
                    {isMounted ? systemStatus.lastSync.toLocaleTimeString() : '...'}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">DB Status:</span>
                  <span className="font-medium text-green-600 dark:text-green-400">
                    {systemStatus.dbStatus}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Performance:</span>
                  <span className="font-medium text-green-600 dark:text-green-400">
                    {systemStatus.performance}
                  </span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 4. View Options */}
      <div ref={popupRefs.viewOptions} className="relative">
        <motion.div
          ref={buttonRefs.viewOptions}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('viewOptions')}
          className={`w-10 h-10 ${popupState.viewOptions ? 'bg-gray-200 dark:bg-gray-700' : 'bg-gray-100 dark:bg-gray-800'} rounded-full flex items-center justify-center cursor-pointer transition-colors`}
          aria-expanded={popupState.viewOptions}
          aria-controls="view-options-popup"
          tabIndex={0}
          role="button"
          aria-label="View options"
        >
          {currentView === 'list' ? (
            <List size={18} className="text-gray-600 dark:text-gray-400" />
          ) : (
            <Grid size={18} className="text-gray-600 dark:text-gray-400" />
          )}
        </motion.div>

        <AnimatePresence>
          {popupState.viewOptions && (
            <motion.div
              id="view-options-popup"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 w-48 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10"
            >
              <div className="p-2">
                <div className="text-sm font-medium text-gray-800 dark:text-gray-200 px-3 py-2">View Options</div>

                <div className="space-y-1">
                  <div
                    className={`flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer ${currentView === 'list' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''}`}
                    onClick={() => { setCurrentView('list'); togglePopup('viewOptions'); }}
                  >
                    <List size={16} className="mr-3" />
                    <span className="text-sm">List View</span>
                  </div>

                  <div
                    className={`flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer ${currentView === 'grid' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''}`}
                    onClick={() => { setCurrentView('grid'); togglePopup('viewOptions'); }}
                  >
                    <Grid size={16} className="mr-3" />
                    <span className="text-sm">Grid View</span>
                  </div>

                  <div className="px-3 py-2 border-t border-gray-100 dark:border-gray-700 mt-1">
                    <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Density</div>

                    <div className="flex space-x-2">
                      <div className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                        Compact
                      </div>
                      <div className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 rounded text-xs cursor-pointer text-blue-600 dark:text-blue-400">
                        Default
                      </div>
                      <div className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                        Comfortable
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 5. Notifications */}
      <div ref={popupRefs.notifications} className="relative">
        <motion.div
          ref={buttonRefs.notifications}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('notifications')}
          className={`w-10 h-10 ${popupState.notifications ? 'bg-gray-200 dark:bg-gray-700' : 'bg-gray-100 dark:bg-gray-800'} rounded-full flex items-center justify-center cursor-pointer relative transition-colors`}
          aria-expanded={popupState.notifications}
          aria-controls="notifications-popup"
          tabIndex={0}
          role="button"
          aria-label={`Notifications ${unreadCount > 0 ? `(${unreadCount} unread)` : ''}`}
        >
          <Bell
            size={18}
            className={`${
              unreadCount > 0
                ? "text-gray-600 dark:text-gray-300"
                : "text-gray-400 dark:text-gray-500"
            }`}
          />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount}
            </span>
          )}
        </motion.div>

        <AnimatePresence>
          {popupState.notifications && (
            <motion.div
              id="notifications-popup"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 w-80 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10"
            >
              <div className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-medium text-gray-800 dark:text-gray-200">Notifications</div>
                  {unreadCount > 0 && (
                    <div
                      className="text-xs text-blue-600 dark:text-blue-400 cursor-pointer hover:underline"
                      onClick={markAllAsRead}
                    >
                      Mark all as read
                    </div>
                  )}
                </div>

                <div className="space-y-2 max-h-80 overflow-y-auto">
                  {notifications.length === 0 ? (
                    <div className="text-center py-6">
                      <Bell size={24} className="mx-auto mb-2 text-gray-400" />
                      <div className="text-sm text-gray-500 dark:text-gray-400">No notifications</div>
                    </div>
                  ) : (
                    notifications.map(notification => (
                      <div
                        key={notification.id}
                        className={`p-2 rounded-lg flex items-start ${notification.read ? 'bg-white dark:bg-gray-800' : 'bg-blue-50 dark:bg-blue-900/20'}`}
                        onClick={() => markAsRead(notification.id)}
                      >
                        {notification.type === 'alert' && (
                          <AlertCircle size={18} className="mt-0.5 mr-3 text-red-500 dark:text-red-400" />
                        )}
                        {notification.type === 'info' && (
                          <Bell size={18} className="mt-0.5 mr-3 text-blue-500 dark:text-blue-400" />
                        )}
                        {notification.type === 'success' && (
                          <Check size={18} className="mt-0.5 mr-3 text-green-500 dark:text-green-400" />
                        )}
                        <div className="flex-1">
                          <div className="text-sm text-gray-800 dark:text-gray-200">{notification.message}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{notification.time}</div>
                        </div>
                        {!notification.read && (
                          <div className="h-2 w-2 rounded-full bg-blue-500 mt-1"></div>
                        )}
                      </div>
                    ))
                  )}
                </div>

                <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                  <div className="text-center text-xs text-blue-600 dark:text-blue-400 cursor-pointer hover:underline">
                    View all notifications
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 6. Help Menu */}
      <div ref={popupRefs.helpMenu} className="relative">
        <motion.div
          ref={buttonRefs.helpMenu}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('helpMenu')}
          className={`w-10 h-10 ${popupState.helpMenu ? 'bg-gray-200 dark:bg-gray-700' : 'bg-gray-100 dark:bg-gray-800'} rounded-full flex items-center justify-center cursor-pointer transition-colors`}
          aria-expanded={popupState.helpMenu}
          aria-controls="help-menu-popup"
          tabIndex={0}
          role="button"
          aria-label="Help menu"
        >
          <HelpCircle size={18} className="text-gray-600 dark:text-gray-400" />
        </motion.div>

        <AnimatePresence>
          {popupState.helpMenu && (
            <motion.div
              id="help-menu-popup"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 w-64 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10"
            >
              <div className="p-3">
                <div className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">Help & Resources</div>

                <div className="space-y-1">
                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <BookOpen size={16} className="text-blue-500 dark:text-blue-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Documentation</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <FileText size={16} className="text-green-500 dark:text-green-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Tutorials</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <MessageSquare size={16} className="text-purple-500 dark:text-purple-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Contact Support</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <ExternalLink size={16} className="text-orange-500 dark:text-orange-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">What's New</span>
                  </div>
                </div>

                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">Need help with this page?</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Check our inventory management guide for detailed instructions.</div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Theme Toggle Button */}
      <div className="relative">
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className={`w-10 h-10 bg-gray-100 dark:bg-dark-800 rounded-full flex items-center justify-center cursor-pointer transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-dark-focus-ring`}
          role="button"
          aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
          tabIndex={0}
        >
          <ThemeToggle isMinimal={true} />
        </motion.div>
      </div>

      {/* 7. User Profile */}
      <div ref={popupRefs.userMenu} className="relative">
        <motion.div
          ref={buttonRefs.userMenu}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => togglePopup('userMenu')}
          className={`w-10 h-10 ${popupState.userMenu ? 'bg-blue-200 dark:bg-blue-700' : 'bg-blue-100 dark:bg-blue-800'} rounded-full flex items-center justify-center cursor-pointer transition-colors`}
          aria-expanded={popupState.userMenu}
          aria-controls="user-menu-popup"
          tabIndex={0}
          role="button"
          aria-label="User menu"
        >
          <span className="text-sm font-medium text-blue-600 dark:text-blue-300">JD</span>
        </motion.div>

        <AnimatePresence>
          {popupState.userMenu && (
            <motion.div
              id="user-menu-popup"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 top-12 w-64 bg-white dark:bg-dark-700 rounded-xl shadow-lg z-10"
            >
              <div className="p-3">
                <div className="flex items-center space-x-3 pb-3 border-b border-gray-100 dark:border-gray-700">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                    <span className="text-lg font-medium text-blue-600 dark:text-blue-300">JD</span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-800 dark:text-gray-200">John Doe</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Administrator</div>
                    <div className="text-xs text-blue-600 dark:text-blue-400 mt-1"><EMAIL></div>
                  </div>
                </div>

                <div className="py-2 space-y-1">
                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <User size={16} className="text-gray-500 dark:text-gray-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">My Profile</span>
                  </div>

                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer">
                    <Settings size={16} className="text-gray-500 dark:text-gray-400 mr-3" />
                    <span className="text-sm text-gray-800 dark:text-gray-200">Preferences</span>
                  </div>
                </div>

                <div className="pt-2 mt-2 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex items-center px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-red-600 dark:text-red-400">
                    <LogOut size={16} className="mr-3" />
                    <span className="text-sm">Sign Out</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default HeaderRightControls;
