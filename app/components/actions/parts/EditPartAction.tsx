'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { PencilIcon } from 'lucide-react';
import { Button } from '@/app/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import PartForm, { PartFormData } from '@/app/components/forms/PartForm';
import { toast } from 'sonner';
import { cn } from '@/app/lib/utils';

interface EditPartActionProps {
  partId: string;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Edit part action component
 * Opens the PartForm in a modal with the part data loaded
 */
export function EditPartAction({
  partId,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: EditPartActionProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [initialData, setInitialData] = useState<Partial<PartFormData> | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch part data when modal is opened
  const handleOpenModal = async () => {
    setIsLoading(true);
    try {
      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);
      console.log('[EditPartAction] Fetching part with encodedPartId:', encodedPartId);

      const response = await fetch(`/api/parts/${encodedPartId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch part data');
      }

      const data = await response.json();
      if (data.data) {
        // Transform API data to match PartFormData structure
        setInitialData({
          _id: data.data._id,
          name: data.data.name,
          description: data.data.description || '',
          technical_specs: data.data.technical_specs || '',
          is_manufactured: data.data.is_manufactured || false,
          reorder_level: data.data.reorder_level || 0,
          status: data.data.status || 'active',
          inventory: {
            current_stock: data.data.inventory?.currentStock || 0,
            location: data.data.inventory?.location || '',
            lastCountDate: data.data.inventory?.lastCountDate || null,
          },
          isAssembly: data.data.isAssembly || false,
          sub_parts: data.data.sub_parts || [],
          schemaVersion: data.data.schemaVersion || 1,
          supplier_id: data.data.supplier_id || '',
        });
        setIsModalOpen(true);
      }
    } catch (error) {
      console.error('Error fetching part data:', error);
      toast.error('Failed to load part data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: PartFormData) => {
    try {
      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);
      console.log('[EditPartAction] Updating part with encodedPartId:', encodedPartId);

      const response = await fetch(`/api/parts/${encodedPartId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update part');
      }

      // Close modal
      setIsModalOpen(false);

      // Show success message
      toast.success(`Part "${data.name}" updated successfully`);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page
      router.refresh();
    } catch (error) {
      console.error('Error updating part:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred while updating the part');
    }
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            onClick={handleOpenModal}
            className={className}
            disabled={isLoading}
          >
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <PencilIcon size={16} className="mr-2" />
                Edit
              </>
            )}
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={handleOpenModal}
            className={cn("h-8 px-2 hover:bg-muted/50", className)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <PencilIcon size={15} className="mr-1" />
                Edit
              </>
            )}
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    handleOpenModal();
                  }}
                  className={cn("h-8 w-8 p-0", className)}
                  id={id || `edit-part-${partId}`}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>Loading...</>
                  ) : (
                    <PencilIcon size={15} />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit Part</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {isModalOpen && initialData && (
        <PartForm
          onSubmit={(data) => Promise.resolve(handleSubmit(data))}
          onClose={() => setIsModalOpen(false)}
          initialData={initialData}
          isEdit={true}
          title={`Edit Part: ${initialData.name}`}
        />
      )}
    </>
  );
}
