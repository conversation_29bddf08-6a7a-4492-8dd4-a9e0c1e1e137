'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Copy } from 'lucide-react';
import { Button } from '@/app/components/ui/button';
import { ConfirmationDialog } from '@/app/components/dialogs/ConfirmationDialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { useAssemblies } from '@/app/contexts/AssembliesContext';

interface DuplicateAssemblyActionProps {
  assembly: Assembly;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Duplicate assembly action component
 */
export function DuplicateAssemblyAction({
  assembly,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: DuplicateAssemblyActionProps) {
  const router = useRouter();
  const { duplicateAssembly } = useAssemblies();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);

  // Handle duplicate confirmation
  const handleDuplicate = async () => {
    if (!assembly._id) {
      setIsDialogOpen(false);
      return;
    }

    setIsDuplicating(true);

    try {
      const newAssembly = await duplicateAssembly(assembly._id);

      if (newAssembly) {
        // Close dialog
        setIsDialogOpen(false);

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }

        // Refresh the page
        router.refresh();

        // Navigate to the new assembly
        if (newAssembly._id) {
          router.push(`/assemblies/${newAssembly._id}/edit`);
        }
      }
    } finally {
      setIsDuplicating(false);
    }
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            onClick={() => setIsDialogOpen(true)}
          >
            <Copy size={16} className="mr-2" />
            Duplicate
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={() => setIsDialogOpen(true)}
          >
            <Copy size={16} className="mr-2" />
            Duplicate
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size={size}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setIsDialogOpen(true);
                  }}
                  className={className}
                  id={id}
                  style={{ position: 'relative', zIndex: 30 }}
                >
                  <Copy size={size === 'sm' ? 16 : 20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Duplicate Assembly</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleDuplicate}
        title="Duplicate Assembly"
        description={(
          <div>
            <div>Are you sure you want to duplicate the assembly <strong>"{assembly.name}"</strong>?</div>
            <div className="mt-2 text-sm">This will create a new copy of the assembly with all its parts and settings. The new assembly will be named "{assembly.name} (Copy)".</div>
          </div>
        )}
        confirmLabel="Duplicate Assembly"
        cancelLabel="Cancel"
        variant="info"
        isLoading={isDuplicating}
      />
    </>
  );
}