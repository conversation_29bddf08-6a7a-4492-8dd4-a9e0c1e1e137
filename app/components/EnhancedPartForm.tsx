"use client";

import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Package, Plus, Truck, Database, Gift, Search, Loader } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
// Assuming searchParts is now imported from the MongoDB service
import { searchParts } from '@/app/services/mongodb';
import debounce from 'lodash.debounce';
// Remove Supabase types if no longer needed
// import { Database as DatabaseTypes } from '@/app/types/supabase';

// Updated FormData interface based on the new Part schema
interface FormData {
  _id?: string; // Use _id as the identifier (string)
  name?: string;
  description?: string | null;
  technical_specs?: string | null;
  is_manufactured?: boolean;
  reorder_level?: number | null;
  status?: string;
  inventory?: {
    currentStock?: number;
    location?: string | null;
  };
}

interface EnhancedPartFormProps {
  onSubmit: (data: FormData) => void;
  onClose: () => void;
  initialData?: FormData;
  isEdit?: boolean;
  title?: string;
} // Added missing closing brace

// Updated SearchResult interface based on MongoDB searchParts service function return type
// Assuming it returns fields matching the new Part schema
interface SearchResult {
  _id: string; // Use _id
  name: string;
  description?: string | null;
  technical_specs?: string | null;
  status?: string;
  inventory?: {
    currentStock?: number;
    location?: string | null;
  };
  // Add other fields returned by searchParts if needed
}

const EnhancedPartForm: React.FC<EnhancedPartFormProps> = ({
  onSubmit,
  onClose,
  initialData,
  isEdit = false,
  title = 'Add New Part',
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // Updated initial state based on new FormData interface
  const [formData, setFormData] = useState<FormData>({
    _id: initialData?._id || '',
    name: initialData?.name || '',
    description: initialData?.description || '',
    technical_specs: initialData?.technical_specs || '',
    is_manufactured: initialData?.is_manufactured || false,
    reorder_level: initialData?.reorder_level || null, // Use null for optional number
    status: initialData?.status || 'active',
    inventory: {
      currentStock: initialData?.inventory?.currentStock || 0,
      location: initialData?.inventory?.location || '',
    },
  });

  // Removed useEffect for initialData as it's handled in useState initialization now

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const debouncedSearch = useRef(
    debounce(async (query: string) => {
      if (query.length < 2) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      try {
        setIsSearching(true);
        // Call the updated searchParts service function from MongoDB service
        const result = await searchParts({ query }); // Pass query in options object

        // Explicitly cast the parts array to SearchResult[] to resolve potential type inference issues
        const searchResultsData = (result?.parts as SearchResult[]) || [];
        setSearchResults(searchResultsData);
        setShowResults(true);
      } catch (error) {
        console.error('Error searching parts:', error);
      } finally {
        setIsSearching(false);
      }
    }, 300)
  ).current;

  useEffect(() => {
    debouncedSearch(searchQuery);

    // Cleanup debounced function on component unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handlePartSelect = (result: SearchResult) => {
    // Update formData based on the selected search result and new schema
    setFormData({
      _id: result._id,
      name: result.name,
      description: result.description || '',
      technical_specs: result.technical_specs || '',
      status: result.status || 'active',
      // Assuming is_manufactured and reorder_level are not returned by search, keep existing or default
      is_manufactured: formData.is_manufactured,
      reorder_level: formData.reorder_level,
      inventory: {
        currentStock: result.inventory?.currentStock || 0,
        location: result.inventory?.location || '',
      },
    });

    setSearchQuery(''); // Clear search query
    setShowResults(false); // Hide results dropdown
  };

  // Removed duplicate handleChange definition
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked; // For checkboxes

    // Handle nested inventory fields
    if (name.startsWith('inventory.')) {
      const inventoryField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        inventory: {
          ...prev.inventory,
          [inventoryField]: type === 'number' ? parseFloat(value) || 0 : value,
        },
      }));
    } else {
      // Handle top-level fields
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : (type === 'number' ? parseFloat(value) || 0 : value),
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Main form element with tabs
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{title}</h2>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
        >
          <X size={20} className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Search for existing parts */}
      <div ref={searchContainerRef} className="mb-6 relative">
        <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-2">
          <Search size={18} className="text-gray-500 dark:text-gray-400 mr-2" />
          <input
            type="text"
            placeholder="Search for existing parts..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="bg-transparent border-none outline-none w-full text-gray-700 dark:text-gray-200"
          />
          {isSearching && <Loader size={18} className="text-blue-500 animate-spin" />}
        </div>

        {/* Search results dropdown */}
        <AnimatePresence>
          {showResults && searchResults.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md overflow-hidden border border-gray-200 dark:border-gray-700"
            >
              <div className="max-h-60 overflow-y-auto">
                {searchResults.map((result) => (
                  <div
                    key={result._id} // Use _id as key
                    className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700"
                    onClick={() => handlePartSelect(result)}
                  >
                    <div className="font-medium text-gray-800 dark:text-gray-200">
                      {result.name} ({result._id}) {/* Display name and _id */}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Status: {result.status || 'N/A'} • Stock: {result.inventory?.currentStock ?? 'N/A'}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Tab navigation */}
      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'general'
              ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
              : 'text-gray-500 dark:text-gray-400'
          }`}
          onClick={() => setActiveTab('general')}
        >
          General Info
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'inventory'
              ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
              : 'text-gray-500 dark:text-gray-400'
          }`}
          onClick={() => setActiveTab('inventory')}
        >
          Inventory
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'additional'
              ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
              : 'text-gray-500 dark:text-gray-400'
          }`}
          onClick={() => setActiveTab('additional')} // Keep tab, but remove fields below
        >
          Additional Details
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        {/* General Info Tab */}
        {activeTab === 'general' && (
          <div className="space-y-4">
             {/* Part ID (Read-only if editing) */}
             {isEdit && formData._id && (
               <div>
                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                   Part ID
                 </label>
                 <input
                   type="text"
                   value={formData._id}
                   readOnly
                   className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-600 rounded-md text-gray-400"
                 />
               </div>
             )}
            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Part Name*
              </label>
              <input
                type="text"
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                required
              />
            </div>
            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                rows={3}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
              />
            </div>
             {/* Technical Specs */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Technical Specifications
              </label>
              <textarea
                name="technical_specs"
                value={formData.technical_specs || ''}
                onChange={handleChange}
                rows={3}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
              />
            </div>
             {/* Is Manufactured & Status */}
            <div className="grid grid-cols-2 gap-4">
               <div>
                 <label className="flex items-center space-x-2">
                   <input
                     type="checkbox"
                     name="is_manufactured"
                     checked={formData.is_manufactured || false}
                     onChange={handleChange}
                     className="rounded border-gray-300 dark:border-gray-700 dark:bg-gray-800 text-blue-600 focus:ring-blue-500"
                   />
                   <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                     Is Manufactured In-House?
                   </span>
                 </label>
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                   Status*
                 </label>
                 <select
                   name="status"
                   value={formData.status || 'active'}
                   onChange={handleChange}
                   className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                   required
                 >
                   <option value="active">Active</option>
                   <option value="inactive">Inactive</option>
                   <option value="obsolete">Obsolete</option>
                   {/* Add other relevant statuses */}
                 </select>
               </div>
             </div>
            {/* Removed outdated fields: assemblyStage, manufacturer_part_number, barcode_sku */}
          </div>
        )}

        {/* Inventory Tab */}
        {activeTab === 'inventory' && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4"> {/* Adjusted grid */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Current Stock
                </label>
                <input
                  type="number"
                  name="inventory.currentStock" // Nested name
                  value={formData.inventory?.currentStock ?? 0} // Use ?? for default
                  onChange={handleChange}
                  min="0"
                  className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Reorder Level
                </label>
                <input
                  type="number"
                  name="reorder_level"
                  value={formData.reorder_level ?? ''} // Use empty string for optional number
                  onChange={handleChange}
                  min="0"
                  className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Location
              </label>
              <input
                type="text"
                name="inventory.location"
                value={formData.inventory?.location || ''}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md"
              />
            </div>
          </div>
        )}

        {/* Additional Details Tab - Content removed as fields are deprecated */}
        {activeTab === 'additional' && (
           <div className="text-gray-600 dark:text-gray-400">
             Fields previously in this section (e.g., dimensions, weight) are no longer part of the core Part schema.
           </div>
        )}

        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 mr-4 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            {isEdit ? 'Update Part' : 'Add Part'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EnhancedPartForm;
