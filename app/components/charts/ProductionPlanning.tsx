"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, Users, ArrowRight, AlertTriangle } from 'lucide-react';
import { WorkOrder, Product } from '@/app/types';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Props for the ProductionPlanning component
 */
interface ProductionPlanningProps {
  /** List of work orders to display */
  workOrders: WorkOrder[];
  /** List of products to check for material shortages */
  products: Product[];
}

/**
 * Component that displays production planning information including resource utilization,
 * upcoming deadlines, and material shortages
 */
const ProductionPlanning: React.FC<ProductionPlanningProps> = ({ workOrders, products }) => {
  const { theme } = useTheme();

  /**
   * Resource utilization percentages for different resource types
   * In a real implementation, this would be calculated from actual data
   */
  const resourceUtilization = {
    machinery: 75, // 75% utilization
    labor: 60,     // 60% utilization
    materials: 85  // 85% utilization
  };

  /**
   * Overall production efficiency percentage
   * In a real implementation, this would be calculated from actual data
   */
  const productionEfficiency = 82; // 82% efficiency

  /**
   * Calculate upcoming deadlines from work orders
   * Filters out completed and cancelled work orders
   * Sorts by end date (earliest first)
   * Takes the top 3 upcoming deadlines
   */
  const upcomingDeadlines = (workOrders || [])
    .filter(wo => wo.status !== 'completed' && wo.status !== 'cancelled' && wo.endDate)
    .sort((a, b) => new Date(a.endDate!).getTime() - new Date(b.endDate!).getTime())
    .slice(0, 3);

  /**
   * Calculate material shortages from products
   * Identifies products where current stock is below reorder level
   * Takes the top 3 shortages
   */
  const materialShortages = (products || [])
    .filter(p => (p.currentStock || 0) < (p.reorderLevel || 0))
    .slice(0, 3);

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white dark:bg-card rounded-3xl p-6 shadow-md dark:shadow-gray-900/30"
    >
      <div className="flex justify-between items-start mb-6">
        <h3 className="text-xl font-medium text-gray-800 dark:text-text-primary">Production Planning</h3>
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-8 h-8 bg-gray-200 dark:bg-sidebar rounded-full flex items-center justify-center cursor-pointer"
        >
          <ArrowRight size={16} className="text-gray-600 dark:text-text-secondary" />
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Resource Utilization */}
        <div className="bg-gray-50 dark:bg-sidebar rounded-xl p-4">
          <h4 className="font-medium text-gray-700 dark:text-text-primary mb-3 flex items-center">
            <Users size={18} className="mr-2 text-blue-600 dark:text-blue-400" />
            Resource Utilization
          </h4>
          <div className="space-y-3">
            {Object.entries(resourceUtilization).map(([resource, value]) => (
              <div key={resource} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="capitalize text-gray-600 dark:text-text-secondary">{resource}</span>
                  <span className="font-medium text-gray-800 dark:text-text-primary">{value}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-hover rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      value > 90 ? 'bg-red-500 dark:bg-red-600' :
                      value > 70 ? 'bg-green-500 dark:bg-green-600' :
                      'bg-blue-500 dark:bg-blue-600'
                    }`}
                    style={{ width: `${value}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-3 border-t border-gray-200 dark:border-border">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-text-secondary">Overall Efficiency</span>
              <span className="font-medium text-green-600 dark:text-green-400">{productionEfficiency}%</span>
            </div>
          </div>
        </div>

        {/* Upcoming Deadlines */}
        <div className="bg-gray-50 dark:bg-sidebar rounded-xl p-4">
          <h4 className="font-medium text-gray-700 dark:text-text-primary mb-3 flex items-center">
            <Calendar size={18} className="mr-2 text-blue-600 dark:text-blue-400" />
            Upcoming Deadlines
          </h4>
          {upcomingDeadlines.length > 0 ? (
            <div className="space-y-3">
              {upcomingDeadlines.map((wo) => (
                <div key={wo.id} className="flex items-start">
                  <div className={`w-2 h-2 mt-1.5 rounded-full ${
                    wo.priority === 'urgent' ? 'bg-red-500' :
                    wo.priority === 'high' ? 'bg-orange-500' :
                    wo.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  } mr-2`}></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800 dark:text-text-primary truncate">{wo.description}</p>
                    <div className="flex items-center text-xs text-gray-500 dark:text-text-secondary">
                      <Clock size={12} className="mr-1" />
                      {new Date(wo.endDate!).toLocaleDateString()}
                    </div>
                  </div>
                  <div className={`px-2 py-1 text-xs rounded-full capitalize ${
                    wo.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                    wo.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400' :
                    wo.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  }`}>
                    {wo.priority || 'standard'}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500 dark:text-text-secondary">No upcoming deadlines</p>
          )}
        </div>

        {/* Material Shortages */}
        <div className="bg-gray-50 dark:bg-sidebar rounded-xl p-4 md:col-span-2">
          <h4 className="font-medium text-gray-700 dark:text-text-primary mb-3 flex items-center">
            <AlertTriangle size={18} className="mr-2 text-orange-500 dark:text-orange-400" />
            Material Shortages
          </h4>
          {materialShortages.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {materialShortages.map((product) => (
                <div key={product.id} className="bg-white dark:bg-card p-3 rounded-lg border border-orange-200 dark:border-orange-900/30">
                  <p className="text-sm font-medium text-gray-800 dark:text-text-primary truncate">{product.name}</p>
                  <div className="mt-2 flex justify-between">
                    <span className="text-xs text-gray-500 dark:text-text-secondary">Current Stock</span>
                    <span className="text-xs font-medium text-red-600 dark:text-red-400">{product.currentStock}</span>
                  </div>
                  <div className="mt-1 flex justify-between">
                    <span className="text-xs text-gray-500 dark:text-text-secondary">Reorder Level</span>
                    <span className="text-xs font-medium dark:text-text-secondary">{product.reorderLevel}</span>
                  </div>
                  <div className="mt-2">
                    <button className="w-full text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-1 rounded hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                      Create Purchase Order
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-500 dark:text-text-secondary">No material shortages detected</p>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ProductionPlanning;