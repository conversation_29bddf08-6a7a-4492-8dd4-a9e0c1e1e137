'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useTheme } from '@/app/context/ThemeContext';

// Define the types from the original component
interface CapacityData {
  department: string;
  currentCapacity: number;
  maxCapacity: number;
  bottleneck: boolean;
}

interface ProductionCapacityProps {
  capacityData: CapacityData[];
  forecastAccuracy: number;
  productionTrend: 'increasing' | 'decreasing' | 'stable';
  bottleneckImpact: number;
}

// Dynamically import the ProductionCapacity component
const DynamicProductionCapacity = dynamic(
  () => import('./ProductionCapacity'),
  {
    loading: () => <LoadingFallback />,
    ssr: false,
  }
);

// Loading fallback component
function LoadingFallback() {
  const { theme } = useTheme();
  
  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-md dark:shadow-gray-900/30 h-[400px]"
    >
      <div className="flex justify-between items-start mb-6">
        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100">Production Capacity</h3>
      </div>
      <div className="flex items-center justify-center h-[300px]">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading capacity data...</p>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Lazy-loaded wrapper for ProductionCapacity
 * This component dynamically imports the actual chart component only when needed
 */
export function LazyProductionCapacity(props: ProductionCapacityProps) {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <DynamicProductionCapacity {...props} />
    </Suspense>
  );
}
