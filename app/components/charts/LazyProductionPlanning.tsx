'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useTheme } from '@/app/context/ThemeContext';
import { WorkOrder, Product } from '@/app/types';

// Dynamically import the ProductionPlanning component
const DynamicProductionPlanning = dynamic(
  () => import('./ProductionPlanning'),
  {
    loading: () => <LoadingFallback />,
    ssr: false,
  }
);

// Loading fallback component
function LoadingFallback() {
  const { theme } = useTheme();
  
  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white dark:bg-card rounded-3xl p-6 shadow-md dark:shadow-gray-900/30 h-[400px]"
    >
      <div className="flex justify-between items-start mb-6">
        <h3 className="text-xl font-medium text-gray-800 dark:text-text-primary">Production Planning</h3>
      </div>
      <div className="flex items-center justify-center h-[300px]">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading production planning data...</p>
        </div>
      </div>
    </motion.div>
  );
}

// Props interface
interface LazyProductionPlanningProps {
  workOrders: WorkOrder[];
  products: Product[];
}

/**
 * Lazy-loaded wrapper for ProductionPlanning
 * This component dynamically imports the actual chart component only when needed
 */
export function LazyProductionPlanning({ workOrders, products }: LazyProductionPlanningProps) {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <DynamicProductionPlanning workOrders={workOrders} products={products} />
    </Suspense>
  );
}
