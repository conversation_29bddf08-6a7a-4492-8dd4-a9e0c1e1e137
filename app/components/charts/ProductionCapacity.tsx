"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, TrendingUp, AlertCircle, ArrowRight } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';

interface CapacityData {
  department: string;
  currentCapacity: number;
  maxCapacity: number;
  bottleneck: boolean;
}

interface ProductionCapacityProps {
  capacityData: CapacityData[];
  forecastAccuracy: number;
  productionTrend: 'increasing' | 'decreasing' | 'stable';
  bottleneckImpact: number;
}

const ProductionCapacity: React.FC<ProductionCapacityProps> = ({
  capacityData,
  forecastAccuracy,
  productionTrend,
  bottleneckImpact
}) => {
  const { theme } = useTheme();

  // Guard against undefined or empty data
  if (!capacityData || capacityData.length === 0) {
    // Render a placeholder or loading state, or return null
    // For now, just return null to avoid errors
    return null;
  }

  // Calculate overall capacity utilization
  const overallUtilization = capacityData.reduce((sum, item) => sum + (item.currentCapacity / item.maxCapacity), 0) / capacityData.length * 100;

  // Identify bottlenecks
  const bottlenecks = capacityData.filter(item => item.bottleneck);

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-md dark:shadow-gray-900/30"
    >
      <div className="flex justify-between items-start mb-6">
        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100">Production Capacity</h3>
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center cursor-pointer"
        >
          <ArrowRight size={16} className="text-gray-600 dark:text-gray-400" />
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Overall Capacity Utilization */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
          <h4 className="font-medium text-gray-700 dark:text-gray-200 mb-3 flex items-center">
            <BarChart size={18} className="mr-2 text-purple-600 dark:text-purple-400" />
            Capacity Utilization
          </h4>
          <div className="flex items-center justify-center py-4">
            <div className="relative w-32 h-32">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  className="text-gray-200 dark:text-gray-600"
                  strokeWidth="10"
                  stroke="currentColor"
                  fill="transparent"
                  r="40"
                  cx="50"
                  cy="50"
                />
                <circle
                  className={`${
                    overallUtilization > 90 ? 'text-red-500 dark:text-red-400' :
                    overallUtilization > 75 ? 'text-orange-500 dark:text-orange-400' :
                    'text-green-500 dark:text-green-400'
                  }`}
                  strokeWidth="10"
                  strokeDasharray={`${overallUtilization * 2.51} 251`}
                  strokeLinecap="round"
                  stroke="currentColor"
                  fill="transparent"
                  r="40"
                  cx="50"
                  cy="50"
                  transform="rotate(-90 50 50)"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold dark:text-gray-100">{Math.round(overallUtilization)}%</span>
              </div>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Forecast Accuracy</span>
              <span className="font-medium dark:text-gray-200">{forecastAccuracy}%</span>
            </div>
            <div className="flex items-center justify-between text-sm mt-2">
              <span className="text-gray-600 dark:text-gray-400">Production Trend</span>
              <span className={`font-medium flex items-center ${
                productionTrend === 'increasing' ? 'text-green-600 dark:text-green-400' :
                productionTrend === 'decreasing' ? 'text-red-600 dark:text-red-400' :
                'text-blue-600 dark:text-blue-400'
              }`}>
                {productionTrend.charAt(0).toUpperCase() + productionTrend.slice(1)}
                {productionTrend === 'increasing' && <TrendingUp size={14} className="ml-1" />}
              </span>
            </div>
          </div>
        </div>

        {/* Department Capacity */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
          <h4 className="font-medium text-gray-700 dark:text-gray-200 mb-3">Department Capacity</h4>
          <div className="space-y-3">
            {capacityData.map((item) => (
              <div key={item.department} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className={`${item.bottleneck ? 'font-medium text-red-600 dark:text-red-400 flex items-center' : 'dark:text-gray-300'}`}>
                    {item.bottleneck && <AlertCircle size={14} className="mr-1" />}
                    {item.department}
                  </span>
                  <span className="font-medium dark:text-gray-200">
                    {Math.round(item.currentCapacity / item.maxCapacity * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      item.bottleneck ? 'bg-red-500 dark:bg-red-600' :
                      (item.currentCapacity / item.maxCapacity) > 0.9 ? 'bg-orange-500 dark:bg-orange-600' :
                      'bg-blue-500 dark:bg-blue-600'
                    }`}
                    style={{ width: `${(item.currentCapacity / item.maxCapacity) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottleneck Analysis */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 md:col-span-2">
          <h4 className="font-medium text-gray-700 dark:text-gray-200 mb-3 flex items-center">
            <AlertCircle size={18} className="mr-2 text-red-500 dark:text-red-400" />
            Bottleneck Analysis
          </h4>
          {bottlenecks.length > 0 ? (
            <div>
              <div className="mb-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Estimated Impact</span>
                  <span className="text-sm font-medium text-red-600 dark:text-red-400">-{bottleneckImpact}% Production</span>
                </div>
              </div>
              <div className="space-y-3">
                {bottlenecks.map((item) => (
                  <div key={item.department} className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-red-200 dark:border-red-900/30">
                    <div className="flex justify-between items-center">
                      <h5 className="font-medium text-gray-800 dark:text-gray-200">{item.department}</h5>
                      <span className="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 rounded-full text-xs">Bottleneck</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                      Operating at {Math.round(item.currentCapacity / item.maxCapacity * 100)}% capacity
                    </p>
                    <div className="mt-3 flex space-x-2">
                      <button className="flex-1 text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-1 px-2 rounded hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                        Optimize
                      </button>
                      <button className="flex-1 text-xs bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 py-1 px-2 rounded hover:bg-green-100 dark:hover:bg-green-900/50 transition-colors">
                        Add Capacity
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-400">No bottlenecks detected in the production line</p>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ProductionCapacity;