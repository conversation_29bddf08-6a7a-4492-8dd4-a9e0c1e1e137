"use client";

import React, { useState, useEffect, useRef } from 'react';
import { debounce, throttle, createDebouncedSearch } from '@/app/lib/utils';
import { Input } from '@/app/components/ui/input';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { InfoIcon, AlertCircle } from 'lucide-react';

export function DebounceDemo() {
  // State for the demos
  const [debounceInput, setDebounceInput] = useState('');
  const [debounceOutput, setDebounceOutput] = useState('');
  const [debounceCalls, setDebounceCalls] = useState(0);
  const [debounceLastCall, setDebounceLastCall] = useState('');

  const [throttleInput, setThrottleInput] = useState('');
  const [throttleOutput, setThrottleOutput] = useState('');
  const [throttleCalls, setThrottleCalls] = useState(0);
  const [throttleLastCall, setThrottleLastCall] = useState('');

  const [searchInput, setSearchInput] = useState('');
  const [searchOutput, setSearchOutput] = useState('');
  const [searchMessage, setSearchMessage] = useState('');
  const [searchCalls, setSearchCalls] = useState(0);

  // Create debounced function
  const debouncedFunction = useRef(
    debounce((value: string) => {
      setDebounceOutput(value);
      setDebounceCalls(prev => prev + 1);
      setDebounceLastCall(new Date().toLocaleTimeString());
    }, 500)
  ).current;

  // Create throttled function
  const throttledFunction = useRef(
    throttle((value: string) => {
      setThrottleOutput(value);
      setThrottleCalls(prev => prev + 1);
      setThrottleLastCall(new Date().toLocaleTimeString());
    }, 500)
  ).current;

  // Create debounced search function
  const debouncedSearch = useRef(
    createDebouncedSearch(
      (query: string) => {
        setSearchOutput(query);
        setSearchCalls(prev => prev + 1);
        setSearchMessage(`Search executed for: "${query}"`);
      },
      {
        wait: 500,
        minLength: 3,
        onEmpty: () => {
          setSearchOutput('');
          setSearchMessage('Search query is empty');
        },
        onTooShort: (query) => {
          setSearchOutput('');
          setSearchMessage(`Please type at least 3 characters (${query.length}/3)`);
        }
      }
    )
  ).current;

  // Handle input changes
  const handleDebounceInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDebounceInput(value);
    debouncedFunction(value);
  };

  const handleThrottleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setThrottleInput(value);
    throttledFunction(value);
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInput(value);
    debouncedSearch(value);
  };

  // Cancel debounced functions on unmount
  useEffect(() => {
    return () => {
      debouncedFunction.cancel();
      throttledFunction.cancel();
      debouncedSearch.cancel();
    };
  }, [debouncedFunction, throttledFunction, debouncedSearch]);

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Utility Functions Demo</h2>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          Demonstration of the debounce, throttle, and debounced search utility functions.
        </p>
      </div>

      <Alert>
        <InfoIcon className="h-4 w-4" />
        <AlertTitle>Utility Functions</AlertTitle>
        <AlertDescription>
          These utility functions help optimize performance by controlling how frequently functions are called.
          See <code>app/lib/utils.ts</code> for implementation details.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="debounce" className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="debounce">Debounce</TabsTrigger>
          <TabsTrigger value="throttle">Throttle</TabsTrigger>
          <TabsTrigger value="search">Debounced Search</TabsTrigger>
        </TabsList>

        <TabsContent value="debounce">
          <Card>
            <CardHeader>
              <CardTitle>Debounce Demo</CardTitle>
              <CardDescription>
                Debounce delays function execution until after a specified wait time has elapsed since the last call.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Input (try typing quickly)</label>
                <Input
                  value={debounceInput}
                  onChange={handleDebounceInputChange}
                  placeholder="Type something..."
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Output (500ms delay)</label>
                  <div className="p-2 border rounded-md bg-gray-50 dark:bg-gray-800 min-h-10">
                    {debounceOutput || <span className="text-gray-400">Waiting for input...</span>}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Stats</label>
                  <div className="p-2 border rounded-md bg-gray-50 dark:bg-gray-800 min-h-10">
                    <p>Function calls: {debounceCalls}</p>
                    <p>Last call: {debounceLastCall || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                variant="outline" 
                onClick={() => debouncedFunction.cancel()}
                className="mr-2"
              >
                Cancel Pending
              </Button>
              <Button 
                variant="outline" 
                onClick={() => debouncedFunction.flush()}
              >
                Flush (Execute Now)
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="throttle">
          <Card>
            <CardHeader>
              <CardTitle>Throttle Demo</CardTitle>
              <CardDescription>
                Throttle limits function execution to at most once per specified time period.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Input (try typing quickly)</label>
                <Input
                  value={throttleInput}
                  onChange={handleThrottleInputChange}
                  placeholder="Type something..."
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Output (max once per 500ms)</label>
                  <div className="p-2 border rounded-md bg-gray-50 dark:bg-gray-800 min-h-10">
                    {throttleOutput || <span className="text-gray-400">Waiting for input...</span>}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Stats</label>
                  <div className="p-2 border rounded-md bg-gray-50 dark:bg-gray-800 min-h-10">
                    <p>Function calls: {throttleCalls}</p>
                    <p>Last call: {throttleLastCall || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                variant="outline" 
                onClick={() => throttledFunction.cancel()}
              >
                Cancel Pending
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="search">
          <Card>
            <CardHeader>
              <CardTitle>Debounced Search Demo</CardTitle>
              <CardDescription>
                Debounced search combines debouncing with minimum query length validation.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Search Query (min 3 characters)</label>
                <Input
                  value={searchInput}
                  onChange={handleSearchInputChange}
                  placeholder="Search..."
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Search Results</label>
                  <div className="p-2 border rounded-md bg-gray-50 dark:bg-gray-800 min-h-10">
                    {searchOutput ? (
                      <p>Results for: "{searchOutput}"</p>
                    ) : (
                      <span className="text-gray-400">No results</span>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <div className="p-2 border rounded-md bg-gray-50 dark:bg-gray-800 min-h-10">
                    <p>{searchMessage || 'Ready to search'}</p>
                    <p>Function calls: {searchCalls}</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                variant="outline" 
                onClick={() => debouncedSearch.cancel()}
                className="mr-2"
              >
                Cancel Pending
              </Button>
              <Button 
                variant="outline" 
                onClick={() => debouncedSearch.flush()}
              >
                Flush (Execute Now)
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
