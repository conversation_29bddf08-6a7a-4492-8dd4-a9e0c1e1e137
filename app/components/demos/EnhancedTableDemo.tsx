"use client";

import React, { useState } from "react";
import { <PERSON>, Pencil, Trash2, MoreHorizontal } from "lucide-react";
import { EnhancedTable, ColumnDef, SortState, PaginationState } from "@/app/components/ui/enhanced-table";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";

// Sample data type
interface Product {
  id: string;
  name: string;
  stock: number;
  supplier: string;
  reorderLevel: number;
  status: "in-stock" | "low-stock" | "out-of-stock";
}

// Sample data
const sampleProducts: Product[] = Array.from({ length: 50 }).map((_, i) => {
  const stock = Math.floor(Math.random() * 100);
  const reorderLevel = Math.floor(Math.random() * 30);
  let status: "in-stock" | "low-stock" | "out-of-stock";
  
  if (stock === 0) {
    status = "out-of-stock";
  } else if (stock < reorderLevel) {
    status = "low-stock";
  } else {
    status = "in-stock";
  }
  
  return {
    id: `PROD-${(i + 1).toString().padStart(4, "0")}`,
    name: `Product ${i + 1}`,
    stock,
    supplier: `Supplier ${Math.floor(i / 10) + 1}`,
    reorderLevel,
    status,
  };
});

export function EnhancedTableDemo() {
  // State for server-side sorting and pagination
  const [sortState, setSortState] = useState<SortState>({
    id: "name",
    direction: "asc",
  });
  
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex: 1,
    pageSize: 10,
  });

  // Column definitions
  const columns: ColumnDef<Product>[] = [
    {
      id: "name",
      header: "Name",
      accessorFn: (row) => row.name,
      sortable: true,
    },
    {
      id: "id",
      header: "Product ID",
      accessorFn: (row) => row.id,
      sortable: true,
    },
    {
      id: "stock",
      header: "Stock",
      accessorFn: (row) => row.stock,
      cell: (value) => (
        <span className="font-mono">{value}</span>
      ),
      sortable: true,
    },
    {
      id: "supplier",
      header: "Supplier",
      accessorFn: (row) => row.supplier,
      sortable: true,
      hideOnMobile: true,
    },
    {
      id: "reorderLevel",
      header: "Reorder Level",
      accessorFn: (row) => row.reorderLevel,
      cell: (value) => (
        <span className="font-mono">{value}</span>
      ),
      sortable: true,
      hideOnMobile: true,
    },
    {
      id: "status",
      header: "Status",
      accessorFn: (row) => row.status,
      cell: (value) => {
        switch (value) {
          case "in-stock":
            return <Badge className="bg-green-500">In Stock</Badge>;
          case "low-stock":
            return <Badge className="bg-yellow-500">Low Stock</Badge>;
          case "out-of-stock":
            return <Badge className="bg-red-500">Out of Stock</Badge>;
          default:
            return null;
        }
      },
      sortable: true,
    },
  ];

  // Process data based on sort and pagination (simulating server-side processing)
  const processedData = React.useMemo(() => {
    // Sort data
    const sorted = [...sampleProducts].sort((a, b) => {
      const column = columns.find((col) => col.id === sortState.id);
      if (!column) return 0;
      
      const aValue = column.accessorFn(a);
      const bValue = column.accessorFn(b);
      
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortState.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortState.direction === "asc"
          ? aValue - bValue
          : bValue - aValue;
      }
      
      return 0;
    });
    
    // Paginate data
    const { pageIndex, pageSize } = paginationState;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    
    return sorted.slice(start, end);
  }, [sortState, paginationState, columns]);

  // Render row actions
  const renderRowActions = (row: Product) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => console.log("View product", row.id)}
          className="cursor-pointer"
        >
          <Eye className="mr-2 h-4 w-4" />
          View
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => console.log("Edit product", row.id)}
          className="cursor-pointer"
        >
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => console.log("Delete product", row.id)}
          className="cursor-pointer text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Enhanced Table Demo</h2>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          A standardized table component with sorting, filtering, and pagination capabilities.
        </p>
      </div>

      <div className="space-y-8">
        <div>
          <h3 className="text-lg font-semibold mb-4">Default Table</h3>
          <EnhancedTable
            data={processedData}
            columns={columns}
            totalItems={sampleProducts.length}
            onSortChange={setSortState}
            onPaginationChange={setPaginationState}
            initialSortState={sortState}
            initialPaginationState={paginationState}
            renderRowActions={renderRowActions}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Simple Table</h3>
          <EnhancedTable
            data={processedData.slice(0, 5)}
            columns={columns}
            simple
            enablePagination={false}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Striped Table</h3>
          <EnhancedTable
            data={processedData.slice(0, 5)}
            columns={columns}
            striped
            enablePagination={false}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Dense Table</h3>
          <EnhancedTable
            data={processedData.slice(0, 5)}
            columns={columns}
            dense
            compact
            enablePagination={false}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Zebra Striped Table</h3>
          <EnhancedTable
            data={processedData.slice(0, 5)}
            columns={columns}
            zebraStriped
            enablePagination={false}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Grid Pattern Table</h3>
          <EnhancedTable
            data={processedData.slice(0, 5)}
            columns={columns}
            gridPattern
            enablePagination={false}
          />
        </div>
      </div>
    </div>
  );
}
