"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, ChevronDown, MoreVertical, Edit, Trash2, Eye } from 'lucide-react';
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';
import { Product } from '@/app/types';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Props for the ProductTable component
 */
interface ProductTableProps {
  /** List of products to display in the table */
  products: Product[];
  /** Function called when a product is edited */
  onEdit?: (product: Product) => void;
  /** Alias for onEdit for backward compatibility */
  onEditProduct?: (product: Product) => void;
  /** Function called when a product is deleted */
  onDelete?: (id: string) => void;
  /** Alias for onDelete for backward compatibility */
  onDeleteProduct?: (id: string) => void;
  /** Current page number for pagination */
  currentPage?: number;
  /** Total number of pages for pagination */
  totalPages?: number;
  /** Function called when page is changed */
  onPageChange?: (pageNumber: number) => void;
}

/**
 * Table component for displaying and managing products
 * Supports sorting, searching, and row actions (edit, delete, view)
 */
const ProductTable: React.FC<ProductTableProps> = ({ products, onEdit, onEditProduct, onDelete, onDeleteProduct }) => {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Product>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showActionMenu, setShowActionMenu] = useState<string | null>(null);

  /**
   * Handles sorting of the product table
   * If the same field is clicked again, toggles the sort direction
   * If a different field is clicked, sorts by that field in ascending order
   * @param field - The field to sort by
   */
  const handleSort = (field: keyof Product) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  /**
   * Filters products based on the search term
   * Searches in name, id, supplier/manufacturer, and description fields
   */
  const filteredProducts = products.filter((p) =>
    p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (p.supplierManufacturer && p.supplierManufacturer.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (p.description && p.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    if (aValue === undefined || bValue === undefined) return 0;

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  // Handle delete confirmation
  const handleDeleteClick = (id: string) => {
    confirmAlert({
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete this part?',
      buttons: [
        {
          label: 'Yes',
          onClick: () => {
            if (onDelete) onDelete(id);
            if (onDeleteProduct) onDeleteProduct(id);
          }
        },
        {
          label: 'No',
          onClick: () => {}
        }
      ]
    });
  };

  // Handle select/deselect all
  const toggleSelectAll = () => {
    if (selectedProducts.length === sortedProducts.length && sortedProducts.length > 0) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(sortedProducts.map((p) => p.id));
    }
  };

  // Handle select/deselect a single product
  const toggleSelectProduct = (id: string) => {
    if (selectedProducts.includes(id)) {
      setSelectedProducts(selectedProducts.filter((pid) => pid !== id));
    } else {
      setSelectedProducts([...selectedProducts, id]);
    }
  };

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden mt-6 shadow-md dark:shadow-gray-900/30"
    >
      {/* Search Bar */}
      <div className="p-4 flex items-center justify-between">
        <div className="relative flex-1 max-w-xl">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400 dark:text-gray-500" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-200 dark:border-gray-700 rounded-full bg-gray-100 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-300 dark:focus:ring-yellow-500 focus:border-transparent dark:text-gray-200"
            placeholder="Search parts by ID, name, supplier, or description"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-yellow-500 rounded border-gray-300 dark:border-gray-600 focus:ring-yellow-400 dark:focus:ring-yellow-500"
                  checked={selectedProducts.length === sortedProducts.length && sortedProducts.length > 0}
                  onChange={toggleSelectAll}
                />
              </th>
              {/* Table Headers */}
              {['id', 'name', 'supplierManufacturer', 'description', 'currentStock', 'reorderLevel'].map((field) => (
                <th
                  key={field}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort(field as keyof Product)}
                >
                  <div className="flex items-center">
                    {field === 'id' ? 'Part ID' :
                     field === 'supplierManufacturer' ? 'Supplier' :
                     field === 'currentStock' ? 'Current Stock' :
                     field === 'reorderLevel' ? 'Reorder Level' :
                     field.charAt(0).toUpperCase() + field.slice(1)}
                    {sortField === field && (
                      <ChevronDown
                        size={14}
                        className={`ml-1 transform ${sortDirection === 'desc' ? 'rotate-180' : ''}`}
                      />
                    )}
                  </div>
                </th>
              ))}
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {sortedProducts.map((product, index) => (
              <motion.tr
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.05 * Math.min(index, 10) }}
                className="hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-yellow-500 rounded border-gray-300 dark:border-gray-600 focus:ring-yellow-400 dark:focus:ring-yellow-500"
                    checked={selectedProducts.includes(product.id)}
                    onChange={() => toggleSelectProduct(product.id)}
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{product.id}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{product.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-200">
                    {product.supplierManufacturer || '-'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-200">
                    {product.description || '-'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div
                    className={`text-sm ${
                      (product.currentStock ?? 0) <= (product.reorderLevel ?? 0)
                        ? 'text-orange-600 dark:text-orange-400 font-medium'
                        : 'text-gray-900 dark:text-gray-200'
                    }`}
                  >
                    {product.currentStock ?? 0}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-200">
                    {product.reorderLevel ?? '-'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium relative">
                  <button
                    className="text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                    onClick={() => setShowActionMenu(showActionMenu === product.id ? null : product.id)}
                  >
                    <MoreVertical size={16} />
                  </button>
                  {showActionMenu === product.id && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.1 }}
                      className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-lg shadow-lg dark:shadow-gray-900/50 z-10 py-1"
                    >
                      <button
                        onClick={() => {
                          if (onEdit) onEdit(product);
                          if (onEditProduct) onEditProduct(product);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-blue-500 hover:text-blue-700 dark:hover:text-blue-300 flex items-center"
                      >
                        <Edit size={16} className="mr-2" />
                        Edit Part
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center"
                        onClick={() => handleDeleteClick(product.id)}
                      >
                        <Trash2 size={14} className="mr-2" />
                        Delete Part
                      </button>
                    </motion.div>
                  )}
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty State */}
      {sortedProducts.length === 0 && (
        <div className="py-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            No parts found matching your search criteria.
          </p>
        </div>
      )}
    </motion.div>
  );
};

export default ProductTable;