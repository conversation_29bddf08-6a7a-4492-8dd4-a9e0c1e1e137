"use client";

import React, { useState } from 'react';
import { useAppContext } from '@/app/context/AppContext';
import { ChevronDown, ChevronUp, AlertCircle } from 'lucide-react';

const DatabaseStatus: React.FC = () => {
  const {
    isLoading,
    error,
    isUsingMockData,
    products,
    stockStatus,
    refreshData
  } = useAppContext();

  const [showEnvDetails, setShowEnvDetails] = useState(false);

  // Check if environment variables are defined
  const mongoDbUri = process.env.MONGODB_URI || '';
  const mongoDbUriMasked = mongoDbUri
    ? `${mongoDbUri.substring(0, 10)}...${mongoDbUri.includes('@') ? mongoDbUri.split('@')[1].substring(0, 15) : ''}`
    : undefined;

  const envStatus = {
    mongoDB: Boolean(process.env.MONGODB_URI),
    devMode: process.env.NODE_ENV === 'development',
    browserSupported: typeof window !== 'undefined' && 'fetch' in window,
    onLine: typeof navigator !== 'undefined' && navigator.onLine
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-4 mb-4">
      <h2 className="text-xl font-semibold mb-2 flex items-center">
        Database Connection Status
        {isUsingMockData && (
          <span className="ml-2 px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs rounded-full flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            Sample Data Mode
          </span>
        )}
      </h2>

      {/* Debug Info */}
      <div className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 p-3 rounded mb-2">
        <p className="font-medium">DEBUG INFO:</p>
        <p className="text-sm">isUsingMockData: {isUsingMockData ? 'true' : 'false'}</p>
        <p className="text-sm">products.length: {products.length}</p>
        <p className="text-sm">stockStatus: {JSON.stringify(stockStatus)}</p>
        <p className="text-sm">First product: {products.length > 0 ? JSON.stringify(products[0]).substring(0, 100) + '...' : 'No products'}</p>
        <button
          onClick={() => {
            console.log('Debug button clicked');
            console.log('isUsingMockData:', isUsingMockData);
            console.log('products:', products);
            console.log('stockStatus:', stockStatus);
            refreshData();
          }}
          className="mt-2 bg-blue-500 text-white px-2 py-1 rounded text-xs"
        >
          Debug Refresh
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
          <span>Connecting to database...</span>
        </div>
      ) : error ? (
        <div>
          <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 p-3 rounded mb-2">
            <p className="font-medium">Using Sample Data - Connection Error</p>
            <p className="text-sm">{error}</p>
            <p className="text-sm mt-2">
              <strong>Note:</strong> The application is showing demo data for preview purposes.
              MongoDB connection issues need to be resolved to see actual inventory data.
            </p>
            <p className="text-sm mt-2">
              Common causes:
              <ul className="list-disc pl-5 mt-1">
                <li>Network connectivity issues</li>
                <li>Firewall blocking MongoDB connections</li>
                <li>SSL/TLS configuration issues</li>
                <li>MongoDB Atlas access restrictions (IP whitelist)</li>
              </ul>
            </p>
          </div>

          <div className="mt-3">
            <button
              onClick={() => setShowEnvDetails(!showEnvDetails)}
              className="flex items-center text-blue-600 dark:text-blue-400 mb-2"
            >
              {showEnvDetails ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-1" />
                  Hide environment details
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  Show environment details
                </>
              )}
            </button>

            {showEnvDetails && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded mb-3 text-xs font-mono">
                <p>MongoDB URI: {envStatus.mongoDB ? 'Defined ✓' : 'Missing ✗'} (But connection failed)</p>
                <p>Development Mode: {envStatus.devMode ? 'Yes' : 'No'}</p>
                <p>Browser Support: {envStatus.browserSupported ? 'Yes ✓' : 'Limited ✗'}</p>
                <p>Network Status: {envStatus.onLine ? 'Online ✓' : 'Offline ✗'}</p>
              </div>
            )}
          </div>

          <button
            onClick={refreshData}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            Retry Connection
          </button>
        </div>
      ) : (
        <div>
          <div className={`p-3 rounded mb-2 ${
            isUsingMockData
              ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
              : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
          }`}>
            <p className="font-medium">
              {isUsingMockData
                ? 'Using Sample Demonstration Data'
                : 'Connected to MongoDB Database - USING REAL DATA'}
            </p>
            <p className="text-sm mt-1">
              {isUsingMockData
                ? 'The application is currently using sample data due to connection issues with MongoDB.'
                : `Loaded ${products.length} products from database. Using real MongoDB data.`}
            </p>
          </div>

          {!isUsingMockData && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
                <p className="font-medium">Product Stats</p>
                <ul className="text-sm mt-2 space-y-1">
                  <li>Total Products: {products.length}</li>
                  <li>Low Stock Items: {stockStatus.lowStock}</li>
                  <li>Out of Stock Items: {stockStatus.outOfStock}</li>
                </ul>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded">
                <p className="font-medium">Connection Info</p>
                <ul className="text-sm mt-2 space-y-1">
                  <li>API Status: Active</li>
                  <li>Last Updated: {new Date().toLocaleTimeString()}</li>
                  <li>
                    <button
                      onClick={refreshData}
                      className="text-blue-500 hover:text-blue-600 underline"
                    >
                      Refresh Data
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DatabaseStatus;