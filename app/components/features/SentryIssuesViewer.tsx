"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { Badge } from '@/app/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table';
import { AlertCircle, RefreshCw, ExternalLink, AlertTriangle, Info } from 'lucide-react';
import { Skeleton } from '@/app/components/ui/skeleton';

interface SentryIssue {
  id: string;
  title: string;
  culprit: string;
  status: string;
  level: string;
  count: number;
  lastSeen: string;
  project: string;
  url?: string;
}

interface SentryIssuesViewerProps {
  projectId?: string;
  limit?: number;
  query?: string;
  showRefreshButton?: boolean;
}

/**
 * SentryIssuesViewer component for displaying Sentry issues
 */
export function SentryIssuesViewer({
  projectId = '****************',
  limit = 10,
  query = 'is:unresolved issue.priority:[high, medium]',
  showRefreshButton = true
}: SentryIssuesViewerProps) {
  const [issues, setIssues] = useState<SentryIssue[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  // Function to fetch issues from Sentry
  const fetchSentryIssues = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // In a real implementation, this would be an API call to a backend endpoint
      // that proxies requests to the Sentry API with proper authentication
      const response = await fetch(`/api/monitoring/sentry-issues?projectId=${projectId}&query=${encodeURIComponent(query)}&limit=${limit}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch Sentry issues: ${response.status}`);
      }

      const data = await response.json();
      
      // Transform the data into our expected format
      const formattedIssues: SentryIssue[] = data.issues.map((issue: any) => ({
        id: issue.id,
        title: issue.title,
        culprit: issue.culprit,
        status: issue.status,
        level: issue.level,
        count: issue.count,
        lastSeen: new Date(issue.lastSeen).toLocaleString(),
        project: issue.project.name,
        url: `https://trendtech-innovations.sentry.io/issues/${issue.id}/`
      }));

      setIssues(formattedIssues);
      setLastUpdated(new Date().toLocaleString());
    } catch (err) {
      console.error('Error fetching Sentry issues:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      
      // For demo purposes, set some sample data if the API isn't implemented yet
      setIssues([
        {
          id: 'IMS-TEJ-H',
          title: 'Error fetching analytics data',
          culprit: 'GET /api/analytics',
          status: 'unresolved',
          level: 'error',
          count: 24,
          lastSeen: new Date().toLocaleString(),
          project: 'Trend_IMS',
          url: 'https://trendtech-innovations.sentry.io/issues/?project=****************&query=is%3Aunresolved%20issue.priority%3A%5Bhigh%2C%20medium%5D'
        },
        {
          id: 'IMS-TEJ-G',
          title: 'Failed to load inventory data',
          culprit: 'GET /api/inventory',
          status: 'unresolved',
          level: 'error',
          count: 12,
          lastSeen: new Date(Date.now() - 3600000).toLocaleString(),
          project: 'Trend_IMS',
          url: 'https://trendtech-innovations.sentry.io/issues/?project=****************&query=is%3Aunresolved%20issue.priority%3A%5Bhigh%2C%20medium%5D'
        },
        {
          id: 'IMS-TEJ-3',
          title: 'SentryExampleAPIError',
          culprit: '/api/sentry-example-api',
          status: 'unresolved',
          level: 'error',
          count: 5,
          lastSeen: new Date(Date.now() - 86400000).toLocaleString(),
          project: 'Trend_IMS',
          url: 'https://trendtech-innovations.sentry.io/issues/?project=****************&query=is%3Aunresolved%20issue.priority%3A%5Bhigh%2C%20medium%5D'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch issues on component mount
  useEffect(() => {
    fetchSentryIssues();
  }, [projectId, query, limit]);

  // Get severity badge variant based on issue level
  const getSeverityBadgeVariant = (level: string) => {
    switch (level.toLowerCase()) {
      case 'fatal':
      case 'error':
        return 'destructive';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'secondary';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Sentry Issues</CardTitle>
            <CardDescription>
              Displaying {isLoading ? '...' : issues.length} unresolved issues with high or medium priority
            </CardDescription>
          </div>
          {lastUpdated && (
            <div className="text-xs text-muted-foreground">
              Last updated: {lastUpdated}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          // Loading skeleton
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            ))}
          </div>
        ) : issues.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Issue</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Count</TableHead>
                <TableHead>Last Seen</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {issues.map((issue) => (
                <TableRow key={issue.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{issue.title}</div>
                      <div className="text-xs text-muted-foreground">{issue.culprit}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{issue.status}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getSeverityBadgeVariant(issue.level)}>
                      {issue.level}
                    </Badge>
                  </TableCell>
                  <TableCell>{issue.count}</TableCell>
                  <TableCell>{issue.lastSeen}</TableCell>
                  <TableCell>
                    <a 
                      href={issue.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-sm font-medium text-primary hover:underline"
                    >
                      View <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>No issues found</AlertTitle>
            <AlertDescription>
              No unresolved issues matching the current filters were found.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>

      {showRefreshButton && (
        <CardFooter>
          <Button 
            variant="outline" 
            onClick={fetchSentryIssues}
            disabled={isLoading}
            className="ml-auto"
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Issues
              </>
            )}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}