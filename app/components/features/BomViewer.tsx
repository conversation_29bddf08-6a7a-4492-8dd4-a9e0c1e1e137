"use client";

import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardT<PERSON>le
} from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { ChevronRight, ChevronDown, AlertCircle, Check } from "lucide-react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";

/**
 * Standardized part item interface to handle various data structures
 */
interface TreePart {
  _id: string;
  part_id: string; // Part number for display
  part_code?: string; // Additional field for part code
  name: string;
  is_assembly: boolean;
  current_stock: number;
  sub_parts?: any[];
  children?: any[];
  partsRequired?: any[];
  components?: any[]; // Add components property to fix linter error
  // Track if this part has been processed to avoid duplicates
  _processed?: boolean;
}

/**
 * Component item interface for BOM viewer
 */
interface ComponentItem {
  part?: TreePart;
  quantity: number;
  parentId?: string | null;
  level: number;
  // Legacy properties for backward compatibility
  part_id?: string | any;
  partId?: string | any;
  children?: any[];
}

/**
 * Props for the BomViewer component
 */
interface BomViewerProps {
  components: ComponentItem[];
  level?: number;
  initiallyExpanded?: boolean;
  parentId?: string | null;
}

export function BomViewer({ components, level = 0, initiallyExpanded = true, parentId = null }: BomViewerProps) {
  const { theme } = useTheme();
  const [expandedNodes, setExpandedNodes] = useState<Record<string, boolean>>({});
  const [loadingNodes, setLoadingNodes] = useState<Record<string, boolean>>({});
  const [assemblyCache, setAssemblyCache] = useState<Record<string, any>>({});
  // Removed unused processedIds state

  // Initialize expanded state for all assemblies
  useEffect(() => {
    const initialState: Record<string, boolean> = {};

    // Create a map to track unique parts by ID to handle duplicates
    const uniqueParts = new Map<string, ComponentItem>();

    components.forEach(component => {
      // First, ensure the component is in the expected format
      let standardizedComponent: ComponentItem;

      if (!component.part) {
        // Handle legacy or non-standard format
        standardizedComponent = standardizePart(
          component.partId || component.part_id || component,
          component.quantity || 1,
          component.level !== undefined ? component.level : level,
          parentId
        );
      } else {
        standardizedComponent = component;
      }

      // Skip if the component doesn't have a valid part
      if (!standardizedComponent.part || !standardizedComponent.part._id) {
        return;
      }

      const partId = standardizedComponent.part._id;

      // Skip if we've already processed this part at this level
      if (uniqueParts.has(partId)) {
        // Update quantity if it's a duplicate
        const existingComponent = uniqueParts.get(partId)!;
        existingComponent.quantity += standardizedComponent.quantity;
        return;
      }

      // Add to unique parts map
      uniqueParts.set(partId, {...standardizedComponent});

      // Determine if this part is an assembly
      const isAssembly = isPartAnAssembly(standardizedComponent.part);

      // Set initial expanded state based on level and initiallyExpanded prop
      initialState[partId] = isAssembly && (level === 0 ? initiallyExpanded : false);
    });

    setExpandedNodes(initialState);
  }, [components, level, initiallyExpanded, parentId]);

  /**
   * Helper function to determine if a part is an assembly
   * Checks various properties to identify assemblies
   */
  const isPartAnAssembly = (part: any): boolean => {
      // Determine if this part is an assembly
        if (!part) return false;

        // Direct assembly flag
        if (part.is_assembly === true || part.isAssembly === true) return true;

        // Check name convention
        if (part.name && (
          part.name.includes('Assembly') ||
          part.name.includes('assembly') ||
          part.name.includes('Shaft')
        )) return true;

        // Check ID convention - check various ID fields
        if (typeof part.part_id === 'string' && part.part_id.startsWith('ASM-')) return true;
        if (typeof part.part_code === 'string' && part.part_code.startsWith('ASM-')) return true;
        if (typeof part.assemblyCode === 'string') return true;
        if (typeof part.assembly_id === 'string') return true;
        if (typeof part._id === 'string' && part._id.startsWith('ASM-')) return true;
        if (typeof part.part_id === 'string' && part.part_id.startsWith('DL.')) return true;
      
        // Check item_type from new schema
        if (part.item_type === 'Assembly') return true;

      // Check for child components in various fields
      if (Array.isArray(part.sub_parts) && part.sub_parts.length > 0) return true;
      if (Array.isArray(part.children) && part.children.length > 0) return true;
      if (Array.isArray(part.partsRequired) && part.partsRequired.length > 0) return true;
      if (Array.isArray(part.components) && part.components.length > 0) return true;

      // Check for nested children in the data structure
      if (part.partId && typeof part.partId === 'object' && Array.isArray(part.children) && part.children.length > 0) {
        return true;
      }
    
      // Check for nested children in item_id (new schema)
      if (part.item_id && typeof part.item_id === 'object' && Array.isArray(part.children) && part.children.length > 0) {
        return true;
      }

      return false;
    };

  /**
   * Helper function to standardize part data structure
   * Handles different data formats and normalizes them
   */
  const standardizePart = (part: any, quantity: number = 1, level: number = 0, parentId: string | null = null): ComponentItem => {
    if (!part) {
      return {
        part: {
          _id: 'unknown',
          part_id: 'unknown',
          name: 'Unknown Part',
          is_assembly: false,
          current_stock: 0
        },
        quantity: quantity,
        level: level,
        parentId: parentId
      };
    }

    // Handle string part IDs
    if (typeof part === 'string') {
      // For string IDs, we'll use it as both _id and part_id (readable number)
      // Later, we'll try to fetch the actual part data
      return {
        part: {
          _id: part,
          part_id: part, // Use the ID as the part number for now
          name: 'Loading...',
          is_assembly: false,
          current_stock: 0
        },
        quantity: quantity,
        level: level,
        parentId: parentId
      };
    }

    // Handle parts with partId property (common in partsRequired arrays - new schema)
    if (part.partId) {
      // Check if partId is a string or object
      const partIdObj = typeof part.partId === 'object' ? part.partId : { _id: part.partId, part_id: part.partId };

      // Check if this part has children (making it an assembly)
      const hasChildren = Array.isArray(part.children) && part.children.length > 0;

      return {
        part: {
          _id: partIdObj._id || partIdObj.part_id || '',
          // Use part number from various possible fields
          part_id: partIdObj.partNumber || partIdObj.part_id || partIdObj._id || '',
          part_code: partIdObj.partNumber || partIdObj.part_code || partIdObj._id || '',
          name: partIdObj.name || 'Unknown Part',
          is_assembly: isPartAnAssembly(partIdObj) || hasChildren,
          current_stock: partIdObj.inventory?.currentStock || partIdObj.current_stock || 0,
          sub_parts: partIdObj.sub_parts || [],
          children: part.children || partIdObj.children || [],
          partsRequired: partIdObj.partsRequired || []
        },
        quantity: part.quantityRequired || part.quantity || quantity,
        level: level,
        parentId: parentId
      };
    }
    
    // Handle parts with item_id property (common in components arrays - new schema)
    if (part.item_id) {
      // Check if item_id is a string or object
      const itemObj = typeof part.item_id === 'object' ? part.item_id : { _id: part.item_id, part_id: part.item_id };

      // Check if this part has children (making it an assembly)
      const hasChildren = Array.isArray(part.children) && part.children.length > 0;
      
      // Check if this is explicitly marked as an Assembly
      const isAssemblyType = part.item_type === 'Assembly';

      return {
        part: {
          _id: itemObj._id || '',
          // For parts collection, _id is the part number
          part_id: itemObj._id || itemObj.part_id || '',
          part_code: itemObj._id || itemObj.partNumber || itemObj.part_code || '',
          name: itemObj.name || 'Unknown Part',
          is_assembly: isAssemblyType || isPartAnAssembly(itemObj) || hasChildren,
          current_stock: itemObj.inventory?.currentStock || itemObj.current_stock || 0,
          sub_parts: itemObj.sub_parts || [],
          children: part.children || itemObj.children || [],
          partsRequired: itemObj.partsRequired || [],
          components: itemObj.components || []
        },
        quantity: part.quantity || quantity,
        level: level,
        parentId: parentId
      };
    }

    // Handle parts with part_id property (common in sub_parts arrays)
    if (part.part_id) {
      // Check if part_id is a string or object
      const partIdObj = typeof part.part_id === 'object' ? part.part_id : { _id: part.part_id, part_id: part.part_id };

      return {
        part: {
          _id: partIdObj._id || '',
          part_id: partIdObj.part_id || partIdObj._id || '',
          part_code: partIdObj.partNumber || partIdObj.part_code || partIdObj._id || '',
          name: partIdObj.name || part.name || 'Unknown Part',
          is_assembly: isPartAnAssembly(partIdObj),
          current_stock: partIdObj.inventory?.currentStock || partIdObj.current_stock || 0,
          sub_parts: partIdObj.sub_parts || [],
          children: part.children || partIdObj.children || [],
          partsRequired: partIdObj.partsRequired || [],
          components: partIdObj.components || []
        },
        quantity: part.quantity || quantity,
        level: level,
        parentId: parentId
      };
    }

    // Handle direct part objects
    return {
      part: {
        _id: part._id || '',
        // For parts collection, _id is the part number in the new schema
        part_id: part.part_id || part._id || '',
        part_code: part._id || part.partNumber || part.part_code || '', 
        name: part.name || 'Unknown Part',
        is_assembly: isPartAnAssembly(part),
        current_stock: part.inventory?.currentStock || part.current_stock || 0,
        sub_parts: part.sub_parts || [],
        children: part.children || [],
        partsRequired: part.partsRequired || [],
        components: part.components || []
      },
      quantity: quantity,
      level: level,
      parentId: parentId
    };
  };

  /**
   * Process child components from any source (partsRequired, children, sub_parts)
   * Returns a standardized array of component items
   */
  const processChildComponents = (sourceArray: any[], level: number = 0, parentId: string | null = null): ComponentItem[] => {
    if (!Array.isArray(sourceArray) || sourceArray.length === 0) return [];

    return sourceArray.map(item => {
      // Skip null or undefined items
      if (!item) return null;

      // Create a standardized component
      let standardizedComponent: ComponentItem;

      // Handle different data structures
      if (item.partId || item.item_id) {
        // This is likely from partsRequired array or components array (new schema)
        standardizedComponent = standardizePart(item, item.quantity || 1, level, parentId);

        // Check if this item has children (making it a sub-assembly)
        if (Array.isArray(item.children) && item.children.length > 0 && standardizedComponent.part) {
          // Process children and attach them to the part
          const childComponents = processChildComponents(
            item.children,
            level + 1,
            standardizedComponent.part._id || parentId
          );

          // Ensure the part has a children array
          if (standardizedComponent.part) {
            if (!standardizedComponent.part.children) {
              standardizedComponent.part.children = [];
            }

            // Add processed children to the part
            standardizedComponent.part.children = childComponents;

            // Mark as assembly since it has children
            standardizedComponent.part.is_assembly = true;
          }
        }

        return standardizedComponent;
      } else if (item.part_id) {
        // This is likely from sub_parts array
        return standardizePart(item, item.quantity || 1, level, parentId);
      } else {
        // Direct part reference
        return standardizePart(item, item.quantity || 1, level, parentId);
      }
    }).filter((item): item is ComponentItem => item !== null); // Type guard to filter out null items
  };

  /**
   * Toggle expansion state for an assembly and fetch its details if needed
   * Handles recursive loading of sub-assemblies
   */
  const toggleExpand = async (partId: string, isAssembly: boolean) => {
    // Skip if not an assembly
    if (!isAssembly) return;

    // Toggle the expanded state
    setExpandedNodes(prev => {
      const newState = { ...prev, [partId]: !prev[partId] };
      return newState;
    });

    // If expanding and we don't have the assembly details yet, fetch them
    if (expandedNodes[partId] !== false && !assemblyCache[partId]) {
      // Mark as loading
      setLoadingNodes(prev => ({ ...prev, [partId]: true }));

      try {
        // Try to fetch from assemblies API first (for sub-assemblies)
        const assemblyResponse = await fetch(`/api/assemblies/${partId}?includeParts=true`);

        if (assemblyResponse.ok) {
          const assemblyData = await assemblyResponse.json();

          if (assemblyData.data) {
            // Format the data to match the expected structure
            const formattedData = {
              ...assemblyData.data,
              sub_parts: []
            };

            // Process partsRequired if it exists
            if (assemblyData.data.partsRequired && Array.isArray(assemblyData.data.partsRequired)) {
              // Get the current part's ID to use as parent ID for children
              const currentPartId = assemblyData.data._id || assemblyData.data.assemblyCode;

              // Process all top-level parts with parent ID and level
              const topLevelParts = processChildComponents(
                assemblyData.data.partsRequired,
                level + 1,
                currentPartId
              );

              // Process children for each top-level part if they exist
              topLevelParts.forEach(topPart => {
                if (topPart.part && topPart.part._id) {
                  // Check if this part has children in the original data
                  // We already checked that topPart.part and topPart.part._id exist
                  const partId = topPart.part!._id;
                  const originalPart = assemblyData.data.partsRequired.find(
                    (p: any) => (p.partId === partId ||
                                 (typeof p.partId === 'object' && p.partId?._id === partId))
                  );

                  if (originalPart && originalPart.children && Array.isArray(originalPart.children)) {
                    // Process children with correct level and parent ID
                    topPart.part.children = processChildComponents(
                      originalPart.children,
                      level + 2, // Children are one level deeper than top-level parts
                      topPart.part._id
                    );
                  }
                }
              });

              formattedData.sub_parts = topLevelParts;

              // Track processed sub-assemblies to avoid duplicates and infinite loops
              const processedSubAssemblies = new Set<string>();

              // Process sub-assemblies recursively
              const processSubAssemblies = async (components: ComponentItem[], currentLevel: number) => {
                if (!Array.isArray(components)) return;

                // Process each component in parallel for better performance
                await Promise.all(components.map(async (component) => {
                  if (!component || !component.part) return;

                  const part = component.part;
                  const subPartId = part._id;

                  // Skip if not an assembly or already processed
                  if (!isPartAnAssembly(part) || processedSubAssemblies.has(subPartId)) return;

                  // Mark as processed to avoid infinite loops
                  processedSubAssemblies.add(subPartId);

                  try {
                    const subAssemblyResponse = await fetch(`/api/assemblies/${subPartId}?includeParts=true`);

                    if (subAssemblyResponse.ok) {
                      const subAssemblyData = await subAssemblyResponse.json();

                      if (subAssemblyData.data && subAssemblyData.data.partsRequired) {
                        // Mark as assembly
                        part.is_assembly = true;

                        // Process child parts with correct level and parent ID
                        const childParts = processChildComponents(
                          subAssemblyData.data.partsRequired,
                          currentLevel + 1,
                          subPartId
                        );

                        // Process nested children if they exist
                                      childParts.forEach(childPart => {
                                        if (childPart.part && (childPart.part._id || childPart.part.part_id)) {
                                          // Check if this part has children in the original data
                                          const childPartId = childPart.part._id || childPart.part.part_id;
                            
                                          // Check in partsRequired (old schema)
                                          let originalChildPart = subAssemblyData.data.partsRequired?.find(
                                            (p: any) => (p.partId === childPartId ||
                                                       (typeof p.partId === 'object' && (p.partId?._id === childPartId || p.partId?.part_id === childPartId)))
                                          );
                            
                                          // If not found, check in components (new schema)
                                          if (!originalChildPart && subAssemblyData.data.components) {
                                            originalChildPart = subAssemblyData.data.components.find(
                                              (p: any) => (p.item_id === childPartId ||
                                                         (typeof p.item_id === 'object' && (p.item_id?._id === childPartId || p.item_id?.part_id === childPartId)))
                                            );
                                          }

                                          if (originalChildPart && originalChildPart.children && Array.isArray(originalChildPart.children)) {
                                            // Process children with correct level and parent ID
                                            childPart.part.children = processChildComponents(
                                              originalChildPart.children,
                                              currentLevel + 2, // Children are one level deeper
                                              childPartId
                                            );
                                          }
                                        }
                                      });

                        part.sub_parts = childParts;

                        // Recursively process nested sub-assemblies
                        await processSubAssemblies(part.sub_parts, currentLevel + 1);
                      }
                    }
                  } catch (error) {
                    console.error(`Error fetching sub-assembly details for ${subPartId}:`, error);
                  }
                }));
              };

              // Start recursive processing of sub-assemblies
              await processSubAssemblies(formattedData.sub_parts, level + 1);
            }

            // Store the processed assembly details
            setAssemblyCache(prev => ({ ...prev, [partId]: formattedData }));
            setLoadingNodes(prev => ({ ...prev, [partId]: false }));
            return;
          }
        }

        // Fallback to parts API if assembly API fails
        // Use includeSub_parts=true to get all sub-parts in a single query
        console.log(`Assembly fetch failed, trying parts API for: ${partId}`);
        const partsResponse = await fetch(`/api/parts/${partId}?includeSub_parts=true`);
        const partsData = await partsResponse.json();

        if (partsData.data) {
          console.log(`Fetched part data for ${partId}:`, partsData.data);
          
          // For parts in the new schema, _id is the part number
          if (partsData.data._id && partsData.data._id.length < 24) {
            console.log(`Part ${partId} has a readable part number as _id: ${partsData.data._id}`);
          }
          
          // Store the part details
          setAssemblyCache(prev => ({ ...prev, [partId]: partsData.data }));
        }
      } catch (error) {
        console.error(`Error fetching assembly details for ${partId}:`, error);
      } finally {
        // Clear loading state
        setLoadingNodes(prev => ({ ...prev, [partId]: false }));
      }
    }
  };

  /**
   * Helper function to determine if a part has sufficient stock
   */
  const hasSufficientStock = (part: TreePart, quantity: number) => {
    if (!part || !part.current_stock) return false;
    return part.current_stock >= quantity;
  };

  /**
   * TreeNode component for recursive rendering of BOM items
   */
  const TreeNode = ({ component, index }: { component: ComponentItem; index: number }) => {
    // Handle case where component might not have part property (legacy format)
    if (!component.part && component.part_id) {
      const partData = typeof component.part_id === 'string'
        ? assemblyCache[component.part_id]
        : component.part_id;

      const standardizedComponent = standardizePart(
        partData || component.part_id,
        component.quantity,
        component.level !== undefined ? component.level : level,
        parentId
      );

      return <TreeNode component={standardizedComponent} index={index} />;
    }

    const part = component.part;

    // Skip invalid components - but show more details about the invalid part
    if (!part || (!part._id && !part.part_id)) {
      console.warn("Invalid part reference:", component);
      return (
        <div className={cn(
          "flex items-center py-2 rounded-md",
          theme === 'light' ? "text-gray-500" : "text-gray-400"
        )}>
          <div className="w-6 h-6 mr-2" />
          <span>Invalid part reference {component.partId ? `(${typeof component.partId === 'string' ? component.partId : 'Object'})` : ''}</span>
        </div>
      );
    }

    // Determine if this part is an assembly
    const isAssembly = isPartAnAssembly(part);

    // Check if this assembly is expanded - use either _id or part_id as the key
    const nodeKey = part._id || part.part_id;
    const isExpanded = expandedNodes[nodeKey] || false;

    // Check if this assembly is currently loading its details
    const isLoading = loadingNodes[nodeKey] || false;

    // Check if this part has sufficient stock
    const hasSufficient = hasSufficientStock(part, component.quantity);

    // Calculate indentation based on level
    const indentLevel = component.level;

    // Get child components from any available source
    const getChildComponents = (): ComponentItem[] => {
      const nodeKey = part._id || part.part_id;
    
      // Check for sub_parts in assembly cache first
      if (assemblyCache[nodeKey]?.sub_parts &&
          Array.isArray(assemblyCache[nodeKey].sub_parts) &&
          assemblyCache[nodeKey].sub_parts.length > 0) {
        return assemblyCache[nodeKey].sub_parts;
      }

      // Check for children in assembly cache
      if (assemblyCache[nodeKey]?.children &&
          Array.isArray(assemblyCache[nodeKey].children) &&
          assemblyCache[nodeKey].children.length > 0) {
        return processChildComponents(
          assemblyCache[nodeKey].children,
          indentLevel + 1,
          nodeKey
        );
      }

      // Check for partsRequired in assembly cache
      if (assemblyCache[nodeKey]?.partsRequired &&
          Array.isArray(assemblyCache[nodeKey].partsRequired) &&
          assemblyCache[nodeKey].partsRequired.length > 0) {
        return processChildComponents(
          assemblyCache[nodeKey].partsRequired,
          indentLevel + 1,
          nodeKey
        );
      }

    // Check for children in part itself
    if (part.children &&
        Array.isArray(part.children) &&
        part.children.length > 0) {
      return processChildComponents(
        part.children,
        indentLevel + 1,
        part._id
      );
    }

    // Check for partsRequired in part itself
    if (part.partsRequired &&
        Array.isArray(part.partsRequired) &&
        part.partsRequired.length > 0) {
      return processChildComponents(
        part.partsRequired,
        indentLevel + 1,
        part._id
      );
    }

    // Check for sub_parts in part itself
    if (part.sub_parts &&
        Array.isArray(part.sub_parts) &&
        part.sub_parts.length > 0) {
      return processChildComponents(
        part.sub_parts,
        indentLevel + 1,
        part._id
      );
    }

    return [];
  };

  return (
    <div className="space-y-1">
      <div
        className={cn(
          "flex items-center py-2 rounded-md transition-colors cursor-pointer",
          theme === 'light'
            ? "hover:bg-gray-100 text-gray-800"
            : "hover:bg-gray-800/50 text-gray-200",
          isAssembly && "font-medium"
        )}
        onClick={() => toggleExpand(nodeKey, isAssembly)}
        style={{ paddingLeft: `${indentLevel * 24 + 8}px` }}
      >
        {/* Expansion indicator for assemblies */}
        {isAssembly ? (
          <div className="w-6 h-6 flex items-center justify-center mr-2">
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </div>
        ) : (
          <div className="w-6 h-6 mr-2" />
        )}

        {/* Part details */}
        <div className="flex-1 flex items-center justify-between">
          <div>
            <span>{part.name || "Unknown Part"}</span>
            <span className="ml-2 text-muted-foreground text-sm">
              ({/* Display part number with preference order */}
              {part.part_code || part.part_id || 
               (typeof part._id === 'string' && part._id.length < 24 ? part._id : 'No ID')})
            </span>
            <span className="mx-2 text-muted-foreground">×</span>
            <span className="font-medium">{component.quantity}</span>
          </div>

          {/* Stock status */}
          <div className="flex items-center">
            {hasSufficient ? (
              <Badge variant="outline" className={cn(
                theme === 'light'
                  ? "bg-green-50 border-green-200 text-green-700"
                  : "bg-green-950 border-green-800 text-green-400"
              )}>
                <Check className="h-3 w-3 mr-1" />
                Stock: {part.current_stock}
              </Badge>
            ) : (
              <Badge variant="outline" className={cn(
                theme === 'light'
                  ? "bg-red-50 border-red-200 text-red-700"
                  : "bg-red-950 border-red-800 text-red-400"
              )}>
                <AlertCircle className="h-3 w-3 mr-1" />
                Stock: {part.current_stock || 0}/{component.quantity}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Render sub-parts recursively if this is an expanded assembly */}
      {isAssembly && isExpanded && (
        <div className="pl-4">
          {isLoading ? (
            <div className={cn(
              "py-2 pl-8",
              theme === 'light' ? "text-gray-500" : "text-gray-400"
            )}>
              Loading assembly components...
            </div>
          ) : (
            <>
              {/* Render child components */}
              {(() => {
                const childComponents = getChildComponents();

                if (childComponents.length > 0) {
                  return (
                    <>
                      <div className={cn(
                        "py-2 pl-2 text-xs",
                        theme === 'light' ? "text-gray-500" : "text-gray-400"
                      )}>
                        Sub-assembly components:
                      </div>
                      <div className="space-y-1">
                        {childComponents.map((childComponent, childIndex) => (
                          <TreeNode
                            key={`${childComponent.part?._id || childIndex}-${childIndex}`}
                            component={childComponent}
                            index={childIndex}
                          />
                        ))}
                      </div>
                    </>
                  );
                }

                // No child components found
                return (
                  <>
                    {/* If we're still loading or haven't tried to fetch yet, try to fetch the assembly data */}
                    {!isLoading ? (
                      <div
                        className={cn(
                          "py-2 pl-8 cursor-pointer hover:underline",
                          theme === 'light' ? "text-blue-600" : "text-blue-400"
                        )}
                        onClick={() => toggleExpand(part._id, true)}
                      >
                        Click to load sub-assembly components
                      </div>
                    ) : (
                      <div className={cn(
                        "py-2 pl-8",
                        theme === 'light' ? "text-gray-500" : "text-gray-400"
                      )}>
                        Loading assembly components...
                      </div>
                    )}

                    {!isLoading && (
                      <div className={cn(
                        "py-2 pl-8",
                        theme === 'light' ? "text-gray-500" : "text-gray-400"
                      )}>
                        No components found in this assembly
                      </div>
                    )}
                  </>
                );
              })()}
            </>
          )}
        </div>
      )}
    </div>
  );
};

// Removed unused renderComponentRow function

// Process components to handle duplicates at the same level
const processComponents = () => {
  // Create a map to track unique parts by ID to handle duplicates
  const uniqueParts = new Map<string, ComponentItem>();

  components.forEach(component => {
    // Get the part ID, handling both new and legacy formats
    const partId = component.part ? component.part._id : (
      typeof component.part_id === 'string' ? component.part_id : component.part_id?._id
    );

    if (!partId) return; // Skip invalid components

    // If we've already seen this part at this level, update its quantity
    if (uniqueParts.has(partId)) {
      const existingComponent = uniqueParts.get(partId)!;
      existingComponent.quantity += component.quantity;
    } else {
      // Otherwise add it to our map
      // Convert to new format if needed
      if (!component.part && (component.part_id || typeof component === 'object')) {
        const partData = typeof component.part_id === 'string'
          ? assemblyCache[component.part_id]
          : component.part_id;

        const standardizedComponent = standardizePart(
          partData || component.part_id,
          component.quantity,
          component.level !== undefined ? component.level : level,
          parentId
        );

        uniqueParts.set(partId, standardizedComponent);
      } else {
        uniqueParts.set(partId, {...component});
      }
    }
  });

  // Convert the map back to an array
  return Array.from(uniqueParts.values());
};

// Get processed components with duplicates combined
const processedComponents = processComponents();

return (
  <Card className={cn(
    "w-full",
    theme === 'light' ? "bg-white border-gray-200 shadow-sm" : ""
  )}>
    <CardHeader className={theme === 'light' ? "border-b border-gray-100" : ""}>
      <CardTitle className={theme === 'light' ? "text-gray-800" : ""}>Bill of Materials</CardTitle>
    </CardHeader>
    <CardContent>
      {processedComponents.length === 0 ? (
        <div className={cn(
          "py-4 text-center",
          theme === 'light' ? "text-gray-500" : "text-muted-foreground"
        )}>
          No components found for this assembly.
        </div>
      ) : (
        <div className="space-y-1">
          {processedComponents.map((component, index) => (
            <TreeNode key={`${component.part?._id || index}-${index}`} component={component} index={index} />
          ))}
        </div>
      )}
    </CardContent>
  </Card>
);