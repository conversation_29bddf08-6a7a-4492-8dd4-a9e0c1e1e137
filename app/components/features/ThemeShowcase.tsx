"use client";

import React from 'react';
import { Bell, BarChart2, Users } from 'lucide-react';

const ThemeShowcase: React.FC = () => {
  return (
    <div className="border rounded-lg overflow-hidden">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
        {/* Button Showcase */}
        <div className="flex flex-col space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Buttons</h4>

          <button className="px-4 py-2 bg-blue-500 text-white rounded-lg shadow hover:bg-blue-600 transition-colors">
            Primary Button
          </button>

          <button className="px-4 py-2 bg-white dark:bg-dark-800 text-gray-800 dark:text-gray-200 border border-gray-300 dark:border-dark-border rounded-lg shadow hover:bg-gray-50 dark:hover:bg-dark-600 transition-colors">
            Secondary Button
          </button>

          <button className="px-4 py-2 bg-red-500 text-white rounded-lg shadow hover:bg-red-600 transition-colors">
            Danger Button
          </button>
        </div>

        {/* Card Showcase */}
        <div className="flex flex-col space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cards</h4>

          <div className="bg-white dark:bg-dark-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-dark-border-subtle">
            <div className="flex items-center justify-between mb-2">
              <h5 className="font-medium text-gray-800 dark:text-dark-text-headings">Card Title</h5>
              <span className="text-xs text-gray-500 dark:text-dark-text-secondary">Today</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-dark-text-primary">
              This is a sample card with some content inside.
            </p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg shadow-sm border border-blue-200 dark:border-blue-800">
            <div className="flex items-center mb-2">
              <Bell size={16} className="text-blue-500 mr-2" />
              <h5 className="font-medium text-blue-700 dark:text-blue-300">Notification</h5>
            </div>
            <p className="text-sm text-blue-600 dark:text-blue-400">
              You have a new message in your inbox.
            </p>
          </div>
        </div>

        {/* Charts & Components */}
        <div className="flex flex-col space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Components</h4>

          <div className="flex items-center space-x-2 p-2">
            <div className="w-4 h-4 rounded-full bg-blue-500"></div>
            <div className="w-4 h-4 rounded-full bg-green-500"></div>
            <div className="w-4 h-4 rounded-full bg-yellow-500"></div>
            <div className="w-4 h-4 rounded-full bg-red-500"></div>
          </div>

          <div className="bg-white dark:bg-dark-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-dark-border-subtle">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <BarChart2 size={16} className="text-blue-500 dark:text-blue-400 mr-2" />
                <span className="text-sm font-medium text-gray-700 dark:text-dark-text-primary">Analytics</span>
              </div>
              <div className="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                +24%
              </div>
            </div>
            <div className="flex h-8 space-x-1">
              <div className="w-1/5 bg-blue-200 dark:bg-blue-700/60 rounded"></div>
              <div className="w-2/5 bg-blue-300 dark:bg-blue-600/60 rounded"></div>
              <div className="w-1/5 bg-blue-400 dark:bg-blue-500/60 rounded"></div>
              <div className="w-1/5 bg-blue-500 dark:bg-blue-400/60 rounded"></div>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-700 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-dark-border-subtle">
            <div className="flex items-center mb-2">
              <Users size={16} className="text-purple-500 dark:text-purple-400 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-dark-text-primary">Team</span>
            </div>
            <div className="flex -space-x-2 overflow-hidden">
              <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 border-2 border-gray-100 dark:border-gray-800 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-300">
                JD
              </div>
              <div className="w-8 h-8 rounded-full bg-blue-300 dark:bg-blue-600 border-2 border-gray-100 dark:border-gray-800 flex items-center justify-center text-xs font-medium text-blue-600 dark:text-blue-300">
                MK
              </div>
              <div className="w-8 h-8 rounded-full bg-green-300 dark:bg-green-600 border-2 border-gray-100 dark:border-gray-800 flex items-center justify-center text-xs font-medium text-green-600 dark:text-green-300">
                TP
              </div>
              <div className="w-8 h-8 rounded-full bg-purple-300 dark:bg-purple-600 border-2 border-gray-100 dark:border-gray-800 flex items-center justify-center text-xs font-medium text-purple-600 dark:text-purple-300">
                +5
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeShowcase;