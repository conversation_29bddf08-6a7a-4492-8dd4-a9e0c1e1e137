"use client";

import { useState, useEffect, ChangeEvent } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/app/components/ui/table";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/app/components/ui/select";
import { Badge } from "@/app/components/ui/badge";
import { Plus, Trash2, AlertCircle, Check } from "lucide-react";
import { toast } from "sonner";

interface ProductComponent {
  part_id: string;
  quantity: number;
  _id?: string;
  part_details?: any;
}

interface Part {
  _id: string;
  part_id: string;
  name: string;
  current_stock: number;
  is_assembly: boolean;
  [key: string]: any;
}

interface ProductComponentsListProps {
  components: ProductComponent[];
  onChange: (components: ProductComponent[]) => void;
  disabled?: boolean;
}

export function ProductComponentsList({
  components,
  onChange,
  disabled = false
}: ProductComponentsListProps) {
  const [partsOptions, setPartsOptions] = useState<Part[]>([]);
  const [editableComponents, setEditableComponents] = useState<ProductComponent[]>([]);

  // Load parts data on component mount
  useEffect(() => {
    const fetchParts = async () => {
      try {
        const response = await fetch("/api/parts");
        const data = await response.json();
        if (data.data) {
          setPartsOptions(data.data);
        }
      } catch (error) {
        console.error("Error fetching parts:", error);
        toast.error("Failed to load parts data");
      }
    };

    fetchParts();
  }, []);

  // Initialize editable components when props change
  useEffect(() => {
    // Fetch the details for each component
    const loadComponentDetails = async () => {
      try {
        const componentsWithDetails = await Promise.all(
          components.map(async (component) => {
            // If we already have details or component is new (with part_details), just return it
            if (component.part_details) {
              return component;
            }

            try {
              // Use includeSub_parts=true to get all sub-parts in a single query
              const response = await fetch(`/api/parts/${component.part_id}?includeSub_parts=true`);
              const data = await response.json();

              if (data.data) {
                return {
                  ...component,
                  part_details: data.data
                };
              }

              return component;
            } catch (e) {
              console.error(`Error fetching details for part ${component.part_id}:`, e);
              return component;
            }
          })
        );

        setEditableComponents(componentsWithDetails);
      } catch (error) {
        console.error("Error loading component details:", error);
        setEditableComponents(components);
      }
    };

    loadComponentDetails();
  }, [components]);

  // Update the parent when components change
  const updateComponents = (updatedComponents: ProductComponent[]) => {
    setEditableComponents(updatedComponents);

    // Only return the part_id and quantity to the parent
    const simplifiedComponents = updatedComponents.map(({ part_id, quantity }) => ({
      part_id,
      quantity
    }));

    onChange(simplifiedComponents);
  };

  // Add a new component row
  const addComponent = () => {
    if (partsOptions.length === 0) {
      toast.error("No parts available to add");
      return;
    }

    const newComponent: ProductComponent = {
      part_id: "",
      quantity: 1
    };

    updateComponents([...editableComponents, newComponent]);
  };

  // Remove a component
  const removeComponent = (index: number) => {
    const updatedComponents = [...editableComponents];
    updatedComponents.splice(index, 1);
    updateComponents(updatedComponents);
  };

  // Update a component's quantity
  const updateQuantity = (index: number, quantity: number) => {
    const updatedComponents = [...editableComponents];
    updatedComponents[index].quantity = quantity;
    updateComponents(updatedComponents);
  };

  // Update a component's part
  const updatePart = (index: number, partId: string) => {
    const selectedPart = partsOptions.find(part => part._id === partId);

    if (!selectedPart) {
      toast.error("Selected part not found");
      return;
    }

    const updatedComponents = [...editableComponents];
    updatedComponents[index].part_id = partId;
    updatedComponents[index].part_details = selectedPart;
    updateComponents(updatedComponents);
  };

  // Get part details by ID (either from part_details or partsOptions)
  const getPartDetails = (partId: string): Part | undefined => {
    // First check if we have it in the component's part_details
    const component = editableComponents.find(c => c.part_id === partId && c.part_details);
    if (component?.part_details) {
      return component.part_details;
    }

    // Otherwise look it up in the partsOptions
    return partsOptions.find(part => part._id === partId);
  };

  // Check if we have enough stock for the component
  const hasEnoughStock = (component: ProductComponent): boolean => {
    const part = getPartDetails(component.part_id);
    if (!part) return false;

    return part.current_stock >= component.quantity;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Product Components</h3>
        <Button
          type="button"
          onClick={addComponent}
          disabled={disabled || partsOptions.length === 0}
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" /> Add Component
        </Button>
      </div>

      {editableComponents.length === 0 ? (
        <div className="text-center py-4 text-muted-foreground">
          No components added yet. Click "Add Component" to start building your product.
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Part</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Stock Status</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {editableComponents.map((component, index) => {
              const part = getPartDetails(component.part_id);
              const stockStatus = part ?
                (hasEnoughStock(component) ? "Available" : "Insufficient") :
                "Unknown";

              return (
                <TableRow key={index}>
                  <TableCell>
                    <Select
                      value={component.part_id}
                      onValueChange={(value: string) => updatePart(index, value)}
                      disabled={disabled}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a part" />
                      </SelectTrigger>
                      <SelectContent>
                        {partsOptions.map((part) => (
                          <SelectItem key={part._id} value={part._id}>
                            {part.name} ({part.part_id})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Input
                      type="number"
                      min="1"
                      value={component.quantity}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => updateQuantity(index, parseInt(e.target.value) || 1)}
                      disabled={disabled}
                      className="w-20"
                    />
                  </TableCell>
                  <TableCell>
                    {part ? (
                      <div className="flex items-center gap-2">
                        {stockStatus === "Available" ? (
                          <Badge variant="outline" className="bg-green-50 border-green-200 text-green-700">
                            <Check className="h-3 w-3 mr-1" />
                            Available ({part.current_stock})
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-red-50 border-red-200 text-red-700">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Insufficient ({part.current_stock}/{component.quantity})
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <Badge variant="outline" className="bg-gray-100">
                        Select a part
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => removeComponent(index)}
                      disabled={disabled}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}
    </div>
  );
}