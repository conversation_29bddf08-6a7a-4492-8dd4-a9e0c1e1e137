"use client";

import React, { useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

/**
 * Component for testing Sentry integration
 */
const SentryTest: React.FC = () => {
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Function to test Sentry connectivity
  const checkSentryConnection = async () => {
    try {
      const result = await Sentry.diagnoseSdkConnectivity();
      setIsConnected(result !== 'sentry-unreachable');
      setStatus('success');
    } catch (error) {
      console.error('Error checking Sentry connectivity:', error);
      setIsConnected(false);
      setErrorMessage(error instanceof Error ? error.message : String(error));
      setStatus('error');
    }
  };

  // Function to test Sentry error reporting
  const testSentryError = () => {
    try {
      // Create a test error with a clearer message
      const testError = new Error('[TEST ERROR] This is an intentional test error from the SentryTest component');

      // Add a custom property to identify it as a test
      (testError as any).isTestError = true;

      // Throw the test error
      throw testError;
    } catch (error) {
      // Capture the error with Sentry
      Sentry.captureException(error, {
        tags: {
          test: 'true',
          component: 'SentryTest',
          intentional: 'true'
        },
        extra: {
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV,
          isTest: true,
          description: 'This error was intentionally triggered for testing purposes'
        }
      });

      setStatus('success');
      console.log('Test error sent to Sentry');

      // Show a success message to the user
      alert('Test error successfully sent to Sentry! This error is intentional and used for testing purposes.');
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Sentry Integration Test</CardTitle>
        <CardDescription>
          Test the Sentry error tracking integration
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {status === 'success' && isConnected && (
            <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
              <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-700 dark:text-green-300">
                Sentry is properly configured and connected
              </span>
            </div>
          )}

          {status === 'error' || (status === 'success' && !isConnected) ? (
            <div className="flex items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-md">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-700 dark:text-red-300">
                {errorMessage || 'Sentry is not connected. Check your configuration.'}
              </span>
            </div>
          ) : null}

          <div className="text-sm text-gray-500 dark:text-gray-400">
            <p>
              Use the buttons below to test Sentry connectivity and error reporting.
              Errors will be sent to your Sentry project dashboard.
            </p>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex flex-col space-y-4">
        <div className="flex justify-between w-full">
          <Button
            variant="outline"
            onClick={checkSentryConnection}
            disabled={status === 'success' && isConnected === true}
          >
            Check Connection
          </Button>

          <Button
            variant="default"
            onClick={testSentryError}
            title="This will intentionally generate a test error to verify Sentry error tracking"
          >
            Send Test Error (Safe)
          </Button>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400 italic bg-gray-50 dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-700">
          <strong>Note:</strong> The test error is intentional and helps verify that Sentry is properly capturing errors.
          It will not affect the application's functionality and is tagged as a test in Sentry.
        </div>
      </CardFooter>
    </Card>
  );
};

export default SentryTest;
