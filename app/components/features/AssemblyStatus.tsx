"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { AssemblyItem } from '@/app/types';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Interface extending AssemblyItem with additional properties
 */
interface ExtendedAssemblyItem extends AssemblyItem {
  status: 'completed' | 'in_progress' | 'pending' | 'delayed';
  progress: number;
  dependencies?: string[];
  parts?: {
    id: string;
    name: string;
    status: 'available' | 'low' | 'unavailable';
  }[];
}

/**
 * Component that displays assembly status information
 * Shows progress of different assembly items and their dependencies
 */
const AssemblyStatus: React.FC<{ assemblies?: AssemblyItem[] }> = ({ assemblies = [] }) => {
  const { theme } = useTheme();

  // Mock data for assembly status
  const assemblyData: ExtendedAssemblyItem[] = assemblies.length > 0
    ? assemblies.map(a => ({
        ...a,
        // Convert stage to status if it exists, otherwise use a default status
        status: a.stage === 'Product' ? 'completed' :
               a.stage === 'Sub-Assembly' ? 'in_progress' :
               a.stage === 'Component' ? 'pending' : 'pending',
        progress: Math.floor(Math.random() * 100),
        parts: [
          { id: 'p1', name: 'Component A', status: 'available' },
          { id: 'p2', name: 'Component B', status: Math.random() > 0.7 ? 'low' : 'available' },
          { id: 'p3', name: 'Component C', status: Math.random() > 0.9 ? 'unavailable' : 'available' },
        ]
      }))
    : [
        {
          id: 'a1',
          name: 'Main Circuit Board',
          status: 'in_progress',
          progress: 65,
          startDate: '2023-03-15',
          endDate: '2023-04-10',
          assignedTo: 'Team A',
          parts: [
            { id: 'p1', name: 'Processor', status: 'available' },
            { id: 'p2', name: 'Memory Modules', status: 'available' },
            { id: 'p3', name: 'Capacitors', status: 'low' },
          ]
        },
        {
          id: 'a2',
          name: 'Power Supply Unit',
          status: 'completed',
          progress: 100,
          startDate: '2023-03-10',
          endDate: '2023-03-25',
          assignedTo: 'Team B',
          dependencies: ['a3'],
          parts: [
            { id: 'p4', name: 'Transformer', status: 'available' },
            { id: 'p5', name: 'Voltage Regulator', status: 'available' },
            { id: 'p6', name: 'Cooling Fan', status: 'available' },
          ]
        },
        {
          id: 'a3',
          name: 'Chassis Assembly',
          status: 'pending',
          progress: 0,
          startDate: '2023-04-05',
          endDate: '2023-04-20',
          assignedTo: 'Team C',
          parts: [
            { id: 'p7', name: 'Metal Frame', status: 'available' },
            { id: 'p8', name: 'Screws & Fasteners', status: 'available' },
            { id: 'p9', name: 'Front Panel', status: 'unavailable' },
          ]
        },
        {
          id: 'a4',
          name: 'Display Module',
          status: 'delayed',
          progress: 30,
          startDate: '2023-03-20',
          endDate: '2023-04-15',
          assignedTo: 'Team D',
          dependencies: ['a1'],
          parts: [
            { id: 'p10', name: 'LCD Panel', status: 'low' },
            { id: 'p11', name: 'Controller Board', status: 'available' },
            { id: 'p12', name: 'Ribbon Cables', status: 'available' },
          ]
        }
      ];

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'in_progress':
        return 'bg-blue-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'delayed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get part status color
  const getPartStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'text-green-500 dark:text-green-400';
      case 'low':
        return 'text-yellow-500 dark:text-yellow-400';
      case 'unavailable':
        return 'text-red-500 dark:text-red-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">Assembly Status</h3>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {assemblyData.filter(a => a.status === 'completed').length} of {assemblyData.length} completed
        </div>
      </div>

      <div className="space-y-4">
        {assemblyData.map((assembly, index) => (
          <motion.div
            key={assembly.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
          >
            <div className="p-3 flex items-center justify-between bg-gray-50 dark:bg-gray-750">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full ${getStatusColor(assembly.status)} mr-3`}></div>
                <span className="font-medium text-gray-700 dark:text-gray-300">{assembly.name}</span>
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {assembly.progress}% Complete
              </div>
            </div>

            <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mb-3">
                <div
                  className={`h-1.5 rounded-full ${getStatusColor(assembly.status)}`}
                  style={{ width: `${assembly.progress}%` }}
                ></div>
              </div>

              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="text-gray-500 dark:text-gray-400">
                  Start: <span className="text-gray-700 dark:text-gray-300">{assembly.startDate || 'N/A'}</span>
                </div>
                <div className="text-gray-500 dark:text-gray-400">
                  End: <span className="text-gray-700 dark:text-gray-300">{assembly.endDate || 'N/A'}</span>
                </div>
                <div className="text-gray-500 dark:text-gray-400">
                  Assigned: <span className="text-gray-700 dark:text-gray-300">{assembly.assignedTo || 'Unassigned'}</span>
                </div>
                <div className="text-gray-500 dark:text-gray-400">
                  Status: <span className="capitalize text-gray-700 dark:text-gray-300">{assembly.status && typeof assembly.status === 'string' ? assembly.status.replace('_', ' ') : 'N/A'}</span>
                </div>
              </div>

              {assembly.parts && assembly.parts.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Required Parts:</div>
                  <div className="flex flex-wrap gap-1">
                    {assembly.parts.map(part => (
                      <div
                        key={part.id}
                        className="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 flex items-center"
                      >
                        <span className="mr-1">{part.name}</span>
                        <span className={`w-2 h-2 rounded-full ${getPartStatusColor(part.status)}`}></span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {assembly.dependencies && assembly.dependencies.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Dependencies:</div>
                  <div className="flex items-center">
                    {assembly.dependencies.map((depId, i) => {
                      const dep = assemblyData.find(a => a.id === depId);
                      return (
                        <React.Fragment key={depId}>
                          <div className={`px-2 py-1 text-xs rounded-full ${
                            dep?.status === 'completed' ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300' :
                            'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                          }`}>
                            {dep?.name || depId}
                          </div>
                          {i < assembly.dependencies!.length - 1 && (
                            <ArrowRight size={12} className="mx-1 text-gray-400" />
                          )}
                        </React.Fragment>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default AssemblyStatus;
