"use client";

import { useState, useEffect, use<PERSON>em<PERSON>, useC<PERSON>back } from "react";
import { motion } from "framer-motion";
import { Search, X, Loader2, PlusCircle, Info, Filter, ArrowUpDown } from "lucide-react"; // Added Filter and ArrowUpDown icons
import { toast } from "sonner";
import { getApiUrl } from "@/app/utils/apiUtils"; // Assuming this utility function provides the base API URL
import { createDebouncedSearch, DebouncedFunction, cn } from "@/app/lib/utils"; // Import the createDebouncedSearch utility and cn
import React from "react"; // For JSX.Element return type

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Input } from "@/app/components/ui/input";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/app/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/app/components/ui/collapsible";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Label } from "@/app/components/ui/label";
import { Separator } from "@/app/components/ui/separator";

/**
 * Interface for Part data received from the API
 */
export interface PartSearchResult {
  _id: string; // Unique identifier from the database
  partNumber: string; // Canonical field: Unique business identifier for the part
  name: string; // Name of the part
  description?: string | null; // Description of the part
  isAssembly?: boolean; // Flag indicating if it's an assembly
  inventory?: {
    currentStock?: number; // Current stock level
    warehouseId?: string; // Reference to warehouse
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
  reorderLevel?: number | null; // Reorder threshold
  categoryId?: string | null; // Part category ID
  category?: string; // Part category name (for display)
  technicalSpecs?: string | null; // Technical specifications
  supplierId?: string | null; // Supplier ID
  supplierName?: string; // Direct supplier name field (for display)
  costPrice?: number; // Cost of the part
  unitOfMeasure?: string; // Unit of measure
  isManufactured?: boolean; // Whether the part is manufactured in-house
  status?: string; // Part status

  // Legacy fields for backward compatibility
  part_id?: string; // Legacy: User-facing part identifier
  is_assembly?: boolean; // Legacy: Flag indicating if it's an assembly
  current_stock?: number; // Legacy: Current stock level
  reorder_level?: number; // Legacy: Reorder threshold
  technical_specs?: string; // Legacy: Technical specifications
  supplier_id?: string | { name: string; [key: string]: any }; // Legacy: Supplier ID or object with supplier details
  supplier_name?: string; // Legacy: Direct supplier name field
  cost?: number; // Legacy: Cost of the part
  location?: string; // Legacy: Storage location
  is_manufactured?: boolean; // Legacy: Whether the part is manufactured in-house
}

/**
 * Options for the highlightMatchedTerms function
 */
interface HighlightOptions {
  caseSensitive?: boolean; // Whether to match case-sensitively
  wholeWord?: boolean; // Whether to match whole words only
  maxHighlights?: number; // Maximum number of highlights to apply
  highlightClass?: string; // CSS class to apply to highlighted text
}

/**
 * Utility function to highlight matched terms in a text string
 * Returns a JSX element with highlighted spans
 *
 * @param text - The text to search within
 * @param searchQuery - The search query to highlight
 * @param options - Options for highlighting
 * @returns JSX element with highlighted spans
 */
function highlightMatchedTerms(
  text: string | undefined | null,
  searchQuery: string,
  options: HighlightOptions = {}
): JSX.Element {
  // If text is undefined or null, return an empty fragment
  if (!text) return <></>;

  // If search query is empty, return the original text
  if (!searchQuery.trim()) return <>{text}</>;

  const {
    caseSensitive = false,
    wholeWord = false,
    maxHighlights = 100,
    highlightClass = "bg-yellow-200 dark:bg-yellow-800 rounded px-0.5"
  } = options;

  // Create a regex pattern from the search query
  // Escape special regex characters in the search query
  const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Build the regex pattern based on options
  let pattern = escapedQuery;
  if (wholeWord) {
    pattern = `\\b${pattern}\\b`;
  }

  // Create the regex with appropriate flags
  const regex = new RegExp(pattern, caseSensitive ? 'g' : 'gi');

  // Split the text by matches
  const parts = text.split(regex);

  // Find all matches
  const matches: string[] = [];
  let match;
  let count = 0;

  // Reset the regex to start from the beginning
  regex.lastIndex = 0;

  // Find all matches up to maxHighlights
  while ((match = regex.exec(text)) !== null && count < maxHighlights) {
    matches.push(match[0]);
    count++;

    // Prevent infinite loops for zero-length matches
    if (match.index === regex.lastIndex) {
      regex.lastIndex++;
    }
  }

  // If no matches were found, return the original text
  if (matches.length === 0) return <>{text}</>;

  // Build the result with highlighted spans
  const result: JSX.Element[] = [];

  parts.forEach((part, index) => {
    // Add the non-matching part
    result.push(<span key={`part-${index}`}>{part}</span>);

    // Add the matching part (if there is one)
    if (index < matches.length) {
      result.push(
        <span
          key={`match-${index}`}
          className={highlightClass}
        >
          {matches[index]}
        </span>
      );
    }
  });

  return <>{result}</>;
}

/**
 * Enum for part stock level filtering
 */
export enum StockLevelFilter {
  ALL = 'all',
  IN_STOCK = 'in_stock',
  LOW_STOCK = 'low_stock',
  OUT_OF_STOCK = 'out_of_stock'
}

/**
 * Enum for part type filtering
 */
export enum PartTypeFilter {
  ALL = 'all',
  MANUFACTURED = 'manufactured',
  PURCHASED = 'purchased'
}

/**
 * Enum for sort fields
 */
export enum SortField {
  NAME = 'name',
  PART_ID = 'partNumber',
  STOCK = 'inventory.currentStock',
  CATEGORY = 'category',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt'
}

/**
 * Enum for sort directions
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

/**
 * Interface for advanced filter options
 */
export interface AdvancedFilterOptions {
  categories?: string[]; // Filter by specific categories
  suppliers?: string[]; // Filter by specific suppliers
  stockLevel?: StockLevelFilter; // Filter by stock level
  partType?: PartTypeFilter; // Filter by part type (manufactured vs. purchased)
  minStock?: number; // Minimum stock level
  maxStock?: number; // Maximum stock level
}

/**
 * Props for the PartSearch component
 */
interface PartSearchProps {
  onSelectPart: (part: PartSearchResult) => void; // Callback when a part is selected
  selectedParts?: string[]; // Array of already selected part IDs (_id) to avoid duplicates in results
  onClose?: () => void; // Optional callback to close the search component
  filterByAssembly?: boolean; // If true, only show parts where is_assembly is true
  excludeAssemblies?: boolean; // If true, only show parts where is_assembly is false
  heading?: string; // Optional custom heading for the card

  // New advanced filtering and sorting options
  sortBy?: SortField; // Field to sort by
  sortDirection?: SortDirection; // Sort direction (asc or desc)
  filterOptions?: AdvancedFilterOptions; // Advanced filtering options
  showAdvancedFilters?: boolean; // Whether to show advanced filtering UI
  minQueryLength?: number; // Minimum query length for search (default: 2)
  debounceTime?: number; // Debounce time in milliseconds (default: 300)
}

/**
 * Enhanced Part Search component
 * Allows searching for parts via API, displays recent parts,
 * handles selection, and supports filtering.
 */
function PartSearch({
  onSelectPart,
  selectedParts = [],
  onClose,
  filterByAssembly = false,
  excludeAssemblies = false,
  heading = "Search Parts",
  sortBy = SortField.NAME,
  sortDirection = SortDirection.ASC,
  filterOptions = {},
  showAdvancedFilters = false,
  minQueryLength = 2,
  debounceTime = 300,
}: PartSearchProps) {
  // State variables
  const [searchQuery, setSearchQuery] = useState(""); // Current search input value
  const [isLoading, setIsLoading] = useState(false); // Loading state for API calls
  const [searchResults, setSearchResults] = useState<PartSearchResult[]>([]); // Parts found via search
  const [recentParts, setRecentParts] = useState<PartSearchResult[]>([]); // Recently viewed/added parts
  const [focusedPartId, setFocusedPartId] = useState<string | null>(null); // ID of the part highlighted for keyboard navigation

  // State for advanced filtering and sorting
  const [currentSortBy, setCurrentSortBy] = useState<SortField>(sortBy);
  const [currentSortDirection, setCurrentSortDirection] = useState<SortDirection>(sortDirection);
  const [currentFilterOptions, setCurrentFilterOptions] = useState<AdvancedFilterOptions>(filterOptions);
  const [showFilters, setShowFilters] = useState<boolean>(showAdvancedFilters);

  /**
   * Core asynchronous function to fetch parts based on a query.
   * This is used by both debounced search and manual search.
   * Now includes advanced filtering and sorting options.
   */
  const fetchParts = useCallback(
    async (query: string) => {
      // Basic validation
      if (query.length < minQueryLength) {
        setSearchResults([]); // Clear results if query is too short
        return;
      }

      console.log(`[PartSearch] Fetching parts for query: ${query}`);
      setIsLoading(true); // Set loading state

      try {
        // --- Build API Search Parameters ---
        const searchParams = new URLSearchParams({
          search: query,
          limit: '20', // Limit results for performance
          // Request specific fields to reduce payload size
          includeFields: 'partNumber,name,description,category,supplierId,supplierName,isAssembly,inventory,reorderLevel,isManufactured,costPrice,unitOfMeasure,status',
        });

        // --- Add Sorting Parameters ---
        if (currentSortBy) {
          searchParams.append('sortField', currentSortBy);
          searchParams.append('sortOrder', currentSortDirection);
        }

        // --- Add Basic Filtering Parameters ---
        if (filterByAssembly) {
          searchParams.append('isAssembly', 'true');
        }
        if (excludeAssemblies) {
          searchParams.append('isAssembly', 'false');
        }

        // --- Add Advanced Filtering Parameters ---
        // Stock Level Filter
        if (currentFilterOptions.stockLevel && currentFilterOptions.stockLevel !== StockLevelFilter.ALL) {
          switch (currentFilterOptions.stockLevel) {
            case StockLevelFilter.IN_STOCK:
              searchParams.append('minStock', '1');
              break;
            case StockLevelFilter.LOW_STOCK:
              searchParams.append('lowStock', 'true');
              break;
            case StockLevelFilter.OUT_OF_STOCK:
              searchParams.append('stock', '0');
              break;
          }
        }

        // Part Type Filter
        if (currentFilterOptions.partType && currentFilterOptions.partType !== PartTypeFilter.ALL) {
          searchParams.append('isManufactured',
            currentFilterOptions.partType === PartTypeFilter.MANUFACTURED ? 'true' : 'false'
          );
        }

        // Categories Filter
        if (currentFilterOptions.categories && currentFilterOptions.categories.length > 0) {
          // Some APIs support multiple values for the same parameter
          currentFilterOptions.categories.forEach(category => {
            searchParams.append('category', category);
          });
        }

        // Suppliers Filter
        if (currentFilterOptions.suppliers && currentFilterOptions.suppliers.length > 0) {
          // Some APIs support multiple values for the same parameter
          currentFilterOptions.suppliers.forEach(supplier => {
            searchParams.append('supplier', supplier);
          });
        }

        // Stock Range Filter
        if (currentFilterOptions.minStock !== undefined) {
          searchParams.append('minStock', currentFilterOptions.minStock.toString());
        }
        if (currentFilterOptions.maxStock !== undefined) {
          searchParams.append('maxStock', currentFilterOptions.maxStock.toString());
        }

        // --- Make API Call ---
        const apiUrl = getApiUrl(`/api/parts/search?${searchParams.toString()}`);
        console.log(`[PartSearch] Search URL: ${apiUrl}`);
        const response = await fetch(apiUrl);

        if (!response.ok) {
          console.error(`[PartSearch] API search failed with status: ${response.status}`);
          throw new Error(`Search failed: ${response.statusText || 'Unknown error'}`);
        }

        const data = await response.json();
        console.log(`[PartSearch] API response received. Found ${data.data?.length || 0} raw results.`);

        // --- Process and Filter Results ---
        let results: PartSearchResult[] = data.data || [];

        // --- Apply Client-Side Filtering (as fallback/additional) ---
        // Basic Filters
        if (filterByAssembly) {
          results = results.filter(part => part.isAssembly || part.is_assembly);
        }
        if (excludeAssemblies) {
          results = results.filter(part => !part.isAssembly && !part.is_assembly);
        }

        // Advanced Filters (in case the API doesn't support them)
        // Stock Level Filter
        if (currentFilterOptions.stockLevel && currentFilterOptions.stockLevel !== StockLevelFilter.ALL) {
          results = results.filter(part => {
            // Get stock from inventory.currentStock (canonical) or current_stock (legacy)
            const stock = (part.inventory?.currentStock ?? part.current_stock ?? 0);
            // Get reorder level from reorderLevel (canonical) or reorder_level (legacy)
            const reorderLevel = (part.reorderLevel ?? part.reorder_level ?? 0);

            switch (currentFilterOptions.stockLevel) {
              case StockLevelFilter.IN_STOCK:
                return stock > 0;
              case StockLevelFilter.LOW_STOCK:
                return stock > 0 && stock <= reorderLevel;
              case StockLevelFilter.OUT_OF_STOCK:
                return stock === 0;
              default:
                return true;
            }
          });
        }

        // Part Type Filter
        if (currentFilterOptions.partType && currentFilterOptions.partType !== PartTypeFilter.ALL) {
          const isManufactured = currentFilterOptions.partType === PartTypeFilter.MANUFACTURED;
          results = results.filter(part => part.isManufactured === isManufactured || part.is_manufactured === isManufactured);
        }

        // Categories Filter
        if (currentFilterOptions.categories && currentFilterOptions.categories.length > 0) {
          results = results.filter(part =>
            part.category && currentFilterOptions.categories?.includes(part.category)
          );
        }

        // Suppliers Filter
        if (currentFilterOptions.suppliers && currentFilterOptions.suppliers.length > 0) {
          results = results.filter(part => {
            // Get supplier name from supplierName (canonical) or supplier_name (legacy)
            const supplierName = part.supplierName || part.supplier_name ||
                               (typeof part.supplierId === 'object' && part.supplierId?.name) ||
                               (typeof part.supplier_id === 'object' && part.supplier_id?.name);
            return supplierName && currentFilterOptions.suppliers?.includes(supplierName);
          });
        }

        // Stock Range Filter
        if (currentFilterOptions.minStock !== undefined || currentFilterOptions.maxStock !== undefined) {
          results = results.filter(part => {
            // Get stock from inventory.currentStock (canonical) or current_stock (legacy)
            const stock = (part.inventory?.currentStock ?? part.current_stock ?? 0);
            const minOk = currentFilterOptions.minStock === undefined || stock >= currentFilterOptions.minStock;
            const maxOk = currentFilterOptions.maxStock === undefined || stock <= currentFilterOptions.maxStock;
            return minOk && maxOk;
          });
        }

        // Filter out parts that are already selected in the parent component
        if (selectedParts.length > 0) {
          const selectedSet = new Set(selectedParts); // Use Set for efficient lookup
          results = results.filter(part => !selectedSet.has(part._id));
        }

        // --- Apply Client-Side Sorting (as fallback) ---
        if (currentSortBy && (!data.meta?.sort || data.meta?.sort !== currentSortBy)) {
          results.sort((a, b) => {
            let valueA: any;
            let valueB: any;

            // Extract the values to compare based on the sort field
            switch (currentSortBy) {
              case SortField.NAME:
                valueA = a.name?.toLowerCase() || '';
                valueB = b.name?.toLowerCase() || '';
                break;
              case SortField.PART_ID:
                valueA = (a.partNumber || a.part_id || '')?.toLowerCase();
                valueB = (b.partNumber || b.part_id || '')?.toLowerCase();
                break;
              case SortField.STOCK:
                valueA = a.inventory?.currentStock ?? a.current_stock ?? 0;
                valueB = b.inventory?.currentStock ?? b.current_stock ?? 0;
                break;
              case SortField.CATEGORY:
                valueA = a.category?.toLowerCase() || '';
                valueB = b.category?.toLowerCase() || '';
                break;
              case SortField.CREATED_AT:
              case SortField.UPDATED_AT:
                // These might not be available in the search results
                valueA = a[currentSortBy] ? new Date(a[currentSortBy]).getTime() : 0;
                valueB = b[currentSortBy] ? new Date(b[currentSortBy]).getTime() : 0;
                break;
              default:
                valueA = a.name?.toLowerCase() || '';
                valueB = b.name?.toLowerCase() || '';
            }

            // Compare the values based on the sort direction
            if (currentSortDirection === SortDirection.ASC) {
              return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
            } else {
              return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
            }
          });
        }

        console.log(`[PartSearch] After client-side filtering and sorting: ${results.length} results.`);
        setSearchResults(results); // Update state with final results

      } catch (error) {
        console.error("Error searching parts:", error);
        toast.error(`Failed to search parts: ${error instanceof Error ? error.message : 'Please try again.'}`);
        setSearchResults([]); // Clear results on error
      } finally {
        setIsLoading(false); // Reset loading state
      }
    },
    [
      filterByAssembly,
      excludeAssemblies,
      selectedParts,
      getApiUrl,
      currentSortBy,
      currentSortDirection,
      currentFilterOptions,
      minQueryLength
    ] // Dependencies for the core fetch logic
  );

  /**
   * Enhanced debounced version of the fetchParts function using createDebouncedSearch.
   * This provides better handling of minimum query length and empty/too short queries.
   */
  const debouncedSearch = useMemo(() => {
    // Create the debounced search function with advanced options
    const debouncedFn = createDebouncedSearch(
      // The search function to debounce
      (query: string) => {
        return fetchParts(query);
      },
      {
        // Configuration options
        wait: debounceTime, // Use the debounceTime prop
        minLength: minQueryLength, // Use the minQueryLength prop
        onEmpty: () => {
          // Handle empty query
          setSearchResults([]);
          console.log("[PartSearch] Query is empty, clearing results");
        },
        onTooShort: (query) => {
          // Handle query that's too short
          setSearchResults([]);
          console.log(`[PartSearch] Query too short (${query.length}/${minQueryLength}), need at least ${minQueryLength} characters`);
        },
        // Additional debounce options if needed
        debounceOptions: {
          // Set trailing to true to ensure the function is called after the wait time
          trailing: true
        }
      }
    );

    // Return an object with call and cancel methods for compatibility with existing code
    return {
      call: debouncedFn,
      cancel: () => debouncedFn.cancel(),
    };
  }, [fetchParts, debounceTime, minQueryLength]); // Include the new dependencies

  /**
   * Effect to fetch recent parts when the component mounts or filters change.
   */
  useEffect(() => {
    const fetchRecentParts = async () => {
      console.log("[PartSearch] Fetching recent parts...");
      setIsLoading(true); // Use the same loading state for simplicity
      try {
        // --- Build API Parameters ---
        const recentParams = new URLSearchParams({
          limit: '5', // Limit the number of recent parts
          // Request necessary fields
          includeFields: 'partNumber,name,description,category,supplierId,supplierName,isAssembly,inventory',
        });

        // Add filtering if applicable (assuming API supports it for the general parts endpoint)
        if (filterByAssembly) {
          recentParams.append('isAssembly', 'true');
        }
        if (excludeAssemblies) {
          recentParams.append('isAssembly', 'false');
        }

        // --- Make API Call ---
        const apiUrl = getApiUrl(`/api/parts?${recentParams.toString()}`);
        console.log(`[PartSearch] Recent Parts URL: ${apiUrl}`);
        const response = await fetch(apiUrl);

        if (!response.ok) {
          throw new Error(`Failed to fetch recent parts: ${response.statusText || 'Unknown error'}`);
        }

        const data = await response.json();
        let recent: PartSearchResult[] = data.data || [];
        console.log(`[PartSearch] API returned ${recent.length} recent parts.`);

        // --- Apply Client-Side Filtering (as fallback/additional) ---
        if (filterByAssembly) {
          recent = recent.filter(part => part.isAssembly || part.is_assembly);
        }
        if (excludeAssemblies) {
          recent = recent.filter(part => !part.isAssembly && !part.is_assembly);
        }

        // Filter out already selected parts
        if (selectedParts.length > 0) {
          const selectedSet = new Set(selectedParts);
          recent = recent.filter(part => !selectedSet.has(part._id));
        }

        console.log(`[PartSearch] After filtering recent parts: ${recent.length} results.`);
        setRecentParts(recent);

      } catch (error) {
        console.error("Error fetching recent parts:", error);
        toast.error(`Failed to load recent parts: ${error instanceof Error ? error.message : 'Please try again.'}`);
        setRecentParts([]); // Clear recent parts on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentParts();
    // Include getApiUrl in dependencies if it's not guaranteed to be a stable function reference
  }, [filterByAssembly, excludeAssemblies, selectedParts, getApiUrl]);

  /**
   * Effect to trigger the debounced search when the search query changes.
   */
  useEffect(() => {
    // Call the debounced search function
    debouncedSearch.call(searchQuery);

    // Cleanup function: Cancel any pending debounced calls when the component
    // unmounts or when the query/debounced function changes.
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]); // Depend on query and the debounced function itself

  /**
   * Handler for immediate search (e.g., button click, form submit).
   * Cancels any pending debounced search and triggers the fetch immediately.
   * Now applies the current filter and sort options.
   */
  const handleImmediateSearch = () => {
    console.log(`[PartSearch] Manual search triggered for: ${searchQuery}`);
    console.log(`[PartSearch] Using sort: ${currentSortBy} ${currentSortDirection}`);
    console.log(`[PartSearch] Using filters:`, currentFilterOptions);

    debouncedSearch.cancel(); // Cancel any pending debounced search
    fetchParts(searchQuery); // Call the core fetch function directly with current filter/sort options
  };

  /**
   * Handler for changes in the search input field.
   * This now works with the enhanced debounced search implementation.
   */
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setSearchQuery(newQuery);

    // Reset focused part when typing
    setFocusedPartId(null);

    // The createDebouncedSearch utility will handle empty queries and minimum length checks
    // We don't need to manually clear results or cancel the debounce timer
    // But we'll keep the cancel call for immediate clearing when the input is empty
    if (newQuery.length === 0) {
      debouncedSearch.cancel(); // Cancel any potential debounce timer
    }
  };

  /**
   * Handler for submitting the search form (e.g., pressing Enter).
   */
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault(); // Prevent default form submission
    if (searchQuery.length >= minQueryLength) {
      handleImmediateSearch(); // Trigger immediate search
    } else if (searchQuery.length > 0) {
      // Show a toast message if the query is too short
      toast.info(`Please enter at least ${minQueryLength} characters to search`);
    }
  };

  /**
   * Handler for keyboard navigation (Arrow keys, Enter, Escape).
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Determine the list to navigate (search results or recent parts)
    const activeList = searchQuery.length >= minQueryLength ? searchResults : recentParts;

    if (activeList.length === 0) return; // No list to navigate

    const currentIndex = focusedPartId
      ? activeList.findIndex(part => part._id === focusedPartId)
      : -1; // Find index of currently focused item, or -1 if none

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault(); // Prevent page scrolling
        const nextIndex = currentIndex < activeList.length - 1 ? currentIndex + 1 : 0; // Loop to start
        setFocusedPartId(activeList[nextIndex]._id);
        // Scroll the item into view if needed (optional enhancement)
        // document.getElementById(activeList[nextIndex]._id)?.scrollIntoView({ block: 'nearest' });
        break;

      case "ArrowUp":
        e.preventDefault(); // Prevent page scrolling
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : activeList.length - 1; // Loop to end
        setFocusedPartId(activeList[prevIndex]._id);
        // Scroll the item into view if needed (optional enhancement)
        // document.getElementById(activeList[prevIndex]._id)?.scrollIntoView({ block: 'nearest' });
        break;

      case "Enter":
        e.preventDefault();
        if (focusedPartId) {
          const selectedPart = activeList.find(part => part._id === focusedPartId);
          if (selectedPart) {
            console.log(`[PartSearch] Part selected via Enter key: ${selectedPart.name}`);
            onSelectPart(selectedPart); // Call the selection callback
          }
        } else if (activeList.length > 0 && searchQuery.length >= minQueryLength) {
            // If Enter is pressed in input and no item is focused, trigger search
            handleImmediateSearch();
        } else if (searchQuery.length > 0 && searchQuery.length < minQueryLength) {
            // Show a toast message if the query is too short
            toast.info(`Please enter at least ${minQueryLength} characters to search`);
        }
        break;

      case "Escape":
        e.preventDefault();
        if (onClose) {
          console.log("[PartSearch] Escape key pressed, closing.");
          onClose(); // Call the close callback if provided
        }
        break;
    }
  };

  /**
   * Renders a single part card.
   * @param part The part data to render.
   * @param index The index for animation delay.
   */
  const renderPartCard = (part: PartSearchResult, index: number) => {
    const isFocused = focusedPartId === part._id; // Check if this card is focused
    const isAssembly = part.isAssembly || part.is_assembly; // Normalize assembly flag

    // Determine supplier name safely
    const supplierName = part.supplierName || part.supplier_name ||
                         (typeof part.supplierId === 'object' && part.supplierId?.name) ||
                         (typeof part.supplier_id === 'object' && part.supplier_id?.name) ||
                         '';

    return (
      <motion.div
        key={part._id}
        id={part._id} // Add ID for potential scrollIntoView
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2, delay: index * 0.05 }}
        className={`border rounded-lg p-3 mb-2 cursor-pointer transition-all duration-150 ease-in-out ${ // Use rounded-lg
          isFocused
            ? "bg-yellow-100 dark:bg-yellow-900/30 border-yellow-400 dark:border-yellow-600 ring-2 ring-yellow-300 dark:ring-yellow-700" // Enhanced focus style
            : isAssembly
              ? "hover:bg-blue-50 dark:hover:bg-blue-900/20 border-blue-200 dark:border-blue-800"
              : "hover:bg-gray-50 dark:hover:bg-gray-800/50 border-gray-200 dark:border-gray-700" // Slightly adjusted hover
        }`}
        onClick={() => {
            console.log(`[PartSearch] Part clicked: ${part.name}`);
            onSelectPart(part);
        }}
        onMouseEnter={() => setFocusedPartId(part._id)} // Set focus on hover
        onMouseLeave={() => setFocusedPartId(null)} // Clear focus on mouse leave
        role="button" // Accessibility: Indicate it's clickable
        tabIndex={0} // Accessibility: Make it focusable (though direct click/enter is primary interaction)
        aria-selected={isFocused} // Accessibility: Indicate selection state
      >
        {/* Top section: Name, ID, Add Button */}
        <div className="flex justify-between items-start gap-2">
          {/* Left side: Name and ID */}
          <div className="flex-grow min-w-0"> {/* Allow shrinking */}
            <div className="font-medium flex items-center gap-1.5 flex-wrap"> {/* Allow wrapping */}
              <span className="truncate" title={part.name}>
                {/* Highlight matched terms in the name */}
                {highlightMatchedTerms(part.name, searchQuery, {
                  highlightClass: "bg-yellow-200 dark:bg-yellow-800 rounded px-0.5 font-medium"
                })}
              </span>
              {isAssembly && (
                <Badge variant="outline" className="ml-1 text-xs px-1.5 py-0.5 border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-300 whitespace-nowrap"> {/* Specific assembly style */}
                  Assembly
                </Badge>
              )}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 font-mono truncate" title={part.partNumber || part.part_id}>
              {/* Highlight matched terms in the part ID/number */}
              {highlightMatchedTerms(part.partNumber || part.part_id || 'N/A', searchQuery, {
                highlightClass: "bg-yellow-200 dark:bg-yellow-800 rounded px-0.5 font-mono"
              })}
            </div>
          </div>

          {/* Right side: Add Button */}
          <div className="flex-shrink-0"> {/* Prevent shrinking */}
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon" // Use icon size for consistency
                    variant="ghost"
                    className="h-8 w-8 text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/20 hover:text-green-700 dark:hover:text-green-300" // Use green for add action
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card's onClick from firing
                      console.log(`[PartSearch] Add button clicked for: ${part.name}`);
                      onSelectPart(part);
                    }}
                    aria-label={`Add part ${part.name}`} // Accessibility label
                  >
                    <PlusCircle size={18} /> {/* Slightly larger icon */}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Add this part</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Bottom section: Description and Badges */}
        <div className="text-sm text-gray-600 dark:text-gray-400 mt-2 space-y-2">
          {/* Description (truncated) */}
          {part.description && (
            <p className="line-clamp-2" title={part.description}> {/* Show 2 lines, full on hover */}
              {/* Highlight matched terms in the description */}
              {highlightMatchedTerms(part.description, searchQuery, {
                highlightClass: "bg-yellow-200 dark:bg-yellow-800 rounded px-0.5"
              })}
            </p>
          )}

          {/* Badges for additional info */}
          <div className="flex flex-wrap gap-1.5 items-center">
            {/* Stock Badge */}
            {((part.inventory?.currentStock !== undefined) || (part.current_stock !== undefined)) && (
              <Badge
                variant={(part.inventory?.currentStock ?? part.current_stock ?? 0) > (part.reorderLevel ?? part.reorder_level ?? 0) ? "secondary" : "destructive"} // Use reorder level if available
                className="text-xs px-1.5 py-0.5"
                title={`Current Stock: ${part.inventory?.currentStock ?? part.current_stock}${(part.reorderLevel !== undefined || part.reorder_level !== undefined) ? `, Reorder Level: ${part.reorderLevel ?? part.reorder_level}` : ''}`}
              >
                Stock: {part.inventory?.currentStock ?? part.current_stock}
              </Badge>
            )}
            {/* Supplier Badge */}
            {supplierName && (
              <Badge variant="outline" className="text-xs px-1.5 py-0.5" title={`Supplier: ${supplierName}`}>
                {supplierName}
              </Badge>
            )}
            {/* Category Badge */}
            {part.category && (
              <Badge variant="secondary" className="text-xs px-1.5 py-0.5 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200" title={`Category: ${part.category}`}>
                {part.category}
              </Badge>
            )}
          </div>
        </div>
      </motion.div>
    );
  };

  // --- Component Return JSX ---
  return (
    <Card className="w-full max-w-xl mx-auto shadow-lg border border-gray-200 dark:border-gray-700">
      {/* Card Header */}
      <CardHeader className="pb-4 border-b dark:border-gray-700">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold">{heading}</CardTitle>
          {/* Close Button */}
          {onClose && (
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" onClick={onClose} aria-label="Close search">
                    <X size={18} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Close</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        {/* Description based on filters */}
        <CardDescription className="pt-1">
          {filterByAssembly
            ? "Showing only assemblies. "
            : excludeAssemblies
              ? "Showing only individual parts. "
              : ""}
          Search by name, ID, or description.
        </CardDescription>
      </CardHeader>

      {/* Card Content: Search Input and Results */}
      <CardContent className="pt-4 pb-4">
        {/* Search Form */}
        <form onSubmit={handleSearchSubmit} className="mb-4">
          <div className="flex gap-2 items-center">
            {/* Input with Icon */}
            <div className="relative flex-1">
              <Input
                type="text"
                placeholder="Search by name, ID..."
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyDown={handleKeyDown} // Handle keyboard navigation
                className="w-full pl-10 pr-4 py-2 rounded-md border-gray-300 dark:border-gray-600 focus:ring-yellow-500 focus:border-yellow-500 dark:bg-gray-700 dark:text-white" // Style adjustments
                autoFocus // Focus input on mount
                disabled={isLoading} // Disable while loading
                aria-label="Search parts input"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" /> {/* Centered icon */}
            </div>
            {/* Search Button */}
            <Button
              type="button" // Changed type to button to prevent implicit submission, handled by onClick/onSubmit
              disabled={searchQuery.length < minQueryLength || isLoading}
              onClick={handleImmediateSearch} // Use immediate search handler
              className="px-4 py-2 rounded-md bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50" // Style adjustments
            >
              {isLoading && searchResults.length === 0 ? ( // Show spinner only if loading initial results
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Search size={18}/> // Show search icon otherwise
              )}
              <span className="ml-2 hidden sm:inline">Search</span> {/* Hide text on small screens */}
            </Button>

            {/* Filter Toggle Button */}
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowFilters(!showFilters)}
                    className={cn(
                      "h-10 w-10",
                      showFilters && "bg-muted border-primary"
                    )}
                    aria-label="Toggle filters"
                  >
                    <Filter size={18} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{showFilters ? "Hide filters" : "Show filters"}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Advanced Filters and Sorting */}
          <Collapsible open={showFilters} className="mt-3">
            <CollapsibleContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Sorting Options */}
                <div>
                  <Label htmlFor="sort-by" className="text-sm font-medium block mb-1.5">Sort By</Label>
                  <div className="flex gap-2">
                    <Select
                      value={currentSortBy}
                      onValueChange={(value) => setCurrentSortBy(value as SortField)}
                    >
                      <SelectTrigger id="sort-by" className="flex-1">
                        <SelectValue placeholder="Select field" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Sort Field</SelectLabel>
                          <SelectItem value={SortField.NAME}>Name</SelectItem>
                          <SelectItem value={SortField.PART_ID}>Part ID</SelectItem>
                          <SelectItem value={SortField.STOCK}>Stock Level</SelectItem>
                          <SelectItem value={SortField.CATEGORY}>Category</SelectItem>
                          <SelectItem value={SortField.CREATED_AT}>Date Created</SelectItem>
                          <SelectItem value={SortField.UPDATED_AT}>Date Updated</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>

                    <Select
                      value={currentSortDirection}
                      onValueChange={(value) => setCurrentSortDirection(value as SortDirection)}
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Direction" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={SortDirection.ASC}>Ascending</SelectItem>
                        <SelectItem value={SortDirection.DESC}>Descending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Stock Level Filter */}
                <div>
                  <Label htmlFor="stock-level" className="text-sm font-medium block mb-1.5">Stock Level</Label>
                  <Select
                    value={currentFilterOptions.stockLevel || StockLevelFilter.ALL}
                    onValueChange={(value) => setCurrentFilterOptions({
                      ...currentFilterOptions,
                      stockLevel: value as StockLevelFilter
                    })}
                  >
                    <SelectTrigger id="stock-level" className="w-full">
                      <SelectValue placeholder="All stock levels" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={StockLevelFilter.ALL}>All Stock Levels</SelectItem>
                      <SelectItem value={StockLevelFilter.IN_STOCK}>In Stock</SelectItem>
                      <SelectItem value={StockLevelFilter.LOW_STOCK}>Low Stock</SelectItem>
                      <SelectItem value={StockLevelFilter.OUT_OF_STOCK}>Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Part Type Filter */}
                <div>
                  <Label htmlFor="part-type" className="text-sm font-medium block mb-1.5">Part Type</Label>
                  <Select
                    value={currentFilterOptions.partType || PartTypeFilter.ALL}
                    onValueChange={(value) => setCurrentFilterOptions({
                      ...currentFilterOptions,
                      partType: value as PartTypeFilter
                    })}
                  >
                    <SelectTrigger id="part-type" className="w-full">
                      <SelectValue placeholder="All part types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={PartTypeFilter.ALL}>All Part Types</SelectItem>
                      <SelectItem value={PartTypeFilter.MANUFACTURED}>Manufactured</SelectItem>
                      <SelectItem value={PartTypeFilter.PURCHASED}>Purchased</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Category Filter */}
                <div>
                  <Label htmlFor="category" className="text-sm font-medium block mb-1.5">Category</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-between"
                        id="category"
                      >
                        {currentFilterOptions.categories?.length
                          ? `${currentFilterOptions.categories.length} selected`
                          : "Select categories"}
                        <Filter className="ml-2 h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" align="start">
                      <div className="p-4 max-h-[300px] overflow-auto">
                        <div className="space-y-2">
                          {/* This would ideally be populated from API data */}
                          {["Electronics", "Mechanical", "Fasteners", "Raw Materials", "Tools", "Packaging"].map((category) => (
                            <div key={category} className="flex items-center space-x-2">
                              <Checkbox
                                id={`category-${category}`}
                                checked={currentFilterOptions.categories?.includes(category)}
                                onCheckedChange={(checked) => {
                                  const currentCategories = currentFilterOptions.categories || [];
                                  if (checked) {
                                    setCurrentFilterOptions({
                                      ...currentFilterOptions,
                                      categories: [...currentCategories, category]
                                    });
                                  } else {
                                    setCurrentFilterOptions({
                                      ...currentFilterOptions,
                                      categories: currentCategories.filter(c => c !== category)
                                    });
                                  }
                                }}
                              />
                              <Label
                                htmlFor={`category-${category}`}
                                className="text-sm cursor-pointer"
                              >
                                {category}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                      <Separator />
                      <div className="p-2 flex justify-between">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setCurrentFilterOptions({
                              ...currentFilterOptions,
                              categories: undefined
                            });
                          }}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            // Close the popover (would need a ref to the popover)
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Min/Max Stock Filter */}
                <div>
                  <Label className="text-sm font-medium block mb-1.5">Stock Range</Label>
                  <div className="flex gap-2 items-center">
                    <Input
                      type="number"
                      placeholder="Min"
                      min={0}
                      value={currentFilterOptions.minStock || ""}
                      onChange={(e) => {
                        const value = e.target.value ? parseInt(e.target.value) : undefined;
                        setCurrentFilterOptions({
                          ...currentFilterOptions,
                          minStock: value
                        });
                      }}
                      className="w-full"
                    />
                    <span className="text-muted-foreground">to</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      min={0}
                      value={currentFilterOptions.maxStock || ""}
                      onChange={(e) => {
                        const value = e.target.value ? parseInt(e.target.value) : undefined;
                        setCurrentFilterOptions({
                          ...currentFilterOptions,
                          maxStock: value
                        });
                      }}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Supplier Filter */}
              <div>
                <Label htmlFor="supplier" className="text-sm font-medium block mb-1.5">Supplier</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between"
                      id="supplier"
                    >
                      {currentFilterOptions.suppliers?.length
                        ? `${currentFilterOptions.suppliers.length} selected`
                        : "Select suppliers"}
                      <Filter className="ml-2 h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <div className="p-4 max-h-[300px] overflow-auto">
                      <div className="space-y-2">
                        {/* This would ideally be populated from API data */}
                        {["Acme Inc.", "TechSupply Co.", "Global Parts", "Quality Components", "Precision Manufacturing"].map((supplier) => (
                          <div key={supplier} className="flex items-center space-x-2">
                            <Checkbox
                              id={`supplier-${supplier}`}
                              checked={currentFilterOptions.suppliers?.includes(supplier)}
                              onCheckedChange={(checked) => {
                                const currentSuppliers = currentFilterOptions.suppliers || [];
                                if (checked) {
                                  setCurrentFilterOptions({
                                    ...currentFilterOptions,
                                    suppliers: [...currentSuppliers, supplier]
                                  });
                                } else {
                                  setCurrentFilterOptions({
                                    ...currentFilterOptions,
                                    suppliers: currentSuppliers.filter(s => s !== supplier)
                                  });
                                }
                              }}
                            />
                            <Label
                              htmlFor={`supplier-${supplier}`}
                              className="text-sm cursor-pointer"
                            >
                              {supplier}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    <Separator />
                    <div className="p-2 flex justify-between">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setCurrentFilterOptions({
                            ...currentFilterOptions,
                            suppliers: undefined
                          });
                        }}
                      >
                        Clear
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => {
                          // Close the popover (would need a ref to the popover)
                        }}
                      >
                        Apply
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              </div>

              {/* Filter Actions */}
              <div className="flex justify-end gap-2 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Reset filters to defaults
                    setCurrentFilterOptions({});
                    setCurrentSortBy(sortBy);
                    setCurrentSortDirection(sortDirection);
                  }}
                >
                  Reset Filters
                </Button>
                <Button
                  type="button"
                  size="sm"
                  onClick={() => {
                    // Apply filters and trigger search
                    if (searchQuery.length >= minQueryLength) {
                      handleImmediateSearch();
                    }
                  }}
                >
                  Apply Filters
                </Button>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </form>

        {/* Results Area */}
        <div className="space-y-4">
          {/* Loading Indicator */}
          {isLoading && searchResults.length === 0 && searchQuery.length >= 2 && ( // Show loading only when actively searching and no results yet
            <div className="flex items-center justify-center py-6 text-gray-500 dark:text-gray-400">
              <Loader2 className="h-6 w-6 animate-spin text-yellow-500 mr-2" />
              <span>Searching for parts...</span>
            </div>
          )}

          {/* Display Search Results */}
          {searchResults.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 px-1">
                Search Results ({searchResults.length})
              </h3>
              {/* Scrollable container for results */}
              <div className="max-h-[350px] overflow-y-auto pr-1 custom-scrollbar"> {/* Increased max height */}
                {searchResults.map((part, index) => renderPartCard(part, index))}
              </div>
            </div>
          )}

          {/* No Search Results Message */}
          {!isLoading && searchQuery.length >= minQueryLength && searchResults.length === 0 && (
            <div className="text-center py-6 border border-dashed rounded-md border-gray-300 dark:border-gray-600">
              <p className="text-gray-500 dark:text-gray-400">No matching parts found for "{searchQuery}"</p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Try refining your search terms.</p>
            </div>
          )}

          {/* Display Recent Parts (only if no search query) */}
          {!isLoading && searchQuery.length === 0 && recentParts.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 px-1">
                Recent Parts
              </h3>
              {/* Scrollable container for recent parts */}
              <div className="max-h-[350px] overflow-y-auto pr-1 custom-scrollbar">
                {recentParts.map((part, index) => renderPartCard(part, index))}
              </div>
            </div>
          )}

           {/* No Recent Parts Message (only if no search query and no recent parts) */}
           {!isLoading && searchQuery.length === 0 && recentParts.length === 0 && (
            <div className="text-center py-6 border border-dashed rounded-md border-gray-300 dark:border-gray-600">
              <p className="text-gray-500 dark:text-gray-400">No recent parts to display.</p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Start searching to find parts.</p>
            </div>
          )}
        </div>
      </CardContent>

      {/* Card Footer */}
      <CardFooter className="flex justify-between items-center border-t dark:border-gray-700 pt-3 pb-3">
        {/* Info Tooltip */}
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <Info className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Search results are limited to 20 items.</p>
              <p>Keyboard navigation: Arrow Up/Down, Enter to select, Esc to close.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {/* Close Button (Alternative) */}
        {onClose && (
             <Button
             variant="outline" // Use outline for secondary action
             size="sm"
             onClick={() => {
               setSearchQuery(""); // Clear search on close
               setSearchResults([]);
               if (onClose) {
                 onClose();
               }
             }}
             className="rounded-md"
           >
             Close
           </Button>
        )}
      </CardFooter>
    </Card>
  );
}

// Optional: Add some basic CSS for custom scrollbar if desired
/*
<style>
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5); // gray-400 with opacity
  border-radius: 3px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.6); // gray-500 with opacity
}

// Dark mode scrollbar
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.6); // gray-600 with opacity
}
.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(55, 65, 81, 0.7); // gray-700 with opacity
}
</style>
*/

// Export the component
export { PartSearch };
