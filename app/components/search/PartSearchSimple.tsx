"use client";

import React, { useState, useEffect } from "react";
import { Search, Loader2 } from "lucide-react";
import { Input } from "@/app/components/ui/input";
import { PartSearchResult } from "./PartSearch";

interface PartSearchSimpleProps {
  onSelect: (part: PartSearchResult) => void;
  initialValue?: string;
  placeholder?: string;
  disabled?: boolean;
}

/**
 * A simplified version of the PartSearch component
 * This is a temporary solution until the full PartSearch component is fixed
 */
export function PartSearch({
  onSelect,
  initialValue = "",
  placeholder = "Search for a part...",
  disabled = false
}: PartSearchSimpleProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [parts, setParts] = useState<PartSearchResult[]>([]);

  // Fetch parts when the search query changes
  useEffect(() => {
    if (!searchQuery || searchQuery.length < 2) return;

    const fetchParts = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/parts/search?search=${encodeURIComponent(searchQuery)}&limit=5`);
        if (!response.ok) {
          throw new Error("Failed to fetch parts");
        }
        const data = await response.json();
        setParts(data.data || []);
      } catch (error) {
        console.error("Error fetching parts:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const timer = setTimeout(() => {
      fetchParts();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle part selection
  const handlePartSelect = (part: PartSearchResult) => {
    onSelect(part);
    setSearchQuery("");
    setParts([]);
  };

  return (
    <div className="relative">
      <div className="relative">
        <Input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full pl-10"
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          ) : (
            <Search className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
      </div>

      {parts.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
          <ul className="py-1">
            {parts.map((part) => (
              <li
                key={part._id}
                className="px-4 py-2 hover:bg-accent cursor-pointer"
                onClick={() => handlePartSelect(part)}
              >
                <div className="font-medium">{part.name}</div>
                <div className="text-sm text-muted-foreground">
                  {part.part_id || part.partNumber || "No ID"}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
