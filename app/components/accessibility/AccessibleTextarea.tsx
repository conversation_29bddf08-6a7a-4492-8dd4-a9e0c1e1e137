'use client';

import React, { useState } from 'react';
import { Textarea } from '@/app/components/ui/textarea';
import { Label } from '@/app/components/ui/label';
import { cn } from '@/app/lib/utils';
import { LiveRegion } from './LiveRegion';

export interface AccessibleTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /**
   * Label for the textarea field (required for accessibility)
   */
  label: string;
  
  /**
   * Optional description for the textarea field
   */
  description?: string;
  
  /**
   * Error message to display when validation fails
   */
  error?: string;
  
  /**
   * Whether the textarea is required
   */
  isRequired?: boolean;
  
  /**
   * ID for the textarea field (will be generated if not provided)
   */
  id?: string;
  
  /**
   * Additional class name for the container
   */
  containerClassName?: string;
  
  /**
   * Additional class name for the label
   */
  labelClassName?: string;
  
  /**
   * Additional class name for the description
   */
  descriptionClassName?: string;
  
  /**
   * Additional class name for the error message
   */
  errorClassName?: string;
  
  /**
   * Whether to announce errors to screen readers
   */
  announceErrors?: boolean;
  
  /**
   * Character count display
   */
  showCharacterCount?: boolean;
  
  /**
   * Maximum character count
   */
  maxCharacterCount?: number;
}

/**
 * AccessibleTextarea component that enhances the standard Textarea with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleTextarea = React.forwardRef<HTMLTextAreaElement, AccessibleTextareaProps>(
  ({
    label,
    description,
    error,
    isRequired = false,
    id: propId,
    containerClassName,
    labelClassName,
    descriptionClassName,
    errorClassName,
    announceErrors = true,
    showCharacterCount = false,
    maxCharacterCount,
    className,
    onFocus,
    onBlur,
    onChange,
    value,
    defaultValue,
    ...props
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [wasBlurred, setWasBlurred] = useState(false);
    const [currentValue, setCurrentValue] = useState(value || defaultValue || '');
    const id = propId || React.useId();
    const descriptionId = `${id}-description`;
    const errorId = `${id}-error`;
    const counterId = `${id}-counter`;
    
    // Handle focus and blur events
    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(true);
      if (onFocus) onFocus(e);
    };
    
    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(false);
      setWasBlurred(true);
      if (onBlur) onBlur(e);
    };
    
    // Handle change event
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setCurrentValue(e.target.value);
      if (onChange) onChange(e);
    };
    
    // Determine if we should show the error
    const showError = !!error && (wasBlurred || !isFocused);
    
    // Calculate character count
    const characterCount = currentValue.toString().length;
    const isOverLimit = maxCharacterCount !== undefined && characterCount > maxCharacterCount;
    
    // Determine the aria-describedby attribute
    const ariaDescribedBy = [
      description ? descriptionId : null,
      showError ? errorId : null,
      showCharacterCount ? counterId : null
    ].filter(Boolean).join(' ') || undefined;
    
    return (
      <div className={cn("space-y-2", containerClassName)}>
        <Label 
          htmlFor={id}
          className={cn(
            showError && "text-destructive",
            labelClassName
          )}
        >
          {label}
          {isRequired && (
            <span className="text-destructive ml-1" aria-hidden="true">*</span>
          )}
        </Label>
        
        <Textarea
          id={id}
          ref={ref}
          aria-describedby={ariaDescribedBy}
          aria-invalid={showError || isOverLimit}
          aria-required={isRequired}
          required={isRequired}
          className={cn(
            (showError || isOverLimit) && "border-destructive focus-visible:ring-destructive",
            className
          )}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleChange}
          value={value}
          defaultValue={defaultValue}
          {...props}
        />
        
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            {description && (
              <p 
                id={descriptionId}
                className={cn(
                  "text-sm text-muted-foreground dark:text-text-secondary",
                  descriptionClassName
                )}
              >
                {description}
              </p>
            )}
            
            {showError && (
              <p 
                id={errorId}
                className={cn(
                  "text-sm font-medium text-destructive dark:text-destructive",
                  errorClassName
                )}
                role="alert"
              >
                {error}
              </p>
            )}
          </div>
          
          {showCharacterCount && (
            <p 
              id={counterId}
              className={cn(
                "text-sm text-muted-foreground dark:text-text-secondary",
                isOverLimit && "text-destructive dark:text-destructive"
              )}
              aria-live="polite"
            >
              {characterCount}
              {maxCharacterCount !== undefined && ` / ${maxCharacterCount}`}
            </p>
          )}
        </div>
        
        {/* Announce errors to screen readers */}
        {announceErrors && showError && (
          <LiveRegion politeness="assertive">
            {`Error for ${label}: ${error}`}
          </LiveRegion>
        )}
        
        {/* Announce character limit exceeded */}
        {isOverLimit && (
          <LiveRegion politeness="assertive">
            {`Character limit exceeded for ${label}. Maximum is ${maxCharacterCount}.`}
          </LiveRegion>
        )}
      </div>
    );
  }
);

AccessibleTextarea.displayName = 'AccessibleTextarea';

export default AccessibleTextarea;
