'use client';

import React from 'react';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/app/components/ui/form';
import { cn } from '@/app/lib/utils';
import { LiveRegion } from './LiveRegion';
import { ControllerProps, FieldPath, FieldValues } from 'react-hook-form';

export interface AccessibleFormFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends Omit<ControllerProps<TFieldValues, TName>, 'render'> {
  /**
   * Label for the form field (required for accessibility)
   */
  label: string;
  
  /**
   * Optional description for the form field
   */
  description?: string;
  
  /**
   * Whether the form field is required
   */
  isRequired?: boolean;
  
  /**
   * Additional class name for the form item
   */
  formItemClassName?: string;
  
  /**
   * Additional class name for the label
   */
  labelClassName?: string;
  
  /**
   * Additional class name for the description
   */
  descriptionClassName?: string;
  
  /**
   * Additional class name for the error message
   */
  messageClassName?: string;
  
  /**
   * Whether to announce errors to screen readers
   */
  announceErrors?: boolean;
  
  /**
   * Render function for the form control
   */
  render: (props: {
    field: any;
    fieldState: {
      invalid: boolean;
      isTouched: boolean;
      isDirty: boolean;
      error?: any;
    };
    formState: any;
    id: string;
    isRequired: boolean;
    describedBy: string | undefined;
  }) => React.ReactElement;
}

/**
 * AccessibleFormField component that enhances FormField with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export function AccessibleFormField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  label,
  description,
  isRequired = false,
  formItemClassName,
  labelClassName,
  descriptionClassName,
  messageClassName,
  announceErrors = true,
  render,
  ...props
}: AccessibleFormFieldProps<TFieldValues, TName>) {
  return (
    <FormField
      {...props}
      render={({ field, fieldState, formState }) => {
        const id = `form-field-${field.name}`;
        const descriptionId = `${id}-description`;
        const messageId = `${id}-message`;
        
        // Determine the aria-describedby attribute
        const describedBy = [
          description ? descriptionId : null,
          fieldState.error ? messageId : null
        ].filter(Boolean).join(' ') || undefined;
        
        return (
          <FormItem className={cn("space-y-2", formItemClassName)}>
            <FormLabel 
              className={cn(
                fieldState.error && "text-destructive",
                labelClassName
              )}
              htmlFor={id}
            >
              {label}
              {isRequired && (
                <span className="text-destructive ml-1" aria-hidden="true">*</span>
              )}
            </FormLabel>
            
            <FormControl>
              {render({
                field,
                fieldState,
                formState,
                id,
                isRequired,
                describedBy
              })}
            </FormControl>
            
            {description && (
              <FormDescription 
                id={descriptionId}
                className={descriptionClassName}
              >
                {description}
              </FormDescription>
            )}
            
            <FormMessage 
              id={messageId}
              className={messageClassName}
            />
            
            {/* Announce errors to screen readers */}
            {announceErrors && fieldState.error && (
              <LiveRegion politeness="assertive">
                {`Error for ${label}: ${fieldState.error.message}`}
              </LiveRegion>
            )}
          </FormItem>
        );
      }}
    />
  );
}

export default AccessibleFormField;
