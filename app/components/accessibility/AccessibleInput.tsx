'use client';

import React, { useState } from 'react';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { cn } from '@/app/lib/utils';
import { LiveRegion } from './LiveRegion';

export interface AccessibleInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /**
   * Label for the input field (required for accessibility)
   */
  label: string;
  
  /**
   * Optional description for the input field
   */
  description?: string;
  
  /**
   * Error message to display when validation fails
   */
  error?: string;
  
  /**
   * Whether the input is required
   */
  isRequired?: boolean;
  
  /**
   * ID for the input field (will be generated if not provided)
   */
  id?: string;
  
  /**
   * Additional class name for the container
   */
  containerClassName?: string;
  
  /**
   * Additional class name for the label
   */
  labelClassName?: string;
  
  /**
   * Additional class name for the description
   */
  descriptionClassName?: string;
  
  /**
   * Additional class name for the error message
   */
  errorClassName?: string;
  
  /**
   * Whether to announce errors to screen readers
   */
  announceErrors?: boolean;
}

/**
 * AccessibleInput component that enhances the standard Input with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleInput = React.forwardRef<HTMLInputElement, AccessibleInputProps>(
  ({
    label,
    description,
    error,
    isRequired = false,
    id: propId,
    containerClassName,
    labelClassName,
    descriptionClassName,
    errorClassName,
    announceErrors = true,
    className,
    onFocus,
    onBlur,
    ...props
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [wasBlurred, setWasBlurred] = useState(false);
    const id = propId || React.useId();
    const descriptionId = `${id}-description`;
    const errorId = `${id}-error`;
    
    // Handle focus and blur events
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      if (onFocus) onFocus(e);
    };
    
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      setWasBlurred(true);
      if (onBlur) onBlur(e);
    };
    
    // Determine if we should show the error
    const showError = !!error && (wasBlurred || !isFocused);
    
    // Determine the aria-describedby attribute
    const ariaDescribedBy = [
      description ? descriptionId : null,
      showError ? errorId : null
    ].filter(Boolean).join(' ') || undefined;
    
    return (
      <div className={cn("space-y-2", containerClassName)}>
        <Label 
          htmlFor={id}
          className={cn(
            showError && "text-destructive",
            labelClassName
          )}
        >
          {label}
          {isRequired && (
            <span className="text-destructive ml-1" aria-hidden="true">*</span>
          )}
        </Label>
        
        <Input
          id={id}
          ref={ref}
          aria-describedby={ariaDescribedBy}
          aria-invalid={showError}
          aria-required={isRequired}
          required={isRequired}
          className={cn(
            showError && "border-destructive focus-visible:ring-destructive",
            className
          )}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        
        {description && (
          <p 
            id={descriptionId}
            className={cn(
              "text-sm text-muted-foreground dark:text-text-secondary",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}
        
        {showError && (
          <p 
            id={errorId}
            className={cn(
              "text-sm font-medium text-destructive dark:text-destructive",
              errorClassName
            )}
            role="alert"
          >
            {error}
          </p>
        )}
        
        {/* Announce errors to screen readers */}
        {announceErrors && showError && (
          <LiveRegion politeness="assertive">
            {`Error for ${label}: ${error}`}
          </LiveRegion>
        )}
      </div>
    );
  }
);

AccessibleInput.displayName = 'AccessibleInput';

export default AccessibleInput;
