'use client';

import React, { useState, useEffect } from 'react';
import { 
  TableBody, 
  TableRow, 
  TableCell,
  TableFooter
} from '@/app/components/ui/table';
import { cn } from '@/app/lib/utils';
import { Loader2, AlertTriangle } from 'lucide-react';
import { AccessibleTable } from './AccessibleTable';
import { AccessibleTableHeader, AccessibleTableColumn } from './AccessibleTableHeader';
import { AccessibleTablePagination } from './AccessibleTablePagination';
import { LiveRegion } from './LiveRegion';

export interface AccessibleDataTableProps<T> {
  /**
   * Data to display in the table
   */
  data: T[];
  
  /**
   * Column definitions
   */
  columns: AccessibleTableColumn[];
  
  /**
   * Function to get a unique key for each row
   */
  getRowKey: (row: T) => string | number;
  
  /**
   * Function to get cell content for each column
   */
  getCellContent: (row: T, columnId: string) => React.ReactNode;
  
  /**
   * Table caption (required for accessibility)
   */
  caption: string;
  
  /**
   * Whether to visually hide the caption
   */
  hideCaption?: boolean;
  
  /**
   * Table summary for screen readers
   */
  summary?: string;
  
  /**
   * Whether the table is sortable
   */
  isSortable?: boolean;
  
  /**
   * Current sort column ID
   */
  sortColumnId?: string;
  
  /**
   * Current sort direction
   */
  sortDirection?: 'asc' | 'desc';
  
  /**
   * Callback when sort changes
   */
  onSortChange?: (columnId: string) => void;
  
  /**
   * Whether the table has pagination
   */
  hasPagination?: boolean;
  
  /**
   * Current page index (1-based)
   */
  pageIndex?: number;
  
  /**
   * Number of items per page
   */
  pageSize?: number;
  
  /**
   * Total number of items
   */
  totalItems?: number;
  
  /**
   * Callback when page changes
   */
  onPageChange?: (pageIndex: number) => void;
  
  /**
   * Callback when page size changes
   */
  onPageSizeChange?: (pageSize: number) => void;
  
  /**
   * Whether the table data is loading
   */
  isLoading?: boolean;
  
  /**
   * Error message to display
   */
  error?: string | null;
  
  /**
   * Function to render row actions
   */
  renderRowActions?: (row: T) => React.ReactNode;
  
  /**
   * Function to render table actions
   */
  renderTableActions?: () => React.ReactNode;
  
  /**
   * Function to render empty state
   */
  renderEmptyState?: () => React.ReactNode;
  
  /**
   * Function to render error state
   */
  renderErrorState?: (error: string) => React.ReactNode;
  
  /**
   * Function to render loading state
   */
  renderLoadingState?: () => React.ReactNode;
  
  /**
   * Function to render footer
   */
  renderFooter?: () => React.ReactNode;
  
  /**
   * Whether to enable keyboard navigation within the table
   */
  enableKeyboardNavigation?: boolean;
  
  /**
   * Whether to enable row hover state
   */
  enableRowHover?: boolean;
  
  /**
   * Whether to use striped rows
   */
  useStripedRows?: boolean;
  
  /**
   * Whether to use dense layout
   */
  useDenseLayout?: boolean;
  
  /**
   * Whether to use bordered layout
   */
  useBorderedLayout?: boolean;
  
  /**
   * Additional class name for the table container
   */
  containerClassName?: string;
  
  /**
   * Additional class name for the table
   */
  tableClassName?: string;
  
  /**
   * Additional class name for the table header
   */
  headerClassName?: string;
  
  /**
   * Additional class name for the table body
   */
  bodyClassName?: string;
  
  /**
   * Additional class name for the table rows
   */
  rowClassName?: string;
  
  /**
   * Additional class name for the table cells
   */
  cellClassName?: string;
  
  /**
   * Additional class name for the pagination
   */
  paginationClassName?: string;
  
  /**
   * ID for the table
   */
  id?: string;
}

/**
 * AccessibleDataTable component that provides a fully accessible data table
 * 
 * @param props - Component properties
 * @returns React component
 */
export function AccessibleDataTable<T>({
  data,
  columns,
  getRowKey,
  getCellContent,
  caption,
  hideCaption = false,
  summary,
  isSortable = false,
  sortColumnId,
  sortDirection,
  onSortChange,
  hasPagination = false,
  pageIndex = 1,
  pageSize = 10,
  totalItems,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
  error = null,
  renderRowActions,
  renderTableActions,
  renderEmptyState,
  renderErrorState,
  renderLoadingState,
  renderFooter,
  enableKeyboardNavigation = true,
  enableRowHover = true,
  useStripedRows = false,
  useDenseLayout = false,
  useBorderedLayout = true,
  containerClassName,
  tableClassName,
  headerClassName,
  bodyClassName,
  rowClassName,
  cellClassName,
  paginationClassName,
  id,
}: AccessibleDataTableProps<T>) {
  const [announcement, setAnnouncement] = useState<string | null>(null);
  
  // Update columns with sort state
  const columnsWithSort = columns.map(column => ({
    ...column,
    sortDirection: column.id === sortColumnId ? sortDirection : null,
  }));
  
  // Handle sort change
  const handleSortChange = (columnId: string) => {
    if (onSortChange) {
      onSortChange(columnId);
      
      // Announce sort change
      const column = columns.find(col => col.id === columnId);
      if (column) {
        const direction = column.id === sortColumnId && sortDirection === 'asc' ? 'descending' : 'ascending';
        setAnnouncement(`Table sorted by ${column.header} in ${direction} order`);
        
        // Clear announcement after 3 seconds
        setTimeout(() => {
          setAnnouncement(null);
        }, 3000);
      }
    }
  };
  
  // Calculate if the table is empty
  const isEmpty = !isLoading && !error && (!data || data.length === 0);
  
  // Calculate total items for pagination
  const calculatedTotalItems = totalItems !== undefined ? totalItems : data.length;
  
  return (
    <div className={cn("space-y-4", containerClassName)}>
      {/* Table actions */}
      {renderTableActions && (
        <div className="flex justify-end mb-4">
          {renderTableActions()}
        </div>
      )}
      
      <div className={cn(
        "relative rounded-lg",
        useBorderedLayout && "border border-gray-200 dark:border-gray-700",
        "bg-white dark:bg-gray-800",
        "shadow-sm"
      )}>
        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/50 dark:bg-gray-800/50 flex items-center justify-center z-10 rounded-lg">
            {renderLoadingState ? (
              renderLoadingState()
            ) : (
              <div className="flex flex-col items-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Loading data...</p>
              </div>
            )}
          </div>
        )}
        
        {/* Error state */}
        {error && !isLoading ? (
          renderErrorState ? (
            renderErrorState(error)
          ) : (
            <div className="p-8 text-center">
              <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
              <p className="text-destructive font-medium">{error}</p>
            </div>
          )
        ) : (
          <>
            {/* Empty state */}
            {isEmpty ? (
              renderEmptyState ? (
                renderEmptyState()
              ) : (
                <div className="p-8 text-center">
                  <p className="text-gray-500 dark:text-gray-400">No data available</p>
                </div>
              )
            ) : (
              /* Table */
              <AccessibleTable
                caption={caption}
                hideCaption={hideCaption}
                summary={summary}
                hasHeader={true}
                hasFooter={!!renderFooter}
                isSortable={isSortable}
                isSorting={!!sortColumnId}
                isLoading={isLoading}
                enableKeyboardNavigation={enableKeyboardNavigation}
                tableClassName={tableClassName}
                id={id}
              >
                {/* Table header */}
                <AccessibleTableHeader
                  columns={columnsWithSort}
                  onSort={isSortable ? handleSortChange : undefined}
                  className={headerClassName}
                  cellClassName={cellClassName}
                />
                
                {/* Table body */}
                <TableBody className={bodyClassName}>
                  {data.map((row, rowIndex) => (
                    <TableRow
                      key={getRowKey(row)}
                      className={cn(
                        enableRowHover && "hover:bg-gray-50 dark:hover:bg-gray-700/30",
                        useStripedRows && rowIndex % 2 === 1 && "bg-gray-50 dark:bg-gray-800/50",
                        useDenseLayout ? "h-10" : "h-12",
                        rowClassName
                      )}
                    >
                      {columns.map(column => (
                        <TableCell
                          key={`${getRowKey(row)}-${column.id}`}
                          className={cn(
                            useDenseLayout ? "py-2" : "py-4",
                            cellClassName
                          )}
                        >
                          {getCellContent(row, column.id)}
                        </TableCell>
                      ))}
                      
                      {/* Row actions */}
                      {renderRowActions && (
                        <TableCell className="text-right">
                          {renderRowActions(row)}
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
                
                {/* Table footer */}
                {renderFooter && (
                  <TableFooter>
                    {renderFooter()}
                  </TableFooter>
                )}
              </AccessibleTable>
            )}
          </>
        )}
      </div>
      
      {/* Pagination */}
      {hasPagination && !isEmpty && !error && (
        <AccessibleTablePagination
          pageIndex={pageIndex}
          pageSize={pageSize}
          totalItems={calculatedTotalItems}
          onPageChange={onPageChange || (() => {})}
          onPageSizeChange={onPageSizeChange}
          showPageSizeSelector={!!onPageSizeChange}
          className={paginationClassName}
          id={id ? `${id}-pagination` : undefined}
        />
      )}
      
      {/* Announcements */}
      {announcement && (
        <LiveRegion politeness="assertive">
          {announcement}
        </LiveRegion>
      )}
    </div>
  );
}
