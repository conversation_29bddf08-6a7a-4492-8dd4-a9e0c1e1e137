'use client';

import React from 'react';
import { Button } from '@/app/components/ui/button';
import { cn } from '@/app/lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { LiveRegion } from './LiveRegion';

export interface AccessibleTablePaginationProps {
  /**
   * Current page index (1-based)
   */
  pageIndex: number;
  
  /**
   * Number of items per page
   */
  pageSize: number;
  
  /**
   * Total number of items
   */
  totalItems: number;
  
  /**
   * Callback when page changes
   */
  onPageChange: (pageIndex: number) => void;
  
  /**
   * Available page sizes
   */
  pageSizeOptions?: number[];
  
  /**
   * Callback when page size changes
   */
  onPageSizeChange?: (pageSize: number) => void;
  
  /**
   * Whether to show the page size selector
   */
  showPageSizeSelector?: boolean;
  
  /**
   * Label for the page size selector
   */
  pageSizeSelectorLabel?: string;
  
  /**
   * Whether to announce page changes to screen readers
   */
  announcePageChanges?: boolean;
  
  /**
   * Additional class name for the pagination container
   */
  className?: string;
  
  /**
   * ID for the pagination component
   */
  id?: string;
}

/**
 * AccessibleTablePagination component that provides accessible pagination controls
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleTablePagination: React.FC<AccessibleTablePaginationProps> = ({
  pageIndex,
  pageSize,
  totalItems,
  onPageChange,
  pageSizeOptions = [10, 25, 50, 100],
  onPageSizeChange,
  showPageSizeSelector = true,
  pageSizeSelectorLabel = 'Items per page:',
  announcePageChanges = true,
  className,
  id,
}) => {
  const [announcement, setAnnouncement] = React.useState<string | null>(null);
  
  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));
  
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;
  
  // Function to determine which page numbers to show
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    
    // Always show first page
    pageNumbers.push(1);
    
    // Calculate range around current page
    const rangeStart = Math.max(2, pageIndex - 1);
    const rangeEnd = Math.min(totalPages - 1, pageIndex + 1);
    
    // Add ellipsis after first page if needed
    if (rangeStart > 2) {
      pageNumbers.push("...");
    }
    
    // Add pages in range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pageNumbers.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (rangeEnd < totalPages - 1) {
      pageNumbers.push("...");
    }
    
    // Always show last page if more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };
  
  // Handle page change
  const handlePageChange = (newPageIndex: number) => {
    if (newPageIndex < 1 || newPageIndex > totalPages) return;
    
    onPageChange(newPageIndex);
    
    if (announcePageChanges) {
      setAnnouncement(`Page ${newPageIndex} of ${totalPages}`);
      
      // Clear announcement after 3 seconds
      setTimeout(() => {
        setAnnouncement(null);
      }, 3000);
    }
  };
  
  // Handle page size change
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = parseInt(e.target.value, 10);
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize);
      
      if (announcePageChanges) {
        setAnnouncement(`Showing ${newPageSize} items per page`);
        
        // Clear announcement after 3 seconds
        setTimeout(() => {
          setAnnouncement(null);
        }, 3000);
      }
    }
  };
  
  // Calculate range of items being displayed
  const startItem = (pageIndex - 1) * pageSize + 1;
  const endItem = Math.min(pageIndex * pageSize, totalItems);
  
  return (
    <nav
      className={cn("flex flex-col sm:flex-row items-center justify-between gap-4", className)}
      aria-label="Table pagination"
      id={id}
    >
      {/* Page size selector */}
      {showPageSizeSelector && onPageSizeChange && (
        <div className="flex items-center space-x-2">
          <label htmlFor="page-size-selector" className="text-sm text-gray-500 dark:text-gray-400">
            {pageSizeSelectorLabel}
          </label>
          <select
            id="page-size-selector"
            className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            value={pageSize}
            onChange={handlePageSizeChange}
            aria-label="Select number of items per page"
          >
            {pageSizeOptions.map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>
      )}
      
      {/* Pagination info */}
      <div className="text-sm text-gray-500 dark:text-gray-400">
        Showing {startItem} to {endItem} of {totalItems} items
      </div>
      
      {/* Pagination controls */}
      <div className="flex items-center space-x-2">
        {/* Previous button */}
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300",
            pageIndex === 1 ? "opacity-50 cursor-not-allowed" : ""
          )}
          onClick={() => handlePageChange(pageIndex - 1)}
          disabled={pageIndex === 1}
          aria-label="Go to previous page"
        >
          <ChevronLeft size={16} className="mr-1" aria-hidden="true" />
          <span className="hidden sm:inline">Previous</span>
        </Button>
        
        {/* Page numbers */}
        <div className="flex items-center space-x-1" role="group" aria-label="Pagination">
          {getPageNumbers().map((page, index) => {
            if (page === "...") {
              return (
                <span
                  key={`ellipsis-${index}`}
                  className="px-2 text-gray-500 dark:text-gray-400"
                  aria-hidden="true"
                >
                  ...
                </span>
              );
            }
            
            const pageNum = page as number;
            const isCurrentPage = pageIndex === pageNum;
            
            return (
              <Button
                key={pageNum}
                variant={isCurrentPage ? "default" : "outline"}
                size="sm"
                className="w-8 h-8 p-0"
                onClick={() => handlePageChange(pageNum)}
                aria-label={`Page ${pageNum}`}
                aria-current={isCurrentPage ? "page" : undefined}
              >
                {pageNum}
              </Button>
            );
          })}
        </div>
        
        {/* Next button */}
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300",
            pageIndex === totalPages ? "opacity-50 cursor-not-allowed" : ""
          )}
          onClick={() => handlePageChange(pageIndex + 1)}
          disabled={pageIndex === totalPages}
          aria-label="Go to next page"
        >
          <span className="hidden sm:inline">Next</span>
          <ChevronRight size={16} className="ml-1" aria-hidden="true" />
        </Button>
      </div>
      
      {/* Announce page changes to screen readers */}
      {announcement && (
        <LiveRegion politeness="assertive">
          {announcement}
        </LiveRegion>
      )}
    </nav>
  );
};

export default AccessibleTablePagination;
