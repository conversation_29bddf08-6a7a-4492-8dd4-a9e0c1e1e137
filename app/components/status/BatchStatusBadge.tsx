'use client';

import React from 'react';
import { 
  CheckCircle2, 
  AlertCircle, 
  Clock, 
  Pause, 
  XCircle, 
  Beaker 
} from 'lucide-react';
import { Badge } from '@/app/components/ui/badge';
import { cn } from '@/app/lib/utils';
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/app/components/ui/tooltip';
import { useTheme } from '@/app/context/ThemeContext';
import { BATCH_STATUSES } from '../forms/BatchForm/BatchForm';

interface BatchStatusBadgeProps {
  status: string;
  showLabel?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showTooltip?: boolean;
}

/**
 * Component to display batch status with appropriate visual indicators
 */
export function BatchStatusBadge({
  status,
  showLabel = true,
  size = 'default',
  className,
  showTooltip = true,
}: BatchStatusBadgeProps) {
  const { theme } = useTheme();
  const normalizedStatus = status.toLowerCase().replace(' ', '_');
  
  // Define status configurations
  const statusConfig = {
    [BATCH_STATUSES.PENDING]: {
      icon: <Clock className={cn("text-amber-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Pending',
      description: 'Batch is planned but not yet started',
      className: cn(
        "bg-amber-100 text-amber-800 border-amber-300",
        "dark:bg-amber-900/30 dark:text-amber-500 dark:border-amber-800"
      )
    },
    [BATCH_STATUSES.IN_PROGRESS]: {
      icon: <Beaker className={cn("text-blue-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'In Progress',
      description: 'Batch is currently being produced',
      className: cn(
        "bg-blue-100 text-blue-800 border-blue-300",
        "dark:bg-blue-900/30 dark:text-blue-500 dark:border-blue-800"
      )
    },
    [BATCH_STATUSES.QUALITY_CHECK]: {
      icon: <AlertCircle className={cn("text-purple-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Quality Check',
      description: 'Batch is undergoing quality inspection',
      className: cn(
        "bg-purple-100 text-purple-800 border-purple-300",
        "dark:bg-purple-900/30 dark:text-purple-500 dark:border-purple-800"
      )
    },
    [BATCH_STATUSES.ON_HOLD]: {
      icon: <Pause className={cn("text-orange-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'On Hold',
      description: 'Batch production has been temporarily paused',
      className: cn(
        "bg-orange-100 text-orange-800 border-orange-300",
        "dark:bg-orange-900/30 dark:text-orange-500 dark:border-orange-800"
      )
    },
    [BATCH_STATUSES.COMPLETED]: {
      icon: <CheckCircle2 className={cn("text-green-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Completed',
      description: 'Batch has been successfully completed',
      className: cn(
        "bg-green-100 text-green-800 border-green-300",
        "dark:bg-green-900/30 dark:text-green-500 dark:border-green-800"
      )
    },
    [BATCH_STATUSES.CANCELLED]: {
      icon: <XCircle className={cn("text-red-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Cancelled',
      description: 'Batch has been cancelled',
      className: cn(
        "bg-red-100 text-red-800 border-red-300",
        "dark:bg-red-900/30 dark:text-red-500 dark:border-red-800"
      )
    },
    // Default fallback
    default: {
      icon: <AlertCircle className={cn("text-gray-500", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),
      description: 'Unknown batch status',
      className: cn(
        "bg-gray-100 text-gray-800 border-gray-300",
        "dark:bg-gray-800/50 dark:text-gray-400 dark:border-gray-700"
      )
    }
  };

  // Get the appropriate status configuration
  const config = statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.default;

  // Render the badge with optional tooltip
  const badge = (
    <Badge 
      variant="outline" 
      className={cn(
        config.className,
        size === 'sm' ? 'text-xs py-0 px-2' : size === 'lg' ? 'text-sm py-1 px-3' : 'text-xs py-0.5 px-2.5',
        className
      )}
    >
      {config.icon}
      {showLabel && <span className={cn("ml-1", size === 'sm' ? 'text-xs' : '')}>{config.label}</span>}
    </Badge>
  );

  // Wrap in tooltip if needed
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent>
            <p>{config.description}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badge;
}
