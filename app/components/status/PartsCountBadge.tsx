'use client';

import React from 'react';
import { Layers, AlertTriangle, CheckCircle2 } from 'lucide-react';
import { Badge } from '@/app/components/ui/badge';
import { cn } from '@/app/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';

interface PartsCountBadgeProps {
  assembly: Assembly;
  showIcon?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

/**
 * Component to display part count and availability with appropriate visual indicators
 */
export function PartsCountBadge({
  assembly,
  showIcon = true,
  size = 'default',
  className,
}: PartsCountBadgeProps) {
  // Check if assembly has valid parts using the correct field name (partsRequired)
  const hasValidParts = assembly &&
    assembly.partsRequired &&
    Array.isArray(assembly.partsRequired) &&
    assembly.partsRequired.length > 0;

  const partsCount = hasValidParts ? assembly.partsRequired.length : 0;

  // Check for missing part references in partsRequired array
  const missingRefsCount = hasValidParts
    ? assembly.partsRequired.filter(p => !p || !p.partId).length
    : 0;

  // Determine if all parts are available - simplified logic since partId can be string or object
  const allPartsReferenced = hasValidParts && missingRefsCount === 0;

  // Determine status based on parts
  const getStatus = () => {
    if (!hasValidParts) {
      return {
        label: `0 parts`,
        variant: 'outline' as const,
        icon: <Layers className={cn(
          size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
          'text-theme-secondary'
        )} />,
        description: 'This assembly has no parts',
      };
    }

    if (missingRefsCount > 0) {
      return {
        label: `${partsCount} parts (${missingRefsCount} invalid)`,
        variant: 'destructive' as const,
        icon: <AlertTriangle className={cn(
          size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
          'text-theme-error'
        )} />,
        description: `This assembly has ${missingRefsCount} parts with missing references`,
      };
    }

    if (allPartsReferenced) {
      return {
        label: `${partsCount} ${partsCount === 1 ? 'part' : 'parts'}`,
        variant: 'success' as const,
        icon: <CheckCircle2 className={cn(
          size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
          'text-theme-success'
        )} />,
        description: 'All parts are properly referenced',
      };
    }

    // Default status
    return {
      label: `${partsCount} ${partsCount === 1 ? 'part' : 'parts'}`,
      variant: 'secondary' as const,
      icon: <Layers className={cn(
        size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
        'text-theme-info'
      )} />,
      description: 'Parts information',
    };
  };

  const status = getStatus();

  // Custom variant styles using theme variables
  const getVariantStyles = () => {
    switch (status.variant) {
      case 'success':
        return 'bg-theme-success-light text-theme-success hover:bg-theme-success/10 border border-theme-success/20';
      case 'destructive':
        return 'bg-theme-error-light text-theme-error hover:bg-theme-error/10 border border-theme-error/20';
      case 'secondary':
        return 'bg-theme-info-light text-theme-info hover:bg-theme-info/10 border border-theme-info/20';
      case 'outline':
      default:
        return 'bg-theme-tertiary text-theme-secondary hover:bg-theme-hover border border-theme-secondary/20';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={cn(
              'flex items-center gap-1 font-normal',
              getVariantStyles(),
              size === 'sm' ? 'text-xs py-0 px-2' : size === 'lg' ? 'text-sm py-1 px-3' : 'text-xs py-0.5 px-2.5',
              className
            )}
          >
            {showIcon && status.icon}
            <span>{status.label}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{status.description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
