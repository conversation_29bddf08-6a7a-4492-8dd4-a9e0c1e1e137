'use client';

import React, { useState } from 'react';
import { 
  AccessibleTable,
  AccessibleTableHeader,
  AccessibleTablePagination,
  AccessibleDataTable,
  AccessibleTableColumn,
  AccessibleButton,
  AccessibleIconButton
} from '@/app/components/accessibility/index';
import { 
  TableBody, 
  TableRow, 
  TableCell,
  TableFooter
} from '@/app/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Badge } from '@/app/components/ui/badge';
import { Eye, Pencil, Trash2, ArrowUpDown } from 'lucide-react';
import { cn } from '@/app/lib/utils';

// Sample data for the tables
interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  stock: number;
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
}

const sampleProducts: Product[] = [
  { id: 1, name: 'Laptop', category: 'Electronics', price: 999.99, stock: 45, status: 'in-stock' },
  { id: 2, name: 'Smartphone', category: 'Electronics', price: 699.99, stock: 32, status: 'in-stock' },
  { id: 3, name: 'Headphones', category: 'Audio', price: 149.99, stock: 78, status: 'in-stock' },
  { id: 4, name: 'Monitor', category: 'Electronics', price: 249.99, stock: 5, status: 'low-stock' },
  { id: 5, name: 'Keyboard', category: 'Accessories', price: 59.99, stock: 0, status: 'out-of-stock' },
  { id: 6, name: 'Mouse', category: 'Accessories', price: 29.99, stock: 12, status: 'in-stock' },
  { id: 7, name: 'Speakers', category: 'Audio', price: 89.99, stock: 3, status: 'low-stock' },
  { id: 8, name: 'Tablet', category: 'Electronics', price: 349.99, stock: 18, status: 'in-stock' },
  { id: 9, name: 'Webcam', category: 'Accessories', price: 79.99, stock: 0, status: 'out-of-stock' },
  { id: 10, name: 'Microphone', category: 'Audio', price: 119.99, stock: 7, status: 'low-stock' },
];

/**
 * Example component demonstrating the use of accessible table components
 */
export function AccessibleTablesExample() {
  // State for basic table example
  const [sortColumnId, setSortColumnId] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // State for data table example
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Columns for the tables
  const columns: AccessibleTableColumn[] = [
    { id: 'name', header: 'Product Name', sortable: true },
    { id: 'category', header: 'Category', sortable: true },
    { id: 'price', header: 'Price', sortable: true },
    { id: 'stock', header: 'Stock', sortable: true },
    { id: 'status', header: 'Status', sortable: false }
  ];
  
  // Handle sort change for basic table
  const handleSortChange = (columnId: string) => {
    if (columnId === sortColumnId) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumnId(columnId);
      setSortDirection('asc');
    }
  };
  
  // Sort data for basic table
  const sortedData = [...sampleProducts].sort((a, b) => {
    const aValue = a[sortColumnId as keyof Product];
    const bValue = b[sortColumnId as keyof Product];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    }
    
    return 0;
  });
  
  // Get cell content for data table
  const getCellContent = (row: Product, columnId: string) => {
    switch (columnId) {
      case 'name':
        return row.name;
      case 'category':
        return row.category;
      case 'price':
        return `$${row.price.toFixed(2)}`;
      case 'stock':
        return row.stock;
      case 'status':
        return (
          <Badge
            variant={
              row.status === 'in-stock'
                ? 'success'
                : row.status === 'low-stock'
                ? 'warning'
                : 'destructive'
            }
          >
            {row.status === 'in-stock'
              ? 'In Stock'
              : row.status === 'low-stock'
              ? 'Low Stock'
              : 'Out of Stock'}
          </Badge>
        );
      default:
        return null;
    }
  };
  
  // Render row actions for data table
  const renderRowActions = (row: Product) => (
    <div className="flex justify-end space-x-2">
      <AccessibleIconButton
        icon={<Eye size={16} />}
        accessibleLabel={`View ${row.name}`}
        size="sm"
        variant="ghost"
      />
      <AccessibleIconButton
        icon={<Pencil size={16} />}
        accessibleLabel={`Edit ${row.name}`}
        size="sm"
        variant="ghost"
      />
      <AccessibleIconButton
        icon={<Trash2 size={16} />}
        accessibleLabel={`Delete ${row.name}`}
        size="sm"
        variant="ghost"
      />
    </div>
  );
  
  // Render table actions for data table
  const renderTableActions = () => (
    <AccessibleButton variant="default" size="sm">
      Add Product
    </AccessibleButton>
  );
  
  // Simulate loading
  const handleSimulateLoading = () => {
    setIsLoading(true);
    setError(null);
    
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };
  
  // Simulate error
  const handleSimulateError = () => {
    setIsLoading(true);
    setError(null);
    
    setTimeout(() => {
      setIsLoading(false);
      setError('Failed to load products. Please try again later.');
    }, 1000);
  };
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Basic Accessible Table</CardTitle>
        </CardHeader>
        <CardContent>
          <AccessibleTable
            caption="Product Inventory"
            summary="This table shows product inventory information including name, category, price, stock, and status."
            hideCaption={false}
            isSortable={true}
            isSorting={true}
            enableKeyboardNavigation={true}
          >
            <AccessibleTableHeader
              columns={columns.map(column => ({
                ...column,
                sortDirection: column.id === sortColumnId ? sortDirection : null
              }))}
              onSort={handleSortChange}
            />
            <TableBody>
              {sortedData.slice(0, 5).map((product) => (
                <TableRow key={product.id}>
                  <TableCell>{product.name}</TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell>${product.price.toFixed(2)}</TableCell>
                  <TableCell>{product.stock}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        product.status === 'in-stock'
                          ? 'success'
                          : product.status === 'low-stock'
                          ? 'warning'
                          : 'destructive'
                      }
                    >
                      {product.status === 'in-stock'
                        ? 'In Stock'
                        : product.status === 'low-stock'
                        ? 'Low Stock'
                        : 'Out of Stock'}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell colSpan={3}>Total Products</TableCell>
                <TableCell colSpan={2}>{sortedData.length}</TableCell>
              </TableRow>
            </TableFooter>
          </AccessibleTable>
          
          <div className="mt-4">
            <AccessibleTablePagination
              pageIndex={1}
              pageSize={5}
              totalItems={sampleProducts.length}
              onPageChange={() => {}}
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Accessible Data Table</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4 mb-4">
            <AccessibleButton onClick={handleSimulateLoading}>
              Simulate Loading
            </AccessibleButton>
            <AccessibleButton onClick={handleSimulateError} variant="destructive">
              Simulate Error
            </AccessibleButton>
          </div>
          
          <AccessibleDataTable
            data={sampleProducts}
            columns={columns}
            getRowKey={(row) => row.id}
            getCellContent={getCellContent}
            caption="Product Inventory"
            summary="This table shows product inventory information including name, category, price, stock, and status."
            hideCaption={false}
            isSortable={true}
            sortColumnId={sortColumnId}
            sortDirection={sortDirection}
            onSortChange={handleSortChange}
            hasPagination={true}
            pageIndex={pageIndex}
            pageSize={pageSize}
            totalItems={sampleProducts.length}
            onPageChange={setPageIndex}
            onPageSizeChange={setPageSize}
            isLoading={isLoading}
            error={error}
            renderRowActions={renderRowActions}
            renderTableActions={renderTableActions}
            enableKeyboardNavigation={true}
            enableRowHover={true}
            useStripedRows={true}
            useBorderedLayout={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}
