'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  AccessibleInput,
  AccessibleTextarea,
  AccessibleSelect,
  AccessibleCheckbox,
  AccessibleFormField,
  AccessibleButton
} from '@/app/components/accessibility/index';
import { Form } from '@/app/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/app/components/ui/card';
import { Input } from '@/app/components/ui/input';
import { Textarea } from '@/app/components/ui/textarea';
import { Checkbox } from '@/app/components/ui/checkbox';

// Define form schema with Zod
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  message: z.string().min(10, { message: "Message must be at least 10 characters" }),
  category: z.string().min(1, { message: "Please select a category" }),
  agreeTerms: z.literal(true, {
    errorMap: () => ({ message: "You must agree to the terms and conditions" }),
  }),
});

type FormValues = z.infer<typeof formSchema>;

/**
 * Example component demonstrating the use of accessible form components
 */
export function AccessibleFormsExample() {
  const [formData, setFormData] = useState<FormValues | null>(null);
  
  // Initialize form with react-hook-form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      message: '',
      category: '',
      agreeTerms: false,
    },
  });
  
  // Handle form submission
  const onSubmit = (data: FormValues) => {
    setFormData(data);
  };
  
  // Category options for select
  const categoryOptions = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'support', label: 'Technical Support' },
    { value: 'billing', label: 'Billing Question' },
    { value: 'feedback', label: 'Feedback' },
  ];
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Accessible Form Components</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Standalone Components (not connected to form) */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Standalone Components</h3>
              
              <AccessibleInput
                label="Your Name"
                placeholder="Enter your name"
                description="We'll use this to address you in our response"
                isRequired
              />
              
              <AccessibleTextarea
                label="Your Message"
                placeholder="Enter your message"
                description="Please provide as much detail as possible"
                showCharacterCount
                maxCharacterCount={500}
                isRequired
              />
              
              <AccessibleSelect
                label="Category"
                options={categoryOptions}
                placeholder="Select a category"
                description="This helps us route your message to the right team"
                isRequired
              />
              
              <AccessibleCheckbox
                label="I agree to the terms and conditions"
                description="By checking this box, you agree to our Terms of Service and Privacy Policy"
                isRequired
              />
            </div>
            
            {/* Form-Connected Components */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Form-Connected Components</h3>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <AccessibleFormField
                    control={form.control}
                    name="name"
                    label="Your Name"
                    description="We'll use this to address you in our response"
                    isRequired
                    render={({ field, id, isRequired, describedBy }) => (
                      <Input
                        {...field}
                        id={id}
                        placeholder="Enter your name"
                        aria-required={isRequired}
                        aria-describedby={describedBy}
                      />
                    )}
                  />
                  
                  <AccessibleFormField
                    control={form.control}
                    name="email"
                    label="Email Address"
                    description="We'll never share your email with anyone else"
                    isRequired
                    render={({ field, id, isRequired, describedBy }) => (
                      <Input
                        {...field}
                        id={id}
                        type="email"
                        placeholder="Enter your email"
                        aria-required={isRequired}
                        aria-describedby={describedBy}
                      />
                    )}
                  />
                  
                  <AccessibleFormField
                    control={form.control}
                    name="message"
                    label="Your Message"
                    description="Please provide as much detail as possible"
                    isRequired
                    render={({ field, id, isRequired, describedBy }) => (
                      <Textarea
                        {...field}
                        id={id}
                        placeholder="Enter your message"
                        aria-required={isRequired}
                        aria-describedby={describedBy}
                      />
                    )}
                  />
                  
                  <AccessibleFormField
                    control={form.control}
                    name="category"
                    label="Category"
                    description="This helps us route your message to the right team"
                    isRequired
                    render={({ field, id, isRequired, describedBy }) => (
                      <AccessibleSelect
                        label=""
                        options={categoryOptions}
                        placeholder="Select a category"
                        value={field.value}
                        onValueChange={field.onChange}
                        id={id}
                        isRequired={isRequired}
                        error={form.formState.errors.category?.message}
                        containerClassName="mt-0"
                      />
                    )}
                  />
                  
                  <AccessibleFormField
                    control={form.control}
                    name="agreeTerms"
                    label=""
                    isRequired
                    render={({ field, id, isRequired, describedBy }) => (
                      <AccessibleCheckbox
                        label="I agree to the terms and conditions"
                        description="By checking this box, you agree to our Terms of Service and Privacy Policy"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        id={id}
                        isRequired={isRequired}
                        error={form.formState.errors.agreeTerms?.message}
                      />
                    )}
                  />
                  
                  <AccessibleButton type="submit">
                    Submit Form
                  </AccessibleButton>
                </form>
              </Form>
            </div>
          </div>
        </CardContent>
        
        {formData && (
          <CardFooter className="flex-col items-start">
            <h3 className="text-lg font-medium mb-2">Form Submission</h3>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md w-full overflow-auto">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
