'use client';

import React, { useState } from 'react';
import { 
  AccessibleButton, 
  AccessibleIconButton, 
  AccessibleLink 
} from '@/app/components/accessibility/index';
import { 
  Home, 
  Plus, 
  Menu, 
  Settings, 
  Bell, 
  Search,
  ExternalLink
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';

/**
 * Example component demonstrating the use of accessible interactive controls
 */
export function AccessibleButtonsExample() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Accessible Buttons</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <AccessibleButton>Standard Button</AccessibleButton>
            
            <AccessibleButton variant="destructive">
              Delete Item
            </AccessibleButton>
            
            <AccessibleButton 
              variant="outline" 
              accessibleLabel="Save current document"
            >
              Save
            </AccessibleButton>
            
            <AccessibleButton 
              isExpanded={menuOpen}
              onClick={() => setMenuOpen(!menuOpen)}
              controls="example-menu"
            >
              Toggle Menu
            </AccessibleButton>
            
            <AccessibleButton 
              isPressed={notificationsEnabled}
              onClick={() => setNotificationsEnabled(!notificationsEnabled)}
            >
              {notificationsEnabled ? 'Disable' : 'Enable'} Notifications
            </AccessibleButton>
            
            <AccessibleButton 
              disabled 
              isFocusableWhenDisabled
              accessibleLabel="Submit form (disabled until form is complete)"
            >
              Submit
            </AccessibleButton>
          </div>
          
          {menuOpen && (
            <div 
              id="example-menu" 
              className="p-4 border rounded-md mt-2 bg-background"
            >
              This is the menu content that is controlled by the toggle button.
            </div>
          )}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Accessible Icon Buttons</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <AccessibleIconButton
              icon={<Home />}
              accessibleLabel="Go to home page"
            />
            
            <AccessibleIconButton
              icon={<Plus />}
              accessibleLabel="Add new item"
              variant="default"
            />
            
            <AccessibleIconButton
              icon={<Menu />}
              accessibleLabel="Toggle navigation menu"
              isExpanded={menuOpen}
              onClick={() => setMenuOpen(!menuOpen)}
              controls="example-menu"
            />
            
            <AccessibleIconButton
              icon={<Settings />}
              accessibleLabel="Open settings"
              variant="outline"
            />
            
            <AccessibleIconButton
              icon={<Bell />}
              accessibleLabel="Toggle notifications"
              isPressed={notificationsEnabled}
              onClick={() => setNotificationsEnabled(!notificationsEnabled)}
              variant="secondary"
            />
            
            <AccessibleIconButton
              icon={<Search />}
              accessibleLabel="Search"
              size="lg"
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Accessible Links</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex flex-wrap gap-4">
              <AccessibleLink href="/dashboard">
                Dashboard
              </AccessibleLink>
              
              <AccessibleLink 
                href="/profile" 
                isActive
              >
                Profile (Current Page)
              </AccessibleLink>
              
              <AccessibleLink 
                href="https://example.com" 
                isExternal
              >
                External Documentation
              </AccessibleLink>
              
              <AccessibleLink 
                href="https://example.com/api" 
                isExternal 
                showExternalIcon={false}
                accessibleLabel="API Documentation (opens in new tab)"
              >
                API Docs <ExternalLink size={14} className="inline" />
              </AccessibleLink>
              
              <AccessibleLink 
                href="/settings" 
                accessibleLabel="Go to application settings"
              >
                <Settings size={16} className="mr-1 inline" />
                Settings
              </AccessibleLink>
            </div>
            
            <div className="flex gap-2 mt-4">
              <AccessibleLink 
                href="/dashboard" 
                accessibleLabel="Go to dashboard"
              >
                <Home />
              </AccessibleLink>
              
              <AccessibleLink 
                href="/settings" 
                accessibleLabel="Go to settings"
              >
                <Settings />
              </AccessibleLink>
              
              <AccessibleLink 
                href="/notifications" 
                accessibleLabel="View notifications"
              >
                <Bell />
              </AccessibleLink>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
