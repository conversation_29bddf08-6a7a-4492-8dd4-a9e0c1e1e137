import { ReactNode } from 'react';

/**
 * Props for the main component
 */
export interface ComponentProps {
  /** Title to display at the top of the component */
  title: string;
  /** Description text shown below the title */
  description: string;
  /** Child elements to render in the expandable section */
  children: ReactNode;
}

/**
 * Props for the client component
 */
export interface ClientComponentProps {
  /** Child elements to render in the expandable section */
  children: ReactNode;
} 