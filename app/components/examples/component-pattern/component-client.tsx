"use client";

import { useState } from 'react';
import { ClientComponentProps } from './types';

/**
 * Client Component that handles interactive elements
 * 
 * This component is responsible for handling client-side interactivity
 * while keeping the JS bundle size minimal
 */
export default function ComponentClient({ children }: ClientComponentProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  return (
    <div className="mt-4">
      <div className={`overflow-hidden transition-all ${isExpanded ? 'max-h-96' : 'max-h-24'}`}>
        {children}
      </div>
      
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        {isExpanded ? 'Show Less' : 'Show More'}
      </button>
    </div>
  );
} 