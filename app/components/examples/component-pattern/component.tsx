// This is a Server Component by default (no "use client" directive)
import ComponentClient from './component-client';
import { ComponentProps } from './types';

/**
 * Example component following Next.js best practices
 * 
 * Uses Server Component for static parts and delegates client-side
 * interactivity to a dedicated Client Component lower in the tree
 */
export default function ExampleComponent({ 
  title, 
  description, 
  children 
}: ComponentProps) {
  // Any server-side data fetching or logic can be done here
  // without needing to send JS to the client
  
  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-xl font-bold">{title}</h2>
      <p className="mt-2 text-gray-600 dark:text-gray-300">{description}</p>
      
      {/* Client component is only used for interactive parts */}
      <ComponentClient>{children}</ComponentClient>
    </div>
  );
} 