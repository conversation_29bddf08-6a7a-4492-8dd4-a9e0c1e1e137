'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { 
  Layers, 
  Calendar, 
  ClipboardList, 
  FileText, 
  Link2, 
  Edit, 
  Trash2,
  Package,
  Clock,
  BarChart3,
  X
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useTheme } from '@/app/context/ThemeContext';
import { Button } from '@/app/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogClose
} from '@/app/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { BatchStatusBadge } from '@/app/components/status/BatchStatusBadge';
import BatchLogsView from '@/app/components/logs/BatchLogsView';
import BatchInventoryView from '@/app/components/inventory/BatchInventoryView';

interface Part {
  _id: string;
  name: string;
  description?: string;
}

interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
}

interface WorkOrder {
  _id: string;
  woNumber: string;
  status: string;
}

interface Batch {
  _id: string;
  batchCode: string;
  partId?: string | Part;
  assemblyId?: string | Assembly;
  quantityPlanned: number;
  quantityProduced?: number;
  startDate: string;
  endDate?: string;
  status: string;
  notes?: string;
  workOrderId: string | WorkOrder;
  createdAt: string;
  updatedAt: string;
}

interface BatchDetailsViewProps {
  batch: Batch;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (batch: Batch) => void;
  onDelete?: (batch: Batch) => void;
}

export default function BatchDetailsView({
  batch,
  isOpen,
  onClose,
  onEdit,
  onDelete
}: BatchDetailsViewProps) {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('details');
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  // Get part or assembly name
  const getItemName = () => {
    if (batch.partId) {
      return typeof batch.partId === 'string' 
        ? 'Unknown Part' 
        : batch.partId.name;
    }
    if (batch.assemblyId) {
      return typeof batch.assemblyId === 'string'
        ? 'Unknown Assembly'
        : batch.assemblyId.name;
    }
    return 'N/A';
  };

  // Get work order number
  const getWorkOrderNumber = () => {
    if (!batch.workOrderId) return 'N/A';
    return typeof batch.workOrderId === 'string'
      ? batch.workOrderId
      : batch.workOrderId.woNumber;
  };

  // Format date for display
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'Not set';
    return format(new Date(date), 'MMM d, yyyy');
  };

  // Calculate completion percentage
  const getCompletionPercentage = () => {
    if (!batch.quantityProduced || !batch.quantityPlanned) return 0;
    const percentage = (batch.quantityProduced / batch.quantityPlanned) * 100;
    return Math.min(100, Math.max(0, percentage));
  };

  // Handle edit button click
  const handleEdit = () => {
    if (onEdit) {
      onEdit(batch);
      onClose();
    }
  };

  // Handle delete button click
  const handleDelete = () => {
    setIsDeleteConfirmOpen(true);
  };

  // Confirm delete
  const confirmDelete = () => {
    if (onDelete) {
      onDelete(batch);
      setIsDeleteConfirmOpen(false);
      onClose();
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center text-xl">
              <Layers className="mr-2 h-5 w-5" />
              Batch Details
            </DialogTitle>
            <DialogDescription>
              View detailed information about batch <span className="font-mono">{batch.batchCode}</span>
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="details" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Details
              </TabsTrigger>
              <TabsTrigger value="logs" className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                Activity Log
              </TabsTrigger>
              <TabsTrigger value="inventory" className="flex items-center">
                <Package className="mr-2 h-4 w-4" />
                Inventory
              </TabsTrigger>
            </TabsList>

            {/* Details Tab */}
            <TabsContent value="details" className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <ClipboardList className="mr-2 h-4 w-4 text-muted-foreground" />
                      Batch Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <dl className="space-y-4">
                      <div>
                        <dt className="text-sm text-muted-foreground">Batch Code</dt>
                        <dd className="font-medium">{batch.batchCode}</dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Status</dt>
                        <dd><BatchStatusBadge status={batch.status} /></dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Item Type</dt>
                        <dd>{batch.partId ? 'Part' : batch.assemblyId ? 'Assembly' : 'N/A'}</dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Item Name</dt>
                        <dd>{getItemName()}</dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Work Order</dt>
                        <dd className="font-mono text-sm">{getWorkOrderNumber()}</dd>
                      </div>
                    </dl>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <BarChart3 className="mr-2 h-4 w-4 text-muted-foreground" />
                      Production Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <dl className="space-y-4">
                      <div>
                        <dt className="text-sm text-muted-foreground">Quantity Planned</dt>
                        <dd className="font-medium">{batch.quantityPlanned}</dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Quantity Produced</dt>
                        <dd className="font-medium">{batch.quantityProduced || 0}</dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Completion</dt>
                        <dd className="mt-1">
                          <div className="w-full bg-muted rounded-full h-2.5">
                            <div 
                              className="bg-primary h-2.5 rounded-full" 
                              style={{ width: `${getCompletionPercentage()}%` }}
                            ></div>
                          </div>
                          <div className="flex justify-between text-xs mt-1">
                            <span>{getCompletionPercentage().toFixed(0)}%</span>
                            <span>{batch.quantityProduced || 0} / {batch.quantityPlanned}</span>
                          </div>
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">Start Date</dt>
                        <dd>{formatDate(batch.startDate)}</dd>
                      </div>
                      <div>
                        <dt className="text-sm text-muted-foreground">End Date</dt>
                        <dd>{formatDate(batch.endDate)}</dd>
                      </div>
                    </dl>
                  </CardContent>
                </Card>
              </div>

              {/* Notes Section */}
              {batch.notes && (
                <Card className="mt-6">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <FileText className="mr-2 h-4 w-4 text-muted-foreground" />
                      Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm whitespace-pre-line">{batch.notes}</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Logs Tab */}
            <TabsContent value="logs" className="pt-4">
              <BatchLogsView batchId={batch._id.toString()} />
            </TabsContent>

            {/* Inventory Tab */}
            <TabsContent value="inventory" className="pt-4">
              <BatchInventoryView batchId={batch._id.toString()} />
            </TabsContent>
          </Tabs>

          <DialogFooter className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground">
              Created: {formatDate(batch.createdAt)}
              {batch.updatedAt && batch.updatedAt !== batch.createdAt && (
                <> • Updated: {formatDate(batch.updatedAt)}</>
              )}
            </div>
            <div className="flex space-x-2">
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEdit}
                  className="flex items-center"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDelete}
                  className="flex items-center"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-xl text-red-600 dark:text-red-400">
              <Trash2 className="mr-2 h-5 w-5" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete batch <span className="font-mono">{batch.batchCode}</span>?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              Deleting this batch will remove all associated data, including logs and inventory transactions.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
            >
              Delete Batch
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
