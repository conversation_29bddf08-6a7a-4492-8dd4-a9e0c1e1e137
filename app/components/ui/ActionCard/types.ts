import { ReactNode } from 'react';

/**
 * Props for the ActionCard component
 */
export interface ActionCardProps {
  /** Icon displayed next to the label */
  icon?: ReactNode;
  /** Text label for the action */
  label: string;
  /** Click handler for the action */
  onClick?: () => void;
  /** Color theme for the action */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Additional CSS classes */
  className?: string;
}

/**
 * Color class mapping for different themes
 */
export const colorClasses = {
  blue: 'text-blue-500',
  green: 'text-green-500',
  red: 'text-red-500',
  yellow: 'text-yellow-500',
  purple: 'text-purple-500',
  orange: 'text-orange-500',
  gray: 'text-gray-500'
}; 