"use client";

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ActionCard from './ActionCard';
import { Package } from 'lucide-react';

// Mock the ThemeContext
jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({
    theme: 'light',
    toggleTheme: jest.fn(),
  }),
}));

describe('ActionCard', () => {
  // Default props for the component
  const defaultProps = {
    label: 'Add Product',
    icon: <Package data-testid="package-icon" />,
    color: 'blue' as const,
    onClick: jest.fn(),
  };

  // Setup function to render the component with props
  const setup = (props = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return render(<ActionCard {...mergedProps} />);
  };

  it('renders the label correctly', () => {
    setup();
    expect(screen.getByText('Add Product')).toBeInTheDocument();
  });

  it('renders the icon', () => {
    setup();
    expect(screen.getByTestId('package-icon')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    setup({ onClick: handleClick });
    
    fireEvent.click(screen.getByText('Add Product'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies the correct color class', () => {
    setup({ color: 'green' });
    // In a real test, you would check for specific color classes
    // This is a simplified example
    const button = screen.getByText('Add Product').closest('button');
    expect(button).toBeInTheDocument();
  });

  it('renders without an icon', () => {
    setup({ icon: undefined });
    expect(screen.queryByTestId('package-icon')).not.toBeInTheDocument();
    expect(screen.getByText('Add Product')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'custom-class';
    setup({ className: customClass });
    
    const button = screen.getByText('Add Product').closest('button');
    expect(button).toHaveClass(customClass);
  });
});
