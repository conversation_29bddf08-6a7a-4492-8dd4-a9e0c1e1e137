/**
 * Event type for calendar events
 */
export interface CalendarEvent {
  /** Date the event occurs on */
  date: Date;
  /** Title or description of the event */
  title: string;
  /** Type of event (task, meeting, deadline, etc.) */
  type: string;
}

/**
 * Day object for calendar grid
 */
export interface CalendarDay {
  /** Day number (1-31) */
  day: number;
  /** Whether the day is in the current month */
  currentMonth: boolean;
  /** Date object for the day */
  date: Date;
}

/**
 * Date format options
 */
export type DateFormat = 'MM/DD/YYYY' | 'DD/MM/YYYY';

/**
 * Props for the CalendarComponent
 */
export interface CalendarComponentProps {
  /** Initial events to display */
  initialEvents?: CalendarEvent[];
  /** Initial selected date */
  initialSelectedDate?: Date | null;
  /** Initial date format */
  initialDateFormat?: DateFormat;
  /** Custom event color mapping */
  eventColors?: Record<string, string>;
  /** Optional className */
  className?: string;
} 