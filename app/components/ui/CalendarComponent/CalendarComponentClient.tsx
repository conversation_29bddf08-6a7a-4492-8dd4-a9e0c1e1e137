"use client";

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { CalendarComponentProps, CalendarDay, CalendarEvent, DateFormat } from './types';

/**
 * Client component for calendar with interactive features
 */
const CalendarComponentClient: React.FC<CalendarComponentProps> = ({
  initialEvents = [],
  initialSelectedDate = null,
  initialDateFormat = 'MM/DD/YYYY',
  eventColors = {
    task: 'bg-blue-500',
    meeting: 'bg-purple-500',
    deadline: 'bg-red-500'
  },
  className = ''
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [showYear, setShowYear] = useState(false);
  const [selectedDateFormat, setSelectedDateFormat] = useState<DateFormat>(initialDateFormat);
  const [selectedDate, setSelectedDate] = useState<Date | null>(initialSelectedDate);
  const [events] = useState<CalendarEvent[]>(initialEvents.length > 0 ? initialEvents : [
    { date: new Date(2023, 3, 5), title: 'Inventory Check', type: 'task' },
    { date: new Date(2023, 3, 12), title: 'Supplier Meeting', type: 'meeting' },
    { date: new Date(2023, 3, 15), title: 'Order Deadline', type: 'deadline' },
    { date: new Date(2023, 3, 20), title: 'Maintenance', type: 'task' },
    { date: new Date(2023, 3, 25), title: 'Quarterly Review', type: 'meeting' },
  ]);

  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const getMonthData = (date: Date): CalendarDay[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days: CalendarDay[] = [];
    let day = 1;

    // Previous month days
    for (let i = 0; i < startingDayOfWeek; i++) {
      const prevMonthLastDay = new Date(year, month, 0).getDate();
      days.push({
        day: prevMonthLastDay - startingDayOfWeek + i + 1,
        currentMonth: false,
        date: new Date(year, month - 1, prevMonthLastDay - startingDayOfWeek + i + 1),
      });
    }

    // Current month days
    while (day <= daysInMonth) {
      days.push({
        day,
        currentMonth: true,
        date: new Date(year, month, day),
      });
      day++;
    }

    // Next month days
    const remainingDays = 7 - (days.length % 7);
    if (remainingDays < 7) {
      for (let i = 1; i <= remainingDays; i++) {
        days.push({
          day: i,
          currentMonth: false,
          date: new Date(year, month + 1, i),
        });
      }
    }

    return days;
  };

  const handlePrevMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
    );
  };

  const handlePrevYear = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), 1)
    );
  };

  const handleNextYear = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), 1)
    );
  };

  const handleMonthClick = () => {
    setShowYear(!showYear);
  };

  const handleMonthSelect = (monthIndex: number) => {
    setCurrentDate(new Date(currentDate.getFullYear(), monthIndex, 1));
    setShowYear(false);
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return selectedDateFormat === 'MM/DD/YYYY'
      ? `${month}/${day}/${year}`
      : `${day}/${month}/${year}`;
  };

  const hasEvent = (date: Date) => {
    return events.some(
      (event) =>
        event.date.getDate() === date.getDate() &&
        event.date.getMonth() === date.getMonth() &&
        event.date.getFullYear() === date.getFullYear()
    );
  };

  const getEventType = (date: Date) => {
    const event = events.find(
      (event) =>
        event.date.getDate() === date.getDate() &&
        event.date.getMonth() === date.getMonth() &&
        event.date.getFullYear() === date.getFullYear()
    );
    return event ? event.type : null;
  };

  const getEventTitle = (date: Date) => {
    const event = events.find(
      (event) =>
        event.date.getDate() === date.getDate() &&
        event.date.getMonth() === date.getMonth() &&
        event.date.getFullYear() === date.getFullYear()
    );
    return event ? event.title : null;
  };

  const getEventColor = (type: string | null) => {
    if (!type) return 'bg-gray-400';
    return eventColors[type] || 'bg-gray-400';
  };

  const days = getMonthData(currentDate);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <button
            onClick={showYear ? handlePrevYear : handlePrevMonth}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <ChevronLeft className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          </button>
          <button
            onClick={handleMonthClick}
            className="mx-2 font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded"
          >
            {showYear
              ? currentDate.getFullYear()
              : `${months[currentDate.getMonth()]} ${currentDate.getFullYear()}`}
          </button>
          <button
            onClick={showYear ? handleNextYear : handleNextMonth}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <ChevronRight className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>
        <div className="flex items-center">
          <select
            value={selectedDateFormat}
            onChange={(e) =>
              setSelectedDateFormat(e.target.value as DateFormat)
            }
            className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300"
          >
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
          </select>
        </div>
      </div>

      {showYear ? (
        <div className="grid grid-cols-3 gap-2">
          {months.map((month, index) => (
            <button
              key={month}
              onClick={() => handleMonthSelect(index)}
              className={`p-2 rounded text-sm ${
                currentDate.getMonth() === index
                  ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              }`}
            >
              {month}
            </button>
          ))}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-7 gap-1 mb-2">
            {daysOfWeek.map((day) => (
              <div
                key={day}
                className="text-center text-xs font-medium text-gray-500 dark:text-gray-400"
              >
                {day}
              </div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1">
            {days.map((day, index) => (
              <button
                key={index}
                onClick={() => handleDateSelect(day.date)}
                className={`relative p-2 rounded-full text-sm ${
                  day.currentMonth
                    ? 'text-gray-700 dark:text-gray-300'
                    : 'text-gray-400 dark:text-gray-500'
                } ${
                  selectedDate &&
                  selectedDate.getDate() === day.date.getDate() &&
                  selectedDate.getMonth() === day.date.getMonth() &&
                  selectedDate.getFullYear() === day.date.getFullYear()
                    ? 'bg-blue-500 text-white dark:text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                {day.day}
                {hasEvent(day.date) && (
                  <span
                    className={`absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 rounded-full ${getEventColor(
                      getEventType(day.date)
                    )}`}
                    title={getEventTitle(day.date) || ''}
                  ></span>
                )}
              </button>
            ))}
          </div>
        </>
      )}

      {selectedDate && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Selected Date: {formatDate(selectedDate)}
          </div>
          {hasEvent(selectedDate) && (
            <div className="mt-2 p-2 rounded bg-gray-50 dark:bg-gray-750 text-sm">
              <div className="flex items-center">
                <span
                  className={`inline-block w-2 h-2 rounded-full ${getEventColor(
                    getEventType(selectedDate)
                  )} mr-2`}
                ></span>
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {getEventTitle(selectedDate)}
                </span>
              </div>
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Type: {getEventType(selectedDate)}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CalendarComponentClient; 