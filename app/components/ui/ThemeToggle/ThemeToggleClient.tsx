"use client";

import { motion } from 'framer-motion';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import { ThemeToggleProps } from './types';

/**
 * Client component for ThemeToggle that handles interactive elements
 */
export default function ThemeToggleClient({ isMinimal = false }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();

  const handleToggle = () => {
    toggleTheme();
  };

  return (
    <motion.button
      onClick={handleToggle}
      className={`${isMinimal
        ? 'p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
        : 'p-2 rounded-full backdrop-blur-md bg-white/20 dark:bg-dark-800/60 text-gray-800 dark:text-gray-200 shadow-lg'} focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-dark-focus-ring transition-all duration-300`}
      whileHover={{ scale: 1.1, rotate: theme === 'light' ? -15 : 15 }}
      whileTap={{ scale: 0.9 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
        >
          <Moon size={isMinimal ? 16 : 20} className={isMinimal ? "text-gray-500" : "text-gray-400 dark:text-gray-200"} />
        </motion.div>
      ) : (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
        >
          <Sun size={isMinimal ? 16 : 20} className={isMinimal ? "text-gray-400" : "text-yellow-400 dark:text-yellow-300"} />
        </motion.div>
      )}
    </motion.button>
  );
} 