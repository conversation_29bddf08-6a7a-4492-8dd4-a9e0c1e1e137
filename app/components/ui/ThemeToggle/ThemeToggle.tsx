// This is a Server Component by default (no "use client" directive)
import { ThemeToggleProps } from './types';
import ThemeToggleClient from './ThemeToggleClient';

/**
 * ThemeToggle component following Next.js best practices
 * 
 * Server component that delegates to client component since this component
 * is inherently interactive (theme toggling) and uses the theme context.
 */
export default function ThemeToggle(props: ThemeToggleProps) {
  // ThemeToggle is entirely interactive and relies on the theme context,
  // so we'll always use the client component.
  
  return <ThemeToggleClient {...props} />;
} 