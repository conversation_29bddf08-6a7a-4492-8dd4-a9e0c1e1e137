// This is a Server Component by default (no "use client" directive)
import { FormContainerProps } from './types';
import FormContainerClient from './FormContainerClient';

/**
 * FormContainer provides consistent styling and behavior for all forms
 * 
 * Server component that delegates to client component since the FormContainer
 * uses animations and conditional rendering.
 */
export default function FormContainer(props: FormContainerProps) {
  // FormContainer delegates to the client component since it uses
  // framer-motion for animations and other client-side features
  
  return <FormContainerClient {...props} />;
} 