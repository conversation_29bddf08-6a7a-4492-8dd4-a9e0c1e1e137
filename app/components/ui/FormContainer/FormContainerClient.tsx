"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Loader2, AlertCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { cn } from "@/app/lib/utils";
import { FormContainerProps } from "./types";

/**
 * Client component for FormContainer that handles animations and interactive elements
 */
export default function FormContainerClient({
  title,
  description,
  isLoading = false,
  error = null,
  animate = true,
  className = "",
  footer,
  maxWidth = "max-w-3xl",
  children
}: FormContainerProps) {
  const Container = animate ? motion.div : React.Fragment;
  const animationProps = animate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : {};

  return (
    <Container {...animationProps}>
      <Card className={cn("bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700", maxWidth, className)}>
        <CardHeader className="pb-4 space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold text-gray-800 dark:text-white">
              {title}
            </CardTitle>
            {isLoading && (
              <Loader2 className="h-5 w-5 animate-spin text-yellow-500" />
            )}
          </div>
          {description && (
            <CardDescription className="text-gray-500 dark:text-gray-400">
              {description}
            </CardDescription>
          )}
        </CardHeader>

        {error && (
          <div className="px-6">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        <CardContent className="space-y-4">
          {/* Apply disabled styling when loading */}
          <div className={cn("transition-opacity", isLoading && "opacity-70 pointer-events-none")}>
            {children}
          </div>
        </CardContent>

        {footer && <CardFooter className="flex justify-end gap-2 pt-2">{footer}</CardFooter>}
      </Card>
    </Container>
  );
} 