"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Props for the ThemeToggle component.
 */
interface ThemeToggleProps {
  isMinimal?: boolean;
}

/**
 * A component that allows users to toggle between light and dark themes.
 *
 * It uses the `useTheme` hook to access and update the current theme.
 * The appearance can be a standard button or a minimal icon-only version.
 *
 * @param {ThemeToggleProps} props - The props for the ThemeToggle component.
 * @param {boolean} [props.isMinimal=false] - If true, renders a minimal version of the toggle button. Defaults to false.
 * @returns {JSX.Element} The rendered theme toggle button.
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({ isMinimal = false }) => {
  const { theme, toggleTheme } = useTheme();

  const handleToggle = () => {
    toggleTheme();
  };

  return (
    <motion.button
      onClick={handleToggle}
      className={`${isMinimal
        ? 'p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
        : 'p-2 rounded-full backdrop-blur-md bg-white/20 dark:bg-dark-800/60 text-gray-800 dark:text-gray-200 shadow-lg'} focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-dark-focus-ring transition-all duration-300`}
      whileHover={{ scale: 1.1, rotate: theme === 'light' ? -15 : 15 }}
      whileTap={{ scale: 0.9 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
        >
          <Moon size={isMinimal ? 16 : 20} className={isMinimal ? "text-gray-500" : "text-gray-400 dark:text-gray-200"} />
        </motion.div>
      ) : (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
        >
          <Sun size={isMinimal ? 16 : 20} className={isMinimal ? "text-gray-400" : "text-yellow-400 dark:text-yellow-300"} />
        </motion.div>
      )}
    </motion.button>
  );
};

export default ThemeToggle;