// This is a Server Component by default (no "use client" directive)
import { SelectProps } from './types';
import * as SelectElements from './SelectClient';

/**
 * Server component wrapper for Select
 * Re-exports all select components from the client component
 */
export const Select = SelectElements.Select;
export const SelectGroup = SelectElements.SelectGroup;
export const SelectValue = SelectElements.SelectValue;
export const SelectTrigger = SelectElements.SelectTrigger;
export const SelectContent = SelectElements.SelectContent;
export const SelectLabel = SelectElements.SelectLabel;
export const SelectItem = SelectElements.SelectItem;
export const SelectSeparator = SelectElements.SelectSeparator;
export const SelectScrollUpButton = SelectElements.SelectScrollUpButton;
export const SelectScrollDownButton = SelectElements.SelectScrollDownButton; 