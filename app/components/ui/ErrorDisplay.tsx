"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ErrorDisplayProps {
  error: Error | string;
  onRetry?: () => void;
  message?: string;
  suggestion?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  message = "An error occurred",
  suggestion = "Please try again later"
}) => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-red-100 dark:border-red-900/30"
    >
      <div className="flex flex-col items-center text-center">
        <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mb-4">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        
        <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-1">{message}</h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">{suggestion}</p>
        
        <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-md w-full max-w-lg mb-6">
          <p className="text-red-500 dark:text-red-400 text-sm font-mono">{errorMessage}</p>
        </div>
        
        {onRetry && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onRetry}
            className="flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors"
          >
            <RefreshCw size={16} />
            <span>Retry</span>
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export default ErrorDisplay; 