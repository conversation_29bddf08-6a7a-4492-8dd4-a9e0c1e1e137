"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/app/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/app/components/ui/alert";
import { <PERSON><PERSON>Circle, Loader2 } from "lucide-react";
import { cn } from "@/app/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { AutosaveIndicator } from "./AutosaveIndicator";
import { AutosaveStatus } from "@/app/hooks/useAutosave";

export interface EnhancedFormContainerProps {
  /**
   * Form title
   */
  title: string;
  
  /**
   * Optional form description
   */
  description?: string;
  
  /**
   * Whether the form is in a loading state
   */
  isLoading?: boolean;
  
  /**
   * Error message to display
   */
  error?: string | null;
  
  /**
   * Whether to animate the form container
   */
  animate?: boolean;
  
  /**
   * Additional class names
   */
  className?: string;
  
  /**
   * Footer content
   */
  footer?: React.ReactNode;
  
  /**
   * Maximum width of the form container
   */
  maxWidth?: string;
  
  /**
   * Form content
   */
  children: React.ReactNode;
  
  /**
   * Autosave status
   */
  autosaveStatus?: AutosaveStatus;
  
  /**
   * Last saved timestamp
   */
  lastSaved?: Date | null;
  
  /**
   * Autosave error
   */
  autosaveError?: Error | null;
  
  /**
   * Whether to show the autosave indicator
   */
  showAutosaveIndicator?: boolean;
}

export const EnhancedFormContainer: React.FC<EnhancedFormContainerProps> = ({
  title,
  description,
  isLoading = false,
  error = null,
  animate = true,
  className = "",
  footer,
  maxWidth = "max-w-3xl",
  children,
  autosaveStatus = "idle",
  lastSaved = null,
  autosaveError = null,
  showAutosaveIndicator = false,
}) => {
  const Container = animate ? motion.div : React.Fragment;
  const animationProps = animate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : {};

  return (
    <Container {...animationProps}>
      <Card className={cn(
        "shadow-sm border border-input bg-background transition-all duration-300 hover:shadow-md",
        maxWidth,
        className
      )}>
        <CardHeader className="pb-4 space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent dark:from-accent dark:to-accent/70">
              {title}
            </CardTitle>
            {isLoading && (
              <Loader2 className="h-5 w-5 animate-spin text-primary dark:text-accent" />
            )}
          </div>
          
          <div className="flex items-center justify-between">
            {description && (
              <CardDescription className="text-muted-foreground">
                {description}
              </CardDescription>
            )}
            
            {showAutosaveIndicator && (
              <AutosaveIndicator
                status={autosaveStatus}
                lastSaved={lastSaved}
                error={autosaveError}
                className="ml-auto"
              />
            )}
          </div>
        </CardHeader>

        {error && (
          <div className="px-6">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                {/* Handle multi-line error messages */}
                {error.split('\n').map((line, i) => (
                  <div key={i} className="mb-1">{line}</div>
                ))}
              </AlertDescription>
            </Alert>
          </div>
        )}

        <CardContent className="space-y-4">
          {/* Loading overlay */}
          <div className="relative">
            {/* Apply disabled styling when loading */}
            <div className={cn(
              "transition-opacity",
              isLoading && "opacity-70 pointer-events-none"
            )}>
              {children}
            </div>
            
            {/* Loading overlay with animation */}
            <AnimatePresence>
              {isLoading && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-background/50 backdrop-blur-[1px] flex items-center justify-center z-10"
                >
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    className="bg-card p-4 rounded-lg shadow-lg flex items-center space-x-3"
                  >
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    <span className="text-sm font-medium">Loading...</span>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>

        {footer && (
          <CardFooter className="flex justify-end gap-2 pt-2 border-t">
            {footer}
          </CardFooter>
        )}
      </Card>
    </Container>
  );
};
