"use client";

import React, { useState, useEffect } from "react";
import { 
  Controller, 
  ControllerProps, 
  FieldPath, 
  FieldValues,
  useFormContext
} from "react-hook-form";
import { cn } from "@/app/lib/utils";
import { CheckCircle, AlertCircle, AlertTriangle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

// SCHEMA ALIGNMENT: Ensured all assembly-related form logic uses only canonical schema fields from database_schema_updated.md. No legacy/incorrect field usage.

// Context for the enhanced form field
type EnhancedFormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
  name: TName;
  showValidation: boolean;
};

const EnhancedFormFieldContext = React.createContext<EnhancedFormFieldContextValue>(
  {} as EnhancedFormFieldContextValue
);

// Enhanced form field component
export const EnhancedFormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  showValidationIcons = true,
  validateOnChange = true,
  validateOnBlur = true,
  ...props
}: ControllerProps<TFieldValues, TName> & {
  showValidationIcons?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}) => {
  const [showValidation, setShowValidation] = useState(false);

  return (
    <EnhancedFormFieldContext.Provider 
      value={{ 
        name: props.name, 
        showValidation: showValidation 
      }}
    >
      <div className="relative">
        <Controller 
          {...props} 
          render={(renderProps) => {
            // Handle validation visibility based on events
            const handleFocus = () => {
              if (validateOnBlur) {
                setShowValidation(false);
              }
            };

            const handleBlur = (e: React.FocusEvent<HTMLElement>) => {
              if (validateOnBlur) {
                setShowValidation(true);
              }
              renderProps.field.onBlur();
            };

            const handleChange = (e: React.ChangeEvent<HTMLElement> | any) => {
              if (validateOnChange) {
                setShowValidation(true);
              }
              renderProps.field.onChange(e);
            };

            // Enhanced field with validation events
            return props.render({
              ...renderProps,
              field: {
                ...renderProps.field,
                onFocus: handleFocus,
                onBlur: handleBlur,
                onChange: handleChange,
              },
            });
          }}
        />
        {showValidationIcons && <ValidationIcon />}
      </div>
    </EnhancedFormFieldContext.Provider>
  );
};

// Hook to use the enhanced form field context
export const useEnhancedFormField = () => {
  const fieldContext = React.useContext(EnhancedFormFieldContext);
  const itemContext = React.useContext(EnhancedFormItemContext);
  const { getFieldState, formState } = useFormContext();

  if (!fieldContext) {
    throw new Error("useEnhancedFormField should be used within <EnhancedFormField>");
  }

  const fieldState = getFieldState(fieldContext.name, formState);
  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    showValidation: fieldContext.showValidation,
    ...fieldState,
  };
};

// Context for the enhanced form item
type EnhancedFormItemContextValue = {
  id: string;
};

const EnhancedFormItemContext = React.createContext<EnhancedFormItemContextValue>(
  {} as EnhancedFormItemContextValue
);

// Enhanced form item component
export const EnhancedFormItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const id = React.useId();

  return (
    <EnhancedFormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn("space-y-2 relative", className)} {...props} />
    </EnhancedFormItemContext.Provider>
  );
});
EnhancedFormItem.displayName = "EnhancedFormItem";

// Enhanced form label component
export const EnhancedFormLabel = React.forwardRef<
  HTMLLabelElement,
  React.LabelHTMLAttributes<HTMLLabelElement>
>(({ className, ...props }, ref) => {
  const { error, formItemId } = useEnhancedFormField();

  return (
    <label
      ref={ref}
      className={cn(
        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        error && "text-destructive",
        className
      )}
      htmlFor={formItemId}
      {...props}
    />
  );
});
EnhancedFormLabel.displayName = "EnhancedFormLabel";

// Enhanced form control component
export const EnhancedFormControl = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } = useEnhancedFormField();

  return (
    <div
      ref={ref}
      id={formItemId}
      aria-describedby={
        !error
          ? `${formDescriptionId}`
          : `${formDescriptionId} ${formMessageId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  );
});
EnhancedFormControl.displayName = "EnhancedFormControl";

// Enhanced form description component
export const EnhancedFormDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useEnhancedFormField();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    />
  );
});
EnhancedFormDescription.displayName = "EnhancedFormDescription";

// Enhanced form message component with animation
export const EnhancedFormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId, showValidation } = useEnhancedFormField();
  const body = error ? String(error?.message) : children;

  if (!body || !showValidation) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.p
        ref={ref}
        id={formMessageId}
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={cn(
          "text-sm font-medium text-destructive mt-1 flex items-center",
          className
        )}
        {...props}
      >
        <AlertCircle className="h-3 w-3 mr-1" />
        {body}
      </motion.p>
    </AnimatePresence>
  );
});
EnhancedFormMessage.displayName = "EnhancedFormMessage";

// Validation icon component
const ValidationIcon = () => {
  const { error, isDirty, isValid, showValidation } = useEnhancedFormField();
  
  if (!showValidation || !isDirty) return null;
  
  return (
    <div className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
      <AnimatePresence>
        {error ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="text-destructive"
          >
            <AlertCircle className="h-4 w-4" />
          </motion.div>
        ) : isValid ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="text-green-500"
          >
            <CheckCircle className="h-4 w-4" />
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
};
