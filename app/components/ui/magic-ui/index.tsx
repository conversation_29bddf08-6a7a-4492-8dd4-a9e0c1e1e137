"use client";

import React, { CSSProperties, ComponentPropsWithoutRef, MouseEvent, useEffect, useState } from "react";
import { cn } from "@/app/lib/utils";
import { BorderBeam } from "./BorderBeam";
import { useTheme } from "@/app/context/ThemeContext";

// ShimmerButton Component
export interface ShimmerButtonProps extends ComponentPropsWithoutRef<"button"> {
  shimmerColor?: string;
  shimmerSize?: string;
  borderRadius?: string;
  shimmerDuration?: string;
  background?: string;
  className?: string;
  children?: React.ReactNode;
}

export const ShimmerButton = React.forwardRef<
  HTMLButtonElement,
  ShimmerButtonProps
>(
  (
    {
      shimmerColor = "#ffffff",
      shimmerSize = "0.05em",
      shimmerDuration = "3s",
      borderRadius = "100px",
      background = "rgba(0, 0, 0, 1)",
      className,
      children,
      ...props
    },
    ref,
  ) => {
    return (
      <button
        style={
          {
            "--spread": "90deg",
            "--shimmer-color": shimmerColor,
            "--radius": borderRadius,
            "--speed": shimmerDuration,
            "--cut": shimmerSize,
            "--bg": background,
          } as CSSProperties
        }
        className={cn(
          "group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-gray-300/10 px-6 py-3 [background:var(--bg)] [border-radius:var(--radius)]",
          "transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px",
          "text-white dark:text-black dark:border-gray-700/20",
          className,
        )}
        ref={ref}
        {...props}
      >
        {/* spark container */}
        <div
          className={cn(
            "-z-30 blur-[2px]",
            "absolute inset-0 overflow-visible [container-type:size]",
          )}
        >
          {/* spark */}
          <div className="absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]">
            {/* spark before */}
            <div className="absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]" />
          </div>
        </div>
        {children}

        {/* Highlight */}
        <div
          className={cn(
            "insert-0 absolute size-full",

            "rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]",

            // transition
            "transform-gpu transition-all duration-300 ease-in-out",

            // on hover
            "group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]",

            // on click
            "group-active:shadow-[inset_0_-10px_10px_#ffffff3f]",
          )}
        />

        {/* backdrop */}
        <div
          className={cn(
            "absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]",
          )}
        />
      </button>
    );
  },
);

ShimmerButton.displayName = "ShimmerButton";

// MagicCard Component
export interface MagicCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
}

export const MagicCard = ({
  children,
  className,
  containerClassName,
  ...props
}: MagicCardProps) => {
  const { theme } = useTheme?.() || { theme: 'light' };

  return (
    <div
      className={cn(
        "relative h-full w-full overflow-hidden rounded-xl border border-border bg-background p-4",
        theme === 'light' ? "bg-white shadow-md" : "",
        containerClassName
      )}
    >
      <div
        className={cn(
          "relative z-10 h-full w-full overflow-hidden rounded-xl bg-background p-4 shadow-xl",
          theme === 'light' ? "bg-white border border-gray-100" : "",
          className
        )}
        {...props}
      >
        <div className={cn(
          "absolute inset-0 h-full w-full bg-gradient-to-br z-[-1]",
          theme === 'light' ? "from-gray-50/50 to-gray-100/50" : "from-black/5 to-black/20 dark:from-black/10 dark:to-black/30"
        )} />
        <div className={cn(
          "absolute inset-0 h-full w-full z-[-1]",
          theme === 'light' ? "bg-white opacity-[0.9]" : "bg-background opacity-[0.7] dark:opacity-[0.85]"
        )} />
        {children}
      </div>
      <div className="absolute inset-0 z-0 transform-gpu animate-border rounded-xl bg-background bg-[linear-gradient(to_right,theme(colors.primary.DEFAULT)_0%,theme(colors.primary.DEFAULT/30)_20%,theme(colors.primary.DEFAULT/20)_40%,theme(colors.primary.DEFAULT/10)_60%,theme(colors.primary.DEFAULT/5)_80%,transparent_100%)] dark:bg-[linear-gradient(to_right,var(--primary-blue)_0%,rgba(18,116,243,0.3)_20%,rgba(18,116,243,0.2)_40%,rgba(18,116,243,0.1)_60%,rgba(18,116,243,0.05)_80%,transparent_100%)] bg-[length:200%_100%] bg-[position:0%_0%] transition-all duration-300" />
    </div>
  );
};

// InteractiveHoverButton Component
export interface InteractiveHoverButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {}

export const InteractiveHoverButton = React.forwardRef<
  HTMLButtonElement,
  InteractiveHoverButtonProps
>(({ children, className, ...props }, ref) => {
  return (
    <button
      ref={ref}
      className={cn(
        "group relative w-auto cursor-pointer overflow-hidden rounded-full border bg-background p-2 px-6 text-center font-semibold",
        className,
      )}
      {...props}
    >
      <div className="flex items-center gap-2">
        <div className="h-2 w-2 rounded-full bg-primary transition-all duration-300 group-hover:scale-[100.8]"></div>
        <span className="inline-block transition-all duration-300 group-hover:translate-x-12 group-hover:opacity-0">
          {children}
        </span>
      </div>
      <div className="absolute top-0 z-10 flex h-full w-full translate-x-12 items-center justify-center gap-2 text-primary-foreground opacity-0 transition-all duration-300 group-hover:-translate-x-5 group-hover:opacity-100">
        <span>{children}</span>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-right"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
      </div>
    </button>
  );
});

InteractiveHoverButton.displayName = "InteractiveHoverButton";

// RippleButton Component
export interface RippleButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  rippleColor?: string;
  duration?: string;
}

export const RippleButton = React.forwardRef<
  HTMLButtonElement,
  RippleButtonProps
>(
  (
    {
      className,
      children,
      rippleColor = "#ffffff",
      duration = "600ms",
      onClick,
      ...props
    },
    ref,
  ) => {
    const [buttonRipples, setButtonRipples] = useState<
      Array<{ x: number; y: number; size: number; key: number }>
    >([]);

    const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
      createRipple(event);
      onClick?.(event);
    };

    const createRipple = (event: MouseEvent<HTMLButtonElement>) => {
      const button = event.currentTarget;
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - size / 2;
      const y = event.clientY - rect.top - size / 2;

      const newRipple = { x, y, size, key: Date.now() };
      setButtonRipples((prevRipples) => [...prevRipples, newRipple]);
    };

    useEffect(() => {
      if (buttonRipples.length > 0) {
        const lastRipple = buttonRipples[buttonRipples.length - 1];
        const timeout = setTimeout(() => {
          setButtonRipples((prevRipples) =>
            prevRipples.filter((ripple) => ripple.key !== lastRipple.key),
          );
        }, parseInt(duration));
        return () => clearTimeout(timeout);
      }
    }, [buttonRipples, duration]);

    return (
      <button
        className={cn(
          "relative flex cursor-pointer items-center justify-center overflow-hidden rounded-lg border-2 bg-background px-4 py-2 text-center text-primary",
          className,
        )}
        onClick={handleClick}
        ref={ref}
        {...props}
      >
        <div className="relative z-10">{children}</div>
        <span className="pointer-events-none absolute inset-0">
          {buttonRipples.map((ripple) => (
            <span
              className="absolute animate-rippling rounded-full bg-background opacity-30"
              key={ripple.key}
              style={{
                width: `${ripple.size}px`,
                height: `${ripple.size}px`,
                top: `${ripple.y}px`,
                left: `${ripple.x}px`,
                backgroundColor: rippleColor,
                transform: `scale(0)`,
              }}
            />
          ))}
        </span>
      </button>
    );
  },
);

RippleButton.displayName = "RippleButton";

// Add animations to tailwind.config.js
// Add these animations to your tailwind.config.js file:
/*
extend: {
  animation: {
    "shimmer-slide": "shimmer-slide calc(var(--speed)*0.5) infinite linear",
    "spin-around": "spin-around calc(var(--speed)*1) infinite linear",
    "rippling": "rippling 600ms linear forwards",
    "border": "border 3s ease infinite",
    "border-beam": "border-beam 3s ease infinite",
    "spin-slow": "spin-slow 4s linear infinite",
  },
  keyframes: {
    "shimmer-slide": {
      to: {
        transform: "translateY(-100cqh)",
      },
    },
    "spin-around": {
      to: {
        transform: "rotate(1turn)",
      },
    },
    "rippling": {
      "0%": {
        transform: "scale(0)",
        opacity: "0.5",
      },
      "100%": {
        transform: "scale(6)",
        opacity: "0",
      },
    },
    "border": {
      "0%, 100%": { backgroundPosition: "0% 50%" },
      "50%": { backgroundPosition: "100% 50%" },
    },
    "border-beam": {
      "0%, 100%": { opacity: "0.8" },
      "50%": { opacity: "0.4" },
    },
    "spin-slow": {
      to: {
        transform: "rotate(1turn)",
      },
    },
  },
}
*/

// Export BorderBeam component
export { BorderBeam };
export type { BorderBeamProps } from './BorderBeam';

// Export PaginationControls component
export { PaginationControls } from './PaginationControls';