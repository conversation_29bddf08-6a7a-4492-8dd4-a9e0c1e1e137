"use client";

import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import { ShimmerButton, RippleButton } from "./index";

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export const PaginationControls: React.FC<PaginationControlsProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className,
}) => {
  const { theme } = useTheme();

  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  // Function to determine which page numbers to show
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    
    // Always show first page
    pageNumbers.push(1);
    
    // Calculate range around current page
    const rangeStart = Math.max(2, currentPage - 1);
    const rangeEnd = Math.min(totalPages - 1, currentPage + 1);
    
    // Add ellipsis after first page if needed
    if (rangeStart > 2) {
      pageNumbers.push("...");
    }
    
    // Add pages in range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pageNumbers.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (rangeEnd < totalPages - 1) {
      pageNumbers.push("...");
    }
    
    // Always show last page if more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };

  return (
    <div className={cn("flex flex-col items-center space-y-2", className)}>
      <div className="flex items-center space-x-2">
        {/* Previous button */}
        <RippleButton
          className={cn(
            "flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300",
            currentPage === 1
              ? "opacity-50 cursor-not-allowed bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
              : "bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700 shadow-sm"
          )}
          onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          rippleColor={theme === "dark" ? "#4B5563" : "#E5E7EB"}
        >
          <ChevronLeft size={16} className="mr-1" />
          <span className="hidden sm:inline">Previous</span>
        </RippleButton>

        {/* Page numbers */}
        <div className="flex items-center space-x-1">
          {getPageNumbers().map((page, index) => {
            if (page === "...") {
              return (
                <span
                  key={`ellipsis-${index}`}
                  className="px-2 text-gray-500 dark:text-gray-400"
                >
                  ...
                </span>
              );
            }

            const pageNum = page as number;
            const isCurrentPage = currentPage === pageNum;

            return isCurrentPage ? (
              <ShimmerButton
                key={pageNum}
                className="w-10 h-10 flex items-center justify-center rounded-lg font-medium"
                shimmerColor={theme === "dark" ? "#4B5563" : "#E5E7EB"}
                background={
                  theme === "dark"
                    ? "rgba(18, 116, 243, 0.8)"
                    : "rgba(59, 130, 246, 0.8)"
                }
                onClick={() => onPageChange(pageNum)}
              >
                {pageNum}
              </ShimmerButton>
            ) : (
              <RippleButton
                key={pageNum}
                className="w-10 h-10 flex items-center justify-center rounded-lg font-medium bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700"
                onClick={() => onPageChange(pageNum)}
                rippleColor={theme === "dark" ? "#4B5563" : "#E5E7EB"}
              >
                {pageNum}
              </RippleButton>
            );
          })}
        </div>

        {/* Next button */}
        <RippleButton
          className={cn(
            "flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300",
            currentPage === totalPages
              ? "opacity-50 cursor-not-allowed bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
              : "bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700 shadow-sm"
          )}
          onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          rippleColor={theme === "dark" ? "#4B5563" : "#E5E7EB"}
        >
          <span className="hidden sm:inline">Next</span>
          <ChevronRight size={16} className="ml-1" />
        </RippleButton>
      </div>
      
      {/* Pagination info */}
      <div className="text-sm text-gray-500 dark:text-gray-400">
        Page {currentPage} of {totalPages}
      </div>
    </div>
  );
};
