import { ReactNode } from 'react';

/**
 * Props for the StatusCard component
 */
export interface StatusCardProps {
  /** Title displayed at the top of the card */
  title: string;
  /** Data to display as percentages */
  data: Record<string, number>;
  /** Main statistic to display prominently */
  mainStat: {
    value: number;
    label: string;
  };
  /** Color theme for the card */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Icon displayed in the top-right corner */
  icon?: ReactNode;
  /** Click handler for the card */
  onClick?: () => void;
}

/**
 * Color class mapping for bar charts using theme variables
 */
export const colorClasses = {
  blue: {
    bar: 'bg-theme-info'
  },
  green: {
    bar: 'bg-theme-success'
  },
  red: {
    bar: 'bg-theme-error'
  },
  yellow: {
    bar: 'bg-theme-warning'
  },
  purple: {
    bar: 'bg-purple-500 dark:bg-purple-400'
  },
  orange: {
    bar: 'bg-orange-500 dark:bg-orange-400'
  },
  gray: {
    bar: 'bg-gray-500 dark:text-theme-secondary'
  }
};