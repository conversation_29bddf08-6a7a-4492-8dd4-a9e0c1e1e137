"use client";

import React from 'react';
import { render, screen } from '@testing-library/react';
import StatusCard from './StatusCard';

// Mock the ThemeContext
jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({
    theme: 'light',
    toggleTheme: jest.fn(),
  }),
}));

// Mock the BaseCard component
jest.mock('@/app/components/ui/BaseCard', () => {
  return function MockBaseCard({ children, title, color }: any) {
    return (
      <div data-testid="base-card" data-color={color}>
        <div data-testid="card-title">{title}</div>
        <div data-testid="card-content">{children}</div>
      </div>
    );
  };
});

describe('StatusCard', () => {
  // Default props for the component
  const defaultProps = {
    title: 'Inventory Status',
    data: {
      'In Stock': 120,
      'Low Stock': 15,
      'Out of Stock': 5,
    },
    mainStat: {
      value: 140,
      label: 'Total Items',
    },
    color: 'blue' as const,
  };

  // Setup function to render the component with props
  const setup = (props = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return render(<StatusCard {...mergedProps} />);
  };

  it('renders the title correctly', () => {
    setup();
    expect(screen.getByTestId('card-title')).toHaveTextContent('Inventory Status');
  });

  it('renders the main stat correctly', () => {
    setup();
    const cardContent = screen.getByTestId('card-content');
    expect(cardContent).toHaveTextContent('140');
    expect(cardContent).toHaveTextContent('Total Items');
  });

  it('renders all data items', () => {
    setup();
    const cardContent = screen.getByTestId('card-content');
    expect(cardContent).toHaveTextContent('In Stock');
    expect(cardContent).toHaveTextContent('120');
    expect(cardContent).toHaveTextContent('Low Stock');
    expect(cardContent).toHaveTextContent('15');
    expect(cardContent).toHaveTextContent('Out of Stock');
    expect(cardContent).toHaveTextContent('5');
  });

  it('passes the correct color to BaseCard', () => {
    setup({ color: 'green' });
    expect(screen.getByTestId('base-card')).toHaveAttribute('data-color', 'green');
  });

  it('handles empty data object', () => {
    setup({ data: {} });
    const cardContent = screen.getByTestId('card-content');
    expect(cardContent).toHaveTextContent('No data available');
  });

  it('handles undefined mainStat', () => {
    setup({ mainStat: undefined });
    const cardContent = screen.getByTestId('card-content');
    expect(cardContent).not.toHaveTextContent('Total Items');
  });
});
