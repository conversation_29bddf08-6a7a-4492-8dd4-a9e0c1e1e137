import { ReactNode } from 'react';

/**
 * Props for the BaseCard component
 */
export interface BaseCardProps {
  /** Title displayed at the top of the card */
  title?: string;
  /** Subtitle displayed below the title */
  subtitle?: string;
  /** Icon displayed in the top-right corner */
  icon?: ReactNode;
  /** Color theme for the card */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Click handler for the entire card */
  onClick?: () => void;
  /** Click handler for the view details link */
  onViewDetails?: () => void;
  /** Text for the view details link */
  viewDetailsText?: string;
  /** Whether the card is featured (has a colored border) */
  isFeatured?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Card content */
  children: ReactNode;
  /** Whether to animate the card */
  animate?: boolean;
}

/**
 * Color classes using theme variables for consistent styling
 */
export const colorClasses = {
  blue: {
    accent: 'text-theme-info',
    light: 'bg-theme-info-light',
    bar: 'bg-theme-info',
    icon: 'text-theme-info',
    gradient: 'from-theme-info/10 to-theme-info/5',
    border: 'border-theme-info/20'
  },
  green: {
    accent: 'text-theme-success',
    light: 'bg-theme-success-light',
    bar: 'bg-theme-success',
    icon: 'text-theme-success',
    gradient: 'from-theme-success/10 to-theme-success/5',
    border: 'border-theme-success/20'
  },
  red: {
    accent: 'text-theme-error',
    light: 'bg-theme-error-light',
    bar: 'bg-theme-error',
    icon: 'text-theme-error',
    gradient: 'from-theme-error/10 to-theme-error/5',
    border: 'border-theme-error/20'
  },
  yellow: {
    accent: 'text-theme-warning',
    light: 'bg-theme-warning-light',
    bar: 'bg-theme-warning',
    icon: 'text-theme-warning',
    gradient: 'from-theme-warning/10 to-theme-warning/5',
    border: 'border-theme-warning/20'
  },
  purple: {
    accent: 'text-purple-600 dark:text-purple-400',
    light: 'bg-purple-50 dark:bg-purple-900/20',
    bar: 'bg-purple-500 dark:bg-purple-400',
    icon: 'text-purple-500',
    gradient: 'from-purple-500/10 to-purple-600/5 dark:from-purple-600/20 dark:to-purple-700/10',
    border: 'border-purple-200 dark:border-purple-700/30'
  },
  orange: {
    accent: 'text-orange-600 dark:text-orange-400',
    light: 'bg-orange-50 dark:bg-orange-900/20',
    bar: 'bg-orange-500 dark:bg-orange-400',
    icon: 'text-orange-500',
    gradient: 'from-orange-500/10 to-orange-600/5 dark:from-orange-600/20 dark:to-orange-700/10',
    border: 'border-orange-200 dark:border-orange-700/30'
  },
  gray: {
    accent: 'text-theme-secondary',
    light: 'bg-theme-tertiary',
    bar: 'bg-gray-500 dark:bg-gray-600',
    icon: 'text-theme-secondary',
    gradient: 'from-gray-500/10 to-gray-600/5 dark:from-gray-600/20 dark:to-gray-700/10',
    border: 'border-theme-secondary/20'
  }
};