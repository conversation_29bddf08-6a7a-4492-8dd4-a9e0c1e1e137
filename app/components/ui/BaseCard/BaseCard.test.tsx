"use client";

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import BaseCard from './BaseCard';

// Mock the ThemeContext
jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({
    theme: 'light',
    toggleTheme: jest.fn(),
  }),
}));

describe('BaseCard', () => {
  // Default props for the component
  const defaultProps = {
    title: 'Test Card',
    subtitle: 'Card subtitle',
    children: <div>Card content</div>,
    color: 'blue' as const,
    animate: true,
  };

  // Setup function to render the component with props
  const setup = (props = {}) => {
    const mergedProps = { ...defaultProps, ...props };
    return render(<BaseCard {...mergedProps} />);
  };

  it('renders the card with title and subtitle', () => {
    setup();
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('Card subtitle')).toBeInTheDocument();
    expect(screen.getByText('Card content')).toBeInTheDocument();
  });

  it('renders the card with different color', () => {
    setup({ color: 'green' });
    // In a real test, you would check for specific color classes
    // This is a simplified example
    const card = screen.getByText('Test Card').closest('div');
    expect(card).toBeInTheDocument();
  });

  it('calls onClick when card is clicked', () => {
    const handleClick = jest.fn();
    setup({ onClick: handleClick });
    
    const card = screen.getByText('Test Card').closest('div');
    if (card) {
      fireEvent.click(card);
      expect(handleClick).toHaveBeenCalledTimes(1);
    }
  });

  it('renders view details button when onViewDetails is provided', () => {
    const handleViewDetails = jest.fn();
    setup({ 
      onViewDetails: handleViewDetails,
      viewDetailsText: 'See Details'
    });
    
    const button = screen.getByText('See Details');
    expect(button).toBeInTheDocument();
    
    fireEvent.click(button);
    expect(handleViewDetails).toHaveBeenCalledTimes(1);
  });

  it('applies featured styling when isFeatured is true', () => {
    setup({ isFeatured: true });
    // In a real test, you would check for specific featured classes
    // This is a simplified example
    const card = screen.getByText('Test Card').closest('div');
    expect(card).toBeInTheDocument();
  });

  it('does not animate when animate is false', () => {
    setup({ animate: false });
    // In a real test, you would check for absence of animation classes
    // This is a simplified example
    const card = screen.getByText('Test Card').closest('div');
    expect(card).toBeInTheDocument();
  });
});
