/**
 * Type definitions for the Enhanced Table component
 */

import { ReactNode } from 'react';

/**
 * Column definition for the Enhanced Table
 */
export interface ColumnDef<T> {
  /** Unique identifier for the column */
  id: string;
  /** Header label for the column */
  header: string;
  /** Function to access the cell value */
  accessorFn: (row: T) => any;
  /** Optional cell renderer */
  cell?: (value: any, row: T) => ReactNode;
  /** Whether the column is sortable */
  sortable?: boolean;
  /** Whether the column is filterable */
  filterable?: boolean;
  /** CSS class name for the column */
  className?: string;
  /** Whether to hide the column on mobile */
  hideOnMobile?: boolean;
}

/**
 * Sort direction
 */
export type SortDirection = 'asc' | 'desc';

/**
 * Sort state
 */
export interface SortState {
  id: string;
  direction: SortDirection;
}

/**
 * Filter state
 */
export interface FilterState {
  id: string;
  value: string;
}

/**
 * Pagination state
 */
export interface PaginationState {
  pageIndex: number;
  pageSize: number;
}

/**
 * Props for the Enhanced Table component
 */
export interface EnhancedTableProps<T> {
  /** Data to display in the table */
  data: T[];
  /** Column definitions */
  columns: ColumnDef<T>[];
  /** Whether to enable sorting */
  enableSorting?: boolean;
  /** Whether to enable filtering */
  enableFiltering?: boolean;
  /** Whether to enable pagination */
  enablePagination?: boolean;
  /** Initial sort state */
  initialSortState?: SortState;
  /** Initial filter state */
  initialFilterState?: FilterState[];
  /** Initial pagination state */
  initialPaginationState?: PaginationState;
  /** Total number of items (for server-side pagination) */
  totalItems?: number;
  /** Whether the data is loading */
  isLoading?: boolean;
  /** CSS class name for the table */
  className?: string;
  /** Whether to use a compact layout */
  compact?: boolean;
  /** Whether to use a striped layout */
  striped?: boolean;
  /** Whether to highlight rows on hover */
  highlightOnHover?: boolean;
  /** Whether to show a border */
  bordered?: boolean;
  /** Whether to use a sticky header */
  stickyHeader?: boolean;
  /** Whether to use a sticky first column */
  stickyFirstColumn?: boolean;
  /** Whether to use a sticky last column */
  stickyLastColumn?: boolean;
  /** Whether to use a dense layout */
  dense?: boolean;
  /** Whether to use a full width layout */
  fullWidth?: boolean;
  /** Whether to use a zebra striped layout */
  zebraStriped?: boolean;
  /** Whether to use a hover effect */
  hover?: boolean;
  /** Whether to use a shadow effect */
  shadow?: boolean;
  /** Whether to use a rounded effect */
  rounded?: boolean;
  /** Whether to use a glass effect */
  glass?: boolean;
  /** Whether to use a backdrop blur effect */
  backdropBlur?: boolean;
  /** Whether to use a grid pattern background */
  gridPattern?: boolean;
  /** Whether to use a simple layout (fewer features) */
  simple?: boolean;
  /** Callback for when the sort state changes */
  onSortChange?: (sortState: SortState) => void;
  /** Callback for when the filter state changes */
  onFilterChange?: (filterState: FilterState[]) => void;
  /** Callback for when the pagination state changes */
  onPaginationChange?: (paginationState: PaginationState) => void;
  /** Callback for when a row is clicked */
  onRowClick?: (row: T) => void;
  /** Render function for row actions */
  renderRowActions?: (row: T) => ReactNode;
  /** Render function for table actions */
  renderTableActions?: () => ReactNode;
  /** Render function for empty state */
  renderEmptyState?: () => ReactNode;
  /** Render function for loading state */
  renderLoadingState?: () => ReactNode;
  /** Render function for error state */
  renderErrorState?: (error: Error) => ReactNode;
  /** Error object */
  error?: Error | null;
}

/**
 * Props for the Enhanced Table Header component
 */
export interface EnhancedTableHeaderProps<T> {
  /** Column definitions */
  columns: ColumnDef<T>[];
  /** Sort state */
  sortState?: SortState;
  /** Whether to enable sorting */
  enableSorting?: boolean;
  /** Whether to enable filtering */
  enableFiltering?: boolean;
  /** Callback for when the sort state changes */
  onSortChange?: (sortState: SortState) => void;
  /** Callback for when the filter state changes */
  onFilterChange?: (filterState: FilterState[]) => void;
  /** CSS class name for the header */
  className?: string;
}

/**
 * Props for the Enhanced Table Pagination component
 */
export interface EnhancedTablePaginationProps {
  /** Current page index */
  pageIndex: number;
  /** Page size */
  pageSize: number;
  /** Total number of items */
  totalItems: number;
  /** Callback for when the pagination state changes */
  onPaginationChange: (paginationState: PaginationState) => void;
  /** CSS class name for the pagination */
  className?: string;
}
