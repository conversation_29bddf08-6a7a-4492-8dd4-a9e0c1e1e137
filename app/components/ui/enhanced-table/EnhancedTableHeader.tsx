"use client";

import React from "react";
import { ChevronDown, ChevronUp, ArrowUpDown } from "lucide-react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import { EnhancedTableHeaderProps, SortState } from "./types";
import { TableHead, TableHeader, TableRow } from "@/app/components/ui/table";

/**
 * Enhanced Table Header component
 * Provides sortable and filterable headers for the Enhanced Table
 */
export function EnhancedTableHeader<T>({
  columns,
  sortState,
  enableSorting = true,
  enableFiltering = false,
  onSortChange,
  onFilterChange,
  className,
}: EnhancedTableHeaderProps<T>) {
  const { theme } = useTheme();

  // Handle sort click
  const handleSortClick = (columnId: string) => {
    if (!enableSorting || !onSortChange) return;

    // If the column is already sorted, toggle the direction
    if (sortState?.id === columnId) {
      onSortChange({
        id: columnId,
        direction: sortState.direction === "asc" ? "desc" : "asc",
      });
    } else {
      // Otherwise, sort by the column in ascending order
      onSortChange({
        id: columnId,
        direction: "asc",
      });
    }
  };

  return (
    <TableHeader
      className={cn(
        theme === "light"
          ? "bg-gray-50/80 border-gray-200"
          : "bg-[var(--T-bg-sidebar)] border-[var(--T-border-color)]",
        className
      )}
    >
      <TableRow>
        {columns.map((column) => (
          <TableHead
            key={column.id}
            className={cn(
              column.sortable && enableSorting ? "cursor-pointer select-none" : "",
              column.className,
              column.hideOnMobile ? "hidden md:table-cell" : ""
            )}
            onClick={() => column.sortable && enableSorting && handleSortClick(column.id)}
          >
            <div className="flex items-center space-x-1">
              <span>{column.header}</span>
              {column.sortable && enableSorting && (
                <span className="flex items-center">
                  {sortState?.id === column.id ? (
                    sortState.direction === "asc" ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )
                  ) : (
                    <ArrowUpDown className="h-4 w-4 opacity-50" />
                  )}
                </span>
              )}
            </div>
          </TableHead>
        ))}
      </TableRow>
    </TableHeader>
  );
}
