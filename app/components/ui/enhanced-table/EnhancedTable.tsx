"use client";

import React, { useState, useMemo, useEffect } from "react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@/app/components/ui/table";
import { EnhancedTableHeader } from "./EnhancedTableHeader";
import { EnhancedTablePagination } from "./EnhancedTablePagination";
import {
  EnhancedTableProps,
  SortState,
  FilterState,
  PaginationState,
} from "./types";
import { Loader2 } from "lucide-react";

/**
 * Enhanced Table component
 * A standardized table component with sorting, filtering, and pagination
 */
export function EnhancedTable<T>({
  data,
  columns,
  enableSorting = true,
  enableFiltering = false,
  enablePagination = true,
  initialSortState,
  initialFilterState = [],
  initialPaginationState = { pageIndex: 1, pageSize: 10 },
  totalItems,
  isLoading = false,
  className,
  compact = false,
  striped = false,
  highlightOnHover = true,
  bordered = true,
  stickyHeader = false,
  stickyFirstColumn = false,
  stickyLastColumn = false,
  dense = false,
  fullWidth = true,
  zebraStriped = false,
  hover = true,
  shadow = true,
  rounded = true,
  glass = false,
  backdropBlur = false,
  gridPattern = false,
  simple = false,
  onSortChange,
  onFilterChange,
  onPaginationChange,
  onRowClick,
  renderRowActions,
  renderTableActions,
  renderEmptyState,
  renderLoadingState,
  renderErrorState,
  error = null,
}: EnhancedTableProps<T>) {
  const { theme } = useTheme();

  // State for sorting, filtering, and pagination
  const [sortState, setSortState] = useState<SortState | undefined>(
    initialSortState
  );
  const [filterState, setFilterState] = useState<FilterState[]>(
    initialFilterState
  );
  const [paginationState, setPaginationState] = useState<PaginationState>(
    initialPaginationState
  );

  // Handle sort change
  const handleSortChange = (newSortState: SortState) => {
    setSortState(newSortState);
    if (onSortChange) {
      onSortChange(newSortState);
    }
  };

  // Handle filter change
  const handleFilterChange = (newFilterState: FilterState[]) => {
    setFilterState(newFilterState);
    if (onFilterChange) {
      onFilterChange(newFilterState);
    }
  };

  // Handle pagination change
  const handlePaginationChange = (newPaginationState: PaginationState) => {
    setPaginationState(newPaginationState);
    if (onPaginationChange) {
      onPaginationChange(newPaginationState);
    }
  };

  // Process data with sorting, filtering, and pagination
  const processedData = useMemo(() => {
    // If server-side processing is being used, just return the data
    if (onSortChange || onFilterChange || onPaginationChange) {
      return data;
    }

    let result = [...data];

    // Apply filtering
    if (enableFiltering && filterState.length > 0) {
      result = result.filter((row) => {
        return filterState.every((filter) => {
          const column = columns.find((col) => col.id === filter.id);
          if (!column) return true;
          
          const value = column.accessorFn(row);
          return String(value).toLowerCase().includes(filter.value.toLowerCase());
        });
      });
    }

    // Apply sorting
    if (enableSorting && sortState) {
      const column = columns.find((col) => col.id === sortState.id);
      if (column) {
        result.sort((a, b) => {
          const aValue = column.accessorFn(a);
          const bValue = column.accessorFn(b);

          // Handle different value types
          if (aValue === null || aValue === undefined) return 1;
          if (bValue === null || bValue === undefined) return -1;

          // String comparison
          if (typeof aValue === "string" && typeof bValue === "string") {
            return sortState.direction === "asc"
              ? aValue.localeCompare(bValue)
              : bValue.localeCompare(aValue);
          }

          // Number comparison
          if (typeof aValue === "number" && typeof bValue === "number") {
            return sortState.direction === "asc"
              ? aValue - bValue
              : bValue - aValue;
          }

          // Date comparison
          if (aValue instanceof Date && bValue instanceof Date) {
            return sortState.direction === "asc"
              ? aValue.getTime() - bValue.getTime()
              : bValue.getTime() - aValue.getTime();
          }

          // Default comparison
          return 0;
        });
      }
    }

    // Apply pagination
    if (enablePagination) {
      const { pageIndex, pageSize } = paginationState;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      result = result.slice(start, end);
    }

    return result;
  }, [
    data,
    columns,
    enableSorting,
    enableFiltering,
    enablePagination,
    sortState,
    filterState,
    paginationState,
    onSortChange,
    onFilterChange,
    onPaginationChange,
  ]);

  // Calculate total items for pagination
  const calculatedTotalItems = useMemo(() => {
    // If totalItems is provided, use it (for server-side pagination)
    if (totalItems !== undefined) {
      return totalItems;
    }

    // Otherwise, calculate it from the data
    if (enableFiltering && filterState.length > 0) {
      let filteredData = [...data];
      filteredData = filteredData.filter((row) => {
        return filterState.every((filter) => {
          const column = columns.find((col) => col.id === filter.id);
          if (!column) return true;
          
          const value = column.accessorFn(row);
          return String(value).toLowerCase().includes(filter.value.toLowerCase());
        });
      });
      return filteredData.length;
    }

    return data.length;
  }, [data, totalItems, enableFiltering, filterState, columns]);

  // Render loading state
  if (isLoading) {
    return renderLoadingState ? (
      renderLoadingState()
    ) : (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return renderErrorState ? (
      renderErrorState(error)
    ) : (
      <div className="flex justify-center items-center p-8 text-red-500">
        <span>Error: {error.message}</span>
      </div>
    );
  }

  // Render empty state
  if (data.length === 0) {
    return renderEmptyState ? (
      renderEmptyState()
    ) : (
      <div className="flex justify-center items-center p-8 text-gray-500 dark:text-gray-400">
        <span>No data available</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Table actions */}
      {renderTableActions && (
        <div className="flex justify-end mb-4">{renderTableActions()}</div>
      )}

      {/* Grid pattern background for enhanced styling */}
      <div className="relative rounded-lg overflow-hidden">
        {gridPattern && (
          <div className="absolute inset-0 pointer-events-none opacity-[0.03] dark:opacity-[0.07]">
            <div className="absolute inset-0 bg-grid-pattern-gray-400/30 [mask-image:linear-gradient(to_bottom,white,transparent)] bg-[length:20px_20px]" />
          </div>
        )}

        <div
          className={cn(
            "relative",
            rounded && "rounded-lg",
            bordered && "border",
            shadow && "shadow-sm",
            glass && "bg-white/80 dark:bg-gray-900/80",
            backdropBlur && "backdrop-blur-[2px]",
            theme === "light"
              ? "bg-white border-gray-200"
              : "bg-[var(--T-bg-card)] border-border",
            className
          )}
        >
          <div
            className={cn(
              "overflow-auto",
              stickyHeader && "max-h-[70vh]"
            )}
          >
            <Table className={cn(fullWidth && "w-full")}>
              {/* Table header */}
              <EnhancedTableHeader
                columns={columns}
                sortState={sortState}
                enableSorting={enableSorting && !simple}
                enableFiltering={enableFiltering && !simple}
                onSortChange={handleSortChange}
                onFilterChange={handleFilterChange}
              />

              {/* Table body */}
              <TableBody
                className={cn(
                  theme === "light" ? "bg-white" : "",
                  zebraStriped && theme === "light" && "divide-y divide-gray-200 [&_tr:nth-child(even)]:bg-gray-50",
                  zebraStriped && theme === "dark" && "divide-y divide-gray-700 [&_tr:nth-child(even)]:bg-gray-800/30"
                )}
              >
                {processedData.map((row, rowIndex) => (
                  <TableRow
                    key={rowIndex}
                    className={cn(
                      hover && theme === "light" && "hover:bg-gray-50",
                      hover && theme === "dark" && "hover:bg-[var(--dark-hover)]",
                      theme === "light" ? "border-gray-200" : "border-[var(--T-border-subtle)]",
                      striped && rowIndex % 2 === 1 && theme === "light" && "bg-gray-50",
                      striped && rowIndex % 2 === 1 && theme === "dark" && "bg-gray-800/30",
                      onRowClick && "cursor-pointer"
                    )}
                    onClick={() => onRowClick && onRowClick(row)}
                  >
                    {columns.map((column, columnIndex) => (
                      <TableCell
                        key={column.id}
                        className={cn(
                          dense ? "py-2" : "py-4",
                          compact ? "px-2" : "px-4",
                          column.className,
                          column.hideOnMobile ? "hidden md:table-cell" : "",
                          stickyFirstColumn && columnIndex === 0 && "sticky left-0 bg-white dark:bg-[var(--T-bg-card)] z-10",
                          stickyLastColumn && columnIndex === columns.length - 1 && "sticky right-0 bg-white dark:bg-[var(--T-bg-card)] z-10"
                        )}
                      >
                        {column.cell
                          ? column.cell(column.accessorFn(row), row)
                          : (() => {
                              const cellValue = column.accessorFn(row);
                              if (typeof cellValue === 'object' && cellValue !== null && !React.isValidElement(cellValue)) {
                                // Prioritize common user-facing fields, then stringify as a last resort
                                return String(cellValue.fullName || cellValue.username || cellValue.name || cellValue.label || cellValue.id || cellValue._id || JSON.stringify(cellValue));
                              }
                              return cellValue;
                            })()}
                      </TableCell>
                    ))}

                    {/* Row actions */}
                    {renderRowActions && (
                      <TableCell className="text-right">
                        {renderRowActions(row)}
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {enablePagination && !simple && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <EnhancedTablePagination
                pageIndex={paginationState.pageIndex}
                pageSize={paginationState.pageSize}
                totalItems={calculatedTotalItems}
                onPaginationChange={handlePaginationChange}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
