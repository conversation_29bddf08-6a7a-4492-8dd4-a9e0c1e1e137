"use client";

import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import { Button } from "@/app/components/ui/button";
import { EnhancedTablePaginationProps, PaginationState } from "./types";

/**
 * Enhanced Table Pagination component
 * Provides pagination controls for the Enhanced Table
 */
export function EnhancedTablePagination({
  pageIndex,
  pageSize,
  totalItems,
  onPaginationChange,
  className,
}: EnhancedTablePaginationProps) {
  const { theme } = useTheme();
  
  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));
  
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  // Function to determine which page numbers to show
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    
    // Always show first page
    pageNumbers.push(1);
    
    // Calculate range around current page
    const rangeStart = Math.max(2, pageIndex - 1);
    const rangeEnd = Math.min(totalPages - 1, pageIndex + 1);
    
    // Add ellipsis after first page if needed
    if (rangeStart > 2) {
      pageNumbers.push("...");
    }
    
    // Add pages in range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pageNumbers.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (rangeEnd < totalPages - 1) {
      pageNumbers.push("...");
    }
    
    // Always show last page if more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };

  // Handle page change
  const handlePageChange = (newPageIndex: number) => {
    if (newPageIndex < 1 || newPageIndex > totalPages) return;
    
    onPaginationChange({
      pageIndex: newPageIndex,
      pageSize,
    });
  };

  return (
    <div className={cn("flex flex-col items-center space-y-2", className)}>
      <div className="flex items-center space-x-2">
        {/* Previous button */}
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300",
            pageIndex === 1
              ? "opacity-50 cursor-not-allowed"
              : ""
          )}
          onClick={() => handlePageChange(pageIndex - 1)}
          disabled={pageIndex === 1}
        >
          <ChevronLeft size={16} className="mr-1" />
          <span className="hidden sm:inline">Previous</span>
        </Button>

        {/* Page numbers */}
        <div className="flex items-center space-x-1">
          {getPageNumbers().map((page, index) => {
            if (page === "...") {
              return (
                <span
                  key={`ellipsis-${index}`}
                  className="px-2 text-gray-500 dark:text-gray-400"
                >
                  ...
                </span>
              );
            }

            const pageNum = page as number;
            const isCurrentPage = pageIndex === pageNum;

            return (
              <Button
                key={pageNum}
                variant={isCurrentPage ? "default" : "outline"}
                size="sm"
                className="w-8 h-8 p-0"
                onClick={() => handlePageChange(pageNum)}
              >
                {pageNum}
              </Button>
            );
          })}
        </div>

        {/* Next button */}
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300",
            pageIndex === totalPages
              ? "opacity-50 cursor-not-allowed"
              : ""
          )}
          onClick={() => handlePageChange(pageIndex + 1)}
          disabled={pageIndex === totalPages}
        >
          <span className="hidden sm:inline">Next</span>
          <ChevronRight size={16} className="ml-1" />
        </Button>
      </div>
      
      {/* Pagination info */}
      <div className="text-sm text-gray-500 dark:text-gray-400">
        Page {pageIndex} of {totalPages}
      </div>
    </div>
  );
}
