"use client";

import { motion } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { ErrorDisplayProps } from './types';

/**
 * Client component for ErrorDisplay that handles interactive elements
 */
export default function ErrorDisplayClient({
  error,
  onRetry,
  message = "An error occurred",
  suggestion = "Please try again later"
}: ErrorDisplayProps) {
  const errorMessage = typeof error === 'string' ? error : error.message;

  // Check if this is a test error
  const isTestError =
    (typeof error !== 'string' && (error as any).isTestError) ||
    errorMessage.includes('[TEST ERROR]') ||
    errorMessage.includes('test error');

  // Use different styling and messaging for test errors
  const bgColorClass = isTestError
    ? "border-amber-200 dark:border-amber-800"
    : "border-red-100 dark:border-red-900/30";

  const iconBgClass = isTestError
    ? "bg-amber-100 dark:bg-amber-900/30"
    : "bg-red-100 dark:bg-red-900/30";

  const iconColorClass = isTestError
    ? "text-amber-500"
    : "text-red-500";

  const errorBgClass = isTestError
    ? "bg-amber-50 dark:bg-amber-900/20"
    : "bg-red-50 dark:bg-red-900/20";

  const errorTextClass = isTestError
    ? "text-amber-600 dark:text-amber-400"
    : "text-red-500 dark:text-red-400";

  // Use different messages for test errors
  const displayMessage = isTestError
    ? "Test Error Detected"
    : message;

  const displaySuggestion = isTestError
    ? "This is an intentional test error to verify error handling. No action is needed."
    : suggestion;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`w-full p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border ${bgColorClass}`}
    >
      <div className="flex flex-col items-center text-center">
        <div className={`${iconBgClass} p-3 rounded-full mb-4`}>
          <AlertCircle className={`h-8 w-8 ${iconColorClass}`} />
        </div>

        <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-1">{displayMessage}</h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">{displaySuggestion}</p>

        <div className={`${errorBgClass} p-3 rounded-md w-full max-w-lg mb-6`}>
          <p className={`${errorTextClass} text-sm font-mono`}>{errorMessage}</p>
        </div>

        {isTestError && (
          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md w-full max-w-lg mb-6 text-sm">
            <p className="text-gray-600 dark:text-gray-300 mb-2">
              <strong>This is a test error</strong> that was intentionally triggered to verify error handling.
            </p>
            <p className="text-gray-500 dark:text-gray-400">
              It has been reported to Sentry with the "test" tag and will not affect application functionality.
            </p>
          </div>
        )}

        {onRetry && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onRetry}
            className="flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors"
          >
            <RefreshCw size={16} />
            <span>Retry</span>
          </motion.button>
        )}
      </div>
    </motion.div>
  );
}