"use client"

import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { Slot } from "@radix-ui/react-slot"
import {
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  FormProvider,
  useFormContext,
} from "react-hook-form"

import { cn, logger } from "@/app/lib/utils"
import { Label } from "@/app/components/ui/label"

/**
 * @typedef {Object} FormPropsExtendingFormProvider
 * @property {React.ReactNode} children - The children of the form.
 * @property {Function} handleSubmit - The function to handle form submission.
 * @property {Object} methods - The methods from react-hook-form.
 */

/**
 * A wrapper around react-hook-form's FormProvider to establish a form context.
 *
 * @param {FormPropsExtendingFormProvider} props - Props for the Form component, extending FormProvider props.
 * @returns {JSX.Element} The FormProvider component with given props.
 */
const Form = FormProvider

type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
  name: TName
}

const FormFieldContext = React.createContext<FormFieldContextValue>(
  {} as FormFieldContextValue
)

/**
 * @template TFieldValues Extends react-hook-form's FieldValues
 * @template TName Extends react-hook-form's FieldPath<TFieldValues>
 * @typedef {Object} FormFieldPropsExtendingControllerProps
 * @property {TName} name - The name of the field.
 * @property {React.ReactNode} children - The children of the form field.
 * @property {Function} render - The render prop for the form field.
 */

/**
 * A component that connects a form field to the form context and react-hook-form's Controller.
 *
 * @template TFieldValues Extends react-hook-form's FieldValues
 * @template TName Extends react-hook-form's FieldPath<TFieldValues>
 * @param {ControllerProps<TFieldValues, TName>} props - Props for the FormField, extending ControllerProps from react-hook-form.
 * @returns {JSX.Element} The Controller component wrapped in FormFieldContext.Provider.
 */
const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  )
}

const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext)
  const itemContext = React.useContext(FormItemContext)
  const { getFieldState, formState } = useFormContext()

  const fieldState = getFieldState(fieldContext.name, formState)

  if (!fieldContext) {
    logger('ERROR', "useFormField was called outside of a <FormField> context.", { hookName: 'useFormField' });
    throw new Error("useFormField should be used within <FormField>")
  }

  const { id } = itemContext

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  }
}

type FormItemContextValue = {
  id: string
}

const FormItemContext = React.createContext<FormItemContextValue>(
  {} as FormItemContextValue
)

/**
 * @typedef {Object} FormItemProps
 * @property {string} [className] - Optional CSS classes for the form item.
 */

/**
 * A layout component to group a label, input, and messages for a single form field.
 * It provides a unique ID for accessibility.
 *
 * @param {FormItemProps & React.HTMLAttributes<HTMLDivElement>} props - Props for the FormItem component.
 * @param {React.Ref<HTMLDivElement>} ref - Forwarded ref to the underlying div element.
 * @returns {JSX.Element} The FormItem component.
 */
const FormItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const id = React.useId()

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn("space-y-2", className)} {...props} />
    </FormItemContext.Provider>
  )
})
FormItem.displayName = "FormItem"

/**
 * @typedef {Object} FormLabelPropsExtendingLabelPrimitive
 * @property {string} [className] - Optional CSS classes for the form label.
 */

/**
 * Renders a label for a form field, automatically connecting it via `htmlFor` to the form item.
 * It applies error styling if the associated field has an error.
 *
 * @param {FormLabelPropsExtendingLabelPrimitive & React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>} props - Props for the FormLabel component.
 * @param {React.Ref<React.ElementRef<typeof LabelPrimitive.Root>>} ref - Forwarded ref to the underlying label element.
 * @returns {JSX.Element} The Label component styled as a FormLabel.
 */
const FormLabel = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>
>(({ className, ...props }, ref) => {
  const { error, formItemId } = useFormField()

  return (
    <Label
      ref={ref}
      className={cn(error && "text-destructive", className)}
      htmlFor={formItemId}
      {...props}
    />
  )
})
FormLabel.displayName = "FormLabel"

/**
 * @typedef {Object} FormControlPropsExtendingSlot
 */

/**
 * A wrapper for the actual input component (e.g., `<Input />`, `<Select />`).
 * It uses `Slot` to pass down props and accessibility attributes (`aria-describedby`, `aria-invalid`).
 *
 * @param {FormControlPropsExtendingSlot & React.ComponentPropsWithoutRef<typeof Slot>} props - Props for the FormControl component.
 * @param {React.Ref<React.ElementRef<typeof Slot>>} ref - Forwarded ref to the underlying Slot element.
 * @returns {JSX.Element} The Slot component configured for form control.
 */
const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={
        !error
          ? `${formDescriptionId}`
          : `${formDescriptionId} ${formMessageId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  )
})
FormControl.displayName = "FormControl"

/**
 * @typedef {Object} FormDescriptionProps
 * @property {string} [className] - Optional CSS classes for the form description.
 */

/**
 * Displays a description or helper text for a form field.
 * It is automatically linked via `aria-describedby` by the `FormControl`.
 *
 * @param {FormDescriptionProps & React.HTMLAttributes<HTMLParagraphElement>} props - Props for the FormDescription component.
 * @param {React.Ref<HTMLParagraphElement>} ref - Forwarded ref to the underlying paragraph element.
 * @returns {JSX.Element} The FormDescription component.
 */
const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField()

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn("text-sm text-muted-foreground dark:text-text-secondary", className)}
      {...props}
    />
  )
})
FormDescription.displayName = "FormDescription"

/**
 * @typedef {Object} FormMessageProps
 * @property {string} [className] - Optional CSS classes for the form message.
 * @property {React.ReactNode} [children] - Optional children to display if there is no error.
 */

/**
 * Displays validation error messages or other messages related to a form field.
 * It only renders if there is an error message or if children are provided.
 *
 * @param {FormMessageProps & React.HTMLAttributes<HTMLParagraphElement>} props - Props for the FormMessage component.
 * @param {React.Ref<HTMLParagraphElement>} ref - Forwarded ref to the underlying paragraph element.
 * @returns {JSX.Element | null} The FormMessage component, or null if no message or error.
 */
const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField()
  const body = error ? String(error?.message) : children

  if (error) {
    logger('ERROR', `Form field '${name}' has an error.`, { fieldName: name, errorMessage: error?.message, formMessageId });
  }

  if (!body) {
    return null
  }

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn("text-sm font-medium text-destructive dark:text-destructive", className)}
      {...props}
    >
      {body}
    </p>
  )
})
FormMessage.displayName = "FormMessage"

export {
  useFormField,
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
}