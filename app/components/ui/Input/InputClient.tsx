"use client";

import * as React from "react";
import { cn } from "@/app/lib/utils";
import { InputProps } from "./types";

/**
 * Client component for Input with forwarded ref
 */
const InputClient = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          "dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
);
InputClient.displayName = "InputClient";

export default InputClient; 