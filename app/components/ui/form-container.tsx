"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card";
import { cn } from "@/app/lib/utils";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface FormContainerProps {
  /** The form title displayed in the card header */
  title: string;
  /** Optional description displayed below the title */
  description?: string;
  /** Whether the form is currently submitting/loading */
  isLoading?: boolean;
  /** Form-level error message to display */
  error?: string | null;
  /** Whether to animate the container when it mounts */
  animate?: boolean;
  /** CSS class to apply to the container */
  className?: string;
  /** Content to display in the footer section */
  footer?: React.ReactNode;
  /** Max width class for the form container - defaults to max-w-3xl */
  maxWidth?: string;
  /** Children content (the form itself) */
  children: React.ReactNode;
}

/**
 * FormContainer provides consistent styling and behavior for all forms
 * It includes card styling, loading states, error display, and responsive layout
 */
export function FormContainer({
  title,
  description,
  isLoading = false,
  error = null,
  animate = true,
  className = "",
  footer,
  maxWidth = "max-w-3xl",
  children
}: FormContainerProps) {
  const Container = animate ? motion.div : React.Fragment;
  const animationProps = animate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : {};

  return (
    <Container {...animationProps}>
      <Card className={cn("shadow-sm border border-input bg-background transition-all duration-300 hover:shadow-md", maxWidth, className)}>
        <CardHeader className="pb-4 space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent dark:from-accent dark:to-accent/70">
              {title}
            </CardTitle>
            {isLoading && (
              <Loader2 className="h-5 w-5 animate-spin text-primary dark:text-accent" />
            )}
          </div>
          {description && (
            <CardDescription className="text-muted-foreground">
              {description}
            </CardDescription>
          )}
        </CardHeader>

        {error && (
          <div className="px-6">
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                {/* Handle multi-line error messages */}
                {error.split('\n').map((line, i) => (
                  <div key={i} className="mb-1">{line}</div>
                ))}
              </AlertDescription>
            </Alert>
          </div>
        )}

        <CardContent className="space-y-4">
          {/* Apply disabled styling when loading */}
          <div className={cn("transition-opacity", isLoading && "opacity-70 pointer-events-none")}>
            {children}
          </div>
        </CardContent>

        {footer && <CardFooter className="flex justify-end gap-2 pt-2">{footer}</CardFooter>}
      </Card>
    </Container>
  );
}