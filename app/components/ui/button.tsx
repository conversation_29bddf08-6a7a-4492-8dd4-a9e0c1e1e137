"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/app/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-theme-focus focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-accent-primary text-bg-primary hover:bg-accent-primary/90",
        destructive:
          "bg-theme-error text-white hover:bg-theme-error/90",
        outline:
          "border border-border-primary bg-bg-primary hover:bg-theme-hover text-text-primary",
        secondary:
          "bg-bg-secondary text-text-primary hover:bg-theme-hover",
        ghost: "hover:bg-theme-hover text-text-primary",
        link: "text-accent-primary underline-offset-4 hover:underline",
        success: "bg-theme-success text-white hover:bg-theme-success/90",
        warning: "bg-theme-warning text-white hover:bg-theme-warning/90",
        info: "bg-theme-info text-white hover:bg-theme-info/90",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

/**
 * Props for the Button component.
 */
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

/**
 * A versatile button component with various styles and sizes.
 *
 * This component can be rendered as a standard HTML button or as a child component using the `asChild` prop.
 * It supports different visual variants (default, destructive, outline, etc.) and sizes.
 *
 * @param {ButtonProps} props - The props for the button component.
 * @param {string} [props.className] - Additional CSS classes to apply to the button.
 * @param {typeof buttonVariants.variants.variant} [props.variant="default"] - The visual style of the button.
 * @param {typeof buttonVariants.variants.size} [props.size="default"] - The size of the button.
 * @param {boolean} [props.asChild=false] - If true, renders the component as a child, passing props to the first child element.
 * @param {React.Ref<HTMLButtonElement>} ref - Forwarded ref to the underlying button element.
 * @returns {JSX.Element} The rendered button component.
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }