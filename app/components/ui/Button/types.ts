import { type VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import * as React from "react";

/**
 * Button variants using class-variance-authority
 */
export const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 dark:bg-accent dark:text-accent-foreground dark:hover:bg-accent/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground dark:border-border dark:bg-background dark:hover:bg-dark-hover dark:hover:text-accent",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:bg-dark-element dark:text-dark-text-primary dark:hover:bg-dark-hover",
        ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-dark-hover dark:hover:text-accent",
        link: "text-primary underline-offset-4 hover:underline dark:text-accent",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

/**
 * Props for the Button component
 */
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
} 