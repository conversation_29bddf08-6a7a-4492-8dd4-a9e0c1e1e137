// This is a Server Component by default (no "use client" directive)
import { ButtonProps } from './types';
import ButtonClient from './ButtonClient';

/**
 * Button component with various styles and sizes
 * 
 * Server component that delegates to client component since buttons
 * require client-side features like ref forwarding.
 */
function Button(props: ButtonProps) {
  return <ButtonClient {...props} />;
}

Button.displayName = "Button";

export { Button }; 