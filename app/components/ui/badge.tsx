import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/app/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-theme-focus focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-accent-primary text-bg-primary hover:bg-accent-primary/80",
        secondary:
          "border-transparent bg-theme-info-light text-theme-info hover:bg-theme-info/10",
        destructive:
          "border-transparent bg-theme-error-light text-theme-error hover:bg-theme-error/10",
        outline:
          "text-theme-primary border-theme-secondary/20 hover:bg-theme-hover",
        success:
          "border-transparent bg-theme-success-light text-theme-success hover:bg-theme-success/10",
        warning:
          "border-transparent bg-theme-warning-light text-theme-warning hover:bg-theme-warning/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }