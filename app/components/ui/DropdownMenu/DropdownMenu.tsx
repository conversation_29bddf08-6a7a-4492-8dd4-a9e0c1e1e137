// This is a Server Component by default (no "use client" directive)
import * as DropdownMenuElements from './DropdownMenuClient';

/**
 * Server component wrapper for DropdownMenu
 * Re-exports all dropdown menu components from the client component
 */
export const DropdownMenu = DropdownMenuElements.DropdownMenu;
export const DropdownMenuTrigger = DropdownMenuElements.DropdownMenuTrigger;
export const DropdownMenuContent = DropdownMenuElements.DropdownMenuContent;
export const DropdownMenuItem = DropdownMenuElements.DropdownMenuItem;
export const DropdownMenuCheckboxItem = DropdownMenuElements.DropdownMenuCheckboxItem;
export const DropdownMenuRadioItem = DropdownMenuElements.DropdownMenuRadioItem;
export const DropdownMenuLabel = DropdownMenuElements.DropdownMenuLabel;
export const DropdownMenuSeparator = DropdownMenuElements.DropdownMenuSeparator;
export const DropdownMenuShortcut = DropdownMenuElements.DropdownMenuShortcut;
export const DropdownMenuGroup = DropdownMenuElements.DropdownMenuGroup;
export const DropdownMenuPortal = DropdownMenuElements.DropdownMenuPortal;
export const DropdownMenuSub = DropdownMenuElements.DropdownMenuSub;
export const DropdownMenuSubContent = DropdownMenuElements.DropdownMenuSubContent;
export const DropdownMenuSubTrigger = DropdownMenuElements.DropdownMenuSubTrigger;
export const DropdownMenuRadioGroup = DropdownMenuElements.DropdownMenuRadioGroup; 