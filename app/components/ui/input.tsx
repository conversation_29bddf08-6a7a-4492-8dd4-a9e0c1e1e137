"use client";

import * as React from "react"

import { cn } from "@/app/lib/utils"

/**
 * Props for the Input component.
 */
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

/**
 * A standard input component with styling consistent with the application's theme.
 *
 * It supports all standard HTML input attributes and applies custom styling for light and dark modes.
 *
 * @param {InputProps} props - The props for the input component.
 * @param {string} [props.className] - Additional CSS classes to apply to the input element.
 * @param {string} [props.type] - The type of the input (e.g., "text", "password", "email").
 * @param {React.Ref<HTMLInputElement>} ref - Forwarded ref to the underlying input element.
 * @returns {JSX.Element} The rendered input component.
 */
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          "dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }