'use client';

import React, { useCallback, useMemo } from 'react';
import { RefreshCw, Clock, ToggleLeft, ToggleRight } from 'lucide-react';
import { But<PERSON> } from '@/app/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { formatDistanceToNow } from 'date-fns';

interface AutoRefreshControlProps {
  className?: string;
}

/**
 * Component for controlling auto-refresh settings
 */
export function AutoRefreshControl({ className }: AutoRefreshControlProps) {
  const {
    refreshAssemblies: refreshAssembliesOriginal,
    isLoading,
    lastUpdated,
    isAutoRefreshEnabled,
    autoRefreshInterval,
    toggleAutoRefresh: toggleAutoRefreshOriginal,
    setAutoRefreshInterval: setAutoRefreshIntervalOriginal,
  } = useAssemblies();

  // Memoize the refresh function to prevent unnecessary re-renders
  const refreshAssemblies = useCallback(() => {
    refreshAssembliesOriginal();
  }, [refreshAssembliesOriginal]);

  // Memoize the toggle function
  const toggleAutoRefresh = useCallback(() => {
    toggleAutoRefreshOriginal();
  }, [toggleAutoRefreshOriginal]);

  // Memoize the interval setter
  const setAutoRefreshInterval = useCallback((interval: number) => {
    setAutoRefreshIntervalOriginal(interval);
  }, [setAutoRefreshIntervalOriginal]);

  // Format the last updated time - memoize to prevent re-renders
  const lastUpdatedText = useMemo(() => {
    return lastUpdated
      ? `Last updated ${formatDistanceToNow(lastUpdated, { addSuffix: true })}`
      : 'Not yet updated';
  }, [lastUpdated]);

  // Available refresh intervals
  const refreshIntervals = useMemo(() => [
    { value: 5000, label: '5 seconds' },
    { value: 10000, label: '10 seconds' },
    { value: 30000, label: '30 seconds' },
    { value: 60000, label: '1 minute' },
    { value: 300000, label: '5 minutes' },
  ], []);

  // Convert to string once to avoid repeated conversions
  const autoRefreshIntervalString = useMemo(() => 
    autoRefreshInterval.toString(), 
    [autoRefreshInterval]
  );

  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshAssemblies}
                  disabled={isLoading}
                  className="h-8 px-2"
                >
                  <RefreshCw
                    size={14}
                    className={isLoading ? 'animate-spin' : ''}
                  />
                  <span className="ml-1">Refresh</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Manually refresh data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant="ghost"
            size="sm"
            className="flex items-center space-x-2 h-8 px-2"
            onClick={toggleAutoRefresh}
          >
            {isAutoRefreshEnabled ? (
              <ToggleRight size={16} className="text-primary mr-1" />
            ) : (
              <ToggleLeft size={16} className="text-muted-foreground mr-1" />
            )}
            <span className="text-sm">Auto-refresh</span>
          </Button>

          {isAutoRefreshEnabled && (
            <div className="flex items-center space-x-2">
              <Clock size={14} className="text-muted-foreground" />
              <Select
                value={autoRefreshIntervalString}
                onValueChange={(value) => setAutoRefreshInterval(parseInt(value))}
              >
                <SelectTrigger className="h-8 w-[130px]">
                  <SelectValue placeholder="Refresh interval" />
                </SelectTrigger>
                <SelectContent>
                  {refreshIntervals.map((interval) => (
                    <SelectItem key={interval.value} value={interval.value.toString()}>
                      {interval.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>

      <div className="text-xs text-muted-foreground flex items-center">
        <Clock size={12} className="mr-1" />
        {lastUpdatedText}
      </div>
    </div>
  );
}
