"use client";

import React from 'react';
import { SentryMonitor } from '@/app/components/features/SentryMonitor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/app/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { Info } from 'lucide-react';

/**
 * Monitoring page for testing and monitoring integrations
 */
export default function MonitoringPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-4">System Monitoring</h1>
      <p className="text-gray-500 dark:text-gray-400 mb-8">
        Monitor and test system integrations and services
      </p>
      
      <Tabs defaultValue="sentry" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="sentry">Sentry</TabsTrigger>
          <TabsTrigger value="about">About Monitoring</TabsTrigger>
        </TabsList>
        
        <TabsContent value="sentry" className="space-y-6">
          <SentryMonitor />
          
          <Card>
            <CardHeader>
              <CardTitle>Sentry Configuration</CardTitle>
              <CardDescription>
                Current Sentry configuration details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Client Configuration</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Automatic error tracking for client-side errors</li>
                    <li>Session replay for error reproduction</li>
                    <li>Performance monitoring for client-side operations</li>
                    <li>User feedback collection on errors</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-2">Server Configuration</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Automatic error tracking for server-side errors</li>
                    <li>API route monitoring</li>
                    <li>Performance monitoring for server-side operations</li>
                    <li>Distributed tracing for request flows</li>
                  </ul>
                </div>
              </div>
              
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Integration Details</AlertTitle>
                <AlertDescription>
                  Sentry is integrated using @sentry/nextjs with automatic instrumentation for both client and server components.
                  Error boundaries are set up to catch and report unhandled exceptions.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="about" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>About System Monitoring</CardTitle>
              <CardDescription>
                Understanding the monitoring capabilities in this application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                This application includes comprehensive monitoring to ensure reliability and performance.
                The monitoring system includes:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Error Tracking</h3>
                  <p className="text-sm">
                    Automatic capture and reporting of errors in both client and server code.
                    This helps identify and fix issues quickly before they affect users.
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-2">Performance Monitoring</h3>
                  <p className="text-sm">
                    Tracking of key performance metrics to ensure the application remains fast and responsive.
                    This includes page load times, API response times, and more.
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-2">User Experience Monitoring</h3>
                  <p className="text-sm">
                    Tracking of user interactions and experiences to identify usability issues and areas for improvement.
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-2">System Health Checks</h3>
                  <p className="text-sm">
                    Regular checks of system components to ensure they are functioning correctly.
                    This includes database connections, API integrations, and more.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
