"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '@/app/components/layout/Header';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import {
  Calendar,
  Download,
  Filter,
  ChevronDown,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  <PERSON><PERSON>hart as LineChartIcon,
  Loader2,
  AlertTriangle,
  ArrowUpDown,
  Info
} from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Badge } from '@/app/components/ui/badge';
import { format, subDays, subMonths } from 'date-fns';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip as UITooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import { analyticsTypeMap, getChartDataKey, getChartTitle } from '@/app/lib/analytics-utils';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Define interfaces for analytics data
interface AnalyticsType {
  id: string;
  name: string;
  description: string;
  endpoint: string;
}

interface InventoryTrend {
  name: string;
  value: number;
  percentChange?: number;
}

interface StockLevel {
  name: string;
  inStock: number;
  lowStock: number;
  outOfStock: number;
}

interface CategoryDistribution {
  name: string;
  value: number;
}

interface InventoryValue {
  name: string;
  value: number;
}

interface DashboardSummary {
  totalItems: number;
  totalCategories: number;
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
}

interface AnalyticsData {
  inventoryTrends?: InventoryTrend[];
  stockLevels?: StockLevel[];
  categoryDistribution?: CategoryDistribution[];
  inventoryValue?: InventoryValue[];
  summary?: DashboardSummary;
  generatedAt: string;
}

// Define chart colors
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#8DD1E1'];

// Custom tooltip component for charts
interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
  theme: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label, theme }) => {
  if (active && payload && payload.length) {
    return (
      <div className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} p-3 border rounded-lg shadow-lg`}>
        <p className="text-gray-600 dark:text-gray-300 font-medium">{label}</p>
        {payload.map((entry, index) => (
          <p key={`item-${index}`} style={{ color: entry.color }} className="text-sm">
            {entry.name}: {entry.value}
            {entry.payload.percentChange !== undefined && (
              <span className={entry.payload.percentChange > 0 ? 'text-green-500' : 'text-red-500'}>
                {' '}({entry.payload.percentChange > 0 ? '+' : ''}{entry.payload.percentChange}%)
              </span>
            )}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const Analytics: React.FC = () => {
  const { theme } = useTheme();
  const [analyticsTypes, setAnalyticsTypes] = useState<AnalyticsType[]>([]);
  const [selectedAnalyticsType, setSelectedAnalyticsType] = useState<string>('inventory-trends');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<string>('month');
  const [startDate, setStartDate] = useState<Date | undefined>(subMonths(new Date(), 1));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'line'>('line');
  const [isExporting, setIsExporting] = useState<boolean>(false);

  // Fetch analytics types on component mount
  useEffect(() => {
    fetchAnalyticsTypes();
  }, []);

  // Fetch analytics data when type or filters change
  useEffect(() => {
    if (selectedAnalyticsType) {
      fetchAnalyticsData();
    }
  }, [selectedAnalyticsType, timeRange, startDate, endDate, categoryFilter]);

  // Fetch available analytics types
  const fetchAnalyticsTypes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/analytics');

      if (!response.ok) {
        throw new Error(`Error fetching analytics types: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setAnalyticsTypes(data.data || []);

      // Set initial analytics type if available
      if (data.data && data.data.length > 0) {
        setSelectedAnalyticsType(data.data[0].id);
      }
    } catch (err) {
      console.error('Error fetching analytics types:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch analytics data based on selected type and filters
  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      params.append('timeRange', timeRange);

      if (startDate && endDate) {
        params.append('startDate', startDate.toISOString());
        params.append('endDate', endDate.toISOString());
      }

      if (categoryFilter !== 'all') {
        params.append('category', categoryFilter);
      }

      // Fetch data
      const response = await fetch(`/api/analytics?type=${selectedAnalyticsType}&${params.toString()}`)
      if (!response.ok) {
        throw new Error(`Error fetching analytics data: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Transform the data based on the selected analytics type
      let transformedData = data.data || null;

      if (transformedData) {
        if (selectedAnalyticsType === 'inventory-trends' && transformedData.trends) {
          transformedData = {
            ...transformedData,
            inventoryTrends: transformedData.trends
          };
        }
      }

      setAnalyticsData(transformedData);
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setAnalyticsData(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Get chart data based on analytics type
  const getChartData = () => {
    if (!analyticsData) return [];

    // Local implementation of analyticsTypeMap as fallback
    const getDataKey = (type: string): string => {
      const mapping: Record<string, string> = {
        'inventory-trends': 'inventoryTrends',
        'stock-levels': 'stockLevels',
        'category-distribution': 'categoryDistribution',
        'inventory-value': 'inventoryValue'
      };
      return mapping[type] || '';
    };

    // Try to use the imported function, fall back to local implementation if it fails
    let dataKey;
    try {
      dataKey = typeof analyticsTypeMap === 'function' 
        ? analyticsTypeMap(selectedAnalyticsType)
        : getDataKey(selectedAnalyticsType);
    } catch (error) {
      console.error('Error using analyticsTypeMap:', error);
      dataKey = getDataKey(selectedAnalyticsType);
    }
    
    return dataKey && analyticsData[dataKey] ? analyticsData[dataKey] : [];
  };

  // Get analytics title based on selected type
  const getAnalyticsTitle = () => {
    const analyticsType = analyticsTypes.find(type => type.id === selectedAnalyticsType);
    return analyticsType ? analyticsType.name : 'Analytics';
  };

  // Get analytics description based on selected type
  const getAnalyticsDescription = () => {
    const analyticsType = analyticsTypes.find(type => type.id === selectedAnalyticsType);
    return analyticsType ? analyticsType.description : '';
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    if (!analyticsData) {
      console.warn("No data available to export");
      alert("No data available to export");
      return;
    }

    setIsExporting(true);

    try {
      let exportData: any[] = [];
      let headers: string[] = [];
      let title = getAnalyticsTitle();

      // Prepare data based on analytics type
      switch (selectedAnalyticsType) {
        case 'inventory-trends':
          headers = ['Period', 'Value', 'Percent Change'];
          exportData = (analyticsData.inventoryTrends || []).map(item => [
            item.name,
            item.value,
            item.percentChange !== undefined ? `${item.percentChange}%` : 'N/A'
          ]);
          break;
        case 'stock-levels':
          headers = ['Period', 'In Stock', 'Low Stock', 'Out of Stock'];
          exportData = (analyticsData.stockLevels || []).map(item => [
            item.name,
            item.inStock,
            item.lowStock,
            item.outOfStock
          ]);
          break;
        case 'category-distribution':
          headers = ['Category', 'Count'];
          exportData = (analyticsData.categoryDistribution || []).map(item => [
            item.name,
            item.value
          ]);
          break;
        case 'inventory-value':
          headers = ['Category', 'Value ($)'];
          exportData = (analyticsData.inventoryValue || []).map(item => [
            item.name,
            item.value
          ]);
          break;
        default:
          break;
      }

      if (exportData.length === 0) {
        throw new Error("No data available to export");
      }

      const fileName = `${selectedAnalyticsType}_${format}_${new Date().toISOString().split('T')[0]}`;

      if (format === 'csv') {
        // Create CSV content
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => row.join(','))
        ].join('\n');

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `${fileName}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      else if (format === 'excel') {
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet([headers, ...exportData]);

        // Create workbook
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, title);

        // Generate Excel file and trigger download
        XLSX.writeFile(wb, `${fileName}.xlsx`);
      }
      else if (format === 'pdf') {
        // Create PDF document
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(16);
        doc.text(title, 14, 15);

        // Add generation date
        doc.setFontSize(10);
        const generatedDate = analyticsData.generatedAt
          ? new Date(analyticsData.generatedAt).toLocaleString()
          : new Date().toLocaleString();
        doc.text(`Generated: ${generatedDate}`, 14, 22);

        // Add table
        (doc as any).autoTable({
          head: [headers],
          body: exportData,
          startY: 25,
          theme: 'grid',
          styles: {
            fontSize: 10,
            cellPadding: 3,
            lineColor: [200, 200, 200],
            lineWidth: 0.1,
          },
          headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
        });

        // Save PDF
        doc.save(`${fileName}.pdf`);
      }

      alert(`Data exported in ${format.toUpperCase()} format successfully`);
    } catch (err) {
      console.error('Error exporting data:', err);
      alert(err instanceof Error ? err.message : 'Failed to export data');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Analytics" />

      <div className="px-8 pb-8">
        {isLoading && !analyticsData ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading analytics...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-700 dark:text-red-400">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <p>{error}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => {
                setError(null);
                fetchAnalyticsTypes();
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : (
          <>
            {/* Analytics Type Selection */}
            <div className="mb-6">
              <Tabs
                defaultValue={selectedAnalyticsType}
                value={selectedAnalyticsType}
                onValueChange={(value) => setSelectedAnalyticsType(value)}
                className="w-full"
              >
                <div className="flex justify-between items-center mb-4">
                  <TabsList>
                    {analyticsTypes.map((type) => (
                      <TabsTrigger key={type.id} value={type.id}>
                        {type.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchAnalyticsData}
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Refresh
                    </Button>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="default"
                          size="sm"
                          disabled={isExporting || !analyticsData}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          {isExporting ? 'Exporting...' : 'Export'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48 p-0">
                        <div className="py-1">
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExport('csv')}
                            disabled={isExporting}
                          >
                            Export as CSV
                          </Button>
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExport('excel')}
                            disabled={isExporting}
                          >
                            Export as Excel
                          </Button>
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExport('pdf')}
                            disabled={isExporting}
                          >
                            Export as PDF
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Analytics Content */}
                {analyticsTypes.map((type) => (
                  <TabsContent key={type.id} value={type.id} className="mt-0">
                    <Card>
                      <CardHeader>
                        <CardTitle>{getAnalyticsTitle()}</CardTitle>
                        <CardDescription>{getAnalyticsDescription()}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {/* Filters */}
                        <div className="flex flex-wrap gap-4 mb-6">
                          <div className="flex items-center space-x-2">
                            <Select
                              value={timeRange}
                              onValueChange={setTimeRange}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Time Range" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="week">Last Week</SelectItem>
                                <SelectItem value="month">Last Month</SelectItem>
                                <SelectItem value="quarter">Last Quarter</SelectItem>
                                <SelectItem value="year">Last Year</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <DatePicker
                              date={startDate}
                              setDate={setStartDate}
                              placeholder="Start Date"
                            />
                            <span>to</span>
                            <DatePicker
                              date={endDate}
                              setDate={setEndDate}
                              placeholder="End Date"
                            />
                          </div>

                          {selectedAnalyticsType === 'inventory-trends' && (
                            <Select
                              value={categoryFilter}
                              onValueChange={setCategoryFilter}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Categories</SelectItem>
                                {analyticsData?.categoryDistribution?.map((category) => (
                                  <SelectItem key={category.name} value={category.name}>
                                    {category.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </div>

                        {/* Summary Cards */}
                        {analyticsData?.summary && (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Total Items</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">{analyticsData.summary.totalItems}</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Categories</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">{analyticsData.summary.totalCategories}</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold text-amber-500">{analyticsData.summary.lowStockCount}</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">${analyticsData?.summary?.totalValue != null ? analyticsData.summary.totalValue.toFixed(2) : '—'}</div>
                              </CardContent>
                            </Card>
                          </div>
                        )}

                        {/* Chart Type Selection */}
                        <div className="flex justify-end mb-4">
                          <div className="flex border rounded-md overflow-hidden">
                            <Button
                              variant={chartType === 'bar' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('bar')}
                              className="rounded-none"
                            >
                              <BarChart3 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={chartType === 'pie' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('pie')}
                              className="rounded-none"
                            >
                              <PieChartIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={chartType === 'line' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('line')}
                              className="rounded-none"
                            >
                              <LineChartIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Chart */}
                        {isLoading ? (
                          <div className="flex justify-center items-center h-64">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading data...</span>
                          </div>
                        ) : analyticsData ? (
                          <div className="h-96">
                            {getChartData().length > 0 ? (
                              <ResponsiveContainer width="100%" height="100%">
                                {chartType === 'bar' ? (
                                  <BarChart
                                    data={getChartData()}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#444' : '#ddd'} />
                                    <XAxis dataKey="name" tick={{ fill: theme === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <YAxis tick={{ fill: theme === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <Tooltip content={<CustomTooltip theme={theme} />} />
                                    <Legend />
                                    {selectedAnalyticsType === 'stock-levels' ? (
                                      <>
                                        <Bar dataKey="inStock" name="In Stock" stackId="a" fill="#4CAF50" />
                                        <Bar dataKey="lowStock" name="Low Stock" stackId="a" fill="#FF9800" />
                                        <Bar dataKey="outOfStock" name="Out of Stock" stackId="a" fill="#F44336" />
                                      </>
                                    ) : (
                                      <Bar dataKey="value" name="Value" fill="#3B82F6" />
                                    )}
                                  </BarChart>
                                ) : chartType === 'pie' ? (
                                  <PieChart>
                                    <Pie
                                      data={getChartData()}
                                      cx="50%"
                                      cy="50%"
                                      labelLine={false}
                                      outerRadius={150}
                                      fill="#8884d8"
                                      dataKey="value"
                                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                    >
                                      {getChartData().map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                      ))}
                                    </Pie>
                                    <Tooltip content={<CustomTooltip theme={theme} />} />
                                    <Legend />
                                  </PieChart>
                                ) : (
                                  <LineChart
                                    data={getChartData()}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#444' : '#ddd'} />
                                    <XAxis dataKey="name" tick={{ fill: theme === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <YAxis tick={{ fill: theme === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <Tooltip content={<CustomTooltip theme={theme} />} />
                                    <Legend />
                                    <Line
                                      type="monotone"
                                      dataKey="value"
                                      name="Value"
                                      stroke="#3B82F6"
                                      strokeWidth={2}
                                      dot={{ r: 4 }}
                                      activeDot={{ r: 6 }}
                                    />
                                  </LineChart>
                                )}
                              </ResponsiveContainer>
                            ) : (
                              <div className="flex items-center justify-center h-full">
                                <p className="text-gray-500 dark:text-gray-400">No data available for this analytics type.</p>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-64">
                            <p className="text-gray-500 dark:text-gray-400">Select an analytics type to view data.</p>
                          </div>
                        )}
                      </CardContent>
                      <CardFooter className="text-sm text-muted-foreground">
                        {analyticsData && analyticsData.generatedAt && (
                          <div>
                            Data generated at: {format(new Date(analyticsData.generatedAt), 'MMM dd, yyyy HH:mm:ss')}
                          </div>
                        )}
                      </CardFooter>
                    </Card>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Analytics;