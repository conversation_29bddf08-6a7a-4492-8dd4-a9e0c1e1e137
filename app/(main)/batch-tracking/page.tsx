"use client";

import React from 'react';
import { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Layers,
  Plus,
  Search,
  Edit,
  Trash2,
  FileText,
  CheckCircle,
  XCircle,
  Calendar,
  AlertTriangle,
  RefreshCw,
  Filter,
  Eye,
  Info,
  AlertCircle,
  Loader2,
  ArrowUpDown,
  MoreHorizontal
} from 'lucide-react';
import { format, isAfter, isBefore, addDays, parseISO } from 'date-fns';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { useTheme } from '../../context/ThemeContext';
import Header from '../../../app/components/layout/Header';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/app/components/ui/dialog';
import { Label } from '@/app/components/ui/label';
import { Textarea } from '@/app/components/ui/textarea';
import { Badge } from '@/app/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/app/components/ui/popover';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/app/components/ui/dropdown-menu';

// Define interfaces for batch data
interface Part {
  _id: string;
  name: string;
  description?: string;
}

interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
}

interface WorkOrder {
  _id: string;
  woNumber: string;
  status: string;
}

interface Batch {
  _id: string;
  batchCode: string;
  partId?: string | Part;
  assemblyId?: string | Assembly;
  quantityPlanned: number;
  quantityProduced?: number;
  startDate: string;
  endDate?: string;
  status: string;
  notes?: string;
  workOrderId: string | WorkOrder;
  createdAt: string;
  updatedAt: string;
}

interface BatchLog {
  _id: string;
  batchId: string;
  timestamp: string;
  event: string;
  userId: string;
  details?: string;
  createdAt: string;
  updatedAt: string;
}

// Define form schema
const batchFormSchema = z.object({
  batchCode: z.string().optional(),
  partId: z.string().optional(),
  assemblyId: z.string().optional(),
  quantityPlanned: z.number().min(1, "Quantity must be at least 1"),
  quantityProduced: z.number().optional(),
  startDate: z.string(),
  endDate: z.string().optional(),
  status: z.string(),
  notes: z.string().optional(),
  workOrderId: z.string()
});

const BatchTracking: React.FC = () => {
  const { theme } = useTheme();
  const [batches, setBatches] = useState<Batch[]>([]);
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [parts, setParts] = useState<Part[]>([]);
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentBatch, setCurrentBatch] = useState<Batch | null>(null);
  const [selectedBatch, setSelectedBatch] = useState<Batch | null>(null);
  const [batchLogs, setBatchLogs] = useState<BatchLog[]>([]);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentBatchAction, setCurrentBatchAction] = useState<'add' | 'edit'>('add');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isLoadingAction, setIsLoadingAction] = useState(false);

  // Form setup with zod validation
  const form = useForm<z.infer<typeof batchFormSchema>>({
    resolver: zodResolver(batchFormSchema),
    defaultValues: {
      batchCode: '',
      partId: '',
      assemblyId: '',
      quantityPlanned: 1,
      quantityProduced: 0,
      startDate: format(new Date(), 'yyyy-MM-dd'),
      status: 'pending',
      notes: '',
      workOrderId: ''
    }
  });

  // Fetch data on component mount
  useEffect(() => {
    fetchBatches();
    fetchWorkOrders();
    fetchParts();
    fetchAssemblies();
  }, [currentPage, pageSize, statusFilter, sortField, sortOrder]);

  // Fetch batches from API
  const fetchBatches = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sortField,
        sortOrder
      });

      // Add status filter if not 'all'
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      // Add search query if present
      if (searchQuery) {
        params.append('search', searchQuery);
      }

      const response = await fetch(`/api/batches?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Error fetching batches: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setBatches(data.data || []);

      // Update pagination info
      if (data.pagination) {
        setTotalPages(data.pagination.totalPages);
      }
    } catch (err) {
      console.error('Error fetching batches:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load batch data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch work orders from API
  const fetchWorkOrders = async () => {
    try {
      const response = await fetch('/api/work-orders');

      if (!response.ok) {
        throw new Error(`Error fetching work orders: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setWorkOrders(data.data || []);
    } catch (err) {
      console.error('Error fetching work orders:', err);
      toast.error('Failed to load work orders');
    }
  };

  // Fetch parts from API
  const fetchParts = async () => {
    try {
      const response = await fetch('/api/parts');

      if (!response.ok) {
        throw new Error(`Error fetching parts: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setParts(data.data || []);
    } catch (err) {
      console.error('Error fetching parts:', err);
      toast.error('Failed to load parts');
    }
  };

  // Fetch assemblies from API
  const fetchAssemblies = async () => {
    try {
      const response = await fetch('/api/assemblies');

      if (!response.ok) {
        throw new Error(`Error fetching assemblies: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setAssemblies(data.data || []);
    } catch (err) {
      console.error('Error fetching assemblies:', err);
      toast.error('Failed to load assemblies');
    }
  };

  // Fetch batch logs from API
  const fetchBatchLogs = async (batchId: string) => {
    if (!batchId) return;
    try {
      const response = await fetch(`/api/batches/${batchId}/logs`);

      if (!response.ok) {
        throw new Error(`Error fetching batch logs: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setBatchLogs(data.data || []);
    } catch (err) {
      console.error('Error fetching batch logs:', err);
      toast.error('Failed to load batch logs');
    }
  };

  // Open dialog for adding or editing a batch
  const openDialog = (batch: Batch | null = null) => {
    setCurrentBatchAction(batch ? 'edit' : 'add');
    setCurrentBatch(batch);

    if (batch) {
      // Set form values for editing
      form.reset({
        batchCode: batch.batchCode,
        partId: typeof batch.partId === 'string' ? batch.partId : (batch.partId as Part)?._id,
        assemblyId: typeof batch.assemblyId === 'string' ? batch.assemblyId : (batch.assemblyId as Assembly)?._id,
        quantityPlanned: batch.quantityPlanned,
        quantityProduced: batch.quantityProduced || 0,
        startDate: format(new Date(batch.startDate), 'yyyy-MM-dd'),
        endDate: batch.endDate ? format(new Date(batch.endDate), 'yyyy-MM-dd') : undefined,
        status: batch.status,
        notes: batch.notes || '',
        workOrderId: typeof batch.workOrderId === 'string' ? batch.workOrderId : (batch.workOrderId as WorkOrder)._id
      });
    } else {
      // Reset form for adding
      form.reset({
        batchCode: '',
        partId: '',
        assemblyId: '',
        quantityPlanned: 1,
        quantityProduced: 0,
        startDate: format(new Date(), 'yyyy-MM-dd'),
        status: 'pending',
        notes: '',
        workOrderId: ''
      });
    }

    setIsDialogOpen(true);
  };

  // Close dialog
  const closeDialog = () => {
    setIsDialogOpen(false);
    setCurrentBatch(null);
    form.reset();
  };

  // Open view dialog for a batch
  const openViewDialog = (batch: Batch) => {
    setSelectedBatch(batch);
    fetchBatchLogs(typeof batch._id === 'string' ? batch._id : batch._id.toString());
    setIsViewDialogOpen(true);
  };

  // Close view dialog
  const closeViewDialog = () => {
    setIsViewDialogOpen(false);
    setSelectedBatch(null);
    setBatchLogs([]);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (batch: Batch) => {
    setSelectedBatch(batch);
    setIsDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setSelectedBatch(null);
  };

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof batchFormSchema>) => {
    setIsLoadingAction(true);
    try {
      const method = currentBatchAction === 'edit' ? 'PUT' : 'POST';
      const url = currentBatchAction === 'edit' && currentBatch
        ? `/api/batches/${currentBatch._id}`
        : '/api/batches';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`Error ${currentBatchAction === 'edit' ? 'updating' : 'creating'} batch: ${response.status}`);
      }

      const responseData = await response.json();

      if (responseData.error) {
        throw new Error(responseData.error);
      }

      toast.success(`Batch ${currentBatchAction === 'edit' ? 'updated' : 'created'} successfully`);
      closeDialog();
      fetchBatches();
    } catch (err) {
      console.error(`Error ${currentBatchAction === 'edit' ? 'updating' : 'creating'} batch:`, err);
      toast.error(err instanceof Error ? err.message : `Failed to ${currentBatchAction} batch`);
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Delete a batch
  const deleteBatch = async () => {
    if (!selectedBatch) return;

    setIsLoadingAction(true);
    try {
      const response = await fetch(`/api/batches/${selectedBatch._id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Error deleting batch: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      toast.success('Batch deleted successfully');
      closeDeleteDialog();
      fetchBatches();
    } catch (err) {
      console.error('Error deleting batch:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to delete batch');
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Update batch status
  const updateBatchStatus = async (batch: Batch, newStatus: string) => {
    setIsLoadingAction(true);
    try {
      const response = await fetch(`/api/batches/${batch._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error(`Error updating batch status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      toast.success(`Batch status updated to ${newStatus}`);
      fetchBatches();
    } catch (err) {
      console.error('Error updating batch status:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to update batch status');
    } finally {
      setIsLoadingAction(false);
    }
  };

  const handleDelete = (batchId: string) => {
    confirmAlert({
      title: 'Confirm Deletion',
      message: 'Are you sure you want to delete this batch?',
      buttons: [
        {
          label: 'Yes',
          onClick: async () => {
            try {
              setIsLoading(true);
              console.log(`Deleting batch ${batchId} via API...`);
              await new Promise(resolve => setTimeout(resolve, 500));
              toast.success('Batch deleted successfully!');

              fetchBatches();
            } catch (error: any) {
              console.error('Error deleting batch:', error);
              setError(error.message || 'Failed to delete batch');
              toast.error(error.message || 'Failed to delete batch');
            } finally {
              setIsLoading(false);
            }
          }
        },
        {
          label: 'No',
          onClick: () => {}
        }
      ]
    });
  };

  const getQualityStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 bg-yellow-200 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded-full text-xs">Pending</span>;
      case 'approved':
        return <span className="px-2 py-1 bg-green-200 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full text-xs">Approved</span>;
      case 'rejected':
        return <span className="px-2 py-1 bg-red-200 dark:bg-red-900/30 text-red-800 dark:text-red-400 rounded-full text-xs">Rejected</span>;
      default:
        return <span className="px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-full text-xs">{status}</span>;
    }
  };

  const getExpiryStatus = (expiryDate: string | null) => {
    if (!expiryDate) return null;

    const today = new Date();
    const expiry = new Date(expiryDate);
    const warningDate = addDays(today, 30); // 30 days warning

    if (isBefore(expiry, today)) {
      return <span className="px-2 py-1 bg-red-200 dark:bg-red-900/30 text-red-800 dark:text-red-400 rounded-full text-xs">Expired</span>;
    } else if (isBefore(expiry, warningDate)) {
      return <span className="px-2 py-1 bg-yellow-200 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded-full text-xs">Expiring Soon</span>;
    } else {
      return <span className="px-2 py-1 bg-green-200 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full text-xs">Valid</span>;
    }
  };

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchBatches();
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      // Toggle sort order if clicking the same field
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort field and default to ascending
      setSortField(field);
      setSortOrder('asc');
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Get status badge style
  const getStatusBadge = (status: string) => {
    let className = '';

    switch (status.toLowerCase()) {
      case 'pending':
        className = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
        break;
      case 'in_progress':
        className = 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
        break;
      case 'completed':
        className = 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
        break;
      case 'cancelled':
        className = 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
        break;
      case 'on_hold':
        className = 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
        break;
      default:
        className = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }

    return (
      <Badge className={className}>
        {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
      </Badge>
    );
  };

  // Get part or assembly name
  const getItemName = (batch: Batch) => {
    if (batch.partId) {
      if (typeof batch.partId === 'string') {
        const part = parts.find(p => p._id === batch.partId);
        return part ? part.name : batch.partId;
      } else {
        return (batch.partId as Part).name;
      }
    } else if (batch.assemblyId) {
      if (typeof batch.assemblyId === 'string') {
        const assembly = assemblies.find(a => a._id === batch.assemblyId);
        return assembly ? assembly.name : batch.assemblyId;
      } else {
        return (batch.assemblyId as Assembly).name;
      }
    }
    return 'N/A';
  };

  // Get work order number
  const getWorkOrderNumber = (batch: Batch | null | undefined): string => {
    if (!batch || !batch.workOrderId) {
      return 'N/A';
    }
    if (typeof batch.workOrderId === 'string') {
      const workOrder = workOrders.find(wo => wo._id === batch.workOrderId);
      return workOrder?.woNumber ?? 'N/A';
    } else {
      return (batch.workOrderId as WorkOrder)?.woNumber ?? 'N/A';
    }
  };

  // Get status options for filter
  const statusOptions = useMemo(() => {
    const statuses = new Set<string>();
    statuses.add('all');

    batches.forEach(batch => {
      if (batch.status) {
        statuses.add(batch.status.toLowerCase());
      }
    });

    return Array.from(statuses);
  }, [batches]);

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Batch Tracking" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <Layers className="mr-2" />
            Batch Management
          </h2>
          <Button
            onClick={() => openDialog()}
            className="bg-primary hover:bg-primary/90"
          >
            <Plus size={16} className="mr-2" />
            New Batch
          </Button>
        </div>

        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded mb-4">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={fetchBatches}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        )}

        {/* Search and Filter Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search batches..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button variant="outline" onClick={handleSearch}>
              Search
            </Button>
          </div>

          <div className="flex gap-2">
            <Select
              value={statusFilter}
              onValueChange={(value) => {
                setStatusFilter(value);
                setCurrentPage(1); // Reset to first page when changing filter
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status === 'all'
                      ? 'All Statuses'
                      : status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                setPageSize(parseInt(value));
                setCurrentPage(1); // Reset to first page when changing page size
              }}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="Page size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Batch Table */}
        <div className="bg-card rounded-lg shadow-md dark:shadow-gray-900/30 overflow-hidden">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">Loading batches...</span>
            </div>
          ) : batches.length === 0 ? (
            <div className="text-center py-12">
              <Layers className="h-12 w-12 mx-auto text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">No batches found</h3>
              <p className="mt-2 text-gray-500 dark:text-gray-400">
                {searchQuery || statusFilter !== 'all'
                  ? 'Try changing your search or filter criteria'
                  : 'Get started by creating your first batch'}
              </p>
              {(searchQuery || statusFilter !== 'all') && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    fetchBatches();
                  }}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset Filters
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-border bg-muted/50">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSortChange('batchCode')}
                      >
                        Batch Code
                        {sortField === 'batchCode' && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSortChange('workOrderId')}
                      >
                        Work Order
                        {sortField === 'workOrderId' && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Item
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSortChange('quantityPlanned')}
                      >
                        Quantity
                        {sortField === 'quantityPlanned' && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSortChange('startDate')}
                      >
                        Start Date
                        {sortField === 'startDate' && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div
                        className="flex items-center cursor-pointer"
                        onClick={() => handleSortChange('status')}
                      >
                        Status
                        {sortField === 'status' && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {batches.map((batch, index) => (
                    <motion.tr
                      key={batch._id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="hover:bg-muted/50"
                    >
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-foreground">{batch.batchCode}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          ID: {typeof batch._id === 'string' ? batch._id.substring(0, 8) : batch._id.toString().substring(0, 8)}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-foreground">{getWorkOrderNumber(batch)}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-foreground">{getItemName(batch)}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {batch.partId ? 'Part' : batch.assemblyId ? 'Assembly' : 'N/A'}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-foreground">
                          {batch.quantityProduced !== undefined && batch.quantityProduced > 0
                            ? `${batch.quantityProduced} / ${batch.quantityPlanned}`
                            : batch.quantityPlanned}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-foreground">
                        {format(new Date(batch.startDate), 'MMM dd, yyyy')}
                        {batch.endDate && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            End: {format(new Date(batch.endDate), 'MMM dd, yyyy')}
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {getStatusBadge(batch.status)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openViewDialog(batch)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDialog(batch)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {batch.status === 'pending' && (
                              <DropdownMenuItem onClick={() => updateBatchStatus(batch, 'in_progress')}>
                                <Play className="mr-2 h-4 w-4" />
                                Start Production
                              </DropdownMenuItem>
                            )}
                            {batch.status === 'in_progress' && (
                              <DropdownMenuItem onClick={() => updateBatchStatus(batch, 'completed')}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Mark as Completed
                              </DropdownMenuItem>
                            )}
                            {(batch.status === 'pending' || batch.status === 'in_progress') && (
                              <DropdownMenuItem onClick={() => updateBatchStatus(batch, 'on_hold')}>
                                <Pause className="mr-2 h-4 w-4" />
                                Put on Hold
                              </DropdownMenuItem>
                            )}
                            {batch.status === 'on_hold' && (
                              <DropdownMenuItem onClick={() => updateBatchStatus(batch, 'in_progress')}>
                                <Play className="mr-2 h-4 w-4" />
                                Resume Production
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => openDeleteDialog(batch)}
                              className="text-red-600 dark:text-red-400"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {!isLoading && batches.length > 0 && (
            <div className="flex items-center justify-between px-4 py-3 border-t border-border">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Showing <span className="font-medium">{batches.length}</span> of{' '}
                <span className="font-medium">{totalPages * pageSize}</span> results
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <div className="flex items-center space-x-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? 'default' : 'outline'}
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  ))}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>

        {isDialogOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-card rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {currentBatch ? 'Edit Batch' : 'Create Batch'}
                </h2>
              </div>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batch #</label>
                    <input
                      type="text"
                      {...register('batch_number')}
                      readOnly={!!currentBatch}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    {errors.batch_number && <p className="text-red-500 text-xs mt-1">{errors.batch_number.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Part</label>
                    <select
                      {...register('partId', { required: 'Part is required' })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      <option value="">Select Part</option>
                      {parts.map((product) => (
                        <option key={product._id || product.id} value={product._id || product.id}>
                          {product.name}
                        </option>
                      ))}
                    </select>
                    {errors.partId && <p className="text-red-500 text-xs mt-1">{errors.partId.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Quantity</label>
                    <input
                      type="number"
                      {...register('quantity', {
                        required: 'Quantity is required',
                        min: { value: 0.01, message: 'Quantity must be greater than 0' }
                      })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    {errors.quantity && <p className="text-red-500 text-xs mt-1">{errors.quantity.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Manufacturing Date</label>
                    <input
                      type="date"
                      {...register('manufacturingDate', { required: 'Manufacturing date is required' })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    {errors.manufacturingDate && <p className="text-red-500 text-xs mt-1">{errors.manufacturingDate.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expiry Date</label>
                    <input
                      type="date"
                      {...register('expiryDate')}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Supplier Batch #</label>
                    <input
                      type="text"
                      {...register('supplierBatch')}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Quality Status</label>
                    <select
                      {...register('qualityStatus', { required: 'Status is required' })}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      <option value="pending">Pending</option>
                      <option value="approved">Approved</option>
                      <option value="rejected">Rejected</option>
                    </select>
                    {errors.qualityStatus && <p className="text-red-500 text-xs mt-1">{errors.qualityStatus.message}</p>}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
                    <textarea
                      {...register('notes')}
                      rows={3}
                      className="w-full p-2 border dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    ></textarea>
                  </div>
                </div>

                <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={closeDialog}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-yellow-300 dark:bg-yellow-600 text-gray-800 dark:text-gray-100 rounded-md hover:bg-yellow-400 dark:hover:bg-yellow-700"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Saving...' : 'Save Batch'}
                  </button>
                </div>
              </form>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatchTracking;