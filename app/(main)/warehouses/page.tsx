"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Warehouse,
  Plus,
  Search,
  Edit,
  Trash2,
  FileText,
  CheckCircle,
  XCircle,
  MapPin,
  AlertTriangle
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';
import Header from '@/app/components/layout/Header';
import { useTheme } from '@/app/context/ThemeContext';
import { Button } from "@/app/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { Badge } from "@/app/components/ui/badge";
import { Input } from "@/app/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";

/**
 * Interface for warehouse data
 */
interface WarehouseType {
  /** Unique identifier for the warehouse */
  id: string;
  /** Name of the warehouse */
  name: string;
  /** Physical address of the warehouse */
  address?: string;
  /** Type of warehouse (e.g., 'Distribution', 'Storage', 'Manufacturing') */
  type: string;
  /** Whether the warehouse is currently active */
  isActive: boolean;
  /** Geographic coordinates of the warehouse */
  coordinates?: {
    /** Latitude coordinate */
    latitude?: number;
    /** Longitude coordinate */
    longitude?: number;
  };
}

/**
 * Warehouses page component
 * Displays a list of warehouses and allows users to create, edit, and delete warehouses
 * Includes filtering and search functionality
 */
const Warehouses: React.FC = () => {
  const { theme } = useTheme();
  const [warehouses, setWarehouses] = useState<WarehouseType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentWarehouse, setCurrentWarehouse] = useState<WarehouseType | null>(null);
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<WarehouseType>();

  // Simulate loading with a timeout
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const openModal = (warehouse: WarehouseType | null = null) => {
    setIsModalOpen(true);
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'warehouse':
        return <Badge variant="default">Warehouse</Badge>;
      case 'production':
        return <Badge variant="secondary">Production</Badge>;
      case 'retail':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 hover:bg-purple-100/80">Retail</Badge>;
      case 'other':
        return <Badge variant="outline">Other</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive
      ? <Badge variant="secondary">Active</Badge>
      : <Badge variant="destructive">Inactive</Badge>;
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Warehouses" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Warehouse className="text-foreground mr-2" size={24} />
            <h1 className="text-2xl font-semibold text-foreground">Warehouses & Locations</h1>
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-blue-500 dark:bg-blue-600 hover:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
            onClick={() => openModal()}
          >
            <Plus size={18} className="mr-1" />
            <span>Add Location</span>
          </motion.div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="bg-card rounded-xl shadow-md overflow-hidden">
          <div className="p-4 flex flex-wrap gap-4 items-center border-b border-border">
            <div className="relative flex-1 min-w-[200px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <Input
                type="text"
                placeholder="Search warehouses..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="min-w-[150px]">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="warehouse">Warehouse</SelectItem>
                <SelectItem value="production">Production</SelectItem>
                <SelectItem value="retail">Retail</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="p-8 text-center text-muted-foreground">
              <div className="flex justify-center items-center h-64">
                <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                <span className="ml-3">Loading warehouses...</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-10">
              <h3 className="text-xl font-medium text-foreground mb-4">Warehouses Page</h3>
              <p className="text-muted-foreground">
                This page is currently being migrated to Next.js App Router.
              </p>
              <p className="text-muted-foreground mt-2">
                The complete implementation will be available soon.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Warehouses;