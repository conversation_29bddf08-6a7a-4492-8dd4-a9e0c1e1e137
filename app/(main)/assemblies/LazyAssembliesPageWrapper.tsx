'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';

// Dynamically import the EnhancedAssembliesPageWrapper component
const DynamicEnhancedAssembliesPageWrapper = dynamic(
  () => import('./EnhancedAssembliesPageWrapper'),
  {
    loading: () => <LoadingFallback />,
    ssr: false,
  }
);

// Loading fallback component
function LoadingFallback() {
  const { theme } = useTheme();
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Assemblies</h1>
      </div>
      <div className="bg-card rounded-lg border border-border p-8 flex justify-center items-center">
        <div className="flex flex-col items-center">
          <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Loading assemblies data...</p>
        </div>
      </div>
    </div>
  );
}

/**
 * Lazy-loaded wrapper for EnhancedAssembliesPageWrapper
 * This component dynamically imports the actual page component only when needed
 */
export default function LazyAssembliesPageWrapper() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <DynamicEnhancedAssembliesPageWrapper />
    </Suspense>
  );
}
