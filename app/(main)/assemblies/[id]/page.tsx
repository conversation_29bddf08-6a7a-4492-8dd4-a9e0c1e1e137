'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Loader2,
  ExternalLink,
  Layers,
  Tag
} from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Badge } from '@/app/components/ui/badge';
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';
import { BomViewer } from '@/app/components/features/BomViewer';
import { useTheme } from '@/app/context/ThemeContext';

/**
 * Interface for route parameters including the assembly ID
 */
interface AssemblyDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

/**
 * Assembly Detail page component - shows detailed information about an assembly
 */
export default function AssemblyDetailPage({ params }: AssemblyDetailPageProps) {
  // Unwrap params using React.use() to handle the Promise
  const unwrappedParams = React.use(params);
  const router = useRouter();
  const { theme } = useTheme();
  const [assembly, setAssembly] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch assembly data by ID
   */
  useEffect(() => {
    const fetchAssembly = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Add includeParts=true parameter to get detailed parts data
        const url = new URL(`/api/assemblies/${unwrappedParams.id}`, window.location.origin);
        url.searchParams.append('includeParts', 'true');

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new Error('Failed to fetch assembly');
        }

        const data = await response.json();

        if (!data.data) {
          throw new Error('Assembly not found');
        }

        // Log the assembly data to see its structure
        console.log('Assembly data:', data.data);

        // Check if any parts are marked as assemblies using canonical partsRequired
        if (data.data && data.data.partsRequired) {
          const assemblies = data.data.partsRequired.filter((part: any) =>
            (typeof part.partId === 'object' &&
              (part.partId.isAssembly === true || part.partId.is_assembly === true))
          );
          console.log('Sub-assemblies found:', assemblies.length, assemblies);
        }

        setAssembly(data.data);
      } catch (error) {
        console.error('Error fetching assembly:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
        toast.error('Failed to load assembly');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssembly();
  }, [unwrappedParams.id]);

  /**
   * Handle assembly deletion
   */
  const handleDelete = () => {
    confirmAlert({
      title: "Confirm Deletion",
      message: `Are you sure you want to delete assembly "${assembly.name}" (${assembly.assemblyCode})?`,
      buttons: [
        {
          label: 'Yes, Delete It',
          onClick: async () => {
            try {
              setIsDeleting(true);

              const response = await fetch(`/api/assemblies/${unwrappedParams.id}`, {
                method: 'DELETE',
              });

              if (response.ok) {
                toast.success(`Assembly ${assembly.assemblyCode} deleted successfully`);
                router.push('/assemblies');
              } else {
                const data = await response.json();
                throw new Error(data.error || "Failed to delete assembly");
              }
            } catch (error) {
              toast.error(error instanceof Error ? error.message : "An error occurred while deleting the assembly");
              console.error("Delete error:", error);
            } finally {
              setIsDeleting(false);
            }
          }
        },
        {
          label: 'Cancel',
          onClick: () => {}
        }
      ]
    });
  };

  if (isLoading) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-yellow-500" />
          <span className="ml-2 text-lg">Loading assembly details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8 space-y-8">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">Error Loading Assembly</h2>
          <p className="text-red-700 dark:text-red-300 mb-4">{error}</p>
          <div className="flex gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            <Button variant="default" onClick={() => router.refresh()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Make sure assembly is defined before rendering
  if (!assembly) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-yellow-500" />
          <span className="ml-2 text-lg">Loading assembly details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold tracking-tight">{assembly?.name || 'Unnamed Assembly'}</h1>
            <Badge variant="outline" className="ml-2 font-mono text-xs">
              {assembly?.assemblyCode || 'No ID'}
            </Badge>
          </div>
          <p className="text-muted-foreground mt-1">
            {assembly?.status || 'Standard'} Assembly
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/assemblies">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Assemblies
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/assemblies/${unwrappedParams.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="mr-2 h-4 w-4" />
            )}
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Assembly Details */}
        <Card className={`border shadow-sm lg:col-span-1 ${theme === 'light' ? 'bg-white border-gray-200' : 'bg-[var(--T-bg-card)] border-[var(--T-border-color)]'}`}>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className={`divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-[var(--T-border-color)]'}`}>
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>ID</dt>
                <dd className={`col-span-2 text-sm font-mono ${theme === 'light' ? 'text-gray-900' : 'text-gray-100'}`}>
                  {assembly?.assemblyCode || 'No ID'}
                </dd>
              </div>
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Name</dt>
                <dd className={`col-span-2 text-sm ${theme === 'light' ? 'text-gray-900' : 'text-gray-100'}`}>
                  {assembly?.name || 'Unnamed Assembly'}
                </dd>
              </div>
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Status</dt>
                <dd className={`col-span-2 text-sm ${theme === 'light' ? 'text-gray-900' : 'text-gray-100'}`}>
                  {assembly?.status || 'Standard'}
                </dd>
              </div>
              {assembly?.version && (
                <div className="py-3 grid grid-cols-3 gap-1">
                  <dt className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Version</dt>
                  <dd className={`col-span-2 text-sm font-mono ${theme === 'light' ? 'text-gray-900' : 'text-gray-100'}`}>
                    {assembly.version}
                  </dd>
                </div>
              )}
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Parts</dt>
                <dd className={`col-span-2 text-sm ${theme === 'light' ? 'text-gray-900' : 'text-gray-100'}`}>
                  <Badge>{(assembly?.partsRequired && Array.isArray(assembly.partsRequired) ? assembly.partsRequired.length : 0)}</Badge>
                </dd>
              </div>
              {assembly?.description && (
                <div className="py-3 grid grid-cols-3 gap-1">
                  <dt className={`text-sm font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Description</dt>
                  <dd className={`col-span-2 text-sm ${theme === 'light' ? 'text-gray-900' : 'text-gray-100'}`}>
                    {assembly.description}
                  </dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        {/* Assembly Parts */}
        <Card className={`border shadow-sm lg:col-span-2 ${theme === 'light' ? 'bg-white border-gray-200' : 'bg-[var(--T-bg-card)] border-[var(--T-border-color)]'}`}>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Parts List
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`overflow-x-auto rounded-md border p-4 ${theme === 'light' ? 'border-gray-200' : 'border-[var(--T-border-color)]'}`}>
              {(assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0) ? (
                <BomViewer
                  components={(assembly.partsRequired).map((part: any) => {
                    // Log each part to see its structure
                    console.log('Mapping part for BomViewer:', part);

                    // Determine if this part is an assembly
                    let isAssembly = false;
                    
                    // Use canonical partId
                    const partRef = part.item_id || part.partId;
                    const itemType = part.item_type;
                    
                    if (typeof partRef === 'object' && partRef !== null) {
                      isAssembly =
                        partRef.is_assembly === true ||
                        partRef.isAssembly === true ||
                        // Check if the part has an assemblyCode which indicates it's an assembly
                        Boolean(partRef.assemblyCode) ||
                        // Check if the part has partsRequired which indicates it's an assembly
                        (Array.isArray(partRef.partsRequired) && partRef.partsRequired.length > 0) ||
                        // Check if the part ID starts with "ASM-" which indicates it's an assembly
                        (typeof partRef.part_id === 'string' && partRef.part_id.startsWith('ASM-')) ||
                        (typeof partRef._id === 'string' && partRef._id.toString().startsWith('ASM-'))
                    }

                    // Create a properly formatted component for BomViewer
                    return {
                      part: {
                        _id: typeof partRef === 'object' ? (partRef._id || '') : partRef,
                        part_id: typeof partRef === 'object' ? (partRef.partNumber || partRef._id || '') : partRef,
                        name: typeof partRef === 'object' ? (partRef.name || 'Unknown Part') : 'Unknown Part',
                        is_assembly: isAssembly,
                        current_stock: typeof partRef === 'object' ? (partRef.inventory?.currentStock || 0) : 0,
                        children: part.children || [],
                        assemblyCode: typeof partRef === 'object' ? (partRef.assemblyCode || null) : null,
                        partsRequired: typeof partRef === 'object' ? (partRef.partsRequired || []) : []
                      },
                      quantity: part.quantityRequired || part.quantity || 0,
                      level: 0,
                      parentId: null
                    };
                  })}
                  initiallyExpanded={true}
                />
              ) : (
                <div className={`py-4 text-center ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  No parts found for this assembly.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}