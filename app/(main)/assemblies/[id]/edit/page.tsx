import React from 'react';
import AssemblyFormWrapper from '@/app/components/forms/AssemblyFormWrapper';
import AssemblyFormContent from './AssemblyFormContent';

/**
 * Interface for route parameters including the assembly ID
 */
interface EditAssemblyPageProps {
  // Params are usually a direct object in App Router page components
  params: {
    id: string;
  };
}

/**
 * Edit Assembly page component - allows editing an existing assembly
 */
export default function EditAssemblyPage({ params }: EditAssemblyPageProps) {
  console.log("[EditAssemblyPage] Received params:", JSON.stringify(params));
  
  const assemblyId = params?.id;
  console.log("[EditAssemblyPage] Extracted assemblyId type: " + typeof assemblyId + ", value: " + String(assemblyId));

  if (!assemblyId) {
    console.error("[EditAssemblyPage] assemblyId is missing or invalid.");
    // Optionally, render an error message or redirect
    return <div>Error: Assembly ID is missing.</div>;
  }

  return (
    <AssemblyFormWrapper assemblyId={assemblyId}>
      <AssemblyFormContent />
    </AssemblyFormWrapper>
  );
}