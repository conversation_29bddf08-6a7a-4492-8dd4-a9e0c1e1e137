'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { SlidersHorizontal, Plus, LayoutGrid, List, Clock, Layers } from 'lucide-react';
import Link from 'next/link';

import Header from '@/app/components/layout/Header';
import AssembliesTableClient from '@/app/components/tables/AssembliesTable/AssembliesTableClient';
import { EnhancedAssemblyCard } from '@/app/components/cards/EnhancedAssemblyCard';
import { Button } from '@/app/components/ui/button';
import { useTheme } from '@/app/context/ThemeContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Input } from '@/app/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { RefreshDataButton } from '@/app/components/actions/RefreshDataButton';
import { AutoRefreshControl } from '@/app/components/controls/AutoRefreshControl';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { AssemblyFormModal } from '@/app/components/modals/AssemblyFormModal';
import { useAssemblyUIPreferences } from '@/app/hooks/useAssemblyUIPreferences';
import { RecentAssemblies } from '@/app/components/assemblies/RecentAssemblies';
import { toast } from 'sonner';
import { AssembliesGrid } from '@/app/components/tables/AssembliesGrid';

/**
 * Assemblies page content component - displays list of assemblies
 */
const AssembliesPageContent: React.FC = () => {
  const { theme: _ } = useTheme(); // Unused but kept for context
  const {
    assemblies,
    isLoading,
    error,
    refreshAssemblies
  } = useAssemblies();

  // Use UI preferences hook to persist user preferences
  const {
    preferences,
    updatePreference,
    updatePreferences,
    isLoaded
  } = useAssemblyUIPreferences();

  // Initialize state from preferences (defaulting until preferences are loaded)
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState(preferences.searchQuery || '');
  const [statusFilter, setStatusFilter] = useState(preferences.filterStatus || '');
  const [sortBy, setSortBy] = useState(preferences.sortBy || 'name');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>(preferences.viewMode || 'grid');

  // Sync state with preferences once loaded
  useEffect(() => {
    if (isLoaded) {
      setSearchQuery(preferences.searchQuery);
      setStatusFilter(preferences.filterStatus);
      setSortBy(preferences.sortBy);
      setViewMode(preferences.viewMode);
    }
  }, [isLoaded, preferences]);

  // Updates preference when value changes
  useEffect(() => {
    if (!isLoaded) return;
    updatePreference('searchQuery', searchQuery);
  }, [searchQuery, updatePreference, isLoaded]);

  useEffect(() => {
    if (!isLoaded) return;
    updatePreference('filterStatus', statusFilter);
  }, [statusFilter, updatePreference, isLoaded]);

  useEffect(() => {
    if (!isLoaded) return;
    updatePreference('sortBy', sortBy);
  }, [sortBy, updatePreference, isLoaded]);

  // Fetch assemblies with parts data for both grid and table views
  useEffect(() => {
    // Always fetch with parts data for both grid and table views
    if (isLoaded) { // Only refresh if preferences are loaded
      refreshAssemblies({ includeParts: true });
    }
  }, [viewMode, refreshAssemblies, isLoaded]);

  // Re-fetch with parts data when changing view mode - use callback to prevent re-renders
  const handleViewModeChange = useCallback((mode: 'grid' | 'table') => {
    setViewMode(mode);
    if (isLoaded) {
      updatePreference('viewMode', mode);
    }

    if (mode === 'grid') {
      // When switching to grid view, fetch with parts data
      refreshAssemblies({ includeParts: true });
      toast.success('Switched to Grid view', {
        description: 'Showing assemblies in a card-based layout',
        duration: 2000
      });
    } else {
      toast.success('Switched to Table view', {
        description: 'Showing assemblies in a detailed table',
        duration: 2000
      });
    }
  }, [isLoaded, updatePreference, refreshAssemblies]);

  // Helper function to determine assembly status based on partsRequired
  const getAssemblyStatus = (assembly: any): 'complete' | 'incomplete' | 'review' => {
    // Safely handle potentially missing partsRequired field
    if (!assembly) return 'incomplete';

    const hasValidParts = assembly.partsRequired &&
      Array.isArray(assembly.partsRequired) &&
      assembly.partsRequired.length > 0;

    if (!hasValidParts) {
      return 'incomplete';
    }

    // Check for missing partId references within partsRequired
    const hasMissingRefs = assembly.partsRequired.some((p: any) =>
      !p || p.partId === undefined || p.partId === null);

    if (hasMissingRefs) {
      return 'review';
    }

    return 'complete';
  };

  // Filter assemblies based on search query and status filter
  const filteredAssemblies = assemblies
    .filter(assembly => {
      // Skip invalid assemblies - use assemblyCode from new schema
      if (!assembly || !assembly.name) {
        console.warn('[AssembliesPage] Filtering out invalid assembly:', assembly);
        return false;
      }

      // Search by name, assemblyCode (new schema), or _id
      const matchesSearch =
        !searchQuery ||
        assembly.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (assembly.assemblyCode && assembly.assemblyCode.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (assembly._id && assembly._id.toLowerCase().includes(searchQuery.toLowerCase()));

      // Filter by status (using the status field from schema)
      const matchesStatus = !statusFilter || statusFilter === 'all' || assembly.status === statusFilter;

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      // Sort based on selected sort option
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        case 'parts_count':
          // Safely handle potentially missing partsRequired
          const aCount = a.partsRequired && Array.isArray(a.partsRequired) ? a.partsRequired.length : 0;
          const bCount = b.partsRequired && Array.isArray(b.partsRequired) ? b.partsRequired.length : 0;
          return bCount - aCount; // Descending order (most parts first)
        case 'updated':
          // Check for standard updated fields with fallback to _id
          const aDate = a.updatedAt ? new Date(a.updatedAt).getTime() :
                     a.createdAt ? new Date(a.createdAt).getTime() :
                     a._id ? parseInt(a._id.substring(0, 8), 16) * 1000 : 0;
          const bDate = b.updatedAt ? new Date(b.updatedAt).getTime() :
                     b.createdAt ? new Date(b.createdAt).getTime() :
                     b._id ? parseInt(b._id.substring(0, 8), 16) * 1000 : 0;
          return bDate - aDate; // Descending order (newest first)
        default:
          return a.name.localeCompare(b.name);
      }
    });

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Assemblies" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            {/* Toggle Filters */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                className="rounded-full"
                onClick={() => setShowFilters(!showFilters)}
              >
                <SlidersHorizontal size={16} className="mr-2" />
                <span>Filters</span>
              </Button>
            </motion.div>

            {/* Create Assembly Section - Using Modal */}
            <AssemblyFormModal onSuccess={refreshAssemblies} />
          </div>

          <div className="flex items-center space-x-2">
            {/* View Toggle */}
            <div className="bg-muted rounded-full p-1 flex">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                className="rounded-full h-8 px-3"
                onClick={() => handleViewModeChange('grid')}
              >
                <LayoutGrid size={16} className="mr-1" />
                <span className="text-xs">Grid</span>
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                className="rounded-full h-8 px-3"
                onClick={() => handleViewModeChange('table')}
              >
                <List size={16} className="mr-1" />
                <span className="text-xs">Table</span>
              </Button>
            </div>

            {/* Auto-refresh Control */}
            {isLoaded && <AutoRefreshControl />}
          </div>
        </div>

        {/* Recent Assemblies */}
        {isLoaded && <RecentAssemblies />}

        {/* Filter Panel */}
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="bg-card rounded-lg shadow-md p-4 mb-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
                <Input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by name, code, or ID"
                />
              </div>

              {/* Removed Stage Filter */}

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {/* Use status values from schema */}
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending_review">Pending Review</SelectItem>
                    <SelectItem value="in_production">In Production</SelectItem>
                    <SelectItem value="obsolete">Obsolete</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sort By</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="name_desc">Name (Z-A)</SelectItem>
                    <SelectItem value="parts_count">Parts Count</SelectItem>
                    <SelectItem value="updated">Last Updated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </motion.div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-500 dark:text-gray-400">Loading assemblies...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">Error Loading Assemblies</h3>
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && filteredAssemblies.length === 0 && (
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
            <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Assemblies Found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchQuery || statusFilter
                ? "No assemblies match your current filters. Try adjusting your search or filter criteria."
                : "There are no assemblies in the system yet."}
            </p>
            {!(searchQuery || statusFilter) && (
              <Button asChild>
                <Link href="/assemblies/create">Create Assembly</Link>
              </Button>
            )}
          </div>
        )}

        {/* Results */}
        {!isLoading && !error && filteredAssemblies.length > 0 && (
          <div className="bg-card rounded-lg border border-border shadow-sm">
            {viewMode === 'table' ? (
              <AssembliesTableClient assemblies={filteredAssemblies} />
            ) : (
              <CardContent className="p-4">
                <AssembliesGrid assemblies={filteredAssemblies} />
              </CardContent>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AssembliesPageContent;
