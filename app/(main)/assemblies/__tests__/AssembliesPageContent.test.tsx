import React from 'react';
import { render, screen } from '@testing-library/react';
import AssembliesPageContent from '../AssembliesPageContent';

// Mock the dependencies
jest.mock('next/navigation', () => ({
  usePathname: () => '/assemblies',
}));

jest.mock('@/app/components/layout/Header', () => ({
  __esModule: true,
  default: ({ title }: { title: string }) => <div data-testid="header">{title}</div>,
}));

jest.mock('@/app/components/tables/AssembliesTable', () => ({
  AssembliesTable: ({ assemblies }: { assemblies: any[] }) => (
    <div data-testid="assemblies-table">
      {assemblies.length} assemblies in table
    </div>
  ),
}));

jest.mock('@/app/components/tables/AssembliesGrid', () => ({
  AssembliesGrid: ({ assemblies }: { assemblies: any[] }) => (
    <div data-testid="assemblies-grid">
      {assemblies.length} assemblies in grid
    </div>
  ),
}));

jest.mock('@/app/components/controls/AutoRefreshControl', () => ({
  AutoRefreshControl: () => <div data-testid="auto-refresh">Auto Refresh</div>,
}));

jest.mock('@/app/contexts/AssembliesContext', () => ({
  useAssemblies: () => ({
    assemblies: [
      {
        _id: '1',
        name: 'Test Assembly 1',
        assembly_id: 'ASM-001',
        assembly_stage: 'Final Assembly',
        parts: [{ partId: { _id: 'PART-001', name: 'Test Part' }, quantityRequired: 1 }],
      },
      {
        _id: '2',
        name: 'Test Assembly 2',
        assembly_id: 'ASM-002',
        assembly_stage: 'Sub Assembly',
        parts: [{ partId: { _id: 'PART-002', name: 'Test Part 2' }, quantityRequired: 2 }],
      },
    ],
    isLoading: false,
    error: null,
    refreshAssemblies: jest.fn(),
  }),
}));

jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({ theme: 'light' }),
}));

describe('AssembliesPageContent', () => {
  it('renders the assemblies page with correct navigation buttons', () => {
    render(<AssembliesPageContent />);
    
    // Check the header is present
    expect(screen.getByTestId('header')).toHaveTextContent('Assemblies');
    
    // Check for the assembly creation buttons
    expect(screen.getByText('Standard Assembly')).toBeInTheDocument();
    expect(screen.getByText('Hierarchical Assembly')).toBeInTheDocument();
    expect(screen.getByText('Visual Builder')).toBeInTheDocument();
    
    // Check that the links point to the correct routes
    const standardLink = screen.getByText('Standard Assembly').closest('a');
    const hierarchicalLink = screen.getByText('Hierarchical Assembly').closest('a');
    const visualBuilderLink = screen.getByText('Visual Builder').closest('a');
    
    expect(standardLink).toHaveAttribute('href', '/assemblies/create');
    expect(hierarchicalLink).toHaveAttribute('href', '/hierarchical-part-entry');
    expect(visualBuilderLink).toHaveAttribute('href', '/hierarchical-builder');
  });
}); 