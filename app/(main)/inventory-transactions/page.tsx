"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  RotateCcw,
  Plus,
  Search,
  Filter,
  FileText,
  ArrowUpRight,
  ArrowDownLeft,
  RefreshCw,
  MoveHorizontal,
  AlertTriangle,
  Package
} from 'lucide-react';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { Input } from '@/app/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { toast } from 'sonner';
import { TransactionForm } from '@/app/components/features/TransactionForm';
import Header from '@/app/components/layout/Header';
import { useTheme } from '@/app/context/ThemeContext';
import { Button } from "@/app/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";

/**
 * Interface for MongoDB inventory transaction data
 * Aligned with the canonical schema defined in inventorytransaction.model.ts
 */
interface InventoryTransaction {
  /** MongoDB ObjectId */
  _id?: string;
  /** ID of the part involved in the transaction */
  partId: string;
  /** ID of the warehouse involved in the transaction */
  warehouseId: string;
  /** Type of inventory transaction */
  transactionType: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment' | string;
  /** Quantity changed in this transaction */
  quantity: number;
  /** Previous stock level before transaction */
  previousStock: number;
  /** New stock level after transaction */
  newStock: number;
  /** Date when the transaction occurred */
  transactionDate: string | Date;
  /** Reference number for the transaction */
  referenceNumber?: string | null;
  /** Reference type (e.g., PurchaseOrder, WorkOrder) */
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | null;
  /** User who performed the transaction */
  userId: string;
  /** Additional notes about the transaction */
  notes?: string | null;
  /** Creation timestamp */
  createdAt?: string | Date;
  /** Update timestamp */
  updatedAt?: string | Date;

  // UI display fields
  partName?: string;
  warehouseName?: string;
  userName?: string;

  // Legacy field mappings for backward compatibility
  type?: string; // Maps to transactionType
  quantityChanged?: number; // Maps to quantity
  stockOnHandBefore?: number; // Maps to previousStock
  stockOnHandAfter?: number; // Maps to newStock
  referenceModel?: string; // Maps to referenceType
  referenceId?: string; // Maps to referenceNumber
}

/**
 * Interface for product data
 */
interface Product {
  /** Unique identifier for the product */
  id: string;
  /** Name of the product */
  name: string;
}

/**
 * Interface for user data
 */
interface User {
  /** Unique identifier for the user */
  id: string;
  /** Name of the user */
  name: string;
}

/**
 * Inventory Transactions page component
 * Displays a list of inventory transactions and allows users to create new transactions
 * Supports filtering by transaction type and date range
 */
const InventoryTransactions: React.FC = () => {
  const { theme } = useTheme();
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const { register, handleSubmit, reset, formState: { errors } } = useForm<InventoryTransaction>();

  useEffect(() => {
    fetchTransactions();
  }, [typeFilter, dateFilter, currentPage, searchTerm]);

  const fetchTransactions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      if (typeFilter !== 'all') params.append('type', typeFilter);
      if (dateFilter !== 'all') {
        if (dateFilter === 'today') params.append('period', 'day');
        else if (dateFilter === 'week') params.append('period', 'week');
        else if (dateFilter === 'month') params.append('period', 'month');
      }
      if (searchTerm) params.append('search', searchTerm);
      params.append('page', currentPage.toString());
      params.append('limit', '10');

      console.log('Fetching transactions with params:', params.toString());

      const response = await fetch(`/api/inventory-transactions?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache: 'no-store' to prevent caching issues
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        let errorMessage;
        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.error || 'Failed to fetch transactions';
        } catch (e) {
          errorMessage = `Server error: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Type the API response
      interface ApiResponse {
        success: boolean;
        error?: string;
        data: Array<{
          _id: string;
          partId: { _id: string; partNumber: string; name: string; } | string;
          warehouseId: { _id: string; warehouseCode: string; name: string; } | string;
          // Support both canonical and legacy field names
          transactionType?: string;
          type?: string;
          quantity?: number;
          quantityChanged?: number;
          previousStock?: number;
          stockOnHandBefore?: number;
          newStock?: number;
          stockOnHandAfter?: number;
          transactionDate: string;
          referenceNumber?: string;
          referenceId?: string;
          referenceType?: string;
          referenceModel?: string;
          userId: string | { _id: string; username: string; fullName: string; };
          notes?: string;
          createdAt: string;
          updatedAt: string;
        }>;
        pagination: {
          total: number;
          page: number;
          limit: number;
          totalPages: number;
        };
        meta: { duration: number };
      }

      const result = await response.json() as ApiResponse;
      console.log('Transactions API response:', result);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch transactions');
      }

      // Map the API response to match our UI expectations
      const mappedTransactions = (result.data || []).map((transaction) => {
        // Handle part data
        const partIdValue = typeof transaction.partId === 'string'
          ? transaction.partId
          : transaction.partId?._id;

        const partName = typeof transaction.partId === 'object' && transaction.partId
          ? transaction.partId.name || transaction.partId.partNumber
          : '';

        // Handle warehouse data
        const warehouseIdValue = typeof transaction.warehouseId === 'string'
          ? transaction.warehouseId
          : transaction.warehouseId?._id;

        const warehouseName = typeof transaction.warehouseId === 'object' && transaction.warehouseId
          ? transaction.warehouseId.name || transaction.warehouseId.warehouseCode
          : '';

        // Handle user data
        const userIdValue = typeof transaction.userId === 'string'
          ? transaction.userId
          : transaction.userId?._id;

        const userName = typeof transaction.userId === 'object' && transaction.userId
          ? transaction.userId.fullName || transaction.userId.username
          : '';

        return {
          _id: transaction._id,
          partId: partIdValue,
          warehouseId: warehouseIdValue,
          // Use canonical field names with fallbacks to legacy names
          transactionType: transaction.transactionType || transaction.type,
          quantity: transaction.quantity || transaction.quantityChanged,
          previousStock: transaction.previousStock || transaction.stockOnHandBefore,
          newStock: transaction.newStock || transaction.stockOnHandAfter,
          transactionDate: transaction.transactionDate,
          referenceNumber: transaction.referenceNumber || transaction.referenceId,
          referenceType: transaction.referenceType || transaction.referenceModel,
          userId: userIdValue,
          notes: transaction.notes,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
          // Legacy field mappings for backward compatibility
          type: transaction.transactionType || transaction.type,
          quantityChanged: transaction.quantity || transaction.quantityChanged,
          stockOnHandBefore: transaction.previousStock || transaction.stockOnHandBefore,
          stockOnHandAfter: transaction.newStock || transaction.stockOnHandAfter,
          referenceId: transaction.referenceNumber || transaction.referenceId,
          referenceModel: transaction.referenceType || transaction.referenceModel,
          // Populated field data for UI display
          partName,
          warehouseName,
          userName
        };
      });

      setTransactions(mappedTransactions);

      // Use the pagination data from the response
      setTotalPages(result.pagination?.totalPages || 1);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch transactions');
      toast.error('Could not load inventory transactions');
    } finally {
      setIsLoading(false);
    }
  };


  // Open the transaction form modal
  const openModal = () => {
    setIsModalOpen(true);
  };

  // Handle successful transaction creation
  const handleTransactionSuccess = () => {
    fetchTransactions();
    toast.success('Transaction added successfully');
  };

  // Close the transaction form modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      // Canonical transaction types
      case 'stock_in_purchase':
        return <ArrowDownLeft size={20} className="text-green-600 dark:text-green-400 p-1.5 bg-green-100 dark:bg-green-900/40 rounded-full shadow-sm" />;
      case 'stock_out_production':
        return <ArrowUpRight size={20} className="text-red-600 dark:text-red-400 p-1.5 bg-red-100 dark:bg-red-900/40 rounded-full shadow-sm" />;
      case 'adjustment_cycle_count':
        return <RefreshCw size={20} className="text-blue-600 dark:text-blue-400 p-1.5 bg-blue-100 dark:bg-blue-900/40 rounded-full shadow-sm" />;
      case 'stock_in_production':
        return <ArrowDownLeft size={20} className="text-green-600 dark:text-green-400 p-1.5 bg-green-100 dark:bg-green-900/40 rounded-full shadow-sm" />;
      case 'transfer_out':
        return <ArrowUpRight size={20} className="text-amber-600 dark:text-amber-400 p-1.5 bg-amber-100 dark:bg-amber-900/40 rounded-full shadow-sm" />;
      case 'transfer_in':
        return <ArrowDownLeft size={20} className="text-amber-600 dark:text-amber-400 p-1.5 bg-amber-100 dark:bg-amber-900/40 rounded-full shadow-sm" />;
      case 'sales_shipment':
        return <ArrowUpRight size={20} className="text-red-600 dark:text-red-400 p-1.5 bg-red-100 dark:bg-red-900/40 rounded-full shadow-sm" />;

      // Legacy transaction types
      case 'purchase_receipt':
        return <ArrowDownLeft size={20} className="text-green-600 dark:text-green-400 p-1.5 bg-green-100 dark:bg-green-900/40 rounded-full shadow-sm" />;
      case 'production_output':
        return <ArrowDownLeft size={20} className="text-green-600 dark:text-green-400 p-1.5 bg-green-100 dark:bg-green-900/40 rounded-full shadow-sm" />;
      case 'stock_adjustment':
        return <RefreshCw size={20} className="text-blue-600 dark:text-blue-400 p-1.5 bg-blue-100 dark:bg-blue-900/40 rounded-full shadow-sm" />;
      case 'production_consumption':
        return <ArrowUpRight size={20} className="text-red-600 dark:text-red-400 p-1.5 bg-red-100 dark:bg-red-900/40 rounded-full shadow-sm" />;
      case 'internal_transfer_out':
        return <ArrowUpRight size={20} className="text-amber-600 dark:text-amber-400 p-1.5 bg-amber-100 dark:bg-amber-900/40 rounded-full shadow-sm" />;
      case 'internal_transfer_in':
        return <ArrowDownLeft size={20} className="text-amber-600 dark:text-amber-400 p-1.5 bg-amber-100 dark:bg-amber-900/40 rounded-full shadow-sm" />;
      case 'stock_in':
        return <ArrowDownLeft size={20} className="text-green-600 dark:text-green-400 p-1.5 bg-green-100 dark:bg-green-900/40 rounded-full shadow-sm" />;
      case 'stock_out':
        return <ArrowUpRight size={20} className="text-red-600 dark:text-red-400 p-1.5 bg-red-100 dark:bg-red-900/40 rounded-full shadow-sm" />;
      default:
        return <FileText size={20} className="text-gray-600 dark:text-gray-400 p-1.5 bg-gray-100 dark:bg-gray-800/60 rounded-full shadow-sm" />;
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Inventory Transactions" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <RotateCcw className="text-primary dark:text-primary-blue mr-3" size={24} />
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Inventory Transactions</h1>
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button variant="default" onClick={openModal} className="font-medium shadow-sm hover:shadow-md transition-all bg-primary hover:bg-primary/90 dark:bg-primary-blue dark:hover:bg-primary-blue/90 text-white px-4 py-2 rounded-md">
              <Plus size={18} className="mr-2" />
              <span>New Transaction</span>
            </Button>
          </motion.div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6 border-red-200 dark:border-red-900/50 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 shadow-sm">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <AlertTitle className="font-semibold text-red-800 dark:text-red-200">Error</AlertTitle>
            <AlertDescription className="text-red-700 dark:text-red-300">{error}</AlertDescription>
          </Alert>
        )}

        <div className="bg-card rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700/50">
          <div className="p-6 flex flex-wrap gap-5 items-center border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/30">
            <div className="relative flex-1 min-w-[240px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400" size={18} />
              <Input
                type="text"
                placeholder="Search transactions..."
                className="pl-10 border-gray-300 dark:border-gray-700 focus:border-primary/70 focus:ring-2 focus:ring-primary/40 dark:focus:ring-primary-blue/40 shadow-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="min-w-[180px] border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-primary/40 dark:focus:ring-primary-blue/40 shadow-sm">
                <Filter size={16} className="mr-2 text-gray-500 dark:text-gray-400" />
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="stock_in">Receive</SelectItem>
                <SelectItem value="stock_out">Issue</SelectItem>
                <SelectItem value="adjustment">Adjust</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="min-w-[180px] border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-primary/40 dark:focus:ring-primary-blue/40 shadow-sm">
                <FileText size={16} className="mr-2 text-gray-500 dark:text-gray-400" />
                <SelectValue placeholder="All Time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="p-8 text-center">
              <div className="flex flex-col justify-center items-center h-64 bg-gray-50/50 dark:bg-gray-800/20 rounded-lg border border-gray-200 dark:border-gray-700/50">
                <div className="w-12 h-12 border-4 border-primary dark:border-primary-blue border-t-transparent rounded-full animate-spin mb-4"></div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Loading transactions...</span>
                <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">This may take a moment</p>
              </div>
            </div>
          ) : transactions.length === 0 ? (
            <div className="text-center py-16 bg-gray-50/50 dark:bg-gray-800/20 rounded-lg border border-gray-200 dark:border-gray-700/50 my-4">
              <Package className="w-20 h-20 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
              <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">No Transactions Found</h3>
              <p className="text-gray-600 dark:text-gray-300 max-w-md mx-auto">
                No inventory transactions match your current filters.
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setTypeFilter('all');
                  setDateFilter('all');
                  setSearchTerm('');
                }}
                className="mt-6 border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50 shadow-sm font-medium"
              >
                Reset Filters
              </Button>
            </div>
          ) : (
            <div>
              <div className="overflow-x-auto">
                <Table className="border dark:border-gray-800 bg-white dark:bg-gray-900/30 rounded-md shadow-sm">
                  <TableHeader className="bg-gray-100 dark:bg-gray-800/50">
                    <TableRow>
                      <TableHead className="w-[150px]">Transaction Type</TableHead>
                      <TableHead>Part</TableHead>
                      <TableHead>Warehouse</TableHead>
                      <TableHead>Qty Change</TableHead>
                      <TableHead>Before</TableHead>
                      <TableHead>After</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Reference</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction._id} className="hover:bg-gray-50 dark:hover:bg-gray-800/40">
                        <TableCell>
                          <div className="flex items-center">
                            {getTransactionTypeIcon(transaction.transactionType || transaction.type)}
                            <span className="ml-2 capitalize font-medium">{(transaction.transactionType || transaction.type).replace(/_/g, ' ')}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium text-sm">{transaction.partName || 'Unknown Part'}</span>
                            <span className="text-xs text-gray-500 font-mono">{transaction.partId}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium text-sm">{transaction.warehouseName || 'Unknown Warehouse'}</span>
                            <span className="text-xs text-gray-500 font-mono">{transaction.warehouseId}</span>
                          </div>
                        </TableCell>
                        <TableCell className={(transaction.quantity || transaction.quantityChanged) > 0 ? 'text-green-600 dark:text-green-400 font-medium' : 'text-red-600 dark:text-red-400 font-medium'}>
                          {(transaction.quantity || transaction.quantityChanged) > 0 ? '+' : ''}{transaction.quantity || transaction.quantityChanged}
                        </TableCell>
                        <TableCell className="font-medium">{transaction.previousStock || transaction.stockOnHandBefore}</TableCell>
                        <TableCell className="font-medium">{transaction.newStock || transaction.stockOnHandAfter}</TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-300">
                          {transaction.transactionDate instanceof Date
                            ? format(transaction.transactionDate, 'yyyy-MM-dd HH:mm')
                            : format(new Date(transaction.transactionDate), 'yyyy-MM-dd HH:mm')}
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-300">
                          {transaction.referenceType || transaction.referenceModel
                            ? `${transaction.referenceType || transaction.referenceModel}: ${(transaction.referenceNumber || transaction.referenceId)?.substring(0, 8) || '-'}`
                            : '-'}
                        </TableCell>
                        <TableCell className="text-gray-600 dark:text-gray-300">
                          {transaction.userName || transaction.userId?.substring(0, 8) || '-'}
                        </TableCell>
                        <TableCell className="max-w-[200px] truncate text-gray-600 dark:text-gray-300">
                          {transaction.notes || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-4 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-800/30">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50 shadow-sm font-medium"
                  >
                    Previous
                  </Button>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 px-3 py-1.5 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700 shadow-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50 shadow-sm font-medium"
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Transaction Form Modal */}
      <TransactionForm
        isOpen={isModalOpen}
        onClose={closeModal}
        onSuccess={handleTransactionSuccess}
      />
    </div>
  );
};

export default InventoryTransactions;