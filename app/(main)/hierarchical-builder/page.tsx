"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { HierarchicalPartsForm, HierarchicalFormValues } from "@/app/components/forms/HierarchicalPartsForm";
import { getApiUrl } from "@/app/utils/apiUtils";
import { Button } from "@/app/components/ui/button";
import {
  ArrowLeft,
  Info,
  Layers,
  Plus,
  FileText,
  Settings,
  Save,
  Eye,
  Loader2,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";
import { AssemblyFormProvider } from "@/app/contexts/AssemblyFormContext";
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from "@/app/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/app/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import Header from "@/app/components/layout/Header";

export default function HierarchicalBuilderPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const handleFormSubmit = useCallback(async (data: HierarchicalFormValues) => {
    setIsSubmitting(true);
    setFormError(null); // Reset error at the start
    console.log("Submitting assembly data:", data);

    try {
      const apiUrl = getApiUrl("/api/assemblies");
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        let errorPayload;
        let errorMessageText;
        try {
          errorPayload = await response.json();
          errorMessageText = errorPayload.error || errorPayload.message || `API Error: ${response.status} ${response.statusText}`;
        } catch (e) {
          errorMessageText = `HTTP error! status: ${response.status} ${response.statusText || 'Server error'}`;
          // Log the raw response text if it's not JSON
          const rawText = await response.text().catch(() => "Could not read response text.");
          console.error("API response was not JSON. Raw response:", rawText);
        }
        
        console.error("Failed to save assembly. Status:", response.status, "Message:", errorMessageText, "Payload:", errorPayload);
        toast.error("Failed to save assembly", { description: errorMessageText });
        setFormError(errorMessageText); // Set error for UI display
        return; // Important: stop execution here
      }

      const result = await response.json();
      console.log("Assembly saved successfully:", result);
      toast.success("Assembly created successfully!", {
        description: `Assembly ${result.data?.name || data.name} has been saved.`, // Use data.name as fallback
      });
      router.push("/assemblies");

    } catch (error) { // This catches network errors or if response.json() fails for a 2xx response (unlikely)
      console.error("Network or unexpected error in handleFormSubmit:", error);
      const message = error instanceof Error ? error.message : "An unexpected client-side error occurred.";
      setFormError(message); 
      toast.error("Error creating assembly", { description: message });
    } finally {
      setIsSubmitting(false);
    }
  }, [router]);

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Hierarchical Assembly Builder" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Layers className="text-foreground mr-2" size={24} />
            <h1 className="text-2xl font-semibold text-foreground">Visual Assembly Builder</h1>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/assemblies">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Assemblies
              </Link>
            </Button>

            <Button variant="default" asChild>
              <Link href="/assemblies/create">
                <Plus size={16} className="mr-1" />
                <span>Create Standard Assembly</span>
              </Link>
            </Button>
          </div>
        </div>

        <Tabs defaultValue="builder" className="mb-6">
          <TabsList className="mb-4">
            <TabsTrigger value="builder">
              <Layers className="h-4 w-4 mr-2" />
              Visual Builder
            </TabsTrigger>
            <TabsTrigger value="preview">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="builder">
            <Card>
              <CardHeader>
                <CardTitle>Create Hierarchical Assembly</CardTitle>
                <CardDescription>
                  Build complex assemblies with multi-level hierarchies and nested components using the visual builder.
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <AssemblyFormProvider>
                  <HierarchicalPartsForm mode="create" onFormSubmit={handleFormSubmit} />
                </AssemblyFormProvider>
                {formError && (
                  <Alert variant="destructive" className="mt-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{formError}</AlertDescription>
                  </Alert>
                )}
                {isSubmitting && (
                    <div className="flex items-center justify-center mt-4">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        <span>Saving assembly...</span>
                    </div>
                )}
              </CardContent>
              <CardFooter className="border-t bg-muted/50 p-4">
                <Alert className="bg-blue-50 dark:bg-blue-900/20">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Looking for an alternative?</AlertTitle>
                  <AlertDescription>
                    Try the <Link href="/hierarchical-part-entry" className="font-medium underline">Hierarchical Part Entry</Link> for a structured approach to creating assemblies.
                  </AlertDescription>
                </Alert>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="preview">
            <Card>
              <CardHeader>
                <CardTitle>Assembly Preview</CardTitle>
                <CardDescription>
                  Preview how your assembly will look when completed.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-64 border-2 border-dashed rounded-lg p-6 text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Build your assembly in the Visual Builder tab to see a preview here.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Builder Settings</CardTitle>
                <CardDescription>
                  Configure settings for the visual assembly builder.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Display Options</h3>
                      <Separator />
                      <p className="text-sm text-muted-foreground">
                        Configure how the builder displays components and relationships.
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Validation Settings</h3>
                      <Separator />
                      <p className="text-sm text-muted-foreground">
                        Configure validation rules for assemblies.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button variant="outline" className="mr-2">
                  Reset to Defaults
                </Button>
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}