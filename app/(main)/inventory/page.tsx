'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/app/components/layout/Header';
import PartForm from '@/app/components/forms/PartForm';
import { useAppContext } from '@/app/context/AppContext';
import { Plus, RefreshCw, Search } from 'lucide-react';
import { toast } from 'sonner';
import { useTheme } from '@/app/context/ThemeContext';
import type { PartFormData } from '@/app/components/forms/PartForm';
import { Card, CardContent } from '../../../app/components/ui/card';

import { ShimmerButton, RippleButton, PaginationControls } from '@/app/components/ui/magic-ui';
import { debounce } from '@/app/lib/utils';
import {
  FilterState,
  DEFAULT_FILTER_STATE,
  loadFiltersFromStorage,
  saveFiltersToStorage,
  InventoryFilter,
  DataTableToolbar,
  applyFilters
} from '@/app/components/inventory/filters';

// Import for our InventoryTable component
import { InventoryTable } from '@/app/components/inventory/InventoryTable';

// Define InventoryItem interface matching what InventoryTable expects
interface InventoryItem {
  _id: string;
  id: string;
  partNumber: string;
  name: string;
  description: string;
  inventory: {
    currentStock: number;
    warehouseId?: { _id: string; name: string; location?: string } | string;
    warehouse?: { _id: string; name: string; location?: string } | null;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate: Date | null;
    location?: string;
  };
  supplierId?: { _id: string; name: string } | string | null;
  supplierManufacturer: string;
  supplierManufacturerId?: string | null;
  reorderLevel: number;
  status: string;
  unitOfMeasure: string;
  cost: number;
  costPrice?: number; // Added for compatibility with form
  categoryId?: { _id: string; name: string; description?: string } | string | null;
  categoryName?: string;
  warehouseId?: string | null;
  warehouseName?: string;
  isManufactured: boolean;
  isAssembly?: boolean; // Added for compatibility with form
  schemaVersion?: number; // Added for compatibility with form
  subParts?: Array<{ partId: string; quantity: number }>; // Added for compatibility with form
  technicalSpecs?: string;
  createdAt: string;
  updatedAt: string;
  location?: string;
  notes?: string;
  currentStock?: number;
}

// Refactor all product mapping, usage, and display logic to use only canonical fields: productCode, name, description, categoryId, status, sellingPrice, assemblyId, partId, createdAt, updatedAt. Remove or refactor any logic that uses legacy/alternate fields.
const mapToAppProduct = (apiProduct: any) => {
  if (!apiProduct) { // Guard against null/undefined apiProduct
    console.warn('[FRONTEND DEBUG] mapToAppProduct received null or undefined apiProduct');
    return null;
  }
  // Generate a fallback ID if necessary, though ideally, all products should have an _id from the backend.
  const fallbackId = `fallback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  return {
    ...apiProduct,
    _id: apiProduct._id || apiProduct.id || fallbackId,
    id: apiProduct._id || apiProduct.id || fallbackId,
    // Ensure dates are Date objects if they exist, otherwise keep as is or default
    createdAt: apiProduct.createdAt ? new Date(apiProduct.createdAt) : new Date(),
    updatedAt: apiProduct.updatedAt ? new Date(apiProduct.updatedAt) : new Date(),
    // Ensure cost fields are numbers and handle potential null/undefined from apiProduct
    costPrice: Number(apiProduct.costPrice ?? apiProduct.cost ?? 0),
    cost: Number(apiProduct.cost ?? apiProduct.costPrice ?? 0),
    // Ensure reorderLevel is a number
    reorderLevel: Number(apiProduct.reorderLevel ?? apiProduct.inventory?.safetyStockLevel ?? 0),
    // Ensure currentStock is a number (top-level for easier access if needed, though inventory object is preferred)
    currentStock: Number(apiProduct.inventory?.currentStock ?? apiProduct.currentStock ?? 0),
    // Ensure partNumber is present, falling back to productCode or an empty string
    partNumber: apiProduct.partNumber || apiProduct.productCode || '',
  };
};

const Inventory: React.FC = () => {
  // Using theme context for consistent styling
  const { theme } = useTheme(); // Get current theme for consistent styling
  const router = useRouter(); // Get router for navigation and refresh
  const {
    products: srcProducts,
    isLoading,
    isUsingMockData,
    addProduct,
    updateProduct,
    deleteProduct,
    getProducts,
  } = useAppContext();

  // Define adaptProductsForTable function first, before it's used in useMemo hooks
  // Replace the adaptProductsForTable function with a mapper for our new inventory schema
  const adaptProductsForTable = (products: any[]): InventoryItem[] => {
    console.log('[FRONTEND DEBUG] Adapting products for table display. Count:', products.length);

    return products.map((product, index) => {
      // Handle cases where a product object in the list might itself be undefined or null
      if (!product) {
        console.warn(`[FRONTEND DEBUG] Undefined or null product encountered at index ${index}`);
        return {
          _id: `error-item-${index}`,
          id: `error-item-${index}`,
          partNumber: 'ERROR',
          name: 'Invalid Data',
          description: 'This item could not be processed.',
          inventory: {
            currentStock: 0,
            safetyStockLevel: 0,
            maximumStockLevel: 0,
            averageDailyUsage: 0,
            abcClassification: '',
            lastStockUpdate: null
          },
          supplierManufacturer: 'N/A',
          reorderLevel: 0,
          status: 'error',
          unitOfMeasure: 'pcs',
          cost: 0,
          isManufactured: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        } as InventoryItem;
      }

      console.log(`[FRONTEND DEBUG] Processing product (post-mapToAppProduct): ${product.name} (ID: ${product._id})`);

      // Debug logging for critical fields post-mapToAppProduct
      console.log(`[FRONTEND DEBUG] Product partNumber: ${product.partNumber}`);
      console.log(`[FRONTEND DEBUG] Product currentStock (top-level): ${product.currentStock}`);
      console.log(`[FRONTEND DEBUG] Product inventory object:`, product.inventory);
      console.log(`[FRONTEND DEBUG] Product supplier object:`, product.supplier);
      console.log(`[FRONTEND DEBUG] Product supplierId:`, product.supplierId);
      console.log(`[FRONTEND DEBUG] Product supplierName: ${product.supplierName}`);
      console.log(`[FRONTEND DEBUG] Product reorderLevel: ${product.reorderLevel}`);

      const idStringForKeyAndDisplay = String(product._id); // mapToAppProduct ensures _id exists
      const originalName = product.name || 'N/A';

      // Part number is now reliably set by mapToAppProduct
      const finalDisplayId = product.partNumber || idStringForKeyAndDisplay;
      console.log(`[FRONTEND DEBUG] Final part number for ${originalName}: ${finalDisplayId}`);

      // Current stock is now reliably set by mapToAppProduct at the top level and within inventory if present
      const stockValue = Number(product.currentStock || 0);
      console.log(`[FRONTEND DEBUG] Stock value for ${originalName}: ${stockValue}`);

      // Location: Prefer inventory.location, then top-level product.location
      const locationValue = product.inventory?.location || product.location || '';

      // Supplier Data: Simplified due to mapToAppProduct passing through supplier object
      let supplierData = {
        _id: '',
        name: 'N/A'
      };
      if (product.supplier && typeof product.supplier === 'object' && product.supplier.name) {
        supplierData = {
          _id: product.supplier._id || '',
          name: product.supplier.name
        };
      } else if (product.supplierId && typeof product.supplierId === 'object' && product.supplierId.name) {
        // This case handles if supplierId is an object with name (e.g. populated from DB)
        supplierData = {
          _id: product.supplierId._id || '',
          name: product.supplierId.name
        };
      } else if (typeof product.supplierId === 'string' && product.supplierName) {
        // This case handles if supplierId is a string and supplierName is present
        supplierData = {
          _id: product.supplierId,
          name: product.supplierName
        };
      } else if (product.supplierName) {
        supplierData.name = product.supplierName;
      } else if (product.supplierManufacturer) {
        supplierData.name = product.supplierManufacturer;
      }
      console.log(`[FRONTEND DEBUG] Final supplier data for ${originalName}:`, supplierData);

      // Category Data: Simplified, mapToAppProduct passes category/categoryId
      let categoryData: { _id: string; name: string; description?: string } = {
        _id: '',
        name: 'N/A'
      };
      if (product.category && typeof product.category === 'object' && product.category.name) {
        categoryData = {
          _id: product.category._id || '',
          name: product.category.name,
          description: product.category.description || ''
        };
      } else if (product.categoryId && typeof product.categoryId === 'object' && product.categoryId.name) {
        categoryData = {
          _id: product.categoryId._id || '',
          name: product.categoryId.name,
          description: product.categoryId.description || ''
        };
      } else if (product.categoryName) {
        categoryData.name = product.categoryName;
        if (typeof product.categoryId === 'string') categoryData._id = product.categoryId;
      }
      console.log(`[FRONTEND DEBUG] Final category data for ${originalName}:`, categoryData);

      // Inventory Data: mapToAppProduct passes product.inventory. Ensure currentStock is consistent.
      let inventoryData = product.inventory || {}; // Start with existing inventory object or an empty one
      // Ensure currentStock in inventoryData matches the reliable top-level stockValue
      inventoryData.currentStock = stockValue;
      // Ensure other fields are present or defaulted if not in product.inventory
      inventoryData.safetyStockLevel = inventoryData.safetyStockLevel ?? Number(product.reorderLevel ?? 0);
      inventoryData.maximumStockLevel = inventoryData.maximumStockLevel ?? 0;
      inventoryData.averageDailyUsage = inventoryData.averageDailyUsage ?? 0;
      inventoryData.abcClassification = inventoryData.abcClassification ?? '';
      inventoryData.lastStockUpdate = inventoryData.lastStockUpdate ?? null;
      // Preserve warehouseId and warehouse if they exist from mapToAppProduct's pass-through
      if (product.inventory?.warehouseId) inventoryData.warehouseId = product.inventory.warehouseId;
      if (product.inventory?.warehouse) inventoryData.warehouse = product.inventory.warehouse;

      console.log(`[FRONTEND DEBUG] Final inventory data for ${originalName}:`, inventoryData);

      const displayItem: InventoryItem = {
        _id: idStringForKeyAndDisplay,
        id: idStringForKeyAndDisplay,
        partNumber: finalDisplayId,
        name: originalName,
        description: product.description || '',
        inventory: inventoryData, // Use the refined inventoryData
        supplierManufacturer: supplierData.name, // Directly use name from refined supplierData
        supplierId: supplierData, // Pass the whole supplierData object
        reorderLevel: Number(product.reorderLevel || 0), // mapToAppProduct ensures this is a number
        status: product.status || 'active',
        unitOfMeasure: product.unitOfMeasure || 'pcs',
        cost: Number(product.costPrice || product.cost || 0), // mapToAppProduct ensures this is a number
        isManufactured: Boolean(product.isManufactured),
        technicalSpecs: product.technicalSpecs || '',
        categoryId: categoryData, // Pass the whole categoryData object
        categoryName: categoryData.name,
        createdAt: product.createdAt ? new Date(product.createdAt).toISOString() : new Date().toISOString(), // Ensure ISO string
        updatedAt: product.updatedAt ? new Date(product.updatedAt).toISOString() : new Date().toISOString(), // Ensure ISO string
        location: locationValue,
        currentStock: stockValue // Ensure top-level currentStock for direct access if needed by table
      };

      console.log(`[FRONTEND DEBUG] Processed item for ${originalName}:`, {
        partNumber: displayItem.partNumber,
        name: displayItem.name,
        supplier: displayItem.supplierId,
        supplierManufacturer: displayItem.supplierManufacturer,
        currentStock: displayItem.inventory.currentStock,
        reorderLevel: displayItem.reorderLevel
      });

      return displayItem;
    });
  };

  // products derived from srcProducts, mapping them or returning an empty array
  const products = useMemo(() => {
    // If we have real products, map them normally
    if (srcProducts && srcProducts.length > 0) {
      return srcProducts.map(mapToAppProduct).filter(p => p !== null) as InventoryItem[];
    }
    // Otherwise, return an empty array
    console.log("[DEBUG] No srcProducts found or empty, returning empty array for products");
    return [];
  }, [srcProducts]);

  // State for showing/hiding the filter panel and modal
  const [showFilters, setShowFilters] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedPart, setSelectedPart] = useState<InventoryItem | null>(null);

  // Advanced filtering state
  const [filterState, setFilterState] = useState<FilterState>(DEFAULT_FILTER_STATE);
  const [searchQuery, setSearchQuery] = useState('');

  // Server-side pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage] = useState(20); // Fixed number of products per page
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [serverParts, setServerParts] = useState<any[]>([]);
  // Removed isServerSearching state as presence of searchQuery indicates server search
  // const [isServerSearching, setIsServerSearching] = useState(false);

  // Server-side search and pagination
  // const fetchServerParts = async (page: number, query: string = '') => {
  async function fetchServerParts(page: number, query: string = '') {
    try {
      // No need for isServerSearching state, presence of searchQuery indicates server search
      // setIsServerSearching(true); // Removed

      // Use getProducts from context instead of direct fetch
      const result = await getProducts({
        page,
        limit: productsPerPage,
        search: query
      });

      // The server parts are used directly in the UI, so we can keep them as Products
      setServerParts(result.products || []);
      setTotalItems(result.pagination?.total || 0);
      setTotalPages(result.pagination?.pages || 1);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error searching parts:', error);
      toast.error('Failed to search parts');
      // setIsServerSearching(false); // Removed
      setServerParts([]); // Clear results on error
      setTotalItems(0);
      setTotalPages(1);
      setCurrentPage(1);
    }
  }
  // }; // End of original const arrow function

  // Debounced version of fetchServerParts
  const debouncedFetchServerParts = useMemo(() => debounce(fetchServerParts, 500), []); // 500ms delay

  // Extract unique values for filter dropdowns
  const uniqueSuppliers = useMemo(() => {
    // Extract supplier names from the adapted products
    const adaptedProducts = adaptProductsForTable(products);
    const supplierNames = adaptedProducts.map((p: InventoryItem) => p.supplierManufacturer).filter(Boolean);
    // Use a regular array filter to get unique values
    return Array.from(new Set(supplierNames)).sort();
  }, [products]);

  const uniqueCategories = useMemo(() => {
    // Create a map of category IDs to names from adapted products
    const categoryMap = new Map<string, string>();
    const adaptedProducts = adaptProductsForTable(products);

    adaptedProducts.forEach((product: InventoryItem) => {
      if (product.categoryId && product.categoryName) {
        const categoryId = typeof product.categoryId === 'object' ? product.categoryId._id : product.categoryId;
        if (!categoryMap.has(categoryId)) {
          categoryMap.set(categoryId, product.categoryName);
        }
      }
    });

    return Array.from(categoryMap, ([id, name]) => ({ id, name })).sort((a, b) => a.name.localeCompare(b.name));
  }, [products]);

  // Load filters from localStorage on mount
  useEffect(() => {
    const savedFilters = loadFiltersFromStorage();
    if (savedFilters) {
      setFilterState(savedFilters);
    }
  }, []);

  // Fetch inventory data from the database when component mounts
  useEffect(() => {
    // Fetch initial data when component mounts using getProducts from context
    // Ensure getProducts is defined before calling it
    if (getProducts) {
      getProducts({ page: currentPage, limit: productsPerPage });
      console.log('[DEBUG] Initial data fetch triggered using getProducts');
    } else {
      console.warn('[DEBUG] getProducts function is not yet available on mount');
    }
  }, [getProducts, currentPage, productsPerPage]); // Add dependencies

  // Apply filters to products (client-side filtering)
  const filteredProducts = useMemo(() => {
    // If a search query is active, the data comes from serverParts
    if (searchQuery && serverParts && serverParts.length > 0) {
      return serverParts.map(mapToAppProduct).filter(p => p !== null) as InventoryItem[];
    }
    if (searchQuery) { // Search query active but no serverParts or empty
        console.log("[DEBUG] Search query active, but no serverParts or empty, returning empty array for filteredProducts");
        return [];
    }

    // Apply the advanced filters to the products (which are already mapped and filtered for nulls)
    return applyFilters(products, filterState);
  }, [products, filterState, searchQuery, serverParts]);

  // Client-side pagination (still needed for non-search filtering)
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const clientTotalPages = Math.ceil(filteredProducts.length / productsPerPage);

  // Determine the data source for the table and pagination
  // If searchQuery is true, filteredProducts contains the mapped current page of server results.
  // If searchQuery is false, filteredProducts contains all client-side filtered items, so we slice for pagination.
  const displayProducts = searchQuery ? filteredProducts : filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  const displayTotalItems = searchQuery ? totalItems : filteredProducts.length;
  const displayTotalPages = searchQuery ? totalPages : clientTotalPages;


  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    // Always use fetchServerParts if there's a search query,
    // otherwise rely on client-side pagination (which is handled by slice below)
    if (searchQuery) {
      fetchServerParts(pageNumber, searchQuery);
    }
    // Client-side pagination updates automatically via slice when currentPage changes
  };

  const resetFilters = () => {
    // Reset to default filter state
    setFilterState({ ...DEFAULT_FILTER_STATE });
    setSearchQuery('');
    // When clearing search, reset to page 1 and fetch initial data
    setCurrentPage(1);
    if (getProducts) {
      getProducts({ page: 1, limit: productsPerPage }); // Fetch initial paginated data using context
    }
    // Save the reset state to localStorage
    saveFiltersToStorage(DEFAULT_FILTER_STATE);
  };

  // Restore the original implementation of handleSearchSubmit
  const handleSearchSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // The debounced function handles the actual fetching on input change
    // This submit handler can optionally trigger an immediate search if needed,
    // but with debouncing on change, it might be redundant.
    // Let's keep it for explicit search button click.
    if (searchQuery.trim()) {
      await fetchServerParts(1, searchQuery);
    } else {
      // If search query is empty on submit, reset to initial state
      resetFilters();
    }
  };




  // Effect to trigger search when searchQuery changes (debounced)
  useEffect(() => {
    if (searchQuery) {
      // Trigger the debounced search when the query changes
      debouncedFetchServerParts(1, searchQuery);
    } else {
      // If search query is cleared, reset to initial state
      resetFilters();
    }
    // Cleanup the debounced function on unmount or when searchQuery/debouncedFetchServerParts changes
    // Removed .cancel() call as simple debounce doesn't have it
    // return () => {
    //   debouncedFetchServerParts.cancel();
    // };
  }, [searchQuery, debouncedFetchServerParts]); // Depend on searchQuery and the debounced function


  // CRUD Operations
  const handleAddPart = async (formData: PartFormData) => {
    try {
      console.log('[FRONTEND DEBUG] Adding part with form data:', formData);

      // The form data is already in camelCase format which matches MongoDB schema
      // So we can pass it directly, perhaps with some defaults for missing fields
      const payload = {
        name: formData.name,
        description: formData.description || '',
        technicalSpecs: formData.technicalSpecs || '',
        isManufactured: formData.isManufactured,
        partNumber: formData.partNumber || generatePartNumber(), // Ensure partNumber is included
        inventory: {
          currentStock: formData.inventory.currentStock,
          location: formData.inventory.location || '', // Pass location through inventory object
          safetyStockLevel: formData.inventory.safetyStockLevel,
          maximumStockLevel: formData.inventory.maximumStockLevel,
          averageDailyUsage: formData.inventory.averageDailyUsage,
          abcClassification: formData.inventory.abcClassification,
          warehouseId: formData.inventory.warehouseId || '',
          lastStockUpdate: formData.inventory.lastStockUpdate || new Date()
        },
        reorderLevel: formData.reorderLevel !== null ? formData.reorderLevel : undefined,
        status: formData.status,
        supplierId: formData.supplierId,
        unitOfMeasure: formData.unitOfMeasure,
        costPrice: formData.costPrice,
        categoryId: formData.categoryId,
        isAssembly: formData.isAssembly,
        schemaVersion: formData.schemaVersion || 1
      };

      // Use type assertion to bypass TypeScript errors due to mismatch with Product type
      await addProduct(payload as any);

      toast.success('Part added successfully');
      setShowAddForm(false);

      // Force an immediate refresh of the data
      if (searchQuery) {
        // If searching, refetch the current page of search results
        console.log(`[FRONTEND DEBUG] Refreshing search results after add`);
        await fetchServerParts(currentPage, searchQuery);
      } else {
        // If not searching, refetch the current page of initial data
        console.log(`[FRONTEND DEBUG] Refreshing parts list after add`);
        await getProducts({ page: currentPage, limit: productsPerPage });
      }

      // Force a router refresh to ensure UI is updated
      router.refresh();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to add part: ${errorMessage}`);
      console.error('Error adding part:', error);
    }
  };

  // Helper function to generate a part number if not provided
  const generatePartNumber = () => {
    const prefix = 'P';
    const timestamp = Date.now().toString().slice(-6);
    return `${prefix}${timestamp}`;
  }

  /**
   * Handles editing an existing part
   */
  const handleEditPart = async (partData: PartFormData) => {
    try {
      console.log('[FRONTEND DEBUG] Editing part with form data:', partData);

      // The form data is already in camelCase format which matches MongoDB schema
      const payload = {
        name: partData.name,
        description: partData.description || '',
        technicalSpecs: partData.technicalSpecs || '',
        isManufactured: partData.isManufactured,
        partNumber: partData.partNumber,
        inventory: {
          currentStock: partData.inventory.currentStock || 0,
          location: partData.inventory.location || '',
          safetyStockLevel: partData.inventory.safetyStockLevel || 0,
          maximumStockLevel: partData.inventory.maximumStockLevel || 0,
          averageDailyUsage: partData.inventory.averageDailyUsage || 0,
          abcClassification: partData.inventory.abcClassification || 'C',
          lastStockUpdate: partData.inventory.lastStockUpdate || new Date(),
          warehouseId: partData.inventory.warehouseId || ''
        },
        reorderLevel: partData.reorderLevel,
        status: partData.status,
        supplierId: partData.supplierId,
        unitOfMeasure: partData.unitOfMeasure,
        costPrice: partData.costPrice,
        categoryId: partData.categoryId,
        isAssembly: partData.isAssembly,
        schemaVersion: partData.schemaVersion || 1,
        subParts: partData.subParts
          ? partData.subParts.map(part => ({
              partId: part.partId || '',
              quantity: part.quantity
            }))
          : []
      };

      // Use type assertion to bypass TypeScript errors due to mismatch with Product type
      await updateProduct(partData._id, payload as any);

      toast.success('Part updated successfully');
      setShowEditForm(false);
      setSelectedPart(null);

      // Force an immediate refresh of the data
      if (searchQuery) {
        // If searching, refetch the current page of search results
        console.log(`[FRONTEND DEBUG] Refreshing search results after edit`);
        await fetchServerParts(currentPage, searchQuery);
      } else {
        // If not searching, refetch the current page of initial data
        console.log(`[FRONTEND DEBUG] Refreshing parts list after edit`);
        await getProducts({ page: currentPage, limit: productsPerPage });
      }

      // Force a router refresh to ensure UI is updated
      router.refresh();

    } catch (error) {
      console.error('Error updating part:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to update part: ${errorMessage}`);
    }
  };

  const handleDeletePart = async (id: string): Promise<void> => {
    try {
      console.log(`[FRONTEND DEBUG] Attempting to delete part with ID: ${id}`);

      // Call the deleteProduct function from context
      await deleteProduct(id);

      toast.success('Part deleted successfully');

      // Force an immediate refresh of the data
      if (searchQuery) {
        // If searching, refetch the current page of search results
        console.log(`[FRONTEND DEBUG] Refreshing search results after delete`);
        await fetchServerParts(currentPage, searchQuery);
      } else {
        // If not searching, refetch the current page of initial data
        console.log(`[FRONTEND DEBUG] Refreshing parts list after delete`);
        await getProducts({ page: currentPage, limit: productsPerPage });
      }

      // Force a router refresh to ensure UI is updated
      router.refresh();

      // No return value needed for Promise<void>
    } catch (error) {
      // Log the detailed error for debugging
      console.error('[FRONTEND DEBUG] Error deleting part:', error);

      // Extract the error message
      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;

        // Check for specific error messages
        if (error.message.includes('not found')) {
          errorMessage = `Part not found. It may have been already deleted or never existed.`;
        }
      }

      // Show error toast with the extracted message
      toast.error(`Failed to delete part: ${errorMessage}`);

      // Re-throw to allow caller to handle, but don't return a value
      throw error;
    }
  };



  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Inventory" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            {/* Filter Toolbar with Badge */}
            <DataTableToolbar
              filters={filterState}
              showFilters={showFilters}
              onToggleFilters={() => setShowFilters(!showFilters)}
              onResetFilters={resetFilters}
            />

            {/* Add Part */}
            <ShimmerButton
              className="flex items-center rounded-full"
              shimmerColor={theme === 'dark' ? "#4B5563" : "#E5E7EB"}
              background={theme === 'dark' ? "rgba(18, 116, 243, 0.8)" : "rgba(59, 130, 246, 0.8)"}
              onClick={() => setShowAddForm(true)}
            >
              <Plus size={16} className="mr-2" />
              <span>Add Part</span>
            </ShimmerButton>

            {/* Enhanced Search Bar */}
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className="flex items-center">
                <div className="relative group">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      // Trigger debounced search on change
                      debouncedFetchServerParts(1, e.target.value);
                    }}
                    placeholder="Search across all inventory..."
                    className="w-64 px-4 py-2 pr-10 rounded-full border border-input dark:border-dark-border bg-background dark:bg-dark-element/60 text-foreground dark:text-dark-text-primary focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-primary-blue/50 transition-all duration-300 shadow-sm hover:shadow-md"
                  />
                  <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-300 bg-gradient-to-r from-transparent via-primary/5 dark:via-primary-blue/5 to-transparent"></div>
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground dark:text-dark-text-secondary/80 hover:text-foreground dark:hover:text-dark-text-primary transition-colors"
                  >
                    <Search size={16} className="transition-transform duration-300 group-hover:scale-110" />
                  </button>
                </div>
              </div>
            </form>
          </div>

          <div className="flex items-center">
            {/* Data Source Label */}
            {isUsingMockData ? (
              <div className="text-orange-500 mr-4 text-sm bg-orange-100/80 dark:bg-orange-900/30 dark:text-orange-400 px-3 py-1 rounded-full border border-orange-200 dark:border-orange-800/50 transition-colors">
                Using Sample Data
              </div>
            ) : (
              <div className="text-green-500 mr-4 text-sm bg-green-100/80 dark:bg-green-900/30 dark:text-green-400 px-3 py-1 rounded-full border border-green-200 dark:border-green-800/50 transition-colors">
                Using MongoDB Data
              </div>
            )}

            {/* Refresh Button */}
            <RippleButton
              className="p-2 rounded-full bg-secondary dark:bg-dark-element hover:bg-secondary/80 dark:hover:bg-dark-hover text-secondary-foreground dark:text-dark-text-primary"
              onClick={() => {
                // When refreshing, refetch based on current search state
                if (searchQuery) {
                  fetchServerParts(currentPage, searchQuery);
                } else {
                  getProducts({ page: currentPage, limit: productsPerPage });
                }
              }}
              disabled={isLoading}
              rippleColor={theme === 'dark' ? "#4B5563" : "#E5E7EB"}
            >
              <RefreshCw
                size={16}
                className={isLoading ? 'animate-spin' : ''}
              />
            </RippleButton>
          </div>
        </div>

        {/* Search Status */}
        {searchQuery && ( // Show status when searchQuery is active
          <div className="mb-4 p-3 bg-primary/5 dark:bg-primary-blue/5 border border-primary/10 dark:border-primary-blue/10 rounded-lg text-sm transition-all duration-300 shadow-sm hover:shadow-md">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Search size={14} className="mr-2 text-primary dark:text-primary-blue" />
                <span className="text-foreground dark:text-dark-text-primary">
                  Showing search results for "<span className="font-medium text-primary dark:text-primary-blue">{searchQuery}</span>" across all inventory
                </span>
              </div>
              <RippleButton
                onClick={resetFilters}
                className="text-primary dark:text-primary-blue hover:bg-primary/10 dark:hover:bg-primary-blue/10 px-3 py-1 text-sm rounded-md border border-primary/20 dark:border-primary-blue/20"
                rippleColor={theme === 'dark' ? "rgba(18, 116, 243, 0.2)" : "rgba(59, 130, 246, 0.2)"}
              >
                Clear search
              </RippleButton>
            </div>
          </div>
        )}

        {/* Advanced Filter Panel */}
        {showFilters && (
          <InventoryFilter
            filters={filterState}
            onFiltersChange={setFilterState}
            onReset={resetFilters}
            suppliers={uniqueSuppliers}
            categories={uniqueCategories}
            locations={[]}
          />
        )}

        {/* Current Inventory Table within a Card */}
        <Card className="border border-input/20 dark:border-dark-border/20 shadow-md hover:shadow-lg transition-shadow duration-300">
          <CardContent className="p-0"> { /* Remove padding from card content to let table use full width */}
            <InventoryTable
              inventoryItems={adaptProductsForTable(displayProducts)}
              onView={(item) => {
                // Use the item directly since it's already properly formatted
                // Use type assertion to resolve TypeScript error
                setSelectedPart(item as any);
                setShowEditForm(true);
              }}
              onEdit={(item) => {
                // Use the item directly since it's already properly formatted
                // Use type assertion to resolve TypeScript error
                setSelectedPart(item as any);
                setShowEditForm(true);
              }}
              onDelete={(item) => handleDeletePart(item._id)}
            />
          </CardContent>
        </Card>

        {/* Pagination Controls */}
        {(displayTotalItems > 0) && ( // Show pagination if there are items to display
          <div className="mt-6">
            <PaginationControls
              currentPage={currentPage}
              totalPages={displayTotalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}

        {/* Display message when no items are found */}
        {displayTotalItems === 0 && (
          <div className="text-center text-sm text-muted-foreground dark:text-dark-text-secondary mt-3 transition-colors">
            <div className="bg-secondary/10 dark:bg-dark-element/20 inline-block px-4 py-2 rounded-full shadow-sm">
              No items found
            </div>
          </div>
        )}
      </div>

      {/* Add Part Modal */}
      {showAddForm && (
        <PartForm
          onSubmit={handleAddPart}
          onClose={() => setShowAddForm(false)}
        />
      )}

      {/* Edit Part Modal */}
      {showEditForm && selectedPart && (
        <PartForm
          initialData={{
            _id: selectedPart.id,
            name: selectedPart.name,
            description: selectedPart.description || '',
            technicalSpecs: selectedPart.technicalSpecs || '',
            isManufactured: selectedPart.isManufactured || false,
            partNumber: selectedPart.partNumber || '',
            reorderLevel: selectedPart.reorderLevel || 0,
            status: (selectedPart.status as 'active' | 'inactive' | 'obsolete') || 'active',
            inventory: {
              currentStock: selectedPart.inventory?.currentStock || 0,
              location: selectedPart.inventory?.location || selectedPart.location || '',
              lastStockUpdate: selectedPart.inventory?.lastStockUpdate ? new Date(selectedPart.inventory.lastStockUpdate) : null,
              warehouseId: '',
              safetyStockLevel: selectedPart.inventory?.safetyStockLevel || 0,
              maximumStockLevel: selectedPart.inventory?.maximumStockLevel || 0,
              averageDailyUsage: selectedPart.inventory?.averageDailyUsage || 0,
              abcClassification: (selectedPart.inventory?.abcClassification || 'C') as 'A' | 'B' | 'C'
            },
            supplierId: (typeof selectedPart.supplierId === 'object' && selectedPart.supplierId !== null ? selectedPart.supplierId._id : selectedPart.supplierId) || '',
            unitOfMeasure: selectedPart.unitOfMeasure || 'pcs',
            costPrice: selectedPart.costPrice || selectedPart.cost || 0,
            categoryId: typeof selectedPart.categoryId === 'object' && selectedPart.categoryId !== null
              ? selectedPart.categoryId._id
              : (selectedPart.categoryId || ''),
            isAssembly: selectedPart.isAssembly || false,
            schemaVersion: selectedPart.schemaVersion || 1,
            subParts: selectedPart.subParts
              ? selectedPart.subParts.map(part => ({
                  partId: part.partId || '',
                  quantity: part.quantity
                }))
              : []
          }}
          onSubmit={handleEditPart}
          onClose={() => {
            setShowEditForm(false);
            setSelectedPart(null);
          }}
          isEdit={true}
          title="Edit Part"
        />
      )}
    </div>
  );
};

export default Inventory;
