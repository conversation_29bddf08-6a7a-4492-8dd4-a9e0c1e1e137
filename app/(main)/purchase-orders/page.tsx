"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { motion } from 'framer-motion';
import {
  ShoppingCart,
  Plus,
  Search,
  Edit,
  Trash2,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';
// MongoDB service is used via API endpoints
import { PurchaseOrder, Supplier, POLineItem, Product } from '@/app/types';
import { useTheme } from '@/app/context/ThemeContext';
import Header from '@/app/components/layout/Header';
import { useSearchParams } from 'next/navigation';
import { Button } from "@/app/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";

// Wrapper component for the search params usage
const PurchaseOrdersContent = () => {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPO, setCurrentPO] = useState<PurchaseOrder | null>(null);
  const [lineItems, setLineItems] = useState<POLineItem[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const { theme } = useTheme();
  const searchParams = useSearchParams();

  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<PurchaseOrder>();

  useEffect(() => {
    fetchPurchaseOrders();
    fetchSuppliers();
    fetchProducts();

    // Check if we should open the create form based on URL params
    const mode = searchParams?.get('mode');
    if (mode === 'create') {
      openModal();
    }
  }, [searchParams]);

  const fetchPurchaseOrders = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/purchase-orders');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch purchase orders');
      }

      setPurchaseOrders(result.data || []);
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch purchase orders');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/suppliers');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch suppliers');
      }

      setSuppliers(result.data || []);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/parts');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch parts');
      }

      setProducts(result.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const openModal = (po: PurchaseOrder | null = null) => {
    setCurrentPO(po);
    if (po) {
      // Set form values for editing
      Object.entries(po).forEach(([key, value]) => {
        if (key !== 'supplier' && key !== 'lineItems') {
          setValue(key as any, value);
        }
      });
      setLineItems(po.lineItems || []);
    } else {
      // Reset form for new PO
      reset({
        po_id: `PO-${Date.now()}`,
        supplier_id: 0,
        order_date: format(new Date(), 'yyyy-MM-dd'),
        status: 'draft',
        total_amount: 0,
        payment_status: 'unpaid'
      });
      setLineItems([]);
    }
    setIsModalOpen(true);
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Purchase Orders" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium text-foreground">Purchase Orders</h2>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button variant="default" onClick={() => openModal()}>
              <Plus size={18} className="mr-1" />
              <span>New Purchase Order</span>
            </Button>
          </motion.div>
        </div>

        {/* Error message display */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* We'll implement the rest of the UI in the next steps */}
        <div className="bg-card rounded-xl shadow-md p-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-muted-foreground">Loading purchase orders...</span>
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">
                Purchase Orders page is being migrated. Please check back soon.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Main component with Suspense boundary
const PurchaseOrders = () => {
  return (
    <Suspense fallback={
      <div className="flex-1 overflow-y-auto bg-background text-foreground">
        <Header title="Purchase Orders" />
        <div className="px-8 pb-8">
          <div className="flex justify-center items-center h-64">
            <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-muted-foreground">Loading purchase orders...</span>
          </div>
        </div>
      </div>
    }>
      <PurchaseOrdersContent />
    </Suspense>
  );
};

export default PurchaseOrders;