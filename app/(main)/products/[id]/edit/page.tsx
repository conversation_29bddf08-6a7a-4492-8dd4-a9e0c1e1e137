import { notFound } from "next/navigation";
import { ProductForm } from "@/app/components/forms/ProductForm";

async function getProduct(id: string) {
  try {
    // Get the full URL from environment variables or construct it
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Fetch the product data
    const res = await fetch(`${baseUrl}/api/products/${id}`, {
      cache: 'no-store', // Don't cache this data
      next: { tags: [`product-${id}`] } // Tag for revalidation
    });

    if (!res.ok) {
      console.error(`Error fetching product ${id}: ${res.status}`);
      return null;
    }

    const data = await res.json();
    return data.data;
  } catch (error) {
    console.error(`Error fetching product ${id}:`, error);
    return null;
  }
}

export default async function EditProductPage({ params }: { params: Promise<{ id: string }> }) {
  // Since this is an async server component, we can await the params
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="container py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
        <p className="text-muted-foreground mt-1">
          Update product details for {product.name} ({product.product_id})
        </p>
      </div>
      <ProductForm
        mode="edit"
        initialData={product}
      />
    </div>
  );
}