import { notFound } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/app/components/ui/card";
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Bom<PERSON>iewer } from "@/app/components/features/BomViewer";
import { PencilIcon, ArrowLeft, ListIcon } from "lucide-react";
import Link from "next/link";

async function getProduct(id: string) {
  try {
    // Get the full URL from environment variables or construct it
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Fetch the product data
    const res = await fetch(`${baseUrl}/api/products/${id}`, {
      cache: 'no-store', // Don't cache this data
      next: { tags: [`product-${id}`] } // Tag for revalidation
    });

    if (!res.ok) {
      console.error(`Error fetching product ${id}: ${res.status}`);
      return null;
    }

    const data = await res.json();
    return data.data;
  } catch (error) {
    console.error(`Error fetching product ${id}:`, error);
    return null;
  }
}

export default async function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // Since this is an async server component, we can await the params
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="container py-8 space-y-6">
      {/* Back button and actions */}
      <div className="flex justify-between items-center">
        <Link href="/products" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Products
        </Link>
        <div className="flex gap-2">
          <Link href={`/products/${id}/edit`}>
            <Button variant="outline" size="sm">
              <PencilIcon className="h-4 w-4 mr-2" /> Edit Product
            </Button>
          </Link>
        </div>
      </div>

      {/* Product header */}
      <div className="flex flex-col space-y-2">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold">{product.name}</h1>
          <Badge variant="outline" className="text-sm font-medium">
            {product.product_id}
          </Badge>
        </div>
        {product.description && (
          <p className="text-muted-foreground">{product.description}</p>
        )}
      </div>

      {/* Product overview card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ListIcon className="h-5 w-5" />
            Product Information
          </CardTitle>
          <CardDescription>
            Details about the product and its main assembly
          </CardDescription>
        </CardHeader>
        <CardContent className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Main Assembly</h3>
            <p className="text-lg">
              {product.main_assembly_id?.name} ({product.main_assembly_id?.part_id})
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Component Count</h3>
            <p className="text-lg">{product.components?.length || 0} components</p>
          </div>
        </CardContent>
      </Card>

      {/* Bill of Materials */}
      <Card>
        <CardHeader>
          <CardTitle>Bill of Materials</CardTitle>
          <CardDescription>
            Hierarchical breakdown of all components in this product
          </CardDescription>
        </CardHeader>
        <CardContent>
          <BomViewer components={product.components || []} initiallyExpanded={true} />
        </CardContent>
      </Card>
    </div>
  );
}