import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { PlusIcon, PackageIcon } from "lucide-react";
import { ProductsTable } from "@/app/components/tables/ProductsTable";

async function getProducts() {
  try {
    // Get the full URL from environment variables or construct it
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

    // Fetch products
    const res = await fetch(`${baseUrl}/api/products`, {
      cache: 'no-store', // Don't cache this data
    });

    if (!res.ok) {
      console.error(`Error fetching products: ${res.status}`);
      return [];
    }

    const data = await res.json();
    return data.data || [];
  } catch (error) {
    console.error(`Error fetching products:`, error);
    return [];
  }
}

export default async function ProductsPage() {
  const products = await getProducts();

  return (
    <div className="container py-8 space-y-6">
      {/* Header with action button */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Products</h1>
          <p className="text-muted-foreground mt-1">
            Manage your product catalog and bill of materials
          </p>
        </div>
        <Link href="/products/new">
          <Button>
            <PlusIcon className="h-4 w-4 mr-2" /> New Product
          </Button>
        </Link>
      </div>

      {/* Products List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PackageIcon className="h-5 w-5" />
            Product Catalog
          </CardTitle>
          <CardDescription>
            View and manage all products in your system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {products.length > 0 ? (
            <ProductsTable products={products} />
          ) : (
            <div className="py-12 text-center">
              <PackageIcon className="h-12 w-12 mx-auto text-muted-foreground/50" />
              <h3 className="mt-4 text-lg font-medium">No products found</h3>
              <p className="mt-2 text-muted-foreground">
                Get started by creating your first product
              </p>
              <div className="mt-6">
                <Link href="/products/new">
                  <Button>
                    <PlusIcon className="h-4 w-4 mr-2" /> Create Product
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}