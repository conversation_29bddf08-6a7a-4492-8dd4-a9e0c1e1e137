"use client";

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Trash,
  Download,
  Share,
  Package,
  Truck,
  Clock,
  AlertTriangle,
  ChevronDown,
  Clipboard,
  BarChart2,
  ListChecks
} from 'lucide-react';
import Link from 'next/link';
// import { supabase } from '../../../../app/lib/supabase';
// Using MongoDB instead of Supabase
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import Header from '@/app/components/layout/Header';

// Types
interface Product {
  id: string;
  name: string;
  sku: string;
  description: string;
  category: string;
  price: number;
  cost: number;
  stock_quantity: number;
  reorder_level: number;
  supplier_id: string;
  created_at: string;
  updated_at: string;
  image_url?: string;
  unit: string;
  status: 'active' | 'discontinued' | 'out_of_stock';
  location: string;
  tags: string[];
}

interface Supplier {
  _id: string;
  supplier_id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  specialty: string[];
  rating: number | null;
  payment_terms: string | null;
  delivery_terms: string | null;
  is_active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface TransactionHistory {
  id: string;
  type: 'purchase' | 'sale' | 'adjustment';
  quantity: number;
  date: string;
  reference: string;
}

const ProductDetail = () => {
  const params = useParams();
  const productId = params?.id as string || '';

  const [product, setProduct] = useState<Product | null>(null);
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [transactionHistory, setTransactionHistory] = useState<TransactionHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    const fetchProductDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch product data
        const { data: productData, error: productError } = await supabase
          .from('products')
          .select('*')
          .eq('id', productId)
          .single();

        if (productError) throw new Error('Error fetching product data');
        if (!productData) throw new Error('Product not found');

        setProduct(productData);

        // Fetch supplier data if supplier_id exists
        if (productData.supplier_id) {
          const { data: supplierData, error: supplierError } = await supabase
            .from('suppliers')
            .select('*')
            .eq('id', productData.supplier_id)
            .single();

          if (!supplierError && supplierData) {
            setSupplier(supplierData);
          }
        }

        // Fetch transaction history
        const { data: transactionData, error: transactionError } = await supabase
          .from('inventory_transactions')
          .select('*')
          .eq('product_id', productId)
          .order('created_at', { ascending: false })
          .limit(10);

        if (!transactionError && transactionData) {
          setTransactionHistory(transactionData);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (productId) {
      fetchProductDetails();
    }
  }, [productId]);

  const handleDelete = async () => {
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) throw new Error(error.message);

      toast.success('Product deleted successfully');
      // Redirect to products list
      window.location.href = '/inventory';
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete product';
      toast.error(errorMessage);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'discontinued':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      case 'out_of_stock':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStockLevel = () => {
    if (!product) return 'unknown';
    if (product.stock_quantity <= 0) return 'out_of_stock';
    if (product.stock_quantity <= product.reorder_level) return 'low';
    return 'good';
  };

  const getStockLevelColor = () => {
    const level = getStockLevel();
    switch (level) {
      case 'good':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'low':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'out_of_stock':
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="inline-block animate-spin h-8 w-8 border-4 border-gray-300 dark:border-gray-700 rounded-full border-t-blue-500"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="inline-block p-4 rounded-full bg-red-100 dark:bg-red-900/20 text-red-500">
            <AlertTriangle size={24} />
          </div>
          <h2 className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">Error</h2>
          <p className="mt-1 text-gray-600 dark:text-gray-400">{error}</p>
          <div className="mt-4">
            <Link href="/inventory" className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
              Return to inventory
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="inline-block p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/20 text-yellow-500">
            <AlertTriangle size={24} />
          </div>
          <h2 className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">Product Not Found</h2>
          <p className="mt-1 text-gray-600 dark:text-gray-400">The product you're looking for doesn't exist or has been removed.</p>
          <div className="mt-4">
            <Link href="/inventory" className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
              Return to inventory
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
      <Header title={product.name} />

      <div className="px-8 py-6">
        {/* Back button and actions */}
        <div className="flex justify-between mb-6">
          <Link
            href="/inventory"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            <ArrowLeft size={16} className="mr-1" />
            <span>Back to Inventory</span>
          </Link>

          <div className="flex space-x-2">
            <button
              className="p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20"
              onClick={() => copyToClipboard(`Product: ${product.name}, SKU: ${product.sku}`)}
              aria-label="Copy product details"
            >
              <Clipboard size={18} />
            </button>
            <button
              className="p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20"
              aria-label="Download product data"
            >
              <Download size={18} />
            </button>
            <button
              className="p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20"
              aria-label="Share product"
            >
              <Share size={18} />
            </button>
            <Link
              href={`/inventory/edit/${productId}`}
              className="p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20"
              aria-label="Edit product"
            >
              <Edit size={18} />
            </Link>
            <button
              className="p-2 rounded-lg text-gray-600 hover:text-red-600 hover:bg-red-50 dark:text-gray-400 dark:hover:text-red-400 dark:hover:bg-red-900/20"
              onClick={() => setShowDeleteConfirm(true)}
              aria-label="Delete product"
            >
              <Trash size={18} />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-12 gap-6">
          {/* Main Product Info - 8 columns on large screens */}
          <div className="col-span-12 lg:col-span-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
              <div className="flex flex-col md:flex-row">
                {/* Product Image */}
                <div className="w-full md:w-1/3 mb-4 md:mb-0 md:mr-6">
                  <div className="aspect-square rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                    {product.image_url ? (
                      <img
                        src={product.image_url}
                        alt={product.name}
                        className="w-full h-full object-contain rounded-lg"
                      />
                    ) : (
                      <Package size={64} className="text-gray-400 dark:text-gray-500" />
                    )}
                  </div>
                </div>

                {/* Product Details */}
                <div className="w-full md:w-2/3">
                  <div className="mb-4">
                    <div className="flex items-center justify-between">
                      <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                        {product.name}
                      </h1>
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                        {product.status.replace('_', ' ')}
                      </div>
                    </div>
                    <div className="flex items-center mt-1">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        SKU: <span className="font-medium text-gray-700 dark:text-gray-300">{product.sku}</span>
                      </p>
                      <button
                        onClick={() => copyToClipboard(product.sku)}
                        className="ml-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        aria-label="Copy SKU"
                      >
                        <Clipboard size={14} />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6 mb-4">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Category</p>
                      <p className="font-medium text-gray-800 dark:text-gray-200">{product.category}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Location</p>
                      <p className="font-medium text-gray-800 dark:text-gray-200">{product.location || 'Not specified'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Price</p>
                      <p className="font-medium text-gray-800 dark:text-gray-200">
                        ${product.price.toFixed(2)} / {product.unit}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Cost</p>
                      <p className="font-medium text-gray-800 dark:text-gray-200">
                        ${product.cost.toFixed(2)} / {product.unit}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Stock Quantity</p>
                      <div className="flex items-center">
                        <p className="font-medium text-gray-800 dark:text-gray-200">
                          {product.stock_quantity} {product.unit}
                        </p>
                        <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-medium ${getStockLevelColor()}`}>
                          {getStockLevel() === 'good' ? 'In Stock' : getStockLevel() === 'low' ? 'Low Stock' : 'Out of Stock'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Reorder Level</p>
                      <p className="font-medium text-gray-800 dark:text-gray-200">
                        {product.reorder_level} {product.unit}
                      </p>
                    </div>
                  </div>

                  <div className="mt-4">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Description</p>
                    <p className="mt-1 text-gray-700 dark:text-gray-300">
                      {product.description || 'No description available.'}
                    </p>
                  </div>

                  {product.tags && product.tags.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Tags</p>
                      <div className="flex flex-wrap gap-2">
                        {product.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-xs text-gray-700 dark:text-gray-300"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Supplier Information */}
            {supplier && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
                <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <Truck size={18} className="mr-2 text-gray-600 dark:text-gray-400" />
                  Supplier Information
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Supplier Name</p>
                    <p className="font-medium text-gray-800 dark:text-gray-200">{supplier.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                    <p className="font-medium text-gray-800 dark:text-gray-200">{supplier.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Phone</p>
                    <p className="font-medium text-gray-800 dark:text-gray-200">{supplier.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Address</p>
                    <p className="font-medium text-gray-800 dark:text-gray-200">{supplier.address}</p>
                  </div>
                </div>

                <div className="mt-4">
                  <Link
                    href={`/suppliers/${supplier.id}`}
                    className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <span>View supplier details</span>
                    <ChevronDown size={16} className="ml-1 transform rotate-270" />
                  </Link>
                </div>
              </div>
            )}

            {/* Transaction History */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <Clock size={18} className="mr-2 text-gray-600 dark:text-gray-400" />
                Transaction History
              </h2>

              {transactionHistory.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                      <tr>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Reference
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {transactionHistory.map((transaction) => (
                        <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-750">
                          <td className="px-3 py-3 text-sm text-gray-700 dark:text-gray-300">
                            {new Date(transaction.date).toLocaleDateString()}
                          </td>
                          <td className="px-3 py-3 text-sm">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              transaction.type === 'purchase'
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                                : transaction.type === 'sale'
                                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                                  : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
                            }`}>
                              {transaction.type}
                            </span>
                          </td>
                          <td className="px-3 py-3 text-sm text-gray-700 dark:text-gray-300">
                            {transaction.type === 'sale' ? '-' : '+'}{transaction.quantity} {product.unit}
                          </td>
                          <td className="px-3 py-3 text-sm text-gray-700 dark:text-gray-300">
                            {transaction.reference}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">No transaction history available.</p>
              )}

              <div className="mt-4 text-right">
                <Link
                  href={`/inventory-transactions?product=${productId}`}
                  className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <span>View all transactions</span>
                  <ChevronDown size={16} className="ml-1 transform rotate-270" />
                </Link>
              </div>
            </div>
          </div>

          {/* Side Information - 4 columns on large screens */}
          <div className="col-span-12 lg:col-span-4 space-y-6">
            {/* Quick Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <BarChart2 size={18} className="mr-2 text-gray-600 dark:text-gray-400" />
                Quick Stats
              </h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Profit Margin</p>
                  <p className="font-medium text-gray-800 dark:text-gray-200">
                    {product.price > 0 ? (((product.price - product.cost) / product.price) * 100).toFixed(2) : 0}%
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Days in Inventory</p>
                  <p className="font-medium text-gray-800 dark:text-gray-200">
                    {/* Placeholder for actual calculation */}
                    32 days
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Total Value</p>
                  <p className="font-medium text-gray-800 dark:text-gray-200">
                    ${(product.stock_quantity * product.cost).toFixed(2)}
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                  <p className="font-medium text-gray-800 dark:text-gray-200">
                    {new Date(product.updated_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <ListChecks size={18} className="mr-2 text-gray-600 dark:text-gray-400" />
                Quick Actions
              </h2>

              <div className="space-y-3">
                <Link href={`/inventory-transactions/create?product=${productId}&type=purchase`} className="block w-full">
                  <button className="w-full py-2 px-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 text-blue-600 dark:text-blue-400 text-sm font-medium text-left">
                    Record Purchase
                  </button>
                </Link>
                <Link href={`/inventory-transactions/create?product=${productId}&type=sale`} className="block w-full">
                  <button className="w-full py-2 px-4 rounded-lg bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 text-green-600 dark:text-green-400 text-sm font-medium text-left">
                    Record Sale
                  </button>
                </Link>
                <Link href={`/inventory-transactions/create?product=${productId}&type=adjustment`} className="block w-full">
                  <button className="w-full py-2 px-4 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/40 text-yellow-600 dark:text-yellow-400 text-sm font-medium text-left">
                    Adjust Inventory
                  </button>
                </Link>
                <Link href={`/work-orders/create?product=${productId}`} className="block w-full">
                  <button className="w-full py-2 px-4 rounded-lg bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/40 text-purple-600 dark:text-purple-400 text-sm font-medium text-left">
                    Create Work Order
                  </button>
                </Link>
                <Link href={`/purchase-orders/create?product=${productId}`} className="block w-full">
                  <button className="w-full py-2 px-4 rounded-lg bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/40 text-indigo-600 dark:text-indigo-400 text-sm font-medium text-left">
                    Add to Purchase Order
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg max-w-md w-full p-6"
          >
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/20 text-red-500 mb-4">
                <AlertTriangle size={24} />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Delete Product
              </h3>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Are you sure you want to delete this product? This action cannot be undone.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 py-2 px-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="flex-1 py-2 px-4 bg-red-500 rounded-lg text-white hover:bg-red-600"
              >
                Delete
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default ProductDetail;