"use client";

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  Upload,
  FileText,
  AlertTriangle,
  Check,
  Info,
  Download,
  Loader2,
  Table as TableIcon,
  FileSpreadsheet,
  RefreshCw,
  X
} from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import Header from '@/app/components/layout/Header';
import { Button } from "@/app/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table';
import { Badge } from '@/app/components/ui/badge';
import { Progress } from '@/app/components/ui/progress';
import { toast } from 'sonner';

// Define interfaces for import data
interface ImportResults {
  success: boolean;
  message: string;
  details?: string[];
  productsAdded?: number;
  relationshipsAdded?: number;
  errors?: string[];
  warnings?: string[];
}

interface PreviewRow {
  [key: string]: string;
}

interface ValidationError {
  row: number;
  column: string;
  message: string;
}

interface ImportProgress {
  total: number;
  processed: number;
  percentage: number;
  status: 'validating' | 'importing' | 'complete' | 'error';
}

const ProductImport: React.FC = () => {
  const { theme } = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const bomFileInputRef = useRef<HTMLInputElement>(null);

  // File state
  const [file, setFile] = useState<File | null>(null);
  const [bomFile, setBomFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState<boolean>(false);

  // UI state
  const [activeTab, setActiveTab] = useState<string>('products');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [validating, setValidating] = useState<boolean>(false);
  const [showPreview, setShowPreview] = useState<boolean>(false);

  // Data state
  const [previewData, setPreviewData] = useState<PreviewRow[] | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [progress, setProgress] = useState<ImportProgress>({
    total: 0,
    processed: 0,
    percentage: 0,
    status: 'validating'
  });
  const [results, setResults] = useState<ImportResults | null>(null);

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'products' | 'bom') => {
    const selectedFile = e.target.files?.[0] || null;

    if (!selectedFile) {
      if (type === 'products') {
        setFile(null);
      } else {
        setBomFile(null);
      }
      setPreviewData(null);
      setShowPreview(false);
      return;
    }

    // Check file type
    if (!selectedFile.name.endsWith('.csv')) {
      toast.error('Only CSV files are supported');
      return;
    }

    // Update state based on import type
    if (type === 'products') {
      setFile(selectedFile);
    } else {
      setBomFile(selectedFile);
    }

    // Preview the file
    previewFile(selectedFile);
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent<HTMLDivElement>, active: boolean) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(active);
  };

  // Handle file drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const droppedFile = e.dataTransfer.files?.[0];
    if (!droppedFile) return;

    // Check file type
    if (!droppedFile.name.endsWith('.csv')) {
      toast.error('Only CSV files are supported');
      return;
    }

    // Update state based on active tab
    if (activeTab === 'products') {
      setFile(droppedFile);
    } else {
      setBomFile(droppedFile);
    }

    // Preview the file
    previewFile(droppedFile);
  };

  // Reset file input
  const resetFileInput = () => {
    if (activeTab === 'products') {
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } else {
      setBomFile(null);
      if (bomFileInputRef.current) {
        bomFileInputRef.current.value = '';
      }
    }
    setPreviewData(null);
    setShowPreview(false);
    setValidationErrors([]);
    setResults(null);
  };

  // Preview CSV file
  const previewFile = (file: File) => {
    setValidating(true);
    setValidationErrors([]);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());

        // Validate headers based on import type
        const requiredHeaders = activeTab === 'products'
          ? ['Product ID', 'Product Name', 'Assembly Stage']
          : ['Parent ID', 'Child ID', 'Quantity'];

        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
        if (missingHeaders.length > 0) {
          setValidationErrors([{
            row: 0,
            column: 'Headers',
            message: `Missing required headers: ${missingHeaders.join(', ')}`
          }]);
          setValidating(false);
          return;
        }

        // Parse and validate data rows
        const previewRows: PreviewRow[] = [];
        const errors: ValidationError[] = [];

        // Calculate total rows for progress
        const totalRows = lines.length - 1; // Exclude header
        setProgress({
          total: totalRows,
          processed: 0,
          percentage: 0,
          status: 'validating'
        });

        // Process rows with a small delay to show progress
        let currentRow = 1;

        const processRows = () => {
          const batchSize = 10;
          const endRow = Math.min(currentRow + batchSize, lines.length);

          for (let i = currentRow; i < endRow; i++) {
            if (lines[i].trim()) {
              const values = lines[i].split(',').map(v => v.trim());
              const row: Record<string, string> = {};

              // Populate row data
              headers.forEach((header, index) => {
                row[header] = values[index] || '';
              });

              // Add to preview (limit to first 5 rows)
              if (previewRows.length < 5) {
                previewRows.push(row);
              }

              // Validate row data
              if (activeTab === 'products') {
                if (!row['Product ID']) {
                  errors.push({
                    row: i,
                    column: 'Product ID',
                    message: 'Product ID is required'
                  });
                }
                if (!row['Product Name']) {
                  errors.push({
                    row: i,
                    column: 'Product Name',
                    message: 'Product Name is required'
                  });
                }
              } else {
                if (!row['Parent ID']) {
                  errors.push({
                    row: i,
                    column: 'Parent ID',
                    message: 'Parent ID is required'
                  });
                }
                if (!row['Child ID']) {
                  errors.push({
                    row: i,
                    column: 'Child ID',
                    message: 'Child ID is required'
                  });
                }
                if (!row['Quantity'] || isNaN(Number(row['Quantity']))) {
                  errors.push({
                    row: i,
                    column: 'Quantity',
                    message: 'Quantity must be a number'
                  });
                }
              }
            }
          }

          // Update progress
          setProgress(prev => ({
            ...prev,
            processed: endRow - 1,
            percentage: Math.round(((endRow - 1) / totalRows) * 100)
          }));

          currentRow = endRow;

          if (currentRow < lines.length) {
            // Continue processing
            setTimeout(processRows, 10);
          } else {
            // Finished processing
            setPreviewData(previewRows);
            setValidationErrors(errors);
            setValidating(false);
            setShowPreview(true);

            // Update progress status
            setProgress(prev => ({
              ...prev,
              status: errors.length > 0 ? 'error' : 'complete'
            }));

            // Show toast based on validation results
            if (errors.length > 0) {
              toast.error(`Validation failed with ${errors.length} errors`);
            } else {
              toast.success('File validated successfully');
            }
          }
        };

        // Start processing rows
        processRows();

      } catch (error) {
        console.error('Error parsing CSV:', error);
        setPreviewData(null);
        setValidating(false);
        setValidationErrors([{
          row: 0,
          column: 'File',
          message: 'Error parsing CSV file. Please check the format.'
        }]);
        toast.error('Error parsing CSV file');
      }
    };

    reader.readAsText(file);
  };

  // Handle import
  const handleImport = async () => {
    const selectedFile = activeTab === 'products' ? file : bomFile;

    if (!selectedFile) {
      toast.error('Please select a file to import');
      return;
    }

    if (validationErrors.length > 0) {
      toast.error('Please fix validation errors before importing');
      return;
    }

    setIsLoading(true);
    setResults(null);

    // Reset progress for import phase
    setProgress({
      total: 100,
      processed: 0,
      percentage: 0,
      status: 'importing'
    });

    try {
      // In a real implementation, you would:
      // 1. Create a FormData object
      // 2. Append the file
      // 3. Send to the API
      // 4. Handle the response

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('type', activeTab);

      // Simulate API call with progress updates
      const totalSteps = 10;

      for (let step = 1; step <= totalSteps; step++) {
        // Update progress
        setProgress(prev => ({
          ...prev,
          processed: step,
          percentage: Math.round((step / totalSteps) * 100)
        }));

        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Simulate successful import
      if (activeTab === 'products') {
        setResults({
          success: true,
          message: 'Products imported successfully',
          productsAdded: 24,
          details: [
            'Added 15 components',
            'Added 6 sub-assemblies',
            'Added 3 final assemblies'
          ]
        });
      } else {
        setResults({
          success: true,
          message: 'BOM relationships imported successfully',
          relationshipsAdded: 32,
          details: [
            'Added 32 parent-child relationships',
            'Updated 3 existing relationships'
          ]
        });
      }

      // Update progress status
      setProgress(prev => ({
        ...prev,
        status: 'complete'
      }));

      // Show success toast
      toast.success(activeTab === 'products'
        ? 'Products imported successfully'
        : 'BOM relationships imported successfully');

    } catch (error) {
      console.error('Error importing data:', error);

      // Update progress status
      setProgress(prev => ({
        ...prev,
        status: 'error'
      }));

      // Set error results
      setResults({
        success: false,
        message: 'Error importing data',
        details: ['An unexpected error occurred. Please try again.']
      });

      // Show error toast
      toast.error('Error importing data');
    } finally {
      setIsLoading(false);
    }
  };

  // Download template CSV file
  const downloadTemplate = (type: string) => {
    let csvContent = '';

    if (type === 'products') {
      csvContent = 'Product ID,Product Name,Assembly Stage,Sub-Assembly Name,Technical Specs,Supplier/Manufacturer,Current Stock,Reorder Level,Location,Notes\n';
      csvContent += 'DL.23.401,Vibration Shaft with 02 washers 04 keys and 02 castle nuts,Final Assembly,,,,,,,\n';
      csvContent += 'DL23.107,Spacer Ring,Component,Vibration shaft assembly,,,2,20,TEJ,\n';
      csvContent += '2E22.31,Washer,Component,Vibration shaft assembly,,,2,20,Trackline,\n';
    } else {
      csvContent = 'Parent ID,Child ID,Quantity,Position,Notes\n';
      csvContent += 'DL.23.401,DL23.107,2,Top and Bottom,\n';
      csvContent += 'DL.23.401,2E22.31,2,,\n';
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${type}_template.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(`${type === 'products' ? 'Products' : 'BOM'} template downloaded`);
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Product Data Import" />

      <div className="px-8 pb-8">
        <div className="mb-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="products">
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Import Products
              </TabsTrigger>
              <TabsTrigger value="bom">
                <TableIcon className="h-4 w-4 mr-2" />
                Import BOM Relationships
              </TabsTrigger>
            </TabsList>

            <TabsContent value="products">
              <Card>
                <CardHeader>
                  <CardTitle>Import Products</CardTitle>
                  <CardDescription>
                    Upload a CSV file containing product data including components, sub-assemblies, and final assemblies.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Template Download Button */}
                  <div className="flex justify-end mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadTemplate('products')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Template
                    </Button>
                  </div>

                  {/* File Upload Area */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center mb-6 transition-colors
                      ${dragActive ? 'border-primary bg-primary/5' : 'border-border'}
                      ${file ? 'bg-muted/50' : ''}`}
                    onDragOver={(e) => handleDrag(e, true)}
                    onDragEnter={(e) => handleDrag(e, true)}
                    onDragLeave={(e) => handleDrag(e, false)}
                    onDrop={handleDrop}
                  >
                    {file ? (
                      <div className="flex flex-col items-center">
                        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                          <FileText className="h-6 w-6" />
                        </div>
                        <p className="text-sm font-medium mb-1">{file.name}</p>
                        <p className="text-xs text-muted-foreground mb-4">
                          {(file.size / 1024).toFixed(2)} KB • CSV
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={resetFileInput}
                        >
                          <X className="h-4 w-4 mr-2" />
                          Remove File
                        </Button>
                      </div>
                    ) : (
                      <label className="flex flex-col items-center justify-center cursor-pointer">
                        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-muted mb-4">
                          <Upload className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <p className="text-sm font-medium mb-1">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-muted-foreground mb-4">
                          CSV files only (max 10MB)
                        </p>
                        <Button variant="outline" size="sm">
                          <Upload className="h-4 w-4 mr-2" />
                          Select CSV File
                        </Button>
                        <input
                          ref={fileInputRef}
                          type="file"
                          className="hidden"
                          accept=".csv"
                          onChange={(e) => handleFileChange(e, 'products')}
                        />
                      </label>
                    )}
                  </div>

                  {/* Validation Progress */}
                  {validating && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium">Validating file...</p>
                        <p className="text-sm text-muted-foreground">
                          {progress.processed} of {progress.total} rows
                        </p>
                      </div>
                      <Progress value={progress.percentage} className="h-2" />
                    </div>
                  )}

                  {/* Validation Errors */}
                  {validationErrors.length > 0 && (
                    <div className="mb-6">
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Validation Errors</AlertTitle>
                        <AlertDescription>
                          <p className="mb-2">Please fix the following errors before importing:</p>
                          <div className="max-h-40 overflow-y-auto">
                            <ul className="list-disc list-inside space-y-1">
                              {validationErrors.slice(0, 10).map((error, index) => (
                                <li key={index}>
                                  Row {error.row}: {error.message} in column "{error.column}"
                                </li>
                              ))}
                              {validationErrors.length > 10 && (
                                <li>...and {validationErrors.length - 10} more errors</li>
                              )}
                            </ul>
                          </div>
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {/* Data Preview */}
                  {showPreview && previewData && previewData.length > 0 && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium">Data Preview</h4>
                        <Badge variant="outline">
                          Showing {previewData.length} of {progress.total} rows
                        </Badge>
                      </div>
                      <div className="border rounded-md overflow-hidden">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              {Object.keys(previewData[0]).map((header, idx) => (
                                <TableHead key={idx}>{header}</TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {previewData.map((row, rowIdx) => (
                              <TableRow key={rowIdx}>
                                {Object.values(row).map((cell: any, cellIdx) => (
                                  <TableCell key={cellIdx}>
                                    {cell || <span className="text-muted-foreground italic">Empty</span>}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}

                  {/* Import Progress */}
                  {isLoading && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium">
                          {progress.status === 'importing' ? 'Importing data...' : 'Processing...'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {progress.percentage}%
                        </p>
                      </div>
                      <Progress value={progress.percentage} className="h-2" />
                    </div>
                  )}

                  {/* Results */}
                  {results && (
                    <Alert variant={results.success ? 'default' : 'destructive'} className="mb-6">
                      {results.success ? <Check className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
                      <AlertTitle>{results.message}</AlertTitle>
                      <AlertDescription>
                        {results.productsAdded !== undefined && (
                          <p>Products Added: {results.productsAdded}</p>
                        )}
                        {results.details && results.details.length > 0 && (
                          <ul className="list-disc list-inside space-y-1 mt-2">
                            {results.details.map((detail, index) => (
                              <li key={index}>{detail}</li>
                            ))}
                          </ul>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={resetFileInput}
                    disabled={!file || isLoading}
                  >
                    Reset
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={!file || validationErrors.length > 0 || isLoading || !showPreview}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        Import Products
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="bom">
              <Card>
                <CardHeader>
                  <CardTitle>Import BOM Relationships</CardTitle>
                  <CardDescription>
                    Upload a CSV file defining the relationships between products (Bill of Materials).
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Template Download Button */}
                  <div className="flex justify-end mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadTemplate('bom')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Template
                    </Button>
                  </div>

                  {/* File Upload Area */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center mb-6 transition-colors
                      ${dragActive ? 'border-primary bg-primary/5' : 'border-border'}
                      ${bomFile ? 'bg-muted/50' : ''}`}
                    onDragOver={(e) => handleDrag(e, true)}
                    onDragEnter={(e) => handleDrag(e, true)}
                    onDragLeave={(e) => handleDrag(e, false)}
                    onDrop={handleDrop}
                  >
                    {bomFile ? (
                      <div className="flex flex-col items-center">
                        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary mb-4">
                          <FileText className="h-6 w-6" />
                        </div>
                        <p className="text-sm font-medium mb-1">{bomFile.name}</p>
                        <p className="text-xs text-muted-foreground mb-4">
                          {(bomFile.size / 1024).toFixed(2)} KB • CSV
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={resetFileInput}
                        >
                          <X className="h-4 w-4 mr-2" />
                          Remove File
                        </Button>
                      </div>
                    ) : (
                      <label className="flex flex-col items-center justify-center cursor-pointer">
                        <div className="flex items-center justify-center w-12 h-12 rounded-full bg-muted mb-4">
                          <Upload className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <p className="text-sm font-medium mb-1">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-muted-foreground mb-4">
                          CSV files only (max 10MB)
                        </p>
                        <Button variant="outline" size="sm">
                          <Upload className="h-4 w-4 mr-2" />
                          Select CSV File
                        </Button>
                        <input
                          ref={bomFileInputRef}
                          type="file"
                          className="hidden"
                          accept=".csv"
                          onChange={(e) => handleFileChange(e, 'bom')}
                        />
                      </label>
                    )}
                  </div>

                  {/* Same validation, preview, and import UI as products tab */}
                  {/* Validation Progress */}
                  {validating && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium">Validating file...</p>
                        <p className="text-sm text-muted-foreground">
                          {progress.processed} of {progress.total} rows
                        </p>
                      </div>
                      <Progress value={progress.percentage} className="h-2" />
                    </div>
                  )}

                  {/* Validation Errors */}
                  {validationErrors.length > 0 && (
                    <div className="mb-6">
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>Validation Errors</AlertTitle>
                        <AlertDescription>
                          <p className="mb-2">Please fix the following errors before importing:</p>
                          <div className="max-h-40 overflow-y-auto">
                            <ul className="list-disc list-inside space-y-1">
                              {validationErrors.slice(0, 10).map((error, index) => (
                                <li key={index}>
                                  Row {error.row}: {error.message} in column "{error.column}"
                                </li>
                              ))}
                              {validationErrors.length > 10 && (
                                <li>...and {validationErrors.length - 10} more errors</li>
                              )}
                            </ul>
                          </div>
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}

                  {/* Data Preview */}
                  {showPreview && previewData && previewData.length > 0 && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium">Data Preview</h4>
                        <Badge variant="outline">
                          Showing {previewData.length} of {progress.total} rows
                        </Badge>
                      </div>
                      <div className="border rounded-md overflow-hidden">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              {Object.keys(previewData[0]).map((header, idx) => (
                                <TableHead key={idx}>{header}</TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {previewData.map((row, rowIdx) => (
                              <TableRow key={rowIdx}>
                                {Object.values(row).map((cell: any, cellIdx) => (
                                  <TableCell key={cellIdx}>
                                    {cell || <span className="text-muted-foreground italic">Empty</span>}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  )}

                  {/* Import Progress */}
                  {isLoading && (
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium">
                          {progress.status === 'importing' ? 'Importing data...' : 'Processing...'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {progress.percentage}%
                        </p>
                      </div>
                      <Progress value={progress.percentage} className="h-2" />
                    </div>
                  )}

                  {/* Results */}
                  {results && (
                    <Alert variant={results.success ? 'default' : 'destructive'} className="mb-6">
                      {results.success ? <Check className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
                      <AlertTitle>{results.message}</AlertTitle>
                      <AlertDescription>
                        {results.relationshipsAdded !== undefined && (
                          <p>Relationships Added: {results.relationshipsAdded}</p>
                        )}
                        {results.details && results.details.length > 0 && (
                          <ul className="list-disc list-inside space-y-1 mt-2">
                            {results.details.map((detail, index) => (
                              <li key={index}>{detail}</li>
                            ))}
                          </ul>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={resetFileInput}
                    disabled={!bomFile || isLoading}
                  >
                    Reset
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={!bomFile || validationErrors.length > 0 || isLoading || !showPreview}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        Import BOM Relationships
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ProductImport;