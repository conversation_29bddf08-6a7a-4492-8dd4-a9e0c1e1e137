"use client";

import React from "react";
import { EnhancedTableDemo } from "@/app/components/demos/EnhancedTableDemo";
import { DebounceDemo } from "@/app/components/demos/DebounceDemo";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs";

export default function UIComponentsPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">UI Components</h1>

      <Tabs defaultValue="table" className="w-full mb-12">
        <TabsList className="mb-6">
          <TabsTrigger value="table">Enhanced Table</TabsTrigger>
          <TabsTrigger value="utils">Utility Functions</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-12">
          <EnhancedTableDemo />
        </TabsContent>

        <TabsContent value="utils" className="space-y-12">
          <DebounceDemo />
        </TabsContent>
      </Tabs>
    </div>
  );
}
