"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Sidebar from "@/app/components/layout/Sidebar";
import { useTheme } from "@/app/context/ThemeContext";

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MainLayoutContent>{children}</MainLayoutContent>
  );
}

// Extract the content into a separate component that uses the theme
function MainLayoutContent({ children }: { children: React.ReactNode }) {
  const { theme } = useTheme();

  return (
    <>
      {/* Background elements */}
      <div className="fixed inset-0 z-0">
        {theme === 'dark' ? (
          <>
            {/* Dark theme background with animated gradient blobs */}
            <div className="absolute inset-0 bg-dark-900 overflow-hidden">
              <div className="absolute -top-[10%] -left-[10%] w-[50%] h-[50%] rounded-full bg-dark-800/20 mix-blend-soft-light blur-3xl animate-float opacity-50"></div>
              <div className="absolute top-[30%] right-[20%] w-[40%] h-[40%] rounded-full bg-dark-700/20 mix-blend-soft-light blur-3xl animate-float opacity-40" style={{ animationDelay: '2s' }}></div>
              <div className="absolute bottom-[10%] left-[30%] w-[30%] h-[30%] rounded-full bg-dark-700/20 mix-blend-soft-light blur-3xl animate-float opacity-40" style={{ animationDelay: '4s' }}></div>
            </div>
          </>
        ) : (
          <>
            {/* Light theme background with subtle gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white overflow-hidden">
              <div className="absolute -top-[10%] -left-[10%] w-[50%] h-[50%] rounded-full bg-gray-400/5 mix-blend-multiply blur-3xl animate-float opacity-30"></div>
              <div className="absolute top-[30%] right-[20%] w-[40%] h-[40%] rounded-full bg-gray-400/5 mix-blend-multiply blur-3xl animate-float opacity-20" style={{ animationDelay: '2s' }}></div>
            </div>
          </>
        )}
      </div>

      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="flex h-screen overflow-hidden relative z-10"
        >
          <Sidebar />

          <main className="flex-1 flex flex-col overflow-auto custom-scrollbar">
            {children}
          </main>
        </motion.div>
      </AnimatePresence>
    </>
  );
}