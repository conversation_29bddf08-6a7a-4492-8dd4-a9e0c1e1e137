"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Clipboard,
  Plus,
  Search,
  AlertTriangle,
  Loader2,
  X
} from 'lucide-react';
import { WorkOrder } from '@/app/types/orders';
import { useTheme } from '@/app/context/ThemeContext';
import Header from '@/app/components/layout/Header.tsx';
import { Button } from "@/app/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert";
import { Input } from "@/app/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/app/components/ui/dialog";
import { toast } from 'sonner';
import { WorkOrdersTable } from '@/app/components/tables/WorkOrdersTable';
import { WorkOrderForm } from '@/app/components/forms/WorkOrderForm';
import { WorkOrderFormData } from '@/app/components/forms/WorkOrderForm/types';

/**
 * Work Orders page component
 * Displays a list of work orders and allows users to create, edit, and delete work orders
 * Includes filtering and search functionality
 */
const WorkOrders: React.FC = () => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [currentWorkOrder, setCurrentWorkOrder] = useState<WorkOrder | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // Open form modal for creating a new work order
  const openCreateModal = () => {
    setCurrentWorkOrder(null);
    setIsFormModalOpen(true);
    setFormError(null);
  };

  // Open form modal for editing an existing work order
  const handleEditWorkOrder = (workOrder: WorkOrder) => {
    setCurrentWorkOrder(workOrder);
    setIsFormModalOpen(true);
    setFormError(null);
  };

  // Close form modal
  const closeFormModal = () => {
    setIsFormModalOpen(false);
    setCurrentWorkOrder(null);
    setFormError(null);
  };

  // Handle work order form submission
  const handleWorkOrderSubmit = async (data: WorkOrderFormData) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      const isEditing = !!currentWorkOrder;
      const url = isEditing
        ? `/api/work-orders/${currentWorkOrder.woNumber}`
        : '/api/work-orders';

      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save work order');
      }

      const result = await response.json();

      // Close modal and refresh table
      setIsFormModalOpen(false);
      setCurrentWorkOrder(null);
      setRefreshTrigger(prev => prev + 1);

      // Show success toast
      toast.success(isEditing
        ? `Work order ${result.data.woNumber} updated successfully`
        : `Work order ${result.data.woNumber} created successfully`
      );
    } catch (err: any) {
      setFormError(err.message || 'An error occurred while saving the work order');
      console.error('Error saving work order:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle work order deletion
  const handleDeleteWorkOrder = (workOrder: WorkOrder) => {
    // The actual delete operation is handled in the WorkOrdersTable component
    // Here we just refresh the table after deletion
    setRefreshTrigger(prev => prev + 1);
    toast.success(`Work order ${workOrder.woNumber} deleted successfully`);
  };

  // Handle work order view
  const handleViewWorkOrder = (workOrder: WorkOrder) => {
    setCurrentWorkOrder(workOrder);
    setIsFormModalOpen(true);
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Work Orders" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Clipboard className="text-foreground mr-2" size={24} />
            <h1 className="text-2xl font-semibold text-foreground">Work Orders</h1>
          </div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button variant="default" onClick={openCreateModal}>
              <Plus size={18} className="mr-1" />
              <span>New Work Order</span>
            </Button>
          </motion.div>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="bg-card rounded-xl shadow-md dark:shadow-gray-900/30 overflow-hidden mb-6">
          <div className="p-4 flex flex-wrap gap-4 items-center border-b border-border">
            <div className="relative flex-1 min-w-[200px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <Input
                type="text"
                placeholder="Search work orders..."
                className="pl-10"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>

            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="min-w-[150px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="p-4">
            <WorkOrdersTable
              fetchData={true}
              onWorkOrderClick={handleViewWorkOrder}
              onWorkOrderEdit={handleEditWorkOrder}
              onWorkOrderDelete={handleDeleteWorkOrder}
              key={`work-orders-table-${refreshTrigger}`} // Force re-render on refresh
            />
          </div>
        </div>
      </div>

      {/* Work Order Form Modal */}
      <Dialog open={isFormModalOpen} onOpenChange={setIsFormModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {currentWorkOrder ? `Edit Work Order: ${currentWorkOrder.woNumber}` : 'Create New Work Order'}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-4"
              onClick={closeFormModal}
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <WorkOrderForm
            initialData={currentWorkOrder || undefined}
            onSubmit={handleWorkOrderSubmit}
            onCancel={closeFormModal}
            isLoading={isSubmitting}
            error={formError}
            isEditing={!!currentWorkOrder}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WorkOrders;