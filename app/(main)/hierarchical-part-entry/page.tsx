"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/app/components/ui/card';
import {
  Layers,
  Plus,
  ArrowLeft,
  Info,
  FileText,
  Check,
  AlertTriangle,
  Save,
  Loader2
} from 'lucide-react';
import { HierarchicalPartsForm } from '@/app/components/forms/HierarchicalPartsForm';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import Header from '@/app/components/layout/Header';
import { toast } from 'sonner';

/**
 * Hierarchical Part Entry page - allows creating complex hierarchical assemblies
 */
export default function HierarchicalPartEntryPage() {
  const [activeTab, setActiveTab] = useState('form');
  const [isSaving, setIsSaving] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'idle' | 'validating' | 'valid' | 'invalid'>('idle');

  // Simulate validation
  const validateAssembly = () => {
    setValidationStatus('validating');

    // Simulate validation process
    setTimeout(() => {
      // Randomly succeed or fail for demo purposes
      const isValid = Math.random() > 0.3;

      if (isValid) {
        setValidationStatus('valid');
        toast.success('Assembly validated successfully');
      } else {
        setValidationStatus('invalid');
        toast.error('Validation failed. Please check the form for errors.');
      }
    }, 1500);
  };

  // Simulate saving
  const handleSave = () => {
    setIsSaving(true);

    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast.success('Assembly saved successfully');
    }, 2000);
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Hierarchical Part Entry" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Layers className="text-foreground mr-2" size={24} />
            <h1 className="text-2xl font-semibold text-foreground">Hierarchical Part Entry</h1>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/assemblies">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Assemblies
              </Link>
            </Button>

            <Button variant="default" asChild>
              <Link href="/assemblies/create">
                <Plus size={16} className="mr-1" />
                <span>Create Standard Assembly</span>
              </Link>
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="mb-4">
            <TabsTrigger value="form">
              <FileText className="h-4 w-4 mr-2" />
              Assembly Form
            </TabsTrigger>
            <TabsTrigger value="validation">
              <Check className="h-4 w-4 mr-2" />
              Validation
            </TabsTrigger>
          </TabsList>

          <TabsContent value="form">
            <Card>
              <CardHeader>
                <CardTitle>Create Hierarchical Assembly</CardTitle>
                <CardDescription>
                  Build complex assemblies with multi-level hierarchies using a structured form approach.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AssemblyFormProvider>
                  <HierarchicalPartsForm mode="create" />
                </AssemblyFormProvider>
              </CardContent>
              <CardFooter className="border-t bg-muted/50 p-4 flex justify-between">
                <Alert className="bg-blue-50 dark:bg-blue-900/20 flex-1 mr-4">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Looking for an alternative?</AlertTitle>
                  <AlertDescription>
                    Try the <Link href="/hierarchical-builder" className="font-medium underline">Visual Assembly Builder</Link> for a more visual approach to creating assemblies.
                  </AlertDescription>
                </Alert>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={validateAssembly}
                    disabled={validationStatus === 'validating'}
                  >
                    {validationStatus === 'validating' ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Validating...
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Validate
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={handleSave}
                    disabled={isSaving || validationStatus === 'invalid'}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Assembly
                      </>
                    )}
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="validation">
            <Card>
              <CardHeader>
                <CardTitle>Assembly Validation</CardTitle>
                <CardDescription>
                  Validate your assembly structure and components before saving.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {validationStatus === 'idle' && (
                  <div className="flex flex-col items-center justify-center h-64 border-2 border-dashed rounded-lg p-6 text-center">
                    <Check className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      Click the Validate button to check your assembly for errors.
                    </p>
                  </div>
                )}

                {validationStatus === 'validating' && (
                  <div className="flex flex-col items-center justify-center h-64 border-2 border-dashed rounded-lg p-6 text-center">
                    <Loader2 className="h-12 w-12 text-primary mb-4 animate-spin" />
                    <p className="text-muted-foreground">
                      Validating your assembly structure...
                    </p>
                  </div>
                )}

                {validationStatus === 'valid' && (
                  <div className="space-y-4">
                    <Alert variant="default" className="bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
                      <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                      <AlertTitle>Assembly Validated Successfully</AlertTitle>
                      <AlertDescription>
                        Your assembly structure is valid and ready to be saved.
                      </AlertDescription>
                    </Alert>

                    <div className="border rounded-lg p-4">
                      <h3 className="text-sm font-medium mb-2">Validation Summary</h3>
                      <Separator className="mb-4" />
                      <ul className="space-y-2">
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          All required fields are filled
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          Assembly structure is valid
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          All parts exist in the database
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                          No circular dependencies detected
                        </li>
                      </ul>
                    </div>
                  </div>
                )}

                {validationStatus === 'invalid' && (
                  <div className="space-y-4">
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>Validation Failed</AlertTitle>
                      <AlertDescription>
                        Please fix the following errors before saving your assembly.
                      </AlertDescription>
                    </Alert>

                    <div className="border rounded-lg p-4">
                      <h3 className="text-sm font-medium mb-2">Validation Errors</h3>
                      <Separator className="mb-4" />
                      <ul className="space-y-2">
                        <li className="flex items-center text-sm text-red-600 dark:text-red-400">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Missing required field: Assembly Code
                        </li>
                        <li className="flex items-center text-sm text-red-600 dark:text-red-400">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Part "XYZ-123" not found in database
                        </li>
                        <li className="flex items-center text-sm text-red-600 dark:text-red-400">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Circular dependency detected in assembly structure
                        </li>
                      </ul>
                    </div>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={validateAssembly}
                  disabled={validationStatus === 'validating'}
                  className="mr-2"
                >
                  {validationStatus === 'validating' ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Validating...
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Validate Again
                    </>
                  )}
                </Button>

                <Button
                  onClick={() => setActiveTab('form')}
                  variant={validationStatus === 'valid' ? 'default' : 'outline'}
                >
                  {validationStatus === 'valid' ? (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Continue to Save
                    </>
                  ) : (
                    <>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Form
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}