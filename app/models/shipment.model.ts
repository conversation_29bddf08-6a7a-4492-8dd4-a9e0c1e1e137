import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for address structure
interface IShipmentAddress {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// Interface for items within the shipment - Aligned with checklist
interface IShipmentItem {
  salesOrderItemId?: Types.ObjectId | null; // Refers to item in SO
  productId: Types.ObjectId; // Refers to Product model
  quantityShipped: number;
  notes?: string | null;
}

// Define interface for Shipment document - Aligned with checklist
export interface IShipment extends Document {
  _id: Types.ObjectId;
  shipmentNumber: string;
  salesOrderId: Types.ObjectId;
  shipmentDate: Date;
  carrierName?: string | null;
  trackingNumber?: string | null;
  status: 'pending' | 'processing' | 'shipped' | 'in_transit' | 'delivered' | 'cancelled' | 'failed_delivery'; // Updated enum as per checklist
  estimatedDeliveryDate?: Date | null;
  actualDeliveryDate?: Date | null;
  itemsShipped: IShipmentItem[];
  shippingAddress: IShipmentAddress;
  notes?: string | null;
  shippedByUserId?: Types.ObjectId | null;
  // cost field removed as it's not in checklist or target deliveries schema
  createdAt: Date;
  updatedAt: Date;
}

// Sub-schema for address - Kept as per current and checklist
const ShipmentAddressSchema: Schema = new Schema<IShipmentAddress>({
  street: { type: String, required: true, trim: true },
  city: { type: String, required: true, trim: true },
  state: { type: String, required: true, trim: true },
  postalCode: { type: String, required: true, trim: true },
  country: { type: String, required: true, trim: true }
}, { _id: false });

// Sub-schema for shipment items - Aligned with checklist
const ShipmentItemSchema: Schema = new Schema<IShipmentItem>({
  salesOrderItemId: { type: Schema.Types.ObjectId, ref: 'SalesOrder.items', default: null }, // Clarified ref if possible, or keep as is if SalesOrder items aren't directly referenceable as sub-docs
  productId: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
  quantityShipped: { type: Number, required: true, min: 1, validate: { validator: Number.isInteger, message: 'Quantity must be a whole number' } },
  notes: { type: String, default: null, trim: true }
}, { _id: false });

// Define schema for Shipment model
const ShipmentSchema: Schema = new Schema(
  {
    shipmentNumber: { type: String, required: true, unique: true, trim: true, index: true },
    salesOrderId: { type: Schema.Types.ObjectId, ref: 'SalesOrder', required: true, index: true },
    shipmentDate: { type: Date, required: true, default: Date.now, index: true }, // Added index as per checklist
    carrierName: { type: String, default: null, trim: true },
    trackingNumber: { type: String, default: null, trim: true, index: true }, // Kept existing index
    status: {
      type: String,
      required: true,
      enum: ['pending', 'processing', 'shipped', 'in_transit', 'delivered', 'cancelled', 'failed_delivery'], // Updated enum as per checklist
      default: 'pending',
      index: true // Added index as per checklist
    },
    estimatedDeliveryDate: { type: Date, default: null },
    actualDeliveryDate: { type: Date, default: null },
    itemsShipped: {
      type: [ShipmentItemSchema],
      required: true,
      validate: { validator: (v: any[]) => Array.isArray(v) && v.length > 0, message: 'At least one item is required in shipment' }
    },
    shippingAddress: { type: ShipmentAddressSchema, required: true },
    notes: { type: String, default: null, trim: true },
    shippedByUserId: { type: Schema.Types.ObjectId, ref: 'User', default: null }
    // cost field removed
  },
  {
    timestamps: true,
    toJSON: { getters: true },
    toObject: { getters: true }
  }
);

// Ensure all checklist indexes are present
// ShipmentSchema.index({ status: 1, shipmentDate: -1 }); // This is covered by individual indexes on status and shipmentDate with default sort direction if needed or specific compound if required.

const Shipment = mongoose.models.Shipment || mongoose.model<IShipment>('Shipment', ShipmentSchema);

export { Shipment };
export default Shipment; 