import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for individual check results within a QualityCheck
interface IQCCheckResult {
  checkName: string;
  specification: string;
  actualValue: string;
  result: 'pass' | 'fail' | 'not_applicable';
  notes?: string | null;
}

// Interface for attachments (e.g., images, documents)
interface IQCAttachment {
  fileName: string;
  url: string;
  uploadedAt?: Date;
}

// Define interface for QualityCheck document
export interface IQualityCheck extends Document {
  _id: Types.ObjectId;
  qcNumber: string;
  referenceType: 'Part' | 'Assembly' | 'Product' | 'WorkOrder' | 'PurchaseOrderReceipt';
  referenceId: Types.ObjectId;
  checkDate: Date;
  inspectorId: Types.ObjectId;
  status: 'pending' | 'pass' | 'fail' | 'rework_required' | 'reworked_pass' | 'reworked_fail';
  results: IQCCheckResult[];
  overallNotes?: string | null;
  attachments?: IQCAttachment[];
  createdAt: Date;
  updatedAt: Date;
}

// Sub-schema for QC Check Results
const QCCheckResultSchema: Schema = new Schema<IQCCheckResult>({
  checkName: { type: String, required: true, trim: true },
  specification: { type: String, required: true, trim: true },
  actualValue: { type: String, required: true, trim: true },
  result: { type: String, required: true, enum: ['pass', 'fail', 'not_applicable'] },
  notes: { type: String, default: null, trim: true }
}, { _id: false });

// Sub-schema for QC Attachments
const QCAttachmentSchema: Schema = new Schema<IQCAttachment>({
  fileName: { type: String, required: true, trim: true },
  url: { type: String, required: true, trim: true },
  uploadedAt: { type: Date, default: Date.now }
}, { _id: false });

// Define schema for QualityCheck model
const QualityCheckSchema: Schema = new Schema(
  {
    qcNumber: { type: String, required: true, unique: true, trim: true, index: true },
    referenceType: {
      type: String,
      required: true,
      enum: ['Part', 'Assembly', 'Product', 'WorkOrder', 'PurchaseOrderReceipt'],
      index: true
    },
    referenceId: { type: Schema.Types.ObjectId, required: true, index: true },
    checkDate: { type: Date, required: true, default: Date.now, index: true },
    inspectorId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'pass', 'fail', 'rework_required', 'reworked_pass', 'reworked_fail'],
      default: 'pending',
      index: true
    },
    results: {
      type: [QCCheckResultSchema],
      required: true,
      default: []
    },
    overallNotes: { type: String, default: null, trim: true },
    attachments: { type: [QCAttachmentSchema], default: undefined }
  },
  { timestamps: true }
);

// Indexes
QualityCheckSchema.index({ referenceType: 1, referenceId: 1 });
QualityCheckSchema.index({ status: 1, checkDate: -1 });

const QualityCheck = mongoose.models.QualityCheck || mongoose.model<IQualityCheck>('QualityCheck', QualityCheckSchema);

export { QualityCheck };
export default QualityCheck; 