import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Category document based on updated schema
export interface ICategory extends Document {
  _id: Types.ObjectId;
  name: string; // Name of the category (unique)
  description?: string | null; // Optional description
  parentCategory?: Types.ObjectId | null; // Renamed from parentCategoryId, Optional reference to parent Category model
  createdAt: Date;
  updatedAt: Date;
}

// Schema for Category model based on updated schema
const CategorySchema: Schema<ICategory> = new Schema(
  {
    name: { 
      type: String, 
      required: [true, 'Category name is required'], 
      unique: true, 
      trim: true,
      index: true 
    },
    description: { 
      type: String,
      default: null,
      trim: true 
    },
    parentCategory: { // Renamed from parentCategoryId
      type: Schema.Types.ObjectId, 
      ref: 'Category',
      default: null,
      index: true 
    },
  },
  { timestamps: true } // Automatically add createdAt and updatedAt fields
);

// Create and export Category model
const Category = mongoose.models.Category || mongoose.model<ICategory>('Category', CategorySchema);

// Export as both named export and default export for compatibility
export { Category };
export default Category;
