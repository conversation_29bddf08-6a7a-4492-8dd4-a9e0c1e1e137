import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Assembly Parts (used in partsRequired array)
export interface IAssemblyPart {
  partId: Types.ObjectId; // Reference to parts._id
  quantityRequired: number;
  unitOfMeasure: string;
}

// Interface for Image
export interface IImage {
  url: string;
  alt_text?: string | null;
}

// Interface for Attribute (maintained for backward compatibility)
export interface IAttribute {
  name: string;
  value: string;
}

// Interface for Assembly document - aligned with new schema
export interface IAssembly extends Document {
  _id: Types.ObjectId;
  assemblyCode: string;      // Unique business code for the assembly
  name: string;              // Name of the assembly
  description?: string | null; // Optional description
  productId?: Types.ObjectId | null; // Reference to products._id if this assembly is sold as a product
  parentId?: Types.ObjectId | null; // Reference to another assemblies._id if this is a sub-assembly
  isTopLevel: boolean;       // Indicates if this is a top-level assembly
  partsRequired: IAssemblyPart[]; // List of component parts and their quantities
  status: string;            // Assembly definition status, e.g., "active", "pending_review", "obsolete"
  version: number;           // Version number of the assembly definition
  manufacturingInstructions?: string | null; // Link to or text of manufacturing SOPs
  estimatedBuildTime?: string | null; // e.g., "1.5 hours", "30 minutes"
  // createdBy?: Types.ObjectId | null; // Removed to align with markdown schema
  // updatedBy?: Types.ObjectId | null; // Removed to align with markdown schema
  // Additional fields kept for backward compatibility
  images?: IImage[];
  attributes?: IAttribute[];
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  // Legacy field mappings
  assembly_id?: string;      // Legacy field, mapped to assemblyCode for backward compatibility
}

// Schema for Assembly Parts required
const AssemblyPartSchema: Schema<IAssemblyPart> = new Schema({
  partId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Part', 
    required: true 
  },
  quantityRequired: { 
    type: Number, 
    required: true, 
    min: [1, 'Quantity must be at least 1'], 
    validate: Number.isInteger 
  },
  unitOfMeasure: { 
    type: String, 
    required: true, 
    trim: true 
  }
}, { _id: false });

// Schema for Images (maintained for backward compatibility)
const ImageSchema: Schema<IImage> = new Schema({
  url: { 
    type: String, 
    required: true, 
    trim: true 
  },
  alt_text: { 
    type: String, 
    trim: true, 
    default: null 
  }
}, { _id: false });

// Schema for Attributes (maintained for backward compatibility)
const AttributeSchema: Schema<IAttribute> = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  value: { 
    type: String, 
    required: true, 
    trim: true 
  }
}, { _id: false });

// Mongoose Schema for Assembly - aligned with new schema
const AssemblySchema: Schema<IAssembly> = new Schema({
  assemblyCode: {
    type: String,
    required: [true, 'Assembly code is required'],
    unique: true,
    index: true,
    trim: true
  },
  name: {
    type: String,
    required: [true, 'Assembly name is required'],
    trim: true,
    index: true
  },
  description: {
    type: String,
    default: null,
    trim: true
  },
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    default: null
  },
  parentId: {
    type: Schema.Types.ObjectId,
    ref: 'Assembly',
    default: null
  },
  isTopLevel: {
    type: Boolean,
    default: false
  },
  partsRequired: {
    type: [AssemblyPartSchema],
    default: []
  },
  status: {
    type: String,
    required: [true, 'Status is required'],
    enum: {
      values: ['active', 'pending_review', 'in_production', 'obsolete'],
      message: 'Status must be one of: active, pending_review, in_production, obsolete'
    },
    default: 'pending_review',
    index: true
  },
  version: {
    type: Number,
    required: true,
    min: [1, 'Version must be at least 1'],
    default: 1
  },
  manufacturingInstructions: {
    type: String,
    default: null,
    trim: true
  },
  estimatedBuildTime: {
    type: String,
    default: null,
    trim: true
  },
  // createdBy: { // Removed to align with markdown schema
  //   type: Schema.Types.ObjectId,
  //   ref: 'User',
  //   default: null
  // },
  // updatedBy: { // Removed to align with markdown schema
  //   type: Schema.Types.ObjectId,
  //   ref: 'User',
  //   default: null
  // },
  // Additional fields kept for backward compatibility
  images: {
    type: [ImageSchema],
    default: []
  },
  attributes: {
    type: [AttributeSchema],
    default: []
  },
  notes: {
    type: String,
    default: null,
    trim: true
  },
  // Legacy field mapping
  assembly_id: {
    type: String,
    index: true,
    sparse: true
  }
}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

// Create virtual for maintaining backward compatibility
AssemblySchema.virtual('components').get(function() {
  if (!this.partsRequired) return [];
  return this.partsRequired.map(part => ({
    item_id: part.partId,
    item_type: 'Part',
    quantity: part.quantityRequired,
    unit_of_measure: part.unitOfMeasure
  }));
});

// Set up middleware to mirror assemblyCode to assembly_id for backward compatibility
AssemblySchema.pre('save', function(next) {
  if (this.isNew || this.isModified('assemblyCode')) {
    this.assembly_id = this.assemblyCode;
  }
  next();
});

// Ensure the model won't be recompiled multiple times
const Assembly = mongoose.models.Assembly || mongoose.model<IAssembly>('Assembly', AssemblySchema);

export { Assembly };
export default Assembly;
