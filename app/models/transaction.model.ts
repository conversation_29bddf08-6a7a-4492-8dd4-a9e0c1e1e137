import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for Transaction document
export interface ITransaction extends Document {
  _id: Types.ObjectId;
  partId: string; // Reference to part _id which is a string
  transactionType: string;
  quantity: number;
  previousStock: number;
  newStock: number;
  transactionDate: Date;
  referenceNumber: string;
  notes: string;
  batchId?: Types.ObjectId; // Reference to batch _id (optional)
  originalSqlId: any | null;
  createdAt: Date;
  updatedAt: Date;
  schemaVersion: number;
}

// Define schema for Transaction model
const TransactionSchema: Schema = new Schema(
  {
    partId: {
      type: String,
      ref: 'Part',
      required: [true, 'Part ID is required'],
      index: true
    },
    transactionType: {
      type: String,
      required: [true, 'Transaction type is required'],
      enum: {
        values: ['stock_in', 'stock_out', 'adjustment', 'initial'],
        message: 'Transaction type must be one of: stock_in, stock_out, adjustment, initial'
      },
      index: true
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      validate: {
        validator: Number.isInteger,
        message: 'Quantity must be a whole number'
      }
    },
    previousStock: {
      type: Number,
      required: [true, 'Previous stock is required'],
      validate: {
        validator: Number.isInteger,
        message: 'Previous stock must be a whole number'
      }
    },
    newStock: {
      type: Number,
      required: [true, 'New stock is required'],
      validate: {
        validator: Number.isInteger,
        message: 'New stock must be a whole number'
      }
    },
    transactionDate: {
      type: Date,
      required: [true, 'Transaction date is required'],
      default: Date.now
    },
    referenceNumber: {
      type: String,
      required: [true, 'Reference number is required'],
      trim: true
    },
    notes: {
      type: String,
      trim: true
    },
    batchId: {
      type: Schema.Types.ObjectId,
      ref: 'Batch',
      index: true
    },
    originalSqlId: {
      type: Schema.Types.Mixed,
      default: null
    },
    schemaVersion: {
      type: Number,
      default: 1
    }
  },
  { timestamps: true } // Adds createdAt and updatedAt fields
);

// Add an index for efficient queries by transaction date
TransactionSchema.index({ transactionDate: -1 });

// Add validation to ensure consistent stock values
TransactionSchema.pre('validate', function(this: ITransaction & Document, next) {
  // Check if newStock = previousStock + quantity
  if (this.newStock !== this.previousStock + this.quantity) {
    this.invalidate('newStock', 'New stock must equal previous stock plus quantity');
  }
  next();
});

// Create and export Transaction model
const Transaction = mongoose.models.Transaction || mongoose.model<ITransaction>('Transaction', TransactionSchema);

// Export as both named export and default export for compatibility
export { Transaction };
export default Transaction;