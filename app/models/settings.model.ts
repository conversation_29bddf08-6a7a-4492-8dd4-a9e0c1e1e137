import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for a single Setting document - Aligned with checklist
export interface ISetting extends Document {
  _id: Types.ObjectId;
  key: string;
  value: string; // Changed to String as per checklist
  description?: string | null;
  group?: string; // Changed to non-nullable as per checklist interpretation
  dataType: 'string' | 'integer' | 'boolean' | 'json'; // Added as per checklist
  lastModifiedBy: Types.ObjectId; // Added as per checklist
  lastModifiedAt: Date; // Added as per checklist
  isSystemEditableOnly: boolean; // Added as per checklist
  createdAt: Date;
  // Mongoose default updatedAt will not be used, using lastModifiedAt instead for this model
}

// Define schema for Settings model - Aligned with checklist
const SettingsSchema: Schema = new Schema(
  {
    key: {
      type: String,
      required: [true, 'Setting key is required'],
      unique: true,
      trim: true,
      index: true
    },
    value: {
      type: String, // Changed to String
      required: [true, 'Setting value is required']
    },
    description: {
      type: String,
      default: null,
      trim: true
    },
    group: {
      type: String,
      default: 'general',
      trim: true,
      index: true,
      required: [true, 'Group is required'] // Made required based on checklist simple 'String' type
    },
    dataType: { // Added
      type: String,
      required: [true, 'Data type is required'],
      enum: ['string', 'integer', 'boolean', 'json']
    },
    lastModifiedBy: { // Added
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Last modified by User ID is required']
    },
    lastModifiedAt: { // Added
      type: Date,
      required: [true, 'Last modified at date is required'],
      default: Date.now // Default to now, will be updated by application logic
    },
    isSystemEditableOnly: { // Added
      type: Boolean,
      default: false // Default to false (i.e., editable by users with permission)
    }
  },
  // { timestamps: true } // Using custom lastModifiedAt, so only createdAt from Mongoose timestamps if needed
  { timestamps: { createdAt: true, updatedAt: false } } // Only enable createdAt
);

const Setting = mongoose.models.Setting || mongoose.model<ISetting>('Setting', SettingsSchema);

export { Setting };
export default Setting;
