import mongoose, { Schema, Document, Types } from 'mongoose';

/**
 * Interface representing a component part included in a product's Bill of Materials (BOM).
 */
export interface IBomComponent {
  item_id: Types.ObjectId; // Renamed from componentId
  item_type: 'Part' | 'Assembly'; // Renamed from componentType
  quantity: number;
  unit_of_measure: string;
}

/**
 * Interface representing an image in the product's image array.
 */
export interface IImage {
  url: string;
  alt_text?: string | null;
}

/**
 * Interface representing a dimension in the product's dimensions array.
 */
export interface IDimension {
  length: number;
  width: number;
  height: number;
  unit: string; // e.g., "cm", "in"
}

/**
 * Interface representing a weight in the product's weight array.
 */
export interface IWeight {
  value: number;
  unit: string; // e.g., "kg", "lb"
}

/**
 * Interface representing an attribute in the product's attributes array.
 */
export interface IAttribute {
  name: string;
  value: string;
}

/**
 * Interface representing a product document in MongoDB (canonical schema)
 */
export interface IProduct extends Document {
  _id: Types.ObjectId;
  productCode: string; // Unique business code for the product
  name: string;
  description: string;
  categoryId: Types.ObjectId;
  status: string; // e.g., "active", "discontinued", "in_development"
  sellingPrice: number;
  assemblyId?: Types.ObjectId | null;
  partId?: Types.ObjectId | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for product components within the Product model.
 */
const BomComponentSchema: Schema = new Schema<IBomComponent>({
  item_id: { type: Schema.Types.ObjectId, required: true, refPath: 'bill_of_materials.item_type' },
  item_type: { type: String, required: true, enum: ['Part', 'Assembly'] },
  quantity: { type: Number, required: true, min: [1, 'Quantity must be at least 1'], validate: Number.isInteger },
  unit_of_measure: { type: String, required: true, trim: true }
}, { _id: false });

/**
 * Schema for images within the Product model.
 */
const ImageSchema: Schema = new Schema<IImage>({
  url: { type: String, required: true, trim: true },
  alt_text: { type: String, trim: true, default: null }
}, { _id: false });

/**
 * Schema for dimensions within the Product model.
 */
const DimensionSchema: Schema = new Schema<IDimension>({
  length: { type: Number, required: true, min: 0 },
  width: { type: Number, required: true, min: 0 },
  height: { type: Number, required: true, min: 0 },
  unit: { type: String, required: true, trim: true }
}, { _id: false });

/**
 * Schema for weights within the Product model.
 */
const WeightSchema: Schema = new Schema<IWeight>({
  value: { type: Number, required: true, min: 0 },
  unit: { type: String, required: true, trim: true }
}, { _id: false });

/**
 * Schema for attributes within the Product model.
 */
const AttributeSchema: Schema = new Schema<IAttribute>({
  name: { type: String, required: true, trim: true },
  value: { type: String, required: true, trim: true }
}, { _id: false });

/**
 * Mongoose schema for products (canonical schema)
 */
const ProductSchema: Schema = new Schema({
  productCode: {
    type: String,
    required: [true, 'Product code is required'],
    unique: true,
    index: true,
    trim: true
  },
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    index: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: true,
    index: true
  },
  status: {
    type: String,
    required: [true, 'Status is required'],
    enum: ['active', 'discontinued', 'in_development'],
    default: 'active',
    index: true
  },
  sellingPrice: {
    type: Number,
    required: [true, 'Selling price is required'],
    min: [0, 'Selling price cannot be negative']
  },
  assemblyId: {
    type: Schema.Types.ObjectId,
    ref: 'Assembly',
    default: null
  },
  partId: {
    type: Schema.Types.ObjectId,
    ref: 'Part',
    default: null
  }
}, {
  timestamps: true // Automatically add createdAt and updatedAt fields
});

// Additional indexes
ProductSchema.index({ tags: 1 });

// Ensure the model won't be recompiled multiple times
const Product = mongoose.models.Product || mongoose.model<IProduct>('Product', ProductSchema);

// Export as both named export and default export for compatibility
export { Product };
export default Product;