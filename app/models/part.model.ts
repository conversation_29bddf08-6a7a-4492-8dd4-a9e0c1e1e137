import mongoose, { Schema, Document, Types } from 'mongoose';

// Define the interface for the new Inventory sub-document
export interface IInventory {
  currentStock: number;
  warehouseId: Types.ObjectId; // Reference to warehouses._id
  safetyStockLevel: number;
  maximumStockLevel: number;
  averageDailyUsage: number;
  abcClassification: string; // e.g., "A", "B", "C"
  lastStockUpdate?: Date | null;
}

// Define the interface for the Part document
export interface IPart extends Document {
  _id: Types.ObjectId;
  partNumber: string; // Unique business identifier
  name: string;
  description?: string | null;
  technicalSpecs?: string | null; // Renamed from technicalSpecifications
  isManufactured: boolean; // Renamed from isManufactured
  isAssembly?: boolean; // Flag to indicate if this part is an assembly itself
  reorderLevel?: number | null; // Renamed from reorderPoint, type Int32
  status: 'active' | 'obsolete' | 'in_development' | 'pending_approval' | 'inactive'; // Added 'inactive' for broader compatibility with target schema example
  
  inventory: IInventory; // New nested inventory object

  supplierId?: Types.ObjectId | null; // Renamed from preferredSupplierId, ref: 'Supplier'
  unitOfMeasure: string;
  costPrice: number; // Renamed from cost, type Double (Mongoose Number)
  categoryId?: Types.ObjectId | null; // ref: 'Category'
  
  // Timestamps (handled by mongoose)
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for the Inventory sub-document
const InventorySchema = new Schema<IInventory>({
  currentStock: {
    type: Number,
    required: [true, 'Current stock is required'],
    default: 0,
    min: [0, 'Current stock cannot be negative']
  },
  warehouseId: {
    type: Schema.Types.ObjectId,
    ref: 'Warehouse', // Ensure 'Warehouse' matches the warehouse model name
    required: [true, 'Warehouse ID is required for inventory record']
  },
  safetyStockLevel: {
    type: Number,
    required: [true, 'Safety stock level is required'],
    default: 0,
    min: [0, 'Safety stock level cannot be negative']
  },
  maximumStockLevel: {
    type: Number,
    required: [true, 'Maximum stock level is required'],
    default: 0,
    min: [0, 'Maximum stock level cannot be negative']
  },
  averageDailyUsage: {
    type: Number,
    required: [true, 'Average daily usage is required'],
    default: 0,
    min: [0, 'Average daily usage cannot be negative']
  },
  abcClassification: {
    type: String,
    required: [true, 'ABC classification is required'],
    enum: ['A', 'B', 'C'], // Example classifications
    trim: true
  },
  lastStockUpdate: {
    type: Date,
    default: null
  }
}, { _id: false }); // _id is not needed for this sub-document

// Define the schema for the Part document
const PartSchema: Schema<IPart> = new Schema({
  partNumber: {
    type: String,
    required: [true, 'Part number is required'],
    unique: true,
    trim: true,
    index: true
  },
  name: {
    type: String,
    required: [true, 'Part name is required'],
    trim: true,
    minlength: [2, 'Part name must be at least 2 characters'],
    maxlength: [100, 'Part name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
    default: null
  },
  technicalSpecs: { // Renamed
    type: String,
    trim: true,
    default: null
  },
  isManufactured: { // Renamed
    type: Boolean,
    default: false
  },
  isAssembly: {
    type: Boolean,
    default: false
  },
  reorderLevel: { // Renamed
    type: Number, // Int32 maps to Number
    default: null,
    validate: {
      validator: function(v: number | null) {
        if (v === null) return true;
        return Number.isInteger(v) && v >= 0;
      },
      message: 'Reorder level must be a positive integer or null'
    }
  },
  status: {
    type: String,
    required: [true, 'Status is required'],
    enum: {
      values: ['active', 'obsolete', 'in_development', 'pending_approval', 'inactive'], // Kept detailed enums, added 'inactive'
      message: 'Status must be one of: active, inactive, obsolete, in_development, pending_approval'
    },
    default: 'active',
    index: true
  },
  inventory: { // New nested object
    type: InventorySchema,
    required: [true, 'Inventory information is required']
  },
  supplierId: { // Renamed
    type: Schema.Types.ObjectId,
    ref: 'Supplier',
    default: null
  },
  unitOfMeasure: {
    type: String,
    required: [true, 'Unit of measure is required'],
    trim: true,
    default: 'pcs'
  },
  costPrice: { // Renamed and type changed
    type: Number, // Double maps to Number
    required: [true, 'Cost price is required'],
    default: 0,
    min: [0, 'Cost price cannot be negative']
  },
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    default: null,
    index: true
  },
  // Removed fields: dimensions, weight, material, revision, customFields, imageURLs, documents
}, {
  timestamps: true,
  validateBeforeSave: true
});

// Add indexes for improved query performance
PartSchema.index({ name: 1 }); // Keep existing index on name
PartSchema.index({ updatedAt: -1 }); // Keep for sorting
// Updated text index for full-text search
PartSchema.index(
  { partNumber: 'text', name: 'text', description: 'text', technicalSpecs: 'text' },
  { name: 'part_text_search_index' } // Optional: name the index
);

// Create and export the model
export const Part = mongoose.models.Part || mongoose.model<IPart>('Part', PartSchema);

export default Part;