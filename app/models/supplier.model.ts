import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for Supplier document
export interface ISupplier extends Document {
  _id: Types.ObjectId;
  supplier_id: string; // Renamed from supplierCode
  name: string;
  contactPerson: string; // Renamed from contactName, now required
  email: string; // Renamed from contactEmail, now required and unique
  phone: string; // Renamed from contactPhone, now required
  address: string; // Changed from IAddress object to simple string
  specialty: string[]; // Added
  rating?: number | null; // Mongoose Number can be Double or Int32
  payment_terms?: string | null; // Renamed from paymentTerms
  delivery_terms?: string | null; // Added
  is_active: boolean; // Replaced status enum
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for Supplier model
const SupplierSchema: Schema<ISupplier> = new Schema(
  {
    supplier_id: { // Renamed
      type: String,
      required: [true, 'Supplier ID is required'],
      unique: true,
      index: true,
      trim: true
    },
    name: {
      type: String,
      required: [true, 'Supplier name is required'],
      trim: true
    },
    contactPerson: { // Renamed and made required
      type: String,
      required: [true, 'Contact person name is required'],
      trim: true
    },
    email: { // Renamed, made required and unique
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please provide a valid email address'
      ]
    },
    phone: { // Renamed and made required
      type: String,
      required: [true, 'Phone number is required'],
      trim: true
    },
    address: { // Changed to simple string
      type: String,
      required: [true, 'Address is required'],
      trim: true
    },
    specialty: { // Added
        type: [String],
        default: []
    },
    rating: {
      type: Number, // Handles Double or Int32
      min: [0, 'Rating cannot be negative'], // Assuming 0 is a possible rating
      max: [5, 'Rating cannot exceed 5'], // Assuming 5 is max, adjust if different
      default: null
    },
    payment_terms: { // Renamed
      type: String,
      default: null,
      trim: true
    },
    delivery_terms: { // Added
      type: String,
      default: null,
      trim: true
    },
    is_active: { // Replaced status
      type: Boolean,
      required: true,
      default: true
    }
    // notes field removed
  },
  { timestamps: true } // Adds createdAt and updatedAt fields
);

// Add indexes for improved query performance
SupplierSchema.index({ name: 1 });
SupplierSchema.index({ is_active: 1 }); // Index on the new boolean field

// Create and export Supplier model
const Supplier = mongoose.models.Supplier || mongoose.model<ISupplier>('Supplier', SupplierSchema);

export { Supplier };
export default Supplier;