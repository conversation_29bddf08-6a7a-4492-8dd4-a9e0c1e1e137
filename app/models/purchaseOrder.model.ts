import mongoose, { Schema, Document, Types } from 'mongoose';
import { IPart } from './part.model'; // Assuming Part model is part.model.ts
import { IUser } from './user.model'; // Assuming User model
import { ISupplier } from './supplier.model'; // Assuming Supplier model

// Interface for items within the purchase order
interface IPOItemCore {
  item_id: Types.ObjectId; // Renamed from partId
  item_type: 'Part' | 'Assembly' | 'Product'; // Added
  description?: string | null; // Made optional to align with target
  quantity_ordered: number; // Renamed from quantity
  unit_price: mongoose.Types.Decimal128; // Renamed from unitPrice
  total_price: mongoose.Types.Decimal128; // Renamed from lineTotal
  quantity_received?: number; // Retained
  received_date?: Date | null; // Added
  notes?: string | null; // Added
}

export interface IPOItem extends IPOItemCore, Document {} // For subdocument array

// Interface for attachments
interface IPOAttachmentCore {
  file_name: string;
  url: string;
  uploaded_at?: Date;
}
export interface IPOAttachment extends IPOAttachmentCore, Document {}

// Interface for history
interface IPOHistoryCore {
  event: string;
  user_id?: Types.ObjectId | null; // ref: 'User'
  timestamp?: Date;
  details?: string | null;
}
export interface IPOHistory extends IPOHistoryCore, Document {}

// Define interface for Purchase Order document
export interface IPurchaseOrder extends Document {
  _id: Types.ObjectId;
  po_number: string; // Renamed
  supplier_id: Types.ObjectId; // Renamed, ref: 'Supplier'
  order_date: Date; // Retained
  expected_delivery_date?: Date | null; // Retained
  actual_delivery_date?: Date | null; // Added
  items: IPOItem[]; // Updated interface
  sub_total: mongoose.Types.Decimal128; // Added
  tax_amount?: mongoose.Types.Decimal128; // Added
  shipping_cost?: mongoose.Types.Decimal128; // Added
  total_amount: mongoose.Types.Decimal128; // Retained, but calculation logic changes
  status: 'draft' | 'pending_approval' | 'approved' | 'ordered' | 'partially_received' | 'fully_received' | 'cancelled' | 'closed'; // Retained
  shipping_address?: string | null; // Changed from IAddress to string
  billing_address?: string | null; // Changed from IAddress to string
  payment_terms?: string | null; // Added
  terms_and_conditions?: string | null; // Retained
  notes?: string | null; // Retained
  created_by: Types.ObjectId; // Renamed, ref: 'User'
  approved_by?: Types.ObjectId | null; // Retained, ref: 'User'
  approval_date?: Date | null; // Retained
  attachments?: IPOAttachment[]; // Added
  history?: IPOHistory[]; // Added
  tags?: string[] | null; // Added
  createdAt: Date;
  updatedAt: Date;
}

// Sub-schema for purchase order items
const POItemSchema: Schema<IPOItem> = new Schema({
  item_id: {
    type: Schema.Types.ObjectId,
    ref: 'Part', // Ensure this ref matches the Part model name
    required: [true, 'Item ID is required'],
  },
  item_type: {
    type: String,
    required: [true, 'Item type is required'],
    enum: ['Part', 'Assembly', 'Product']
  },
  description: {
    type: String,
    trim: true,
    default: null
  },
  quantity_ordered: {
    type: Number,
    required: [true, 'Quantity ordered is required'],
    min: [1, 'Quantity ordered must be at least 1'],
    validate: {
      validator: Number.isInteger,
      message: 'Quantity ordered must be a whole number'
    }
  },
  unit_price: {
    type: Schema.Types.Decimal128,
    required: [true, 'Unit price is required'],
    get: (v: Types.Decimal128) => v ? parseFloat(v.toString()) : 0, // Getter for easier use
    set: (v: string | number) => Types.Decimal128.fromString(v.toString()) // Setter
  },
  total_price: {
    type: Schema.Types.Decimal128,
    required: [true, 'Total price is required'],
    get: (v: Types.Decimal128) => v ? parseFloat(v.toString()) : 0,
    set: (v: string | number) => Types.Decimal128.fromString(v.toString())
  },
  quantity_received: {
    type: Number,
    default: 0,
    min: [0, 'Received quantity cannot be negative'],
    validate: {
      validator: Number.isInteger,
      message: 'Received quantity must be a whole number'
    }
  },
  received_date: {
    type: Date,
    default: null
  },
  notes: {
    type: String,
    trim: true,
    default: null
  }
}, { _id: false });

// Sub-schema for attachments
const POAttachmentSchema: Schema<IPOAttachment> = new Schema({
  file_name: { type: String, required: true, trim: true },
  url: { type: String, required: true, trim: true },
  uploaded_at: { type: Date, default: Date.now }
}, { _id: false });

// Sub-schema for history
const POHistorySchema: Schema<IPOHistory> = new Schema({
  event: { type: String, required: true, trim: true },
  user_id: { type: Schema.Types.ObjectId, ref: 'User', default: null },
  timestamp: { type: Date, default: Date.now },
  details: { type: String, trim: true, default: null }
}, { _id: false });

// Define schema for Purchase Order model
const PurchaseOrderSchema: Schema<IPurchaseOrder> = new Schema(
  {
    po_number: {
      type: String,
      required: [true, 'PO number is required'],
      unique: true,
      trim: true,
      index: true
    },
    supplier_id: {
      type: Schema.Types.ObjectId,
      ref: 'Supplier', // Ensure this ref matches the Supplier model name
      required: [true, 'Supplier ID is required'],
      index: true
    },
    order_date: {
      type: Date,
      required: [true, 'Order date is required'],
      default: Date.now
    },
    expected_delivery_date: {
      type: Date,
      default: null
    },
    actual_delivery_date: { // Added
      type: Date,
      default: null
    },
    items: {
      type: [POItemSchema],
      required: [true, 'Items are required'],
      validate: {
        validator: function(v: any[]) {
          return Array.isArray(v) && v.length > 0;
        },
        message: 'At least one item is required'
      }
    },
    sub_total: { // Added
      type: Schema.Types.Decimal128,
      required: [true, 'Subtotal is required'],
      default: Types.Decimal128.fromString("0"),
      get: (v: Types.Decimal128) => v ? parseFloat(v.toString()) : 0,
      set: (v: string | number) => Types.Decimal128.fromString(v.toString())
    },
    tax_amount: { // Added
      type: Schema.Types.Decimal128,
      default: Types.Decimal128.fromString("0"),
      get: (v: Types.Decimal128) => v ? parseFloat(v.toString()) : 0,
      set: (v: string | number) => Types.Decimal128.fromString(v.toString())
    },
    shipping_cost: { // Added
      type: Schema.Types.Decimal128,
      default: Types.Decimal128.fromString("0"),
      get: (v: Types.Decimal128) => v ? parseFloat(v.toString()) : 0,
      set: (v: string | number) => Types.Decimal128.fromString(v.toString())
    },
    total_amount: {
      type: Schema.Types.Decimal128,
      required: [true, 'Total amount is required'],
      get: (v: Types.Decimal128) => v ? parseFloat(v.toString()) : 0,
      set: (v: string | number) => Types.Decimal128.fromString(v.toString())
    },
    status: {
      type: String,
      required: [true, 'Status is required'],
      enum: {
        values: ['draft', 'pending_approval', 'approved', 'ordered', 'partially_received', 'fully_received', 'cancelled', 'closed'],
        message: 'Status must be one of the predefined values'
      },
      default: 'draft',
      index: true
    },
    shipping_address: { // Changed
      type: String,
      trim: true,
      default: null
    },
    billing_address: { // Changed
      type: String,
      trim: true,
      default: null
    },
    payment_terms: { // Added
      type: String,
      trim: true,
      default: null
    },
    terms_and_conditions: {
      type: String,
      default: null,
      trim: true
    },
    notes: {
      type: String,
      default: null,
      trim: true
    },
    created_by: { // Renamed
      type: Schema.Types.ObjectId,
      ref: 'User', // Ensure this ref matches the User model name
      required: [true, 'Created by user ID is required']
    },
    approved_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    approval_date: {
      type: Date,
      default: null
    },
    attachments: { // Added
      type: [POAttachmentSchema],
      default: []
    },
    history: { // Added
      type: [POHistorySchema],
      default: []
    },
    tags: { // Added
      type: [String],
      default: null // Or [] if preferred
    }
  },
  {
    timestamps: true,
    toJSON: { getters: true },
    toObject: { getters: true },
    strictPopulate: false,
  }
);

// Add indexes for improved query performance
PurchaseOrderSchema.index({ order_date: -1 });
PurchaseOrderSchema.index({ status: 1, order_date: -1 });
PurchaseOrderSchema.index({ supplier_id: 1, status: 1 });
PurchaseOrderSchema.index({ 'items.item_id': 1 });
PurchaseOrderSchema.index({ tags: 1 });

// Pre-save hook to calculate amounts
PurchaseOrderSchema.pre('save', function(this: IPurchaseOrder & Document, next) {
  if (this.isModified('items') || this.isNew || this.isModified('tax_amount') || this.isModified('shipping_cost')) {
    let calculatedSubTotal = 0;
    this.items.forEach(item => {
      const quantity = item.quantity_ordered || 0;
      const unitPrice = item.unit_price ? parseFloat(item.unit_price.toString()) : 0;
      // Ensure total_price is Decimal128
      const itemTotalPrice = Types.Decimal128.fromString((quantity * unitPrice).toFixed(2));
      item.total_price = itemTotalPrice;
      calculatedSubTotal += parseFloat(itemTotalPrice.toString());
    });
    this.sub_total = Types.Decimal128.fromString(calculatedSubTotal.toFixed(2));

    const tax = this.tax_amount ? parseFloat(this.tax_amount.toString()) : 0;
    const shipping = this.shipping_cost ? parseFloat(this.shipping_cost.toString()) : 0;
    this.total_amount = Types.Decimal128.fromString((calculatedSubTotal + tax + shipping).toFixed(2));
  }
  next();
});

// Create and export Purchase Order model
const PurchaseOrder = mongoose.models.PurchaseOrder || mongoose.model<IPurchaseOrder>('PurchaseOrder', PurchaseOrderSchema);

export { PurchaseOrder };
export default PurchaseOrder;