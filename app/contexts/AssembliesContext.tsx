'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { Assembly, Part } from '@/app/components/tables/AssembliesTable/types';
import { optimisticUpdate, optimisticDelete, optimisticAdd, optimisticUpdateById } from '@/app/utils/optimistic-updates';

interface AssembliesContextType {
  assemblies: Assembly[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  isAutoRefreshEnabled: boolean;
  autoRefreshInterval: number;
  setAutoRefreshInterval: (interval: number) => void;
  toggleAutoRefresh: () => void;
  refreshAssemblies: (options?: { includeParts?: boolean }) => Promise<Assembly[]>;
  getAssembly: (id: string) => Assembly | undefined;
  deleteAssembly: (id: string) => Promise<boolean>;
  updateAssembly: (id: string, data: Partial<Assembly>) => Promise<boolean>;
  createAssembly: (data: Omit<Assembly, '_id'>) => Promise<Assembly | null>;
  duplicateAssembly: (id: string) => Promise<Assembly | null>;
}

const AssembliesContext = createContext<AssembliesContextType | undefined>(undefined);

export function useAssemblies() {
  const context = useContext(AssembliesContext);
  if (context === undefined) {
    throw new Error('useAssemblies must be used within an AssembliesProvider');
  }
  return context;
}

interface AssembliesProviderProps {
  children: React.ReactNode;
}

export function AssembliesProvider({ children }: AssembliesProviderProps) {
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(false);
  const [autoRefreshInterval, setAutoRefreshInterval] = useState(30000); // 30 seconds default

  // Use a ref to store the interval ID so it persists across renders
  const autoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Toggle auto-refresh on/off
   */
  const toggleAutoRefresh = useCallback(() => {
    setIsAutoRefreshEnabled(prev => !prev);
    // The actual interval setup/teardown is handled in the useEffect
  }, []);

  /**
   * Update the auto-refresh interval
   */
  const handleSetAutoRefreshInterval = useCallback((interval: number) => {
    setAutoRefreshInterval(interval);
    // The actual interval reset is handled in the useEffect
  }, []);

  /**
   * Refresh assemblies data from server
   */
  const refreshAssemblies = useCallback(async (options: { includeParts?: boolean } = {}) => {
    // Don't set loading to true if we're doing a background refresh
    // This prevents UI flicker during auto-refresh
    const isBackgroundRefresh = !isLoading;
    const { includeParts = true } = options;

    if (!isBackgroundRefresh) {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Build the URL with query parameters
      const url = new URL('/api/assemblies', window.location.origin);
      url.searchParams.append('includeParts', includeParts ? 'true' : 'false');
      
      console.log(`[AssembliesContext] Fetching assemblies with includeParts=${includeParts}`);

      const response = await fetch(url.toString());

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || 'Failed to fetch assemblies');
      }

      const data = await response.json() as { data?: any[] };
      console.log(`[AssembliesContext] Received ${data.data?.length || 0} assemblies from API`);
      
      // Transform the API data to match the expected schema with better validation
      const transformedAssemblies = (data.data || []).map((assembly: any) => {
        // Validate partsRequired is properly structured and preserve populated Part objects
        let partsRequiredField: { partId: string | Part, quantityRequired: number, unitOfMeasure: string }[] = [];

        if (assembly.partsRequired && Array.isArray(assembly.partsRequired)) {
          partsRequiredField = assembly.partsRequired
            .filter((part: any) => part && typeof part === 'object')
            .map((part: any) => {
              // Preserve the populated Part object if it exists, otherwise use the string ID
              const partIdValue = part.partId || null;

              return {
                partId: partIdValue, // Keep the populated Part object or string ID as-is
                quantityRequired: typeof part.quantityRequired === 'number' ? part.quantityRequired : 1,
                unitOfMeasure: part.unitOfMeasure || 'pcs'
              };
            });
        }
        return {
          ...assembly,
          partsRequired: partsRequiredField, // Validated parts array with preserved Part objects
          isTopLevel: Boolean(assembly.isTopLevel), // Ensure boolean
          status: assembly.status || 'design_complete', // Default status
        } as Assembly; // Cast to Assembly type
      });
      
      setAssemblies(transformedAssemblies);
      setLastUpdated(new Date());

      // Only show success toast for manual refreshes
      if (!isBackgroundRefresh) {
        toast.success('Assemblies refreshed successfully');
      }

      return transformedAssemblies;
    } catch (err) {
      console.error('[AssembliesContext] Error fetching assemblies:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching assemblies');

      // Only show error toast for manual refreshes or if auto-refresh fails with a serious error
      if (!isBackgroundRefresh || (err instanceof Error && err.message !== 'Failed to fetch')) {
        toast.error(err instanceof Error ? err.message : 'Failed to load assemblies');
      }
      return [];
    } finally {
      if (!isBackgroundRefresh) {
        setIsLoading(false);
      }
    }
  }, [isLoading]);

  /**
   * Get a single assembly by ID
   */
  const getAssembly = useCallback((id: string) => {
    return assemblies.find(assembly => assembly._id === id);
  }, [assemblies]);

  /**
   * Delete an assembly
   */
  const deleteAssembly = useCallback(async (id: string) => {
    try {
      // Store original assemblies for rollback if needed
      const originalAssemblies = [...assemblies];

      // Optimistically update UI
      setAssemblies(prev => optimisticDelete(prev, id));

      // Make API call
      const response = await fetch(`/api/assemblies/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        // Rollback optimistic update on error
        setAssemblies(originalAssemblies);
        throw new Error(errorData.error || 'Failed to delete assembly');
      }

      toast.success('Assembly deleted successfully');
      return true;
    } catch (err) {
      console.error('Error deleting assembly:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to delete assembly');
      return false;
    }
  }, [assemblies]);

  /**
   * Update an existing assembly
   */
  const updateAssembly = useCallback(async (id: string, data: Partial<Assembly>) => {
    try {
      // Store original assemblies for rollback if needed
      const originalAssemblies = [...assemblies];
      const originalAssembly = assemblies.find(a => a._id === id);
      
      if (!originalAssembly) {
        toast.error('Assembly not found');
        return false;
      }
      
      // Log update operation
      console.log('[AssembliesContext] Updating assembly:', id, {
        fields: Object.keys(data),
        name: data.name || originalAssembly.name,
        assemblyCode: data.assemblyCode || originalAssembly.assemblyCode
      });

      // Optimistically update UI
      setAssemblies(prev => optimisticUpdateById(prev, id, data));

      // Make API call
      const response = await fetch(`/api/assemblies/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('[AssembliesContext] Update assembly response status:', response.status);

      // Handle non-OK responses
      if (!response.ok) {
        // Get the response text for better error reporting
        const responseText = await response.text();
        let errorMessage = 'Failed to update assembly';
        
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.error('[AssembliesContext] Update assembly error response:', errorData);
        } catch (parseError) {
          console.error('[AssembliesContext] Failed to parse error response:', responseText);
        }
        
        // Rollback optimistic update on error
        setAssemblies(originalAssemblies);
        throw new Error(errorMessage);
      }

      const updatedData = await response.json() as { data: Assembly };
      console.log('[AssembliesContext] Assembly updated successfully:', updatedData.data);

      // Update the assembly in state with the server response
      setAssemblies(prev =>
        prev.map(assembly => (assembly._id === id ? updatedData.data : assembly))
      );

      toast.success('Assembly updated successfully');
      return true;
    } catch (err) {
      console.error('[AssembliesContext] Error updating assembly:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to update assembly');
      return false;
    }
  }, [assemblies]);

  /**
   * Create a new assembly
   */
  const createAssembly = useCallback(async (data: Omit<Assembly, '_id'>) => {
    try {
      // Create a temporary ID for optimistic update
      const tempId = `temp-${Date.now()}`;

      // Create optimistic assembly with temporary ID
      const optimisticAssembly = {
        ...data,
        _id: tempId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as Assembly;

      // Optimistically update UI
      setAssemblies(prev => optimisticAdd(prev, optimisticAssembly));

      // Log the request payload for debugging
      console.log('[AssembliesContext] Creating assembly with data:', {
        assemblyCode: data.assemblyCode,
        name: data.name,
        partsRequired: data.partsRequired?.length || 0,
        status: data.status
      });

      // Make API call
      const response = await fetch('/api/assemblies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('[AssembliesContext] Create assembly response status:', response.status);

      // Handle non-OK responses
      if (!response.ok) {
        // Get the response text for better error reporting
        const responseText = await response.text();
        let errorMessage = 'Failed to create assembly';
        
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.error('[AssembliesContext] Create assembly error response:', errorData);
        } catch (parseError) {
          console.error('[AssembliesContext] Failed to parse error response:', responseText);
        }
        
        // Remove optimistic assembly on error
        setAssemblies(prev => prev.filter(a => a._id !== tempId));
        throw new Error(errorMessage);
      }

      const newAssembly = await response.json() as { data: Assembly };
      console.log('[AssembliesContext] Assembly created successfully:', newAssembly.data);

      // Replace optimistic assembly with actual server response
      setAssemblies(prev =>
        prev.map(assembly =>
          assembly._id === tempId ? newAssembly.data : assembly
        )
      );

      toast.success('Assembly created successfully');
      return newAssembly.data;
    } catch (err) {
      console.error('[AssembliesContext] Error creating assembly:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to create assembly');
      return null;
    }
  }, []);

  /**
   * Duplicate an assembly
   */
  const duplicateAssembly = useCallback(async (id: string) => {
    try {
      // Get original assembly
      const originalAssembly = getAssembly(id);
      
      if (!originalAssembly || !originalAssembly.assemblyCode) { // Added check for assemblyCode
        toast.error('Assembly details or assembly code is missing, cannot duplicate.');
        throw new Error('Assembly not found or assembly code missing');
      }

      // Create a temporary ID for optimistic update
      const tempId = `temp-duplicate-${Date.now()}`;
      
      // Create duplicate assembly data
      const duplicateData = {
        ...originalAssembly,
        _id: tempId, // Will be replaced with server-generated ID
        name: `${originalAssembly.name} (Copy)`,
        assemblyCode: `${originalAssembly.assemblyCode}-COPY`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // Optimistically update UI
      const optimisticAssembly = duplicateData as Assembly;
      setAssemblies(prev => optimisticAdd(prev, optimisticAssembly));
      
      // Make API call
      // Ensure we use assemblyCode for the duplicate endpoint as it expects assemblyCode, not _id
      const response = await fetch(`/api/assemblies/${originalAssembly.assemblyCode}/duplicate`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        // Remove optimistic assembly on error
        setAssemblies(prev => prev.filter(a => a._id !== tempId));
        throw new Error(errorData.error || 'Failed to duplicate assembly');
      }
      
      const result = await response.json() as { data: Assembly };
      
      // Replace optimistic assembly with server response
      setAssemblies(prev =>
        prev.map(assembly =>
          assembly._id === tempId ? result.data : assembly
        )
      );
      
      toast.success('Assembly duplicated successfully');
      return result.data;
    } catch (err) {
      console.error('Error duplicating assembly:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to duplicate assembly');
      return null;
    }
  }, [getAssembly, assemblies]);

  // Initial fetch and set up auto-refresh
  useEffect(() => {
    refreshAssemblies();

    // Clean up any existing interval
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, [refreshAssemblies]);

  // Set up or tear down auto-refresh when enabled state changes
  useEffect(() => {
    // Clean up existing interval first to prevent duplicates
    if (autoRefreshIntervalRef.current) {
      clearInterval(autoRefreshIntervalRef.current);
      autoRefreshIntervalRef.current = null;
    }
    
    // Only set up new interval if auto-refresh is enabled
    if (isAutoRefreshEnabled && autoRefreshInterval > 0) {
      // Use a non-state variable to track the last refresh time to avoid unnecessary renders
      let lastRefreshTime = Date.now();
      
      // Create new interval with the current interval value
      autoRefreshIntervalRef.current = setInterval(() => {
        // Add extra check to prevent too-frequent refreshes in case of state changes
        const now = Date.now();
        if (now - lastRefreshTime >= autoRefreshInterval * 0.9) { // 90% of interval time passed
          refreshAssemblies({ includeParts: false }); // No need for parts in background refresh
          lastRefreshTime = now;
        }
      }, autoRefreshInterval);
    }

    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, [isAutoRefreshEnabled, autoRefreshInterval, refreshAssemblies]);

  const value = {
    assemblies,
    isLoading,
    error,
    lastUpdated,
    isAutoRefreshEnabled,
    autoRefreshInterval,
    setAutoRefreshInterval: handleSetAutoRefreshInterval,
    toggleAutoRefresh,
    refreshAssemblies,
    getAssembly,
    deleteAssembly,
    updateAssembly,
    createAssembly,
    duplicateAssembly,
  };

  return (
    <AssembliesContext.Provider value={value}>
      {children}
    </AssembliesContext.Provider>
  );
}
