'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Assembly, Part } from '@/app/components/tables/AssembliesTable/types';

// Define the initial form state
const initialFormState: Partial<Assembly> = {
  name: '',
  assemblyCode: '',
  status: 'active',
  partsRequired: [],
  productId: null,
  parentId: null,
  isTopLevel: true,
  version: 1,
  manufacturingInstructions: null,
  estimatedBuildTime: null,
};

interface AssemblyFormContextType {
  formData: Partial<Assembly>;
  isLoading: boolean;
  isSaving: boolean;
  isEditing: boolean;
  isDirty: boolean;
  setFormData: (data: Partial<Assembly>) => void;
  updateFormField: (field: string, value: any) => void;
  resetForm: () => void;
  loadAssembly: (id: string) => Promise<void>;
  saveAssembly: () => Promise<boolean>;
  addPart: (part: any) => void;
  updatePart: (index: number, part: any) => void;
  removePart: (index: number) => void;
}

const AssemblyFormContext = createContext<AssemblyFormContextType | undefined>(undefined);

export function useAssemblyForm() {
  const context = useContext(AssemblyFormContext);
  if (context === undefined) {
    throw new Error('useAssemblyForm must be used within an AssemblyFormProvider');
  }
  return context;
}

interface AssemblyFormProviderProps {
  children: React.ReactNode;
  assemblyId?: string;
}

export function AssemblyFormProvider({ children, assemblyId }: AssemblyFormProviderProps) {
  const router = useRouter();
  
  // Use refs to store the latest state values for use in callbacks
  const formDataRef = useRef<Partial<Assembly>>(initialFormState);
  const originalDataRef = useRef<Partial<Assembly>>(initialFormState);
  const isEditingRef = useRef<boolean>(!!assemblyId);

  const [formData, setFormDataState] = useState<Partial<Assembly>>(initialFormState);
  const [originalData, setOriginalData] = useState<Partial<Assembly>>(initialFormState);
  const [isLoading, setIsLoading] = useState(assemblyId ? true : false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(!!assemblyId);

  // Update the refs when the state changes
  useEffect(() => {
    formDataRef.current = formData;
    originalDataRef.current = originalData;
    isEditingRef.current = isEditing;
  }, [formData, originalData, isEditing]);

  // Custom setFormData function that updates both state and ref
  const setFormData = useCallback((data: Partial<Assembly>) => {
    setFormDataState(data);
    formDataRef.current = data;
  }, []);

  // Determine if form has been modified
  const isDirty = JSON.stringify(formData) !== JSON.stringify(originalData);

  /**
   * Update a single form field
   */
  const updateFormField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, [setFormData]);

  /**
   * Reset form to initial state or original data
   */
  const resetForm = useCallback(() => {
    setFormData(isEditingRef.current ? originalDataRef.current : initialFormState);
  }, [setFormData]);

  /**
   * Load assembly data for editing
   */
  const loadAssembly = useCallback(async (id: string) => {
    setIsLoading(true);
    try {
      console.log('Loading assembly with ID:', id);

      // Add includeParts=true parameter to get detailed parts data
      const url = new URL(`/api/assemblies/${id}`, window.location.origin);
      url.searchParams.append('includeParts', 'true');

      // Log the URL being fetched for debugging
      console.log('Fetching from URL:', url.toString());

      const response = await fetch(url.toString());

      console.log('Fetch response status:', response.status);
      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error response:', errorData);
        throw new Error(errorData.error || 'Failed to fetch assembly');
      }

      const data = await response.json();
      console.log('API response data:', data); // Log the full response data

      if (!data.data) {
        console.warn('API response data.data is null or undefined.'); // Log if data.data is missing
        throw new Error('Assembly not found');
      }

      // Process the assembly data to ensure hierarchical structure is preserved
      const assemblyData = data.data;

      // Log the received data for debugging
      console.log('Loaded assembly data:', assemblyData);

      // Initialize partsRequired array if it doesn't exist
      if (!assemblyData.partsRequired) {
        assemblyData.partsRequired = [];
      }

      // Helper function to recursively process part entries for the form data
      const processPartEntryForForm = (entry: any): any => {
        const partDetails = entry.partId; // This is the populated Part object or an ID string
        const isPartDetailsObject = typeof partDetails === 'object' && partDetails !== null;

        return {
          ...entry, // Preserve original fields from the entry (like _id of BOM item, quantityRequired, unitOfMeasure)
          partId: isPartDetailsObject ? partDetails._id?.toString() : (partDetails?.toString() || ''), // Actual Part's _id as string
          name: isPartDetailsObject ? partDetails.name : 'Unknown Part',
          description: isPartDetailsObject ? partDetails.description : '',
          partDisplayIdentifier: isPartDetailsObject
            ? (partDetails.partNumber || partDetails.assemblyCode || partDetails._id) 
            : partDetails, // If partDetails is just an ID string, use it as display
          quantityRequired: Number(entry.quantityRequired || entry.quantity || 1),
          unitOfMeasure: entry.unitOfMeasure || entry.unit_of_measure || 'ea',
          isExpanded: entry.isExpanded !== undefined ? entry.isExpanded : true,
          // Recursively process children
          children: entry.children && Array.isArray(entry.children)
            ? entry.children.map(processPartEntryForForm)
            : [],
        };
      };

      // Convert partsRequired from the API schema format to the format expected by the form
      if (assemblyData.partsRequired && Array.isArray(assemblyData.partsRequired)) {
        console.log('Processing partsRequired array:', assemblyData.partsRequired.length);
        assemblyData.partsRequired = assemblyData.partsRequired.map((part: any) => {
          const actualPartData = part.partId; // This is the populated part object or string ID
          return {
            partId: typeof actualPartData === 'object' ? actualPartData?._id?.toString() : (actualPartData?.toString() || ''),
            partDisplayIdentifier: typeof actualPartData === 'object' ? (actualPartData?.partNumber || actualPartData?.assemblyCode || actualPartData?._id?.toString()) : (actualPartData?.toString() || ''),
            name: typeof actualPartData === 'object' ? (actualPartData?.name || 'Unknown Part') : 'Unknown Part',
            description: typeof actualPartData === 'object' ? (actualPartData?.description || '') : '',
            quantityRequired: Number(part.quantityRequired || 1),
            unitOfMeasure: part.unitOfMeasure || 'ea',
            isExpanded: true,
            children: part.children || []
          };
        });
      }
      // For backward compatibility, check for components field (legacy schema)
      else if (assemblyData.components && Array.isArray(assemblyData.components)) {
        console.log('Processing components (legacy schema):', assemblyData.components.length);
        // Adapt processPartEntryForForm or create a specific one for legacy components if structure differs significantly
        const processLegacyComponentForForm = (component: any): any => {
          // Assuming component.item_id is the part's ID, component.item_name is its name.
          // Legacy might have component.item_part_number or component.item_code for display identifier.
          return {
            ...component, // Preserve original legacy fields
            partId: component.item_id,
            name: component.item_name || 'Unknown Part',
            description: component.item_description || '', // Assuming item_description might exist
            partDisplayIdentifier: component.item_part_number || component.item_code || component.item_id,
            quantityRequired: component.quantity || 1,
            unitOfMeasure: component.unit_of_measure || 'ea',
            isExpanded: component.isExpanded !== undefined ? component.isExpanded : true,
            children: component.children && Array.isArray(component.children)
              ? component.children.map(processLegacyComponentForForm)
              : [],
          };
        };
        assemblyData.partsRequired = assemblyData.components.map((component: any) => {
          // Map from legacy schema to form format using canonical field names
          // Assuming component.item_id is just the ID string and not a populated object here for legacy
          return {
            partId: component.item_id,
            partDisplayIdentifier: component.item_id, // In legacy, item_id might be the only identifier available
            name: component.item_name || 'Unknown Part',
            quantityRequired: component.quantity || 1,
            quantity: component.quantity || 1, // Legacy field for UI compatibility
            unitOfMeasure: component.unit_of_measure || 'ea',
            unit_of_measure: component.unit_of_measure || 'ea', // Legacy field for UI compatibility
            isExpanded: true,
            children: component.children || [],
          };
        });
      }

      // Make sure assemblyCode is set (could be in assembly_id for backward compatibility)
      if (!assemblyData.assemblyCode && assemblyData.assembly_id) {
        assemblyData.assemblyCode = assemblyData.assembly_id;
      }

      setFormData(assemblyData);
      setOriginalData(assemblyData);
      setIsEditing(true);
    } catch (err) {
      console.error('Error loading assembly:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to load assembly');
      // Do not redirect immediately, let the user see the error in the modal if possible
      // router.push('/assemblies'); // Removed immediate redirect
    } finally {
      setIsLoading(false);
    }
  }, []); // Removed router from dependencies, as its usage is in an error path and router object might be unstable

  // Add this function to normalize form data before saving
  const normalizeAssemblyData = (formData: any) => {
    // Create a copy to avoid mutating the original
    const normalizedData = { ...formData };

    // Handle parts required conversion from UI format to DB format using canonical field names
    if (normalizedData.partsRequired && Array.isArray(normalizedData.partsRequired)) {
      normalizedData.partsRequired = normalizedData.partsRequired.map((part: any) => {
        // Ensure partId is a string (ObjectId string)
        const partIdString = typeof part.partId === 'object' && part.partId !== null
          ? part.partId._id?.toString() || part.partId.toString()
          : part.partId?.toString() || '';
    
        return {
          partId: partIdString,
          quantityRequired: Number(part.quantityRequired || 0), // Use canonical quantityRequired
          unitOfMeasure: part.unitOfMeasure || 'ea' // Use canonical unitOfMeasure
        };
      });

      delete normalizedData.components;
    }

    // Map assembly_stage to status if status is not provided (for backward compatibility)
    if (!normalizedData.status && normalizedData.assembly_stage) {
      // Map assembly_stage values to canonical status values
      switch (normalizedData.assembly_stage) {
        case 'Final Assembly':
        case 'FINAL ASSEMBLY':
          normalizedData.status = 'active';
          break;
        case 'Sub-Assembly':
        case 'SUB ASSEMBLY':
          normalizedData.status = 'pending_review';
          break;
        default:
          normalizedData.status = 'active';
      }
    }

    // Remove legacy fields that have been migrated to canonical fields
    if (normalizedData.assembly_id && normalizedData.assemblyCode) {
      delete normalizedData.assembly_id;
    }

    // Ensure version is a number
    if (normalizedData.version && typeof normalizedData.version === 'string') {
      normalizedData.version = parseInt(normalizedData.version, 10) || 1;
    } else if (normalizedData.version === undefined || normalizedData.version === null) {
      normalizedData.version = 1; // Default to version 1 if not set
    }

    // Ensure isTopLevel is a boolean
    if (typeof normalizedData.isTopLevel !== 'boolean') {
      normalizedData.isTopLevel = normalizedData.parentId === null || normalizedData.parentId === undefined;
    }

    // Ensure productId and parentId are null if empty strings, or ObjectId strings if provided
    if (normalizedData.productId === '') {
      normalizedData.productId = null;
    }
    if (normalizedData.parentId === '') {
      normalizedData.parentId = null;
    }

    return normalizedData;
  };

  /**
   * Save assembly (create or update)
   */
  const saveAssembly = useCallback(async () => {
    // Use the ref to get the latest formData
    const currentFormData = formDataRef.current;
    
    // Validate form
    if (!currentFormData.name?.trim()) {
      toast.error('Assembly name is required');
      return false;
    }

    // Check for assembly code
    if (!currentFormData.assemblyCode?.trim()) {
      toast.error('Assembly code is required');
      return false;
    }

    // Validate status is set
    if (!currentFormData.status) {
      toast.error('Assembly status is required');
      return false;
    }

    // Validate that assembly has at least one part
    if (!currentFormData.partsRequired || currentFormData.partsRequired.length === 0) {
      toast.error('Assembly must have at least one part');
      return false;
    }

    // Create a copy of the data to manipulate
    const dataToSave = { ...currentFormData };

    // Ensure status is set to a valid value
    dataToSave.status = dataToSave.status || 'active';

    try {
      setIsSaving(true);

      // Normalize the data before saving
      const normalizedData = normalizeAssemblyData(dataToSave);

      let url = '/api/assemblies';
      let method = 'POST';

      // For edit mode, use PUT with the assembly ID
      if (isEditing && currentFormData._id) {
        url = `/api/assemblies/${currentFormData._id}`;
        method = 'PUT';
        // Remove assemblyCode from the payload for updates
        if (normalizedData.hasOwnProperty('assemblyCode')) {
          delete normalizedData.assemblyCode;
        }
      }

      let response;

      console.log('Saving assembly data (after normalization, before fetch):', JSON.stringify(normalizedData, null, 2));

      // The normalizeAssemblyData function has already prepared partsRequired.
      // The previous block for transforming partsRequired here was redundant
      // and could cause issues, especially with 'children' which are not part of the IAssemblyPart DB schema.

      response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(normalizedData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save assembly');
      }

      const savedData = await response.json();
      console.log('Assembly saved successfully:', savedData);

      toast.success('Assembly updated successfully');

      // Update form data with saved data
      setFormData(savedData.data || savedData);

      return true;
    } catch (error) {
      console.error('Error saving assembly:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save assembly');
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [isEditing]);

  /**
   * Add a part to the assembly
   */
  const addPart = useCallback((part: any) => {
    setFormData(prev => {
      // Always add to partsRequired for consistency in the form
      // The save function will handle converting to components if needed
      return {
        ...prev,
        partsRequired: [...(prev.partsRequired || []), part],
      };
    });
  }, [setFormData]);

  /**
   * Update a part in the assembly
   */
  const updatePart = useCallback((index: number, part: any) => {
    setFormData(prev => {
      const partsRequired = [...(prev.partsRequired || [])];
      partsRequired[index] = part;
      return { ...prev, partsRequired };
    });
  }, [setFormData]);

  /**
   * Remove a part from the assembly
   */
  const removePart = useCallback((index: number) => {
    setFormData(prev => {
      const partsRequired = [...(prev.partsRequired || [])];
      partsRequired.splice(index, 1);
      return { ...prev, partsRequired };
    });
  }, [setFormData]);

  // Load assembly data if editing
  useEffect(() => {
    if (assemblyId) {
      console.log('Loading assembly with ID:', assemblyId);
      loadAssembly(assemblyId);
    }
  }, [assemblyId, loadAssembly]);

  // Debug log for tracking form data changes
  useEffect(() => {
    if (isEditing) {
      console.log('Form data updated:', formData);
    }
  }, [formData, isEditing]);

  const value = {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    setFormData,
    updateFormField,
    resetForm,
    loadAssembly,
    saveAssembly,
    addPart,
    updatePart,
    removePart,
  };

  return (
    <AssemblyFormContext.Provider value={value}>
      {children}
    </AssemblyFormContext.Provider>
  );
}
