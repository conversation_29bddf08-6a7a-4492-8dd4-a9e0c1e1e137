"use client";

import { ObjectId } from 'mongodb';

export interface Product {
  _id: string;
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  assemblyId?: string | null;
  partId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface StockStatus {
  lowStock: number;
  outOfStock: number;
  overstock: number;
  total: number;
}

// Legacy AssemblyItem interface kept for backward compatibility
export interface AssemblyItem {
  id: string;
  name: string;
  stage: string;
  parts: {
    id: string;
    name: string;
    quantity: number;
    inStock: number;
  }[];
}

// New Assembly interface based on updated schema
export interface Assembly {
  // Required fields
  _id: string | ObjectId;             // MongoDB ObjectId (Primary Key)
  id?: string;                        // For frontend compatibility
  assemblyCode: string;               // Unique business code for the assembly (e.g., "ASM-TA-100")
  name: string;                       // Name of the assembly

  // References
  productId?: string | ObjectId | null;  // Reference to products._id
  parentId?: string | ObjectId | null;   // Reference to another assemblies._id if this is a sub-assembly

  // Flags
  isTopLevel: boolean;                // Indicates if this is a top-level assembly

  // Components
  partsRequired: Array<{
    partId: string | ObjectId;        // Reference to parts._id
    quantityRequired: number;         // Quantity needed
    unitOfMeasure: string;            // Should match the UoM of the referenced part
  }>;

  // Status and version
  status: string;                     // Assembly definition status (e.g., "active", "pending_review", "obsolete")
  version: number;                    // Version number of the assembly definition

  // Manufacturing details
  manufacturingInstructions?: string | null; // Link to or text of manufacturing SOPs
  estimatedBuildTime?: string | null;        // e.g., "1.5 hours", "30 minutes"

  // Optional fields
  description?: string | null;        // Description of the assembly

  // Timestamps
  createdAt: string | Date;           // Timestamp of assembly definition creation
  updatedAt: string | Date;           // Timestamp of last assembly definition update
}

export interface BOMItem {
  parent_id: string;
  child_id: string;
  quantity: number;
  level?: number;
  position?: string | null;
  notes?: string | null;
  parent?: Product;
  child?: Product;
}

/**
 * Represents the structure of a document in the 'inventory_transactions' collection.
 * Aligned with the updated schema.
 */
export interface InventoryTransaction {
  _id: string;
  partId: string; // Reference to parts._id
  warehouseId: string; // Reference to warehouses._id
  transactionType: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment';
  quantity: number;
  previousStock: number;
  newStock: number;
  transactionDate: string;
  referenceNumber?: string | null;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | null;
  userId: string;
  notes?: string | null;
  createdAt: string;
  updatedAt?: string;
  // Populated fields
  part?: Product;
  user?: any; // Replaced with proper import
}

/**
 * Represents the structure of a document in the 'warehouses' collection.
 * Aligned with the updated schema.
 */
export interface Warehouse {
  _id: string;
  location_id: string; // Unique business identifier
  name: string;
  location: string;
  capacity: number;
  manager: string;
  contact: string;
  isBinTracked: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Batch {
  id: string;
  partId: string;
  part?: {
    name: string;
  };
  quantity: number;
  manufacturingDate: string;
  expiryDate?: string;
  supplierBatch?: string;
  qualityStatus: 'pending' | 'approved' | 'rejected' | string;
  notes?: string;
}

/**
 * Represents the structure of a document in the 'parts' collection.
 * Aligned with the updated schema.
 */
export interface Part {
  _id: string;
  partNumber: string; // Unique business identifier
  name: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'obsolete' | 'in_development' | 'pending_approval' | 'inactive';
  inventory: {
    currentStock: number;
    warehouseId: string;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Represents the structure of a document in the 'inventory_levels' collection.
 * Aligned with the updated schema.
 */
export interface InventoryLevelRecord {
  _id: string;
  item_id: string; // Reference to parts._id, assemblies._id, or products._id
  item_type: 'Part' | 'Assembly' | 'Product';
  warehouse_id: string; // Reference to warehouses._id
  quantity_on_hand: number;
  quantity_allocated: number;
  quantity_available?: number; // Virtual field
  location_in_warehouse?: string | null;
  reorder_level?: number | null;
  safety_stock_level?: number | null;
  maximum_stock_level?: number | null;
  average_daily_usage?: number | null;
  abc_classification?: string | null;
  last_stock_update: string; // ISO date string
  notes?: string | null;
  createdAt: string;
  updatedAt: string;
}

// Type definitions for inventory management system
export type InventoryLevel = 'low' | 'normal' | 'high';
export type TransactionType = 'receive' | 'issue' | 'adjust' | 'transfer';
export type QualityStatus = 'pending' | 'approved' | 'rejected' | 'quarantine';
export type AssemblyStage = 'Product' | 'Sub-Assembly' | 'Component';

// ProductTable specific props
export interface ProductTableProps {
  products: Product[];
  onEditProduct?: (product: Product) => void;
  onDeleteProduct?: (id: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}
