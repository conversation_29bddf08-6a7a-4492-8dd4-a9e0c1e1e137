// Part and inventory related types based on MongoDB schema

/**
 * Represents the structure of the inventory object embedded within a Part document.
 * Exactly matches the MongoDB schema definition.
 */
export interface PartInventoryData {
  currentStock: number;
  warehouseId: string;
  safetyStockLevel: number;
  maximumStockLevel: number;
  averageDailyUsage: number;
  abcClassification: string;
  lastStockUpdate?: Date | null;
}

/**
 * Represents a sub-part within an assembly
 */
export interface SubPart {
  partId: string;
  quantity: number;
}

/**
 * Represents the complete structure of a document in the 'parts' collection.
 * Matches the MongoDB schema defined in part.model.ts
 */
export interface PartDocument {
  _id: string;
  partNumber: string;
  name: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel: number | null;
  status: string;
  inventory: PartInventoryData;
  isAssembly: boolean;
  subParts?: SubPart[];
  schemaVersion: number;
  supplierId?: string;
  unitOfMeasure: string;
  costPrice?: number;
  categoryId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Simplified Part interface for basic usage
 * Updated to include top-level fields used in the InventoryTable
 */
export interface Part {
  _id: string;
  partNumber: string;
  name: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel: number | null;
  status: string;
  inventory: PartInventoryData;

  // Added for InventoryTable display
  currentStock?: number;
  supplierName?: string | null;
}

/**
 * Represents the data structure used in forms for creating/updating parts.
 * Aligns exactly with the MongoDB schema structure using camelCase.
 */
export interface PartFormData {
  _id?: string;
  name: string;
  partNumber: string;
  description?: string;
  technicalSpecs?: string;
  isManufactured: boolean;
  reorderLevel: number | null;
  status: string;
  inventory: {
    currentStock: number;
    warehouseId: string;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null;
  };
  supplierId?: string;
  unitOfMeasure: string;
  costPrice?: number;
  categoryId?: string;
  isAssembly?: boolean;
  subParts?: SubPart[];
  schemaVersion?: number;
}
