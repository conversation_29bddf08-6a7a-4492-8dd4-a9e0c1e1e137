"use client";

/**
 * Represents the structure of a document in the 'users' collection.
 * Aligned with the updated schema.
 */
export interface User {
  _id:       string;
  username:  string;
  email:     string;
  fullName:  string;
  role:      'admin' | 'manager' | 'staff' | 'viewer'; // or just string
  isActive:  boolean;
  createdAt: string;   // <— not Date
  updatedAt: string;   // <— probably want this too
}

/**
 * Represents the structure of a document in the 'suppliers' collection.
 * Aligned with the updated schema.
 */
export interface Supplier {
  _id:           string;
  supplier_id:   string;
  name:          string;
  contactPerson: string;
  email:         string;
  phone:         string;
  address:       string;
  specialty:     string[];
  rating:        number | null;
  payment_terms: string | null;
  delivery_terms:string | null;
  is_active:     boolean;
  createdAt:     string;  // <— ISO date string
  updatedAt:     string;  // <— likewise
}

// Type definitions for user management
// Note: DB schema uses 'string' for role, but these specific roles might be used in UI logic.
export type UserRole = 'admin' | 'manager' | 'employee' | 'viewer';
