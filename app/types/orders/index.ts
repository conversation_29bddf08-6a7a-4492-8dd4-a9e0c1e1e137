"use client";

import { Product } from '../inventory'; // Product is not used in this file, consider removing if not needed elsewhere via re-export
import { User } from '../user';
import { Supplier } from '../user'; // Assuming Supplier type is correctly defined elsewhere
import { Part } from '../inventory'; // Import Part if needed for POLineItem population
// Removed duplicate User import

export interface OrderStatusCount {
  pending: number;
  processing: number;
  shipped: number;
  delivered: number;
  total: number;
}

export interface LogisticsInfo {
  inTransit: number;
  delivered: number;
  delayed: number;
  total: number;
  delayedItems?: Product[];
  delayDuration?: string;
  status?: string;
}

/**
 * Represents the structure of an item within the 'items' array of a PurchaseOrder.
 * Aligned with the updated purchaseorders schema.
 */
export interface POLineItem {
  partId: string; // Reference to the Part._id
  quantity: number; // Quantity ordered
  unitPrice: number; // Price per unit
  // Optional: Populated part data if needed in UI
  part?: Part; // Use the updated Part type from inventory
}

/**
 * Represents the structure of a document in the 'purchaseorders' collection.
 * Aligned with the updated schema.
 */
export interface PurchaseOrder {
  _id: string; // Represents ObjectId
  poNumber: string;
  supplierId: string; // Represents ObjectId
  orderDate: Date;
  expectedDeliveryDate: Date;
  items: POLineItem[]; // Array of embedded line items
  totalAmount: number; // Represents Int32
  status: string; // Consider using a specific string literal union type if known values
  notes?: string | null;
  createdBy: string; // Represents ObjectId
  createdAt: Date;
  // Optional: Populated supplier data if needed in UI
  supplier?: Supplier;
}


/**
 * Represents the structure of a document in the 'workorders' collection.
 * Aligned with the updated schema.
 */
export interface WorkOrder {
  _id: string; // Represents ObjectId
  woNumber: string;
  assemblyId?: string | null; // Represents ObjectId
  partIdToManufacture?: string; // Represents Part._id
  productId?: string | null; // Represents ObjectId
  quantity: number; // Represents Int32
  status: string; // Consider using a specific string literal union type if known values
  priority: string; // Consider using a specific string literal union type if known values
  assignedTo: string; // Represents ObjectId
  completedAt?: Date | null;
  createdAt: Date;
  notes?: string | null;
  // Optional: Populated data if needed in UI
  assignee?: User;
  // Removed 'items' array and related WorkOrderItem interface
  // Removed 'description', 'startDate', 'endDate', 'createdBy'
}

// Removed WorkOrderItem interface as it's not in the new schema

// Type definitions for general status values (review if still accurate/needed)
// Consider defining more specific status types for PO and WO if needed
export type OrderStatus = 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled'; // Review usage
// Removed PaymentStatus as it's deprecated from PurchaseOrder schema
