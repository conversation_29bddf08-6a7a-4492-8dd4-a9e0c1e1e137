"use client";

// Removed unused import: import { AssemblyStage } from '../inventory';

/**
 * Represents a potentially combined data structure for forms,
 * aligned with the updated schemas. Fields are prefixed where necessary
 * to avoid naming conflicts in this flattened structure.
 * Consider creating more specific form types if possible.
 */
export interface FormData {
  // Part data (aligned with new Part schema)
  _id?: string; // Optional for creation forms, replaces partId
  name: string;
  description?: string | null;
  technical_specs?: string | null; // Aligned name
  is_manufactured?: boolean; // Aligned name, required in schema but optional here for forms?
  reorder_level?: number | null; // Top-level field on Part schema
  status?: string; // Added from Part schema

  // Removed deprecated Part fields: partId, isExternal, supplierId, barcodeSku, manufacturerPartNumber, unitOfMeasure, minimumOrderQuantity, leadTimeDays

  // Inventory data (aligned with PartInventoryData, prefixed)
  inventory_current_stock?: number; // Prefixed
  inventory_location?: string | null; // Prefixed
  inventory_safety_stock_level?: number; // Prefixed
  inventory_maximum_stock_level?: number; // Prefixed

  // Removed deprecated/unmatched inventory fields: reorderLevel (moved to Part), unitCost, lastInventoryCountDate, averageDailyUsage, abcClassification

  // Assembly data (aligned with new Assembly schema)
  assemblyCode?: string | null; // If form handles assemblies

  // Removed deprecated Assembly fields: assemblyStage
  // Removed childParts array (concept moved to Assembly schema's partsRequired)

  // Supplier specific data (aligned with new Supplier schema, prefixed)
  supplier_id?: string; // Added from Supplier schema
  supplier_name?: string | null; // Maps to Supplier 'name'
  supplier_contactPerson?: string | null; // Prefixed
  supplier_phone?: string | null; // Prefixed
  supplier_email?: string | null; // Prefixed
  supplier_address?: string | null; // Prefixed
  supplier_specialty?: string[]; // Added from Supplier schema
  supplier_rating?: number | null; // Renamed from performanceRating & prefixed

  // Removed deprecated Supplier fields: paymentTerms, deliveryTerms, performanceRating
}
