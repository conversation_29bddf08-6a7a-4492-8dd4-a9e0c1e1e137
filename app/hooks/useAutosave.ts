"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';
import debounce from 'lodash/debounce';

export type AutosaveStatus = 'idle' | 'saving' | 'saved' | 'error';

export interface UseAutosaveOptions<T> {
  /**
   * The form instance from react-hook-form
   */
  form: UseFormReturn<T>;
  
  /**
   * Function to save the form data
   */
  onSave: (data: T) => Promise<void>;
  
  /**
   * Debounce delay in milliseconds
   * @default 1000
   */
  delay?: number;
  
  /**
   * Whether to enable autosave
   * @default true
   */
  enabled?: boolean;
  
  /**
   * Key to use for localStorage backup
   * If not provided, autosave will not use localStorage
   */
  localStorageKey?: string;
  
  /**
   * Minimum number of fields that need to be dirty before autosave triggers
   * @default 1
   */
  minDirtyFields?: number;
}

/**
 * Hook for implementing autosave functionality with react-hook-form
 */
export function useAutosave<T extends Record<string, any>>({
  form,
  onSave,
  delay = 1000,
  enabled = true,
  localStorageKey,
  minDirtyFields = 1
}: UseAutosaveOptions<T>) {
  const [status, setStatus] = useState<AutosaveStatus>('idle');
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<Error | null>(null);
  
  // Create a ref for the save function to avoid recreating the debounced function
  const saveRef = useRef(onSave);
  saveRef.current = onSave;
  
  // Create a debounced save function
  const debouncedSave = useCallback(
    debounce(async (data: T) => {
      if (!enabled) return;
      
      try {
        setStatus('saving');
        await saveRef.current(data);
        setStatus('saved');
        setLastSaved(new Date());
        setError(null);
        
        // Save to localStorage if key is provided
        if (localStorageKey) {
          localStorage.setItem(localStorageKey, JSON.stringify(data));
        }
      } catch (err) {
        setStatus('error');
        setError(err instanceof Error ? err : new Error('Failed to save'));
        console.error('Autosave error:', err);
      }
    }, delay),
    [delay, enabled, localStorageKey]
  );
  
  // Watch form values and trigger autosave when they change
  useEffect(() => {
    if (!enabled) return;
    
    const subscription = form.watch((formData, { name, type }) => {
      // Only trigger autosave if the form is dirty and has enough dirty fields
      const dirtyFieldsCount = Object.keys(form.formState.dirtyFields).length;
      
      if (
        type === 'change' && 
        form.formState.isDirty && 
        dirtyFieldsCount >= minDirtyFields
      ) {
        debouncedSave(formData as T);
      }
    });
    
    // Cleanup subscription
    return () => subscription.unsubscribe();
  }, [form, debouncedSave, enabled, minDirtyFields]);
  
  // Load from localStorage on mount if key is provided
  useEffect(() => {
    if (!localStorageKey || !enabled) return;
    
    try {
      const savedData = localStorage.getItem(localStorageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        form.reset(parsedData);
      }
    } catch (err) {
      console.error('Error loading autosaved data:', err);
    }
  }, [form, localStorageKey, enabled]);
  
  // Cleanup localStorage on unmount
  useEffect(() => {
    return () => {
      debouncedSave.cancel();
    };
  }, [debouncedSave]);
  
  // Function to manually trigger save
  const save = useCallback(async () => {
    const data = form.getValues();
    try {
      setStatus('saving');
      await saveRef.current(data as T);
      setStatus('saved');
      setLastSaved(new Date());
      setError(null);
      
      // Save to localStorage if key is provided
      if (localStorageKey) {
        localStorage.setItem(localStorageKey, JSON.stringify(data));
      }
    } catch (err) {
      setStatus('error');
      setError(err instanceof Error ? err : new Error('Failed to save'));
      console.error('Manual save error:', err);
    }
  }, [form, localStorageKey]);
  
  // Function to clear autosaved data
  const clearAutosaved = useCallback(() => {
    if (localStorageKey) {
      localStorage.removeItem(localStorageKey);
    }
  }, [localStorageKey]);
  
  return {
    status,
    lastSaved,
    error,
    save,
    clearAutosaved,
    isAutosaving: status === 'saving',
  };
}
