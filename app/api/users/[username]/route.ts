import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getUser, updateUser, deleteUser, handleMongoDBError } from '@/app/services/mongodb';
import bcrypt from 'bcryptjs';

interface RouteParams {
  username: string; // This 'username' from the route corresponds to the username field
}

/**
 * GET handler for fetching a specific user by its username
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the username
 * @returns JSON response with user data or error
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const username = params.username; // The unique string identifier (username)
  try {
    console.log(`[API] GET /api/users/${username} - Fetching user`);

    // Call the service function to get the user
    const user = await getUser(username);

    const duration = Date.now() - startTime;

    if (!user) {
      console.log(`[API] User ${username} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `User with username ${username} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched user ${username} successfully (${duration}ms)`);
    return NextResponse.json({ data: user, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching user ${username} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific user by its username
 * @param request - The incoming request with updated user data
 * @param params - Route parameters including the username
 * @returns JSON response with updated user data or error
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const username = params.username;
  try {
    console.log(`[API] PUT /api/users/${username} - Updating user`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // If password is being updated, hash it
    if (updateData.password) {
      const salt = await bcrypt.genSalt(10);
      updateData.passwordHash = await bcrypt.hash(updateData.password, salt);
      // Remove the plain text password
      delete updateData.password;
    }

    // Call the service function to update the user
    const updatedUser = await updateUser(username, updateData);

    // Remove passwordHash from response
    const userResponse = {
      ...updatedUser,
      passwordHash: undefined
    };

    const duration = Date.now() - startTime;
    console.log(`[API] Updated user ${username} successfully (${duration}ms)`);
    return NextResponse.json({ data: userResponse, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating user ${username} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific user by its username
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the username
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const username = params.username;
  try {
    console.log(`[API] DELETE /api/users/${username} - Deleting user`);

    // Call the service function to delete the user
    const result = await deleteUser(username);

    const duration = Date.now() - startTime;
    console.log(`[API] Deleted user ${username} successfully (${duration}ms)`);
    return NextResponse.json({ 
      data: result, 
      error: null, 
      meta: { duration } 
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting user ${username} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
