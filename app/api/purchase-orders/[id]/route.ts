import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getPurchaseOrder, updatePurchaseOrder, deletePurchaseOrder, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the poNumber field
}

/**
 * GET handler for fetching a specific purchase order by its poNumber
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the PO Number
 * @returns JSON response with purchase order data or error
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const poNumber = params.id; // The unique string identifier (poNumber)
  try {
    console.log(`[API] GET /api/purchase-orders/${poNumber} - Fetching purchase order`);

    // Call the service function to get the purchase order
    // Note: getPurchaseOrder service function expects the poNumber
    const purchaseOrder = await getPurchaseOrder(poNumber);

    const duration = Date.now() - startTime;

    if (!purchaseOrder) {
      console.log(`[API] Purchase Order ${poNumber} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Purchase Order with number ${poNumber} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched purchase order ${poNumber} successfully (${duration}ms)`);
    return NextResponse.json({ data: purchaseOrder, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching purchase order ${poNumber} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific purchase order by its poNumber
 * @param request - The incoming request with updated PO data
 * @param params - Route parameters including the PO Number
 * @returns JSON response with updated PO data or error
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const poNumber = params.id;
  try {
    console.log(`[API] PUT /api/purchase-orders/${poNumber} - Updating purchase order`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // Call the service function to update the purchase order
    // Note: updatePurchaseOrder service function expects the poNumber
    const updatedPurchaseOrder = await updatePurchaseOrder(poNumber, updateData);

    const duration = Date.now() - startTime;

    console.log(`[API] Updated purchase order ${poNumber} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedPurchaseOrder, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating purchase order ${poNumber} (${duration}ms):`, error);
    let { message, status } = { message: 'An unknown error occurred', status: 500 };
    if (error instanceof Error) {
        message = error.message;
        if (message.includes('not found')) {
            status = 404;
        } else {
             try {
                handleMongoDBError(error); // Call handler, it might throw standardized error
            } catch (handlerError: any) {
                message = handlerError.message || message;
                status = 500;
            }
        }
    }
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific purchase order by its poNumber
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the PO Number
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const poNumber = params.id;
  try {
    console.log(`[API] DELETE /api/purchase-orders/${poNumber} - Deleting purchase order`);

    // Call the service function to delete the purchase order
    // Note: deletePurchaseOrder service function expects the poNumber
    const result = await deletePurchaseOrder(poNumber);

    const duration = Date.now() - startTime;

    const successMessage = result?.message || `Purchase Order ${poNumber} deleted successfully`;
    console.log(`[API] Deleted purchase order ${poNumber} successfully (${duration}ms)`);
    return NextResponse.json({ success: true, message: successMessage, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting purchase order ${poNumber} (${duration}ms):`, error);
    let { message, status } = { message: 'An unknown error occurred', status: 500 };
     if (error instanceof Error) {
        message = error.message;
        if (message.includes('not found')) {
            status = 404;
        } else {
             try {
                handleMongoDBError(error); // Call handler, it might throw standardized error
            } catch (handlerError: any) {
                message = handlerError.message || message;
                status = 500;
            }
        }
    }
    return NextResponse.json(
      { success: false, error: message, meta: { duration } },
      { status }
    );
  }
}
