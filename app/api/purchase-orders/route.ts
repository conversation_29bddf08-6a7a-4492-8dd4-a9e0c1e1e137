import { NextRequest, NextResponse } from 'next/server';
// Import specific service functions and error handler
import { fetchPurchaseOrders, createPurchaseOrder, handleMongoDBError } from '@/app/services/mongodb';

// Maximum allowed limit per request
const MAX_LIMIT = 500;

/**
 * GET handler for fetching purchase orders with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with purchase orders data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/purchase-orders - Fetching purchase orders');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'orderDate'; // Default sort
    const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object ---
    const filter: any = {};
    const status = url.searchParams.get('status');
    const supplierId = url.searchParams.get('supplierId'); // Filter by supplier ObjectId
    const poNumber = url.searchParams.get('poNumber'); // Filter by PO Number

    if (status) filter.status = status;
    if (supplierId) filter.supplier_id = supplierId; // Use supplier_id based on model
    if (poNumber) filter.poNumber = new RegExp(poNumber, 'i'); // Case-insensitive search for PO number

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling fetchPurchaseOrders service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await fetchPurchaseOrders(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchPurchaseOrders completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.purchaseOrders,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/purchase-orders (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new purchase order
 * @param request - The incoming request with purchase order data
 * @returns JSON response with the newly created purchase order
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/purchase-orders - Creating new purchase order');
    const purchaseOrderData = await request.json();

    // Basic validation
    if (!purchaseOrderData || typeof purchaseOrderData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid purchase order data provided' }, { status: 400 });
    }
    // Add checks for required fields based on schema (e.g., supplier_id, items, createdBy)
    if (!purchaseOrderData.supplier_id || !purchaseOrderData.items || !purchaseOrderData.createdBy) {
        return NextResponse.json({ data: null, error: 'Missing required fields (supplier_id, items, createdBy)' }, { status: 400 });
    }

    console.log(`[API] Calling createPurchaseOrder service with data: ${JSON.stringify(purchaseOrderData)}`);

    // Call the createPurchaseOrder service function (using the correct name now)
    const newPurchaseOrder = await createPurchaseOrder(purchaseOrderData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service createPurchaseOrder completed in ${duration}ms`);

    return NextResponse.json({ data: newPurchaseOrder, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/purchase-orders (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
