// File: app/api/parts/route.ts

import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { fetchParts, addPart, handleMongoDBError } from '@/app/services/mongodb';
import withDatabase from '@/app/middlewares/withDatabase';

const MAX_LIMIT = 500;

async function handleGET(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[API] GET /api/parts - Fetching parts');
    const url = new URL(request.url);

    // Pagination
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = Math.min(
      parseInt(url.searchParams.get('limit') || '20', 10),
      MAX_LIMIT
    );

    // Sorting
    const sortField = url.searchParams.get('sortField') || 'updatedAt';
    const sortOrder = (url.searchParams.get('sortOrder') === 'asc') ? 1 : -1;

    // Includes
    const includeSubParts   = url.searchParams.get('includeSubParts')   === 'true';
    const includeInventory  = url.searchParams.get('includeInventory')  === 'true';

    // Filters
    const filter: any = {};
    if (url.searchParams.get('status')) {
      filter.status = url.searchParams.get('status');
    }
    if (url.searchParams.get('isManufactured') !== null) {
      filter.isManufactured =
        url.searchParams.get('isManufactured') === 'true';
    }
    if (url.searchParams.get('categoryId')) {
      const cat = url.searchParams.get('categoryId')!;
      try {
        filter.categoryId = new mongoose.Types.ObjectId(cat);
      } catch (err) {
        console.error(`Invalid categoryId: ${cat}`, err);
        filter.categoryId = cat;
      }
    }
    // Stock range
    if (
      url.searchParams.has('minStock') ||
      url.searchParams.has('maxStock')
    ) {
      filter['inventory.currentStock'] = {};
      if (url.searchParams.get('minStock')) {
        filter['inventory.currentStock'].$gte = parseInt(
          url.searchParams.get('minStock')!,
          10
        );
      }
      if (url.searchParams.get('maxStock')) {
        filter['inventory.currentStock'].$lte = parseInt(
          url.searchParams.get('maxStock')!,
          10
        );
      }
    }

    // Pack options and call service
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
      includeSubParts,
      includeInventory: true, // Always include inventory in API responses
    };
    console.log('[API] fetchParts options:', options);

    const result = await fetchParts(options);

    const duration = Date.now() - startTime;
    console.log(`[API] GET completed in ${duration}ms`);

    return NextResponse.json({
      data: result?.parts || [],
      pagination: result?.pagination || {},
      error: null,
      meta: { duration },
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] GET error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

async function handlePOST(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[API] POST /api/parts - Creating new part');
    const partData = await request.json();

    // --- Top-level validation ---
    if (!partData || typeof partData !== 'object') {
      return NextResponse.json(
        { data: null, error: 'Invalid part data provided' },
        { status: 400 }
      );
    }
    if (!partData.name || typeof partData.name !== 'string') {
      return NextResponse.json(
        { data: null, error: 'Missing or invalid required field: name' },
        { status: 400 }
      );
    }

    // Optional camelCase fields
    if (
      partData.description != null &&
      typeof partData.description !== 'string'
    ) {
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid field type: description must be a string or null',
        },
        { status: 400 }
      );
    }
    if (
      partData.technicalSpecs != null &&
      typeof partData.technicalSpecs !== 'string'
    ) {
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid field type: technicalSpecs must be a string or null',
        },
        { status: 400 }
      );
    }
    if (
      partData.isManufactured != null &&
      typeof partData.isManufactured !== 'boolean'
    ) {
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid field type: isManufactured must be a boolean',
        },
        { status: 400 }
      );
    }
    if (
      partData.reorderLevel != null &&
      typeof partData.reorderLevel !== 'number'
    ) {
      return NextResponse.json(
        {
          data: null,
          error:
            'Invalid field type: reorderLevel must be a number or null',
        },
        { status: 400 }
      );
    }
    if (
      partData.status != null &&
      typeof partData.status !== 'string'
    ) {
      return NextResponse.json(
        { data: null, error: 'Invalid field type: status must be a string' },
        { status: 400 }
      );
    }
    if (
      partData.isAssembly != null &&
      typeof partData.isAssembly !== 'boolean'
    ) {
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid field type: isAssembly must be a boolean',
        },
        { status: 400 }
      );
    }

    // --- Inventory validation ---
    if (!partData.inventory || typeof partData.inventory !== 'object') {
      return NextResponse.json(
        {
          data: null,
          error: 'Inventory data is required and must be an object.',
        },
        { status: 400 }
      );
    }

    const requiredInventoryFields = [
      'currentStock',
      'warehouseId',
      'safetyStockLevel',
      'maximumStockLevel',
      'averageDailyUsage',
      'abcClassification',
    ] as const;

    for (const field of requiredInventoryFields) {
      if (partData.inventory[field] == null) {
        return NextResponse.json(
          {
            data: null,
            error: `Missing required inventory field: ${field}`,
          },
          { status: 400 }
        );
      }
    }

    if (typeof partData.inventory.currentStock !== 'number') {
      return NextResponse.json(
        {
          data: null,
          error:
            'Invalid field type: inventory.currentStock must be a number',
        },
        { status: 400 }
      );
    }

    console.log('[API] addPart with data:', partData);
    const savedPart = await addPart(partData);

    const duration = Date.now() - startTime;
    console.log(`[API] POST completed in ${duration}ms`);

    return NextResponse.json(
      { data: savedPart, error: null, meta: { duration } },
      { status: 201 }
    );
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] POST error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

export const GET  = withDatabase(handleGET);
export const POST = withDatabase(handlePOST);