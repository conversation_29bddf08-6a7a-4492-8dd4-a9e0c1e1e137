import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { searchParts, handleMongoDBError } from '@/app/services/mongodb';
// Remove direct model/db connection imports and response helpers if service handles response structure

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 100; // Keep limit enforcement at API level

/**
 * GET handler for searching parts with pagination
 * This endpoint supports full-text search across all parts
 * @param request - The incoming request with search parameters
 * @returns JSON response with matching parts and pagination data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/parts/search - Searching parts');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const query = url.searchParams.get('search') || ''; // General search term
    const sortField = url.searchParams.get('sortField') || 'updatedAt';
    const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object (Aligned with New Schema) ---
    const filter: any = {};
    const status = url.searchParams.get('status');
    const isManufactured = url.searchParams.get('is_manufactured');
    const minStock = url.searchParams.get('minStock');
    const maxStock = url.searchParams.get('maxStock');
    // Removed outdated filters: description, supplier, stockStatus, excludeAssemblies, onlyAssemblies, category

    if (status) filter.status = status;
    if (isManufactured !== null) filter.is_manufactured = isManufactured === 'true';

    // Filter by nested inventory field
    if (minStock !== null || maxStock !== null) {
      filter['inventory.currentStock'] = {}; // Use dot notation
      if (minStock !== null) filter['inventory.currentStock'].$gte = parseInt(minStock, 10);
      if (maxStock !== null) filter['inventory.currentStock'].$lte = parseInt(maxStock, 10);
    }

    // --- Prepare Options for Service Function ---
    const options = {
      query, // Pass the general search query
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter, // Pass the specific field filters
    };

    console.log(`[API] Calling searchParts service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await searchParts(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service searchParts completed in ${duration}ms`);

    // --- Return Response ---
    // Use NextResponse directly, assuming service returns data in expected format
    return NextResponse.json({
      data: result?.parts,
      pagination: result?.pagination,
      error: null,
      meta: { duration, query, filter, sort: options.sort } // Include search context in meta
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/parts/search (${duration}ms):`, error);
    // Use imported error handler
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
