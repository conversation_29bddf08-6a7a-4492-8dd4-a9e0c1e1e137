import { NextRequest, NextResponse } from 'next/server';
import * as mongodbService from '@/app/services/mongodb';
import mongoose from 'mongoose';

/**
 * GET handler for fetching a specific part by ID
 * @param request - The incoming request
 * @param params - Route parameters including the part ID
 * @returns JSON response with part data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Decode the part ID to handle URL-encoded special characters
    const partId = decodeURIComponent(params.id);
    console.log(`[API] GET /api/parts/${partId} - Fetching part (decoded from URL parameter)`);
    console.log(`[API] Original URL-encoded part ID: ${params.id}`);
    console.log(`[API] Decoded part ID: ${partId}`);

    const url = new URL(request.url);

    // Check if we should include subParts
    const includeSubParts = url.searchParams.get('includeSubParts') === 'true'; // Changed from includeSub_parts

    // Pass the options to the getPart function
    const part = await mongodbService.getPart(partId, { includeSubParts }); // Changed from includeSub_parts

    if (!part) {
      return NextResponse.json(
        { data: null, error: `Part with ID ${partId} not found` },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: part, error: null });
  } catch (error) {
    console.error(`Error fetching part ${params.id}:`, error);
    return NextResponse.json(
      {
        data: null,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a specific part by ID
 * @param request - The incoming request with updated part data
 * @param params - Route parameters including the part ID
 * @returns JSON response with updated part data
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const partId = decodeURIComponent(params.id);
    console.log(`[API] PUT /api/parts/${partId} - Updating part`);

    const updateData = await request.json();

    if (!updateData || Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { data: null, error: 'Update data is required and cannot be empty' },
        { status: 400 }
      );
    }

    // Validate ObjectId fields if they are present and not empty strings
    const objectIdFieldsToValidate: { fieldName: string, fieldValue: any }[] = [
      { fieldName: 'categoryId', fieldValue: updateData.categoryId },
      { fieldName: 'supplierId', fieldValue: updateData.supplierId },
      // Add other potential ObjectId fields from partData here if necessary
    ];

    for (const { fieldName, fieldValue } of objectIdFieldsToValidate) {
      if (fieldValue && typeof fieldValue === 'string' && fieldValue.trim() !== '') {
        if (!mongoose.Types.ObjectId.isValid(fieldValue)) {
          return NextResponse.json(
            { data: null, error: `Invalid ${fieldName} format: Must be a valid ObjectId string.` },
            { status: 400 }
          );
        }
      } else if (fieldValue === '') {
        // Allow empty strings to pass through, assuming backend handles clearing the reference.
      }
    }

    // Validate inventory fields if inventory is present in updateData
    if (updateData.inventory && typeof updateData.inventory === 'object') {
      const inventoryData = updateData.inventory;
      const requiredInventoryFields = [
        'currentStock',
        'warehouseId',
        'safetyStockLevel',
        'maximumStockLevel',
        'averageDailyUsage',
        'abcClassification'
      ];
      const missingInventoryFields = requiredInventoryFields.filter(field => !(field in inventoryData));
      if (missingInventoryFields.length > 0) {
        return NextResponse.json(
          { data: null, error: `Missing required inventory fields: ${missingInventoryFields.join(', ')}. If providing inventory data, all required sub-fields must be present.` },
          { status: 400 }
        );
      }

      // Validate warehouseId (if provided and not empty)
      if (inventoryData.warehouseId && typeof inventoryData.warehouseId === 'string' && inventoryData.warehouseId.trim() !== '') {
        if (!mongoose.Types.ObjectId.isValid(inventoryData.warehouseId)) {
          return NextResponse.json(
            { data: null, error: 'Invalid inventory.warehouseId format: Must be a valid ObjectId string.' },
            { status: 400 }
          );
        }
      }

      // Validate abcClassification (if provided and not empty)
      const validAbcClassifications = ['A', 'B', 'C'];
      if (inventoryData.abcClassification && typeof inventoryData.abcClassification === 'string' && inventoryData.abcClassification.trim() !== '') {
        if (!validAbcClassifications.includes(inventoryData.abcClassification)) {
          return NextResponse.json(
            { data: null, error: `Invalid inventory.abcClassification: Must be one of ${validAbcClassifications.join(', ')}.` },
            { status: 400 }
          );
        }
      }
      // Numerical fields like currentStock, safetyStockLevel etc. are validated by Mongoose schema type Number and min values.
      // Explicit checks for typeof number could be added here if desired for earlier feedback.
    }

    const updatedPart = await mongodbService.updatePart(partId, updateData);

    if (!updatedPart) {
      return NextResponse.json(
        { data: null, error: `Part with ID ${partId} not found` },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: updatedPart, error: null });
  } catch (error) {
    console.error(`Error updating part ${params.id}:`, error);
    return NextResponse.json(
      {
        data: null,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for removing a specific part by ID
 * @param request - The incoming request
 * @param params - Route parameters including the part ID
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const partId = decodeURIComponent(params.id);
    console.log(`[API] DELETE /api/parts/${partId} - Deleting part`);

    const result = await mongodbService.deletePart(partId);

    // Corrected: Check the success property of the result object
    if (!result.success) {
      console.log(`[API] Part with ID ${partId} not found or delete failed: ${result.message}`);
      return NextResponse.json(
        { success: false, error: result.message || `Part with ID ${partId} not found or delete failed` },
        { status: 404 } // Or other appropriate status based on result.message
      );
    }

    console.log(`[API] Part with ID ${partId} deleted successfully`);
    return NextResponse.json({ success: true, error: null });
  } catch (error) {
    console.error(`Error deleting part ${params.id}:`, error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}