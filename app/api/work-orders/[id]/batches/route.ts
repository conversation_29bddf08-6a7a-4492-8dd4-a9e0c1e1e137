import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getBatchesByWorkOrderId, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the woNumber field
}

/**
 * GET handler for fetching batches associated with a specific work order
 * @param request - The incoming request
 * @param params - Route parameters including the work order number
 * @returns JSON response with batches data or error
 */
export async function GET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const woNumber = params.id; // The unique string identifier (woNumber)
  try {
    console.log(`[API] GET /api/work-orders/${woNumber}/batches - Fetching associated batches`);
    
    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    
    // Call the service function to get batches associated with the work order
    const result = await getBatchesByWorkOrderId(woNumber, { page, limit });

    const duration = Date.now() - startTime;
    console.log(`[API] Fetched batches for work order ${woNumber} successfully (${duration}ms)`);
    
    return NextResponse.json({
      data: result?.batches,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching batches for work order ${woNumber} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
