import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getBatchLogs, addBatchLog, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the batch ID
}

/**
 * GET handler for fetching logs for a specific batch
 * @param request - The incoming request
 * @param params - Route parameters containing the batch ID
 * @returns JSON response with the batch logs
 */
export async function GET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { id } = params;
    console.log(`[API] GET /api/batches/${id}/logs - Fetching batch logs`);

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortField = searchParams.get('sortField') || 'timestamp';
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;

    // Call the getBatchLogs service function
    const result = await getBatchLogs(id, { page, limit, sort: { [sortField]: sortOrder } });

    const duration = Date.now() - startTime;
    console.log(`[API] Service getBatchLogs completed in ${duration}ms`);

    return NextResponse.json({
      data: result?.logs,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/batches/${params.id}/logs (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a log to a specific batch
 * @param request - The incoming request with log data
 * @param params - Route parameters containing the batch ID
 * @returns JSON response with the newly created log
 */
export async function POST(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { id } = params;
    console.log(`[API] POST /api/batches/${id}/logs - Adding batch log`);
    const logData = await request.json();

    // Basic validation
    if (!logData || typeof logData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid log data provided' }, { status: 400 });
    }
    
    // Validate required fields
    if (!logData.event) {
      return NextResponse.json({ data: null, error: 'Missing required field: event' }, { status: 400 });
    }
    
    if (!logData.userId) {
      return NextResponse.json({ data: null, error: 'Missing required field: userId' }, { status: 400 });
    }

    // Add the batchId to the log data
    const batchLogData = {
      ...logData,
      batchId: id
    };

    // Call the addBatchLog service function
    const savedLog = await addBatchLog(batchLogData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service addBatchLog completed in ${duration}ms`);

    return NextResponse.json({ data: savedLog, error: null, meta: { duration } }, { status: 201 }); // 201 Created
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/batches/${params.id}/logs (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
