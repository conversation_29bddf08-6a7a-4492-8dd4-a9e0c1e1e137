import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getBatch, updateBatch, deleteBatch, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the batch ID
}

/**
 * GET handler for fetching a specific batch by ID
 * @param request - The incoming request
 * @param params - Route parameters containing the batch ID
 * @returns JSON response with the batch data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { id } = params;
    console.log(`[API] GET /api/batches/${id} - Fetching batch`);

    // Call the getBatch service function
    const batch = await getBatch(id);

    const duration = Date.now() - startTime;
    console.log(`[API] Service getBatch completed in ${duration}ms`);

    if (!batch) {
      return NextResponse.json(
        { data: null, error: `Batch with ID ${id} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: batch,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/batches/[id] (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific batch by ID
 * @param request - The incoming request with updated batch data
 * @param params - Route parameters containing the batch ID
 * @returns JSON response with the updated batch data
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { id } = params;
    console.log(`[API] PUT /api/batches/${id} - Updating batch`);
    const batchData = await request.json();

    // Basic validation
    if (!batchData || typeof batchData !== 'object') {
      return NextResponse.json(
        { data: null, error: 'Invalid batch data provided', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }

    // Extract userId from the request if available
    const userId = batchData.userId || null;
    delete batchData.userId; // Remove userId from batch data as it's not part of the batch model

    // Validate status if provided
    if (batchData.status) {
      const validStatuses = ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold', 'quality_check'];
      if (!validStatuses.includes(batchData.status)) {
        return NextResponse.json({
          data: null,
          error: `Invalid status: ${batchData.status}. Valid statuses are: ${validStatuses.join(', ')}`,
          meta: { duration: Date.now() - startTime }
        }, { status: 400 });
      }
    }

    // Validate quantities if provided
    if (batchData.quantityPlanned !== undefined && (isNaN(batchData.quantityPlanned) || batchData.quantityPlanned < 0)) {
      return NextResponse.json({
        data: null,
        error: 'Quantity planned must be a positive number',
        meta: { duration: Date.now() - startTime }
      }, { status: 400 });
    }

    if (batchData.quantityProduced !== undefined && (isNaN(batchData.quantityProduced) || batchData.quantityProduced < 0)) {
      return NextResponse.json({
        data: null,
        error: 'Quantity produced must be a positive number',
        meta: { duration: Date.now() - startTime }
      }, { status: 400 });
    }

    // Call the updateBatch service function with userId for logging
    const updatedBatch = await updateBatch(id, batchData, userId);

    const duration = Date.now() - startTime;
    console.log(`[API] Service updateBatch completed in ${duration}ms`);

    return NextResponse.json({
      data: updatedBatch,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in PUT /api/batches/[id] (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for deleting a specific batch by ID
 * @param request - The incoming request
 * @param params - Route parameters containing the batch ID
 * @returns JSON response with the deletion status
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { id } = params;
    console.log(`[API] DELETE /api/batches/${id} - Deleting batch`);

    // Call the deleteBatch service function
    const result = await deleteBatch(id);

    const duration = Date.now() - startTime;
    console.log(`[API] Service deleteBatch completed in ${duration}ms`);

    return NextResponse.json({
      data: result,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in DELETE /api/batches/[id] (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
