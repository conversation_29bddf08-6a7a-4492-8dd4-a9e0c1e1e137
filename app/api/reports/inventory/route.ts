import { NextRequest, NextResponse } from 'next/server';
import { generateInventoryReport } from '@/app/services/reports';
import { generateInventoryValueByCategory } from '@/app/services/analytics';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest, logError } from '@/app/services/logging';

/**
 * GET handler for generating inventory report
 * @param request - The incoming request with query parameters
 * @returns JSON response with inventory report data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    const searchParams = request.nextUrl.searchParams;

    // Parse query parameters for logging
    const params = {
      lowStock: searchParams.get('lowStock'),
      category: searchParams.get('category'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate')
    };

    // Log API request
    await logApiRequest('GET', '/api/reports/inventory', params, true);

    // Parse query parameters
    const lowStockOnly = searchParams.get('lowStock') === 'true';
    const categoryFilter = searchParams.get('category') || null;

    // Parse date range if provided
    let dateRange = null;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (startDate && endDate) {
      dateRange = { startDate, endDate };
    }

    // Generate the report with the specified options
    const reportData = await generateInventoryReport({
      lowStockOnly,
      categoryFilter,
      dateRange
    });

    // Fetch additional inventory value by category data
    try {
      const valueData = await generateInventoryValueByCategory({});
      
      // Add the inventory value by category data to the report
      reportData.inventoryValueByCategory = valueData.valueByCategory.reduce(
        (acc, item) => {
          acc[item.name] = item.value;
          return acc;
        },
        {}
      );
    } catch (valueError) {
      console.error('Error fetching inventory value by category:', valueError);
      // Continue with the report even if this data couldn't be fetched
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory report generated in ${duration}ms`);

    return successResponse(
      reportData,
      'Inventory report generated successfully',
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    await logError('API', `Error in GET /api/reports/inventory (${duration}ms)`, error);

    return errorResponse(
      'REPORT_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
