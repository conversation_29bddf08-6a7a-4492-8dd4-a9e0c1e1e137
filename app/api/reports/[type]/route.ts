import { NextRequest, NextResponse } from 'next/server';
import {
  generateInventoryReport,
  generateProductionReport,
  generateProcurementReport,
  generateAssemblyReport
} from '@/app/services/reports';
import { successResponse, errorResponse, notFoundResponse } from '@/app/lib/api-response';
import { logApiRequest, logError } from '@/app/services/logging';

interface RouteParams {
  type: string;
}

/**
 * GET handler for generating reports by type
 * @param request - The incoming request with query parameters
 * @param params - Route parameters including the report type
 * @returns JSON response with report data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { type } = params;
    const searchParams = request.nextUrl.searchParams;

    // Collect query parameters for logging
    const queryParams = {
      type,
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      lowStock: searchParams.get('lowStock'),
      category: searchParams.get('category'),
      status: searchParams.get('status'),
      product: searchParams.get('product')
    };

    // Log API request
    await logApiRequest('GET', `/api/reports/${type}`, queryParams, true);

    // Parse common query parameters
    let dateRange = null;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (startDate && endDate) {
      dateRange = { startDate, endDate };
    }

    let reportData;

    // Generate the appropriate report based on type
    switch (type) {
      case 'inventory':
        const lowStockOnly = searchParams.get('lowStock') === 'true';
        const categoryFilter = searchParams.get('category') || null;

        reportData = await generateInventoryReport({
          lowStockOnly,
          categoryFilter,
          dateRange
        });
        break;

      case 'production':
        const productionStatusFilter = searchParams.get('status') || null;

        reportData = await generateProductionReport({
          statusFilter: productionStatusFilter,
          dateRange
        });
        break;

      case 'procurement':
        const procurementStatusFilter = searchParams.get('status') || null;

        reportData = await generateProcurementReport({
          statusFilter: procurementStatusFilter,
          dateRange
        });
        break;

      case 'assembly':
        const productFilter = searchParams.get('product') || null;

        reportData = await generateAssemblyReport({
          productFilter
        });
        break;

      default:
        return notFoundResponse('Report type', type);
    }

    const duration = Date.now() - startTime;
    console.log(`[API] ${type} report generated in ${duration}ms`);

    return successResponse(
      reportData,
      `${type} report generated successfully`,
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    await logError('API', `Error in GET /api/reports/${params.type} (${duration}ms)`, error);

    return errorResponse(
      'REPORT_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
