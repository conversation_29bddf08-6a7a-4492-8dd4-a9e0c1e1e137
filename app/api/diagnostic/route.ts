import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import { Part } from '@/app/models';
import withDatabase from '@/app/middlewares/withDatabase';

/**
 * GET handler for diagnostic information
 */
async function handleGET(request: NextRequest) {
  try {
    console.log("[API Diagnostic] Starting database connection test");
    // No need to explicitly call connectToDatabase here as withDatabase middleware handles it
    console.log("[API Diagnostic] Database connection established");
    
    // Check if we can query the parts collection
    const partsCount = await Part.countDocuments();
    console.log(`[API Diagnostic] Found ${partsCount} parts in database`);
    
    // Get a sample part to verify we can read data
    const samplePart = await Part.findOne().lean();
    
    // Check MongoDB connection status
    const connectionState = mongoose.connection.readyState;
    const connectionStateText = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
      99: 'uninitialized'
    }[connectionState] || 'unknown';
    
    return NextResponse.json({
      success: true,
      database: {
        connectionState: connectionStateText,
        partsCount,
        samplePartId: samplePart ? samplePart._id.toString() : 'No parts found',
        samplePartName: samplePart ? (samplePart as any).name || 'No name' : 'No parts found'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("[API Diagnostic] Error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Wrap the handler with the withDatabase middleware to ensure connection is established
export const GET = withDatabase(handleGET);
