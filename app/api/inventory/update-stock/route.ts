import { NextRequest, NextResponse } from 'next/server';
import { handleMongoDBError } from '@/app/lib/mongodb';
import * as inventoryService from '@/app/services/inventory.service';
import * as inventoryTransactionService from '@/app/services/inventorytransaction.service';

interface StockUpdateRequest {
  partId: string;
  warehouseId: string;
  quantityChange: number;
  type?: string;
  notes?: string;
  userId: string;
  referenceId?: string;
  referenceModel?: string;
}

/**
 * POST handler for updating stock levels
 * Uses transactional logic to ensure stock and transaction records are updated atomically
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/inventory/update-stock - Updating stock level');
    const data = await request.json() as StockUpdateRequest;

    // Validate required fields
    if (!data.partId || !data.warehouseId || data.quantityChange === undefined || !data.userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Determine transaction type if not provided
    if (!data.type) {
      data.type = data.quantityChange > 0 ? 'stock_adjustment' : 'stock_adjustment';
    }

    // Update stock using the inventory service
    try {
      // First update the inventory
      const updatedInventory = await inventoryService.updateStockLevel(
        data.partId,
        data.warehouseId,
        data.quantityChange
      );

      // Then record the transaction
      const transaction = await inventoryTransactionService.recordTransaction({
        partId: data.partId,
        warehouseId: data.warehouseId,
        type: data.type,
        quantityChanged: data.quantityChange,
        userId: data.userId,
        notes: data.notes || 'Stock update via API',
        referenceId: data.referenceId,
        referenceModel: data.referenceModel
      });

      const duration = Date.now() - startTime;
      console.log(`[API] Stock update completed in ${duration}ms`);

      return NextResponse.json({
        success: true,
        data: {
          inventory: updatedInventory,
          transaction: transaction
        },
        meta: { duration }
      });
    } catch (error: any) {
      console.error('Error updating stock:', error);
      return NextResponse.json(
        { success: false, error: error.message || 'Failed to update stock' },
        { status: error.status || 500 }
      );
    }
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/inventory/update-stock (${duration}ms):`, error);
    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to update stock' },
      { status: 500 }
    );
  }
} 