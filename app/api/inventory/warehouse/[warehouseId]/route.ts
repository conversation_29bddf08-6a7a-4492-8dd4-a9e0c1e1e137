import { NextRequest, NextResponse } from 'next/server';
import { 
  getInventoryByWarehouse,
  handleMongoDBError 
} from '@/app/services/inventory.service';
import mongoose from 'mongoose';
import { successResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

interface RouteParams {
  warehouseId: string;
}

const ROUTE_PATH = '/api/inventory/warehouse/[warehouseId]';

// Maximum allowed limit per request
const MAX_LIMIT = 100;

/**
 * GET handler for fetching all inventory in a specific warehouse
 * @param request - The incoming request
 * @param params - Route parameters including the warehouse ID
 * @returns JSON response with warehouse inventory data
 */
async function handleGET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const warehouseId = params.warehouseId;
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', `${ROUTE_PATH.replace('[warehouseId]', warehouseId)}`, request.nextUrl.searchParams, true);
  
  console.log(`[API] GET /api/inventory/warehouse/${warehouseId} - Fetching warehouse inventory`);

  if (!mongoose.Types.ObjectId.isValid(warehouseId)) {
    return NextResponse.json(
      { data: null, error: `Invalid warehouse ID format: ${warehouseId}` },
      { status: 400 }
    );
  }

  // --- Parsing Query Parameters ---
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

  const sortField = url.searchParams.get('sortField') || 'last_stock_update';
  const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

  // --- Building Filter Object ---
  const filter: any = {};
  
  // Filter by item type
  const itemType = url.searchParams.get('itemType');
  if (itemType) filter.item_type = itemType;
  
  // Filter by stock status
  const stockStatus = url.searchParams.get('stockStatus');
  if (stockStatus) filter.stockStatus = stockStatus;
  
  // Filter by location in warehouse
  const location = url.searchParams.get('location');
  if (location) filter.location_in_warehouse = new RegExp(location, 'i');
  
  // Quantity filters
  const minQuantity = url.searchParams.get('minQuantity');
  const maxQuantity = url.searchParams.get('maxQuantity');
  
  if (minQuantity !== null || maxQuantity !== null) {
    filter.quantity_on_hand = {};
    
    if (minQuantity !== null) {
      filter.quantity_on_hand.$gte = parseInt(minQuantity, 10);
    }
    
    if (maxQuantity !== null) {
      filter.quantity_on_hand.$lte = parseInt(maxQuantity, 10);
    }
  }
  
  // Low stock filter
  const lowStock = url.searchParams.get('lowStock') === 'true';
  if (lowStock) {
    filter.lowStock = true;
  }

  // --- Prepare options for service function ---
  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter,
  };

  console.log(`[API] Calling getInventoryByWarehouse service with options: ${JSON.stringify(options)}`);

  const result = await getInventoryByWarehouse(warehouseId, options);

  const duration = Date.now() - startTime;
  console.log(`[API] Service getInventoryByWarehouse completed in ${duration}ms`);

  return successResponse(
    result.inventoryRecords,
    `Inventory records for warehouse ${warehouseId} retrieved successfully`,
    { 
      duration,
      pagination: result.pagination
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(
  (request: NextRequest) => handleGET(request, { 
    params: { warehouseId: request.nextUrl.pathname.split('/').slice(-1)[0] || '' } 
  }),
  ROUTE_PATH
); 