import { NextRequest, NextResponse } from 'next/server';
import { 
  getInventoryByItem,
  handleMongoDBError 
} from '@/app/services/inventory.service';
import mongoose from 'mongoose';
import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

interface RouteParams {
  itemType: string;
  itemId: string;
}

const ROUTE_PATH = '/api/inventory/item/[itemType]/[itemId]';

/**
 * GET handler for fetching all inventory records for a specific item across all warehouses
 * @param request - The incoming request
 * @param params - Route parameters including the item type and ID
 * @returns JSON response with item inventory data
 */
async function handleGET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const { itemType, itemId } = params;
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', `${ROUTE_PATH.replace('[itemType]', itemType).replace('[itemId]', itemId)}`, 
    request.nextUrl.searchParams, true);
  
  console.log(`[API] GET /api/inventory/item/${itemType}/${itemId} - Fetching item inventory`);

  // Validate itemType
  const validItemTypes = ['Part', 'Assembly', 'Product'];
  if (!validItemTypes.includes(itemType)) {
    throw new InvalidParameterError(
      `Invalid item type. Must be one of: ${validItemTypes.join(', ')}`,
      [{ field: 'itemType', message: 'Invalid item type value' }]
    );
  }

  // Validate itemId
  if (!mongoose.Types.ObjectId.isValid(itemId)) {
    return NextResponse.json(
      { data: null, error: `Invalid item ID format: ${itemId}` },
      { status: 400 }
    );
  }

  try {
    console.log(`[API] Calling getInventoryByItem service for ${itemType} with ID: ${itemId}`);
    
    const inventoryRecords = await getInventoryByItem(itemId, itemType as 'Part' | 'Assembly' | 'Product');
    
    const duration = Date.now() - startTime;
    console.log(`[API] Service getInventoryByItem completed in ${duration}ms, found ${inventoryRecords.length} records`);

    // Calculate total quantities across all warehouses
    const totalOnHand = inventoryRecords.reduce((sum, record) => sum + record.quantity_on_hand, 0);
    const totalAllocated = inventoryRecords.reduce((sum, record) => sum + record.quantity_allocated, 0);
    const totalAvailable = totalOnHand - totalAllocated;
    
    return successResponse(
      inventoryRecords,
      `Inventory records for ${itemType} ${itemId} retrieved successfully`,
      { 
        duration,
        count: inventoryRecords.length,
        totals: {
          quantity_on_hand: totalOnHand,
          quantity_allocated: totalAllocated,
          quantity_available: totalAvailable
        }
      }
    );
  } catch (error: any) {
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory records for ${itemType} ${itemId}`);
  }
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(
  (request: NextRequest) => {
    const pathParts = request.nextUrl.pathname.split('/');
    const itemId = pathParts.pop() || '';
    const itemType = pathParts.pop() || '';
    return handleGET(request, { params: { itemType, itemId } });
  },
  ROUTE_PATH
); 