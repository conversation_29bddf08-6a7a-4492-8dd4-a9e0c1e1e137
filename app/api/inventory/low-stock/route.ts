import { NextRequest } from 'next/server';
import { 
  getLowStockInventory,
  handleMongoDBError 
} from '@/app/services/inventory.service';
import { successResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { Types } from 'mongoose';

// Maximum allowed limit per request
const MAX_LIMIT = 100;
const ROUTE_PATH = '/api/inventory/low-stock';

/**
 * GET handler for fetching inventory items with levels below reorder point
 * @param request - The incoming request
 * @returns JSON response with low stock inventory data
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', ROUTE_PATH, request.nextUrl.searchParams, true);
  
  console.log('[API] GET /api/inventory/low-stock - Fetching low stock inventory');
  const url = new URL(request.url);

  // --- Parsing Query Parameters ---
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

  // By default, sort by quantity_on_hand (lowest first)
  const sortField = url.searchParams.get('sortField') || 'quantity_on_hand';
  const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

  // --- Building Filter Object ---
  const filter: any = {};
  
  // Filter by item type
  const itemType = url.searchParams.get('itemType');
  if (itemType) filter.item_type = itemType;
  
  // Filter by warehouse
  const warehouseId = url.searchParams.get('warehouseId');
  if (warehouseId && Types.ObjectId.isValid(warehouseId)) {
    filter.warehouse_id = new Types.ObjectId(warehouseId);
  }
  
  // Filter by ABC classification
  const abcClass = url.searchParams.get('abcClass');
  if (abcClass) filter.abc_classification = abcClass;

  // --- Prepare Options for Service Function ---
  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter,
  };

  console.log(`[API] Calling getLowStockInventory service with options: ${JSON.stringify(options)}`);

  // Call service function
  const result = await getLowStockInventory(options);

  const duration = Date.now() - startTime;
  console.log(`[API] Service getLowStockInventory completed in ${duration}ms, found ${result.inventoryRecords.length} items`);

  // Return response with pagination metadata
  return successResponse(
    result.inventoryRecords,
    `Retrieved ${result.pagination.totalCount} low stock inventory items`,
    { 
      duration,
      pagination: result.pagination
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(handleGET, ROUTE_PATH); 