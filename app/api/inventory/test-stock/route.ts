import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import { Part, Inventory } from '@/app/models';

/**
 * GET handler for testing inventory stock update
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[API] GET /api/inventory/test-stock - Adding stock to existing parts');
    await connectToDatabase();
    
    // Get a list of existing parts
    const parts = await Part.find().limit(3);
    
    if (parts.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No parts found to update stock'
      });
    }
    
    const results = [];
    
    // Update stock for each part
    for (const part of parts) {
      // Check if inventory already exists for this part
      const existingInventory = await Inventory.findOne({ partId: part._id });
      
      if (existingInventory) {
        // Update existing inventory
        existingInventory.quantity += 100; // Add 100 units
        await existingInventory.save();
        
        results.push({
          partId: part._id,
          partName: part.name,
          inventoryId: existingInventory._id,
          newQuantity: existingInventory.quantity,
          action: 'updated'
        });
      } else {
        // Create new inventory record
        const newInventory = new Inventory({
          partId: part._id,
          warehouseId: new mongoose.Types.ObjectId('681f796ad6a21248b8ec7601'), // Default warehouse ID
          quantity: 100,
          locationInWarehouse: 'A-01',
          minStockLevel: 10,
          maxStockLevel: 200,
          status: 'available',
          notes: 'Test stock added via API'
        });
        
        await newInventory.save();
        
        results.push({
          partId: part._id,
          partName: part.name,
          inventoryId: newInventory._id,
          newQuantity: newInventory.quantity,
          action: 'created'
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Stock added successfully',
      results
    });
  } catch (error) {
    console.error('[API] Error in inventory stock test:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
} 