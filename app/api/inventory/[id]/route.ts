import { NextRequest, NextResponse } from 'next/server';
import { 
  getInventoryById, 
  updateInventoryById,
  deleteInventoryById,
  handleMongoDBError 
} from '@/app/services/inventory.service';
import mongoose from 'mongoose';
import { successResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

interface RouteParams {
  id: string;
}

const ROUTE_PATH = '/api/inventory/[id]';

/**
 * GET handler for fetching a specific inventory record by ID
 * @param request - The incoming request
 * @param params - Route parameters including the inventory record ID
 * @returns JSON response with inventory data
 */
async function handleGET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const id = params.id;
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', `${ROUTE_PATH.replace('[id]', id)}`, request.nextUrl.searchParams, true);
  
  console.log(`[API] GET /api/inventory/${id} - Fetching inventory record`);

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json(
      { data: null, error: `Invalid inventory record ID format: ${id}` },
      { status: 400 }
    );
  }

  const inventoryRecord = await getInventoryById(id);

  const duration = Date.now() - startTime;

  if (!inventoryRecord) {
    console.log(`[API] Inventory record ${id} not found (${duration}ms)`);
    return NextResponse.json(
      { data: null, error: `Inventory record with ID ${id} not found`, meta: { duration } },
      { status: 404 }
    );
  }

  console.log(`[API] Fetched inventory record ${id} successfully (${duration}ms)`);
  return successResponse(inventoryRecord, `Inventory record ${id} retrieved successfully`, { duration });
}

/**
 * PUT handler for updating a specific inventory record
 * @param request - The incoming request with updated inventory data
 * @param params - Route parameters including the inventory record ID
 * @returns JSON response with updated inventory data
 */
async function handlePUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const id = params.id;
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('PUT', `${ROUTE_PATH.replace('[id]', id)}`, null, true);
  
  console.log(`[API] PUT /api/inventory/${id} - Updating inventory record`);
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json(
      { data: null, error: `Invalid inventory record ID format: ${id}` },
      { status: 400 }
    );
  }

  const updateData = await request.json();

  if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
    return NextResponse.json(
      { data: null, error: 'Update data is required and cannot be empty' },
      { status: 400 }
    );
  }

  // Prevent updating item_id, item_type, or warehouse_id as these are composite keys
  if (updateData.item_id !== undefined || updateData.item_type !== undefined || updateData.warehouse_id !== undefined) {
    return NextResponse.json(
      { data: null, error: 'Cannot update item_id, item_type, or warehouse_id. These fields are part of the unique identifier.' },
      { status: 400 }
    );
  }

  const updatedInventory = await updateInventoryById(id, updateData);

  const duration = Date.now() - startTime;
  
  if (!updatedInventory) {
    console.log(`[API] Inventory record ${id} not found for update (${duration}ms)`);
    return NextResponse.json(
      { data: null, error: `Inventory record with ID ${id} not found`, meta: { duration } },
      { status: 404 }
    );
  }
  
  console.log(`[API] Updated inventory record ${id} successfully (${duration}ms)`);
  return successResponse(updatedInventory, `Inventory record ${id} updated successfully`, { duration });
}

/**
 * DELETE handler for removing a specific inventory record
 * @param request - The incoming request
 * @param params - Route parameters including the inventory record ID
 * @returns JSON response indicating success or failure
 */
async function handleDELETE(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const id = params.id;
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('DELETE', `${ROUTE_PATH.replace('[id]', id)}`, null, true);
  
  console.log(`[API] DELETE /api/inventory/${id} - Deleting inventory record`);

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json(
      { data: null, error: `Invalid inventory record ID format: ${id}` },
      { status: 400 }
    );
  }

  try {
    const result = await deleteInventoryById(id);
    
    const duration = Date.now() - startTime;
    console.log(`[API] Deleted inventory record ${id} successfully (${duration}ms)`);
    
    return successResponse(
      { success: true },
      `Inventory record ${id} deleted successfully`,
      { duration }
    );
  } catch (error: any) {
    const errDetails = handleMongoDBError(error);
    
    if (error.message.includes('not found')) {
      return NextResponse.json(
        { data: null, error: `Inventory record with ID ${id} not found or already deleted` },
        { status: 404 }
      );
    }
    
    throw error; // Let the error handler middleware handle other errors
  }
}

// Apply the withErrorHandling middleware to our handlers
export const GET = withErrorHandling(
  (request: NextRequest) => handleGET(request, { params: { id: request.nextUrl.pathname.split('/').pop() || '' } }),
  ROUTE_PATH
);
export const PUT = withErrorHandling(
  (request: NextRequest) => handlePUT(request, { params: { id: request.nextUrl.pathname.split('/').pop() || '' } }),
  ROUTE_PATH
);
export const DELETE = withErrorHandling(
  (request: NextRequest) => handleDELETE(request, { params: { id: request.nextUrl.pathname.split('/').pop() || '' } }),
  ROUTE_PATH
);
