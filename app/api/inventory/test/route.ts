import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import { Part, Warehouse, Inventory, InventoryTransaction } from '@/app/models';

/**
 * GET handler for testing inventory and inventory-transaction functionality
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[API] GET /api/inventory/test - Running inventory system test');
    await connectToDatabase();
    
    // Step 1: Create test data
    const testResults = {
      part: null,
      warehouse: null,
      inventory: null,
      transaction: null,
      cleanup: { success: false, message: '' }
    };
    
    // Create a test part
    const testPart = new Part({
      partNumber: `TEST-${Date.now()}`,
      name: 'Test Part for Schema Test',
      description: 'This is a test part created to verify refactored schema',
      unitOfMeasure: 'pcs',
      cost: 10.99,
      isManufactured: false,
      status: 'active'
    });
    
    const savedPart = await testPart.save();
    testResults.part = {
      _id: savedPart._id,
      partNumber: savedPart.partNumber,
      name: savedPart.name
    };
    
    // Create a test warehouse
    const testWarehouse = new Warehouse({
      warehouseCode: `WH-TEST-${Date.now()}`,
      name: 'Test Warehouse',
      location: 'Test Location',
      capacity: 1000,
      manager: 'Test Manager',
      contact: '<EMAIL>',
      isBinTracked: true
    });
    
    const savedWarehouse = await testWarehouse.save();
    testResults.warehouse = {
      _id: savedWarehouse._id,
      warehouseCode: savedWarehouse.warehouseCode,
      name: savedWarehouse.name
    };
    
    // Create an inventory record
    const testInventory = new Inventory({
      partId: savedPart._id,
      warehouseId: savedWarehouse._id,
      quantity: 100,
      locationInWarehouse: 'A-01-01',
      minStockLevel: 10,
      maxStockLevel: 200,
      status: 'available',
      notes: 'Test inventory record'
    });
    
    const savedInventory = await testInventory.save();
    testResults.inventory = {
      _id: savedInventory._id,
      quantity: savedInventory.quantity,
      partId: savedInventory.partId,
      warehouseId: savedInventory.warehouseId
    };
    
    // Create an inventory transaction
    const testTransaction = new InventoryTransaction({
      partId: savedPart._id,
      warehouseId: savedWarehouse._id,
      type: 'stock_adjustment',
      quantityChanged: 100,
      stockOnHandBefore: 0,
      stockOnHandAfter: 100,
      transactionDate: new Date(),
      userId: '000000000000000000000000', // Default user ID for testing
      notes: 'Initial stock setup'
    });
    
    const savedTransaction = await testTransaction.save();
    testResults.transaction = {
      _id: savedTransaction._id,
      type: savedTransaction.type,
      quantityChanged: savedTransaction.quantityChanged,
      partId: savedTransaction.partId,
      warehouseId: savedTransaction.warehouseId
    };
    
    // Now create a few more transactions for testing
    const additionalTransactions = [
      {
        partId: savedPart._id,
        warehouseId: savedWarehouse._id,
        type: 'purchase_receipt',
        quantityChanged: 50,
        stockOnHandBefore: 100,
        stockOnHandAfter: 150,
        transactionDate: new Date(),
        userId: '000000000000000000000000',
        notes: 'Purchase receipt test transaction'
      },
      {
        partId: savedPart._id,
        warehouseId: savedWarehouse._id,
        type: 'sales_shipment',
        quantityChanged: -25,
        stockOnHandBefore: 150,
        stockOnHandAfter: 125,
        transactionDate: new Date(),
        userId: '000000000000000000000000',
        notes: 'Sales shipment test transaction'
      }
    ];
    
    for (const txData of additionalTransactions) {
      await new InventoryTransaction(txData).save();
    }
    
    // Step 3: Fetch the data to verify it was created correctly
    const fetchResults = {
      inventory: null,
      transaction: null
    };
    
    // Fetch inventory with populated references
    const inventoryWithPopulated = await Inventory.findById(savedInventory._id)
      .populate('partId')
      .populate('warehouseId');
    
    fetchResults.inventory = inventoryWithPopulated;
    
    // Fetch transaction with populated references
    const transactionWithPopulated = await InventoryTransaction.findById(savedTransaction._id)
      .populate('partId')
      .populate('warehouseId');
    
    fetchResults.transaction = transactionWithPopulated;
    
    // Set success message - the data is NOT being cleaned up so it can be viewed in the UI
    testResults.cleanup = { 
      success: false, 
      message: 'Test data has been kept for viewing in the UI' 
    };
    
    return NextResponse.json({
      success: true,
      message: 'Inventory system test completed successfully',
      testData: testResults,
      fetchResults,
      cleanup: testResults.cleanup
    });
  } catch (error) {
    console.error('[API] Error in inventory system test:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
} 