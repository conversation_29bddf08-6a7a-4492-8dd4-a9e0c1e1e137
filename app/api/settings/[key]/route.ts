import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getSetting, updateSetting, deleteSetting, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  key: string; // This 'key' from the route corresponds to the setting key
}

/**
 * GET handler for fetching a specific setting by key
 * @param request - The incoming request
 * @param params - Route parameters containing the setting key
 * @returns JSON response with the setting data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { key } = params;
    console.log(`[API] GET /api/settings/${key} - Fetching setting`);

    // Call the getSetting service function
    const setting = await getSetting(key);

    const duration = Date.now() - startTime;
    console.log(`[API] Service getSetting completed in ${duration}ms`);

    if (!setting) {
      return NextResponse.json(
        { data: null, error: `Setting with key ${key} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: setting,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/settings/[key] (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific setting by key
 * @param request - The incoming request with updated setting data
 * @param params - Route parameters containing the setting key
 * @returns JSON response with the updated setting data
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { key } = params;
    console.log(`[API] PUT /api/settings/${key} - Updating setting`);
    const settingData = await request.json();

    // Basic validation
    if (!settingData || typeof settingData !== 'object') {
      return NextResponse.json(
        { data: null, error: 'Invalid setting data provided', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }
    
    // Ensure lastModifiedBy is provided
    if (!settingData.lastModifiedBy) {
      return NextResponse.json(
        { data: null, error: 'Missing required field: lastModifiedBy', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }

    // Call the updateSetting service function
    const updatedSetting = await updateSetting(key, settingData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service updateSetting completed in ${duration}ms`);

    return NextResponse.json({
      data: updatedSetting,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in PUT /api/settings/[key] (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for deleting a specific setting by key
 * @param request - The incoming request
 * @param params - Route parameters containing the setting key
 * @returns JSON response with the deletion status
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  try {
    const { key } = params;
    console.log(`[API] DELETE /api/settings/${key} - Deleting setting`);

    // Call the deleteSetting service function
    const result = await deleteSetting(key);

    const duration = Date.now() - startTime;
    console.log(`[API] Service deleteSetting completed in ${duration}ms`);

    return NextResponse.json({
      data: result,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in DELETE /api/settings/[key] (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
