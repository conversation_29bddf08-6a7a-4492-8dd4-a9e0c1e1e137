import { NextRequest } from 'next/server';
import { 
  getAssemblyById, 
  updateAssembly, 
  deleteAssembly, 
  getAssemblyByAssemblyCode,
  updateAssemblyByAssemblyCode,
  deleteAssemblyByAssemblyCode
} from '@/app/services/assembly.service';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import mongoose from 'mongoose';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

const ROUTE_PATH = '/api/assemblies/[id]';

/**
 * Helper function to extract the ID parameter from the request URL
 */
function extractIdFromRequest(request: NextRequest): string {
  const pathname = request.nextUrl.pathname;
  const segments = pathname.split('/');
  return segments[segments.length - 1];
}

/**
 * GET handler for retrieving a specific assembly by ID
 */
async function handleGET(request: NextRequest) {
  const id = extractIdFromRequest(request);
  const startTime = Date.now();

  // Log API request
  await logApiRequest('GET', `${ROUTE_PATH} - ${id}`, null, true);
  
  console.log(`[API] GET /api/assemblies/${id} - Fetching assembly`);

  try {
    // Check if URL has includeParts parameter
    const url = new URL(request.url);
    const includeParts = url.searchParams.get('includeParts') === 'true';

    // Verify ID format - try as MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      const assembly = await getAssemblyById(id, includeParts);
      
      if (!assembly) {
        return errorResponse(`Assembly with ID ${id} not found`, '404');
      }
      
      const duration = Date.now() - startTime;
      console.log(`[API] Assembly retrieved by ObjectID in ${duration}ms`);
      
      return successResponse(assembly, 'Assembly retrieved successfully', { duration });
    } else {
      // If not a valid ObjectId, try as an assembly code
      console.log(`[API] ID ${id} is not a valid ObjectId, trying as assembly code`);
      const assembly = await getAssemblyByAssemblyCode(id, includeParts);
      
      if (!assembly) {
        return errorResponse(`Assembly with code ${id} not found`, '404');
      }
      
      const duration = Date.now() - startTime;
      console.log(`[API] Assembly retrieved by assemblyCode in ${duration}ms`);
      
      return successResponse(assembly, 'Assembly retrieved successfully', { duration });
    }
  } catch (error: any) {
    console.error('[API] Error fetching assembly:', error);
    return errorResponse(`Failed to retrieve assembly: ${error.message}`, '500');
  }
}

/**
 * PUT handler for updating a specific assembly by ID
 */
async function handlePUT(request: NextRequest) {
  const id = extractIdFromRequest(request);
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('PUT', `${ROUTE_PATH} - ${id}`, null, true);
  
  console.log(`[API] PUT /api/assemblies/${id} - Updating assembly`);
  
  try {
    const updateData = await request.json();
    
    // Basic validation
    if (!updateData || typeof updateData !== 'object' || Array.isArray(updateData)) {
      return errorResponse('Invalid update data provided', '400');
    }
    
    if (updateData.assemblyCode && updateData.assemblyCode !== id) { // Allow if assemblyCode is same as id (when id is assemblyCode)
      return errorResponse('Cannot update assemblyCode through this endpoint', '400');
    }
    
    // Validate partsRequired if included
    if (updateData.partsRequired) {
      if (!Array.isArray(updateData.partsRequired)) {
        return errorResponse('partsRequired must be an array', '400');
      }
      for (const partItem of updateData.partsRequired) {
        if (!partItem.partId) {
          return errorResponse('Each part entry must have a partId', '400');
        }
        
        const quantityValue = partItem.quantityRequired; // Use canonical quantityRequired
        if (!quantityValue || typeof quantityValue !== 'number' || quantityValue <= 0) {
          return errorResponse(`Invalid quantity for part ${partItem.partId}. Must be a positive number.`, '400');
        }

        if (!partItem.unitOfMeasure) { // Use canonical unitOfMeasure
          return errorResponse(`Missing unitOfMeasure for part ${partItem.partId}`, '400');
        }
      }
    }
    
    let updatedAssembly = null;
    
    // Try as MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      console.log(`[API] Updating assembly using ObjectId: ${id}`);
      updatedAssembly = await updateAssembly(id, updateData);
    } else {
      // If not a valid ObjectId, try as an assembly code
      console.log(`[API] ID ${id} is not a valid ObjectId, updating using assembly code`);
      updatedAssembly = await updateAssemblyByAssemblyCode(id, updateData);
    }
    
    if (!updatedAssembly) {
      return errorResponse(`Assembly with ID/code ${id} not found`, '404');
    }
    
    const duration = Date.now() - startTime;
    return successResponse(updatedAssembly, 'Assembly updated successfully', { duration });
  } catch (error: any) {
    console.error('[API] Error updating assembly:', error);
    return errorResponse(`Failed to update assembly: ${error.message}`, '500');
  }
}

/**
 * PATCH handler for updating a specific assembly by ID
 * This is an alias to the PUT handler for compatibility with frontend requests
 */
async function handlePATCH(request: NextRequest) {
  // Call the PUT handler implementation
  return handlePUT(request);
}

/**
 * DELETE handler for removing a specific assembly by ID
 */
async function handleDELETE(request: NextRequest) {
  const id = extractIdFromRequest(request);
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('DELETE', `${ROUTE_PATH} - ${id}`, null, true);
  
  console.log(`[API] DELETE /api/assemblies/${id} - Deleting assembly`);
  
  try {
    let result = false;
    
    // Try as MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      console.log(`[API] Deleting assembly using ObjectId: ${id}`);
      result = await deleteAssembly(id);
    } else {
      // If not a valid ObjectId, try as an assembly code
      console.log(`[API] ID ${id} is not a valid ObjectId, deleting using assembly code`);
      result = await deleteAssemblyByAssemblyCode(id);
    }
    
    if (!result) {
      return errorResponse(`Assembly with ID/code ${id} not found or cannot be deleted`, '404');
    }
    
    const duration = Date.now() - startTime;
    return successResponse(null, 'Assembly deleted successfully', { duration });
  } catch (error: any) {
    console.error('[API] Error deleting assembly:', error);
    return errorResponse(`Failed to delete assembly: ${error.message}`, '500');
  }
}

// Apply error handling middleware
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const PUT = withErrorHandling(handlePUT, ROUTE_PATH);
export const PATCH = withErrorHandling(handlePATCH, ROUTE_PATH);
export const DELETE = withErrorHandling(handleDELETE, ROUTE_PATH);
