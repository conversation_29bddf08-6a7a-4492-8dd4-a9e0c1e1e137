import { NextRequest, NextResponse } from 'next/server';
// Use functions from assembly.service.ts for schema-aware operations
import { 
  getAssemblyByAssemblyCode, 
  createAssembly,
  handleMongoDBError as handleAssemblyServiceError // Alias to avoid conflict if another handleMongoDBError is ever imported
} from '@/app/services/assembly.service'; 
// Attempting the logically correct relative path for interfaces again
import { IAssemblyPart, IImage, IAttribute, IAssembly } from '@/app/models/assembly.model'; 
import { CreateAssemblyDto } from '@/app/services/assembly.service'; // Import DTO

interface RouteParams {
  id: string; // This is assemblyCode
}

/**
 * POST handler for duplicating an assembly
 * @param _request - The incoming request (unused)
 * @param context - Context object containing route parameters
 * @param context.params - Route parameters containing the assembly ID (assemblyCode)
 * @returns JSON response with the duplicated assembly or error
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const assemblyCodeToDuplicate = params.id; // id is the assemblyCode from the route
  try {
    console.log(`[API] POST /api/assemblies/${assemblyCodeToDuplicate}/duplicate - Duplicating assembly`);

    const originalAssembly = await getAssemblyByAssemblyCode(assemblyCodeToDuplicate, true);

    if (!originalAssembly) {
      console.log(`[API] Assembly ${assemblyCodeToDuplicate} not found for duplication`);
      return NextResponse.json(
        { success: false, error: `Assembly with code ${assemblyCodeToDuplicate} not found`, meta: { duration: Date.now() - startTime } },
        { status: 404 }
      );
    }

    // Map originalAssembly to CreateAssemblyDto
    const newAssemblyDto: CreateAssemblyDto = {
      assemblyCode: `${originalAssembly.assemblyCode}-COPY-${Date.now().toString().slice(-5)}`,
      name: `${originalAssembly.name} (Copy)`,
      description: originalAssembly.description || undefined,
      productId: originalAssembly.productId?.toString() || undefined, // Ensure string or undefined
      parentId: originalAssembly.parentId?.toString() || undefined,   // Ensure string or undefined
      isTopLevel: originalAssembly.isTopLevel,
      partsRequired: originalAssembly.partsRequired?.map((p: IAssemblyPart) => {
        // If p.partId is populated, it's an object like { _id: ..., name: ... }.
        // If it's just an ObjectId string/object, .toString() would work directly or on ._id if it's an ObjectId instance.
        // The DTO expects a string representation of the ObjectId.
        const partIdString = (typeof p.partId === 'string') 
          ? p.partId 
          : (p.partId && typeof (p.partId as any)._id !== 'undefined') 
            ? (p.partId as any)._id.toString() 
            : p.partId?.toString() || ''; // Fallback for ObjectId instance, ensure string
        return {
          partId: partIdString,
          quantityRequired: p.quantityRequired || 1, // Ensure quantity is at least 1
          unitOfMeasure: p.unitOfMeasure
        };
      }) || [],
      status: originalAssembly.status || 'pending_review', // Default status
      version: 1, // Reset version for a new copy
      manufacturingInstructions: originalAssembly.manufacturingInstructions || undefined,
      estimatedBuildTime: originalAssembly.estimatedBuildTime || undefined,
      notes: originalAssembly.notes || undefined,
      createdBy: null, // TODO: Get actual user ID from request context (e.g., session or middleware)
      // Legacy fields from DTO if needed for the createAssembly service function
      // assembly_id: will be handled by model pre-save or createAssembly logic if it uses assemblyCode
      images: originalAssembly.images?.map((img: IImage) => ({ url: img.url, alt_text: img.alt_text || undefined })),
      attributes: originalAssembly.attributes?.map((attr: IAttribute) => ({ name: attr.name, value: attr.value })),
    };
    
    // Remove undefined properties to ensure they don't override Mongoose defaults if any
    Object.keys(newAssemblyDto).forEach(key => (newAssemblyDto as any)[key] === undefined && delete (newAssemblyDto as any)[key]);

    const duplicatedAssembly = await createAssembly(newAssemblyDto);

    const duration = Date.now() - startTime;
    console.log(`[API] Duplicated assembly ${assemblyCodeToDuplicate} successfully (${duration}ms)`);
    
    return NextResponse.json({ 
      success: true, 
      data: duplicatedAssembly, 
      meta: { duration } 
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error duplicating assembly ${assemblyCodeToDuplicate} (${duration}ms):`, error);
    
    let errorResponseData;
    // Check if the error object from assembly.service already has a standard structure (e.g. if it re-threw a handled error)
    if (error.type && error.message && error.code) { 
        errorResponseData = { message: error.message, httpStatus: error.code, errors: error.errors };
    } else {
        // If not, pass it to the service's error handler
        const errDetails = handleAssemblyServiceError(error); // Use aliased error handler
        errorResponseData = { message: errDetails.message, httpStatus: errDetails.code, errors: errDetails.errors };
    }
    
    return NextResponse.json(
      { success: false, error: errorResponseData.message, errors: errorResponseData.errors, meta: { duration } },
      { status: errorResponseData.httpStatus } // Use the httpStatus field for the response status
    );
  }
}
