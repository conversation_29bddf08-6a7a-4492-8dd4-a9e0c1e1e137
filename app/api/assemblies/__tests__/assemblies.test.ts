import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { NextRequest } from 'next/server';
import { GET, POST } from '../route';
import mongoose from 'mongoose';
import Assembly from '@/app/models/assembly.model';
import Part from '@/app/models/part.model';

// Mock the MongoDB connection
vi.mock('@/app/lib/mongodb', () => ({
  default: vi.fn().mockResolvedValue(mongoose),
}));

// Mock the models
vi.mock('@/app/models/assembly.model', () => ({
  default: {
    find: vi.fn(),
    findById: vi.fn(),
    findOne: vi.fn(),
    create: vi.fn(),
    save: vi.fn(),
  },
}));

vi.mock('@/app/models/part.model', () => ({
  default: {
    findById: vi.fn(),
    findOne: vi.fn(),
  },
}));

describe('Assemblies API', () => {
  beforeAll(async () => {
    // Set up any test database connections if needed
  });

  afterAll(async () => {
    // Clean up any test database connections
  });

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  describe('GET /api/assemblies', () => {
    it('should return a list of assemblies', async () => {
      // Mock the Assembly.find method
      const mockAssemblies = [
        {
          _id: 'assembly1',
          assembly_id: 'ASM-001',
          name: 'Test Assembly 1',
          parts: [
            {
              partId: {
                _id: 'part1',
                part_id: 'P001',
                name: 'Test Part 1',
              },
              quantityRequired: 2,
            },
          ],
        },
      ];

      // @ts-ignore - Mocking the populate chain
      Assembly.find.mockReturnValue({
        populate: vi.fn().mockReturnValue({
          lean: vi.fn().mockResolvedValue(mockAssemblies),
        }),
      });

      // Create a mock request
      const request = new NextRequest('http://localhost/api/assemblies');

      // Call the GET handler
      const response = await GET(request);
      const responseData = await response.json();

      // Verify the response
      expect(response.status).toBe(200);
      expect(responseData.data).toEqual(mockAssemblies);
      expect(Assembly.find).toHaveBeenCalled();
    });

    it('should handle errors when fetching assemblies', async () => {
      // Mock the Assembly.find method to throw an error
      // @ts-ignore - Mocking the populate chain
      Assembly.find.mockReturnValue({
        populate: vi.fn().mockReturnValue({
          lean: vi.fn().mockRejectedValue(new Error('Database error')),
        }),
      });

      // Create a mock request
      const request = new NextRequest('http://localhost/api/assemblies');

      // Call the GET handler
      const response = await GET(request);
      const responseData = await response.json();

      // Verify the response
      expect(response.status).toBe(500);
      expect(responseData.error).toContain('Database error');
    });
  });

  describe('POST /api/assemblies', () => {
    it('should create a new assembly', async () => {
      // Mock the Part.findById method for validation
      // @ts-ignore
      Part.findById.mockReturnValue({
        lean: vi.fn().mockReturnValue({
          select: vi.fn().mockResolvedValue({
            _id: 'part1',
            name: 'Test Part',
          }),
        }),
      });

      // Mock the Assembly.save method
      const mockSavedAssembly = {
        _id: 'newAssembly',
        assembly_id: 'ASM-001',
        name: 'New Assembly',
        parts: [
          {
            partId: 'part1',
            quantityRequired: 2,
          },
        ],
        save: vi.fn().mockResolvedValue({
          _id: 'newAssembly',
        }),
      };

      // @ts-ignore
      Assembly.prototype.save = vi.fn().mockResolvedValue(mockSavedAssembly);

      // @ts-ignore - Mocking the populate chain
      Assembly.findById.mockReturnValue({
        populate: vi.fn().mockReturnValue({
          lean: vi.fn().mockResolvedValue(mockSavedAssembly),
        }),
      });

      // Create a mock request with assembly data
      const request = new NextRequest('http://localhost/api/assemblies', {
        method: 'POST',
        body: JSON.stringify({
          name: 'New Assembly',
          parts: [
            {
              partId: 'part1',
              quantityRequired: 2,
            },
          ],
        }),
      });

      // Call the POST handler
      const response = await POST(request);
      const responseData = await response.json();

      // Verify the response
      expect(response.status).toBe(200);
      expect(responseData.data).toEqual(mockSavedAssembly);
    });

    it('should validate assembly data before creation', async () => {
      // Create a mock request with invalid assembly data (missing name)
      const request = new NextRequest('http://localhost/api/assemblies', {
        method: 'POST',
        body: JSON.stringify({
          parts: [
            {
              partId: 'part1',
              quantityRequired: 2,
            },
          ],
        }),
      });

      // Call the POST handler
      const response = await POST(request);
      const responseData = await response.json();

      // Verify the response
      expect(response.status).toBe(400);
      expect(responseData.error).toContain('Assembly name is required');
    });

    it('should validate parts data before creation', async () => {
      // Create a mock request with invalid parts data (empty parts array)
      const request = new NextRequest('http://localhost/api/assemblies', {
        method: 'POST',
        body: JSON.stringify({
          name: 'New Assembly',
          parts: [],
        }),
      });

      // Call the POST handler
      const response = await POST(request);
      const responseData = await response.json();

      // Verify the response
      expect(response.status).toBe(400);
      expect(responseData.error).toContain('Assembly must contain at least one part');
    });
  });
});
