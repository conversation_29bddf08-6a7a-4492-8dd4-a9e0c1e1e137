import { NextRequest, NextResponse } from 'next/server';
import { getAllAssemblies, createAssembly, handleMongoDBError, searchAssemblies } from '@/app/services/assembly.service';
import { successResponse, validationErrorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import { InvalidParameterError } from '@/app/lib/errors';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import connectToDatabase from '@/app/lib/mongodb';
import Assembly from '@/app/models/assembly.model';
import { parse } from 'url';

// Maximum allowed limit per request
const MAX_LIMIT = 500;
const ROUTE_PATH = '/api/assemblies';

// Helper to safely parse integer or return default
const parseIntOrDefault = (value: string | null, defaultValue: number): number => {
  if (value === null) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * GET handler for fetching assemblies with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with assemblies data
 */
async function handleGET(req: NextRequest) {
  try {
    await connectToDatabase();

    const searchParams = req.nextUrl.searchParams;

    const page = parseIntOrDefault(searchParams.get('page'), 1);
    const limit = parseIntOrDefault(searchParams.get('limit'), 20);
    const skip = (page - 1) * limit;

    const filter: any = {};
    // Filters from critical_fixes.md suggestion
    if (searchParams.has('status')) filter.status = searchParams.get('status');
    if (searchParams.has('type')) filter.assemblyType = searchParams.get('type'); // Assuming model field is assemblyType
    
    // Additional filters that might have been in the original code (example)
    if (searchParams.has('assemblyCode')) filter.assemblyCode = searchParams.get('assemblyCode');
    if (searchParams.has('name')) filter.name = new RegExp(searchParams.get('name')!, 'i');
    if (searchParams.has('version')) {
      const versionVal = parseIntOrDefault(searchParams.get('version'), -1);
      if (versionVal !== -1) filter.version = versionVal;
    }
    if (searchParams.has('isTopLevel')) filter.isTopLevel = searchParams.get('isTopLevel') === 'true';

    const sort: any = {};
    const sortBy = searchParams.get('sortBy');
    const sortOrder = searchParams.get('sortOrder');
    if (sortBy && sortOrder) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort
    }
    
    // Handle general query (if it implies a search across multiple fields, requires specific logic)
    // For now, if 'query' exists, it might be a text search. This part needs clarification based on Assembly model schema.
    // if (searchParams.has('query')) { /* Add text search logic if applicable */ }

    console.log(`[GET /api/assemblies] Fetching: page=${page}, limit=${limit}, skip=${skip}, filter=${JSON.stringify(filter)}, sort=${JSON.stringify(sort)}`);

    let query = Assembly.find(filter)
      .sort(sort)
      .limit(limit)
      .skip(skip);

    // Check for includeParts query parameter
    const includeParts = searchParams.get('includeParts') === 'true';
    if (includeParts) {
      console.log('[GET /api/assemblies] Populating partsRequired.partId');
      query = query.populate({
        path: 'partsRequired.partId',
        model: 'Part', // Ensure 'Part' is the correct model name
        select: 'name partNumber description inventory category' // Specify fields to populate
      });
    }

    const assemblies = await query.lean().maxTimeMS(10000); 

    const total = await Assembly.countDocuments(filter);

    return NextResponse.json({
      success: true,
      message: 'Assemblies retrieved successfully',
      data: assemblies,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    }, { status: 200 });

  } catch (err: any) {
    console.error(`[GET /api/assemblies] Failed: ${err.message}`, {
      stack: err.stack,
      url: req.url,
      method: req.method,
    });

    if (err.name === 'MongoServerError') {
      return NextResponse.json({
        success: false,
        message: 'Database error occurred while fetching assemblies.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Database error',
        code: 'DB_ERROR',
      }, { status: 500 });
    }
    
    if (err.name === 'MongoTimeoutError' || err.message.toLowerCase().includes('timeout')) { 
      return NextResponse.json({
        success: false,
        message: 'Request timed out while fetching assemblies.',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Request timeout',
        code: 'TIMEOUT',
      }, { status: 408 });
    }
    
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch assemblies due to an unexpected error.',
      error: process.env.NODE_ENV === 'development' ? err.message : 'Server error',
      code: 'SERVER_ERROR',
    }, { status: 500 });
  }
}

/**
 * POST handler for creating a new assembly
 * 
 * @param request - The incoming request with assembly data
 * @returns JSON response with the newly created assembly
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();
  
  await logApiRequest('POST', ROUTE_PATH, null, true);
  
  console.log('[API] POST /api/assemblies - Creating new assembly');
  const assemblyData = await request.json();

  // Basic validation
  if (!assemblyData || typeof assemblyData !== 'object') {
    throw new InvalidParameterError('Invalid assembly data provided');
  }
  
  // Validate required fields
  const requiredFields = ['assemblyCode', 'name', 'status'];
  const missingFields = requiredFields.filter(field => !(field in assemblyData) || !assemblyData[field]);
  if (missingFields.length > 0) {
    throw new InvalidParameterError(`Missing required fields: ${missingFields.join(', ')}`, 
      missingFields.map(field => ({ field, message: 'This field is required' }))
    );
  }

  // --- Check for duplicate assemblyCode ---
  await connectToDatabase();
  const existingAssembly = await Assembly.findOne({ assemblyCode: assemblyData.assemblyCode }).lean();

  if (existingAssembly) {
    console.warn(`[API] Attempt to create duplicate assembly code: ${assemblyData.assemblyCode}`);
    return NextResponse.json({
      success: false,
      message: `Assembly with code '${assemblyData.assemblyCode}' already exists.`,
      error: 'Duplicate assembly code',
      code: 'DUPLICATE_ASSEMBLY_CODE',
    }, { status: 409 });
  }
  // --- End duplicate check ---

  // Validate partsRequired if included
  if (assemblyData.partsRequired) {
    if (!Array.isArray(assemblyData.partsRequired)) {
      throw new InvalidParameterError('partsRequired must be an array');
    }
    for (const partItem of assemblyData.partsRequired) {
      if (!partItem.partId) {
        throw new InvalidParameterError('Each part entry must have a partId');
      }
      
      const quantityValue = partItem.quantityRequired; // Use canonical quantityRequired
      if (!quantityValue || typeof quantityValue !== 'number' || quantityValue <= 0) {
        throw new InvalidParameterError(`Invalid quantity for part ${partItem.partId}. Must be a positive number.`);
      }
      
      if (!partItem.unitOfMeasure) { // Use canonical unitOfMeasure
        throw new InvalidParameterError(`Missing unitOfMeasure for part ${partItem.partId}`);
      }
    }
  }

  console.log(`[API] Calling createAssembly service with data: ${JSON.stringify({
    assemblyCode: assemblyData.assemblyCode,
    name: assemblyData.name,
    partsRequiredCount: assemblyData.partsRequired?.length || 0
  })}`);

  // Call the createAssembly service function
  const savedAssembly = await createAssembly(assemblyData);

  const duration = Date.now() - startTime;
  console.log(`[API] Service createAssembly completed in ${duration}ms`);

  // Return the saved assembly (service function handles creation logic)
  return successResponse(
    savedAssembly,
    'Assembly created successfully',
    { duration },
    201
  );
}

// Apply the withErrorHandling middleware to our handlers
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const POST = withErrorHandling(handlePOST, ROUTE_PATH);
