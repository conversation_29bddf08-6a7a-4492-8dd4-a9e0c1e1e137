import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getDelivery, updateDelivery, deleteDelivery, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the deliveryId field
}

/**
 * GET handler for fetching a specific delivery by its deliveryId
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the delivery ID
 * @returns JSON response with delivery data or error
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const deliveryId = params.id; // The unique string identifier (deliveryId)
  try {
    console.log(`[API] GET /api/logistics/${deliveryId} - Fetching delivery`);

    // Call the service function to get the delivery
    const delivery = await getDelivery(deliveryId);

    const duration = Date.now() - startTime;

    if (!delivery) {
      console.log(`[API] Delivery ${deliveryId} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Delivery with ID ${deliveryId} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched delivery ${deliveryId} successfully (${duration}ms)`);
    return NextResponse.json({ data: delivery, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching delivery ${deliveryId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific delivery by its deliveryId
 * @param request - The incoming request with updated delivery data
 * @param params - Route parameters including the delivery ID
 * @returns JSON response with updated delivery data or error
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const deliveryId = params.id;
  try {
    console.log(`[API] PUT /api/logistics/${deliveryId} - Updating delivery`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // Call the service function to update the delivery
    const updatedDelivery = await updateDelivery(deliveryId, updateData);

    const duration = Date.now() - startTime;
    console.log(`[API] Updated delivery ${deliveryId} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedDelivery, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating delivery ${deliveryId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific delivery by its deliveryId
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the delivery ID
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const deliveryId = params.id;
  try {
    console.log(`[API] DELETE /api/logistics/${deliveryId} - Deleting delivery`);

    // Call the service function to delete the delivery
    const result = await deleteDelivery(deliveryId);

    const duration = Date.now() - startTime;
    console.log(`[API] Deleted delivery ${deliveryId} successfully (${duration}ms)`);
    return NextResponse.json({ 
      data: result, 
      error: null, 
      meta: { duration } 
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting delivery ${deliveryId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
