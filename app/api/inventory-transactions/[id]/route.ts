import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/app/lib/mongodb';
import { ObjectId } from 'mongodb';

/**
 * GET handler for a specific inventory transaction
 * Fetches a single inventory transaction by ID
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const id = params.id;

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    const transaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Fetch part details
    if (transaction.partId && ObjectId.isValid(transaction.partId)) {
      const part = await db.collection('parts').findOne({
        _id: new ObjectId(transaction.partId)
      });

      if (part) {
        transaction.part = part;
      }
    }

    return NextResponse.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error(`Error fetching transaction ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch transaction' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for a specific inventory transaction
 * Updates an existing inventory transaction
 */
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const id = params.id;
    const data = await request.json();

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    // Find the existing transaction
    const existingTransaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!existingTransaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Only allow updating notes and reference number
    const updateData = {
      notes: data.notes,
      referenceNumber: data.referenceNumber
    };

    const result = await db.collection('transactions').updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    return NextResponse.json({
      success: true,
      data: { ...existingTransaction, ...updateData }
    });
  } catch (error) {
    console.error(`Error updating transaction ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to update transaction' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for a specific inventory transaction
 * Deletes an inventory transaction and reverts the stock change
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const id = params.id;

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    // Find the transaction to be deleted
    const transaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Revert the stock change
    if (transaction.partId && ObjectId.isValid(transaction.partId)) {
      await db.collection('parts').updateOne(
        { _id: new ObjectId(transaction.partId) },
        { $set: { current_stock: transaction.previousStock } }
      );
    }

    // Delete the transaction
    await db.collection('transactions').deleteOne({
      _id: new ObjectId(id)
    });

    return NextResponse.json({
      success: true,
      message: 'Transaction deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting transaction ${params.id}:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete transaction' },
      { status: 500 }
    );
  }
}