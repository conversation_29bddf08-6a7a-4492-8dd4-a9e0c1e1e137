import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import { InventoryTransaction, Part, Warehouse } from '@/app/models';
import { handleMongoDBError } from '@/app/lib/mongodb';

/**
 * Helper function to map legacy transaction types to canonical types
 */
function mapLegacyToCanonicalType(legacyType?: string): string | undefined {
  if (!legacyType) return undefined;

  const typeMap: Record<string, string> = {
    'purchase_receipt': 'stock_in_purchase',
    'production_output': 'stock_in_production',
    'production_consumption': 'stock_out_production',
    'sales_shipment': 'sales_shipment', // Same in both
    'stock_adjustment': 'adjustment_cycle_count',
    'internal_transfer_out': 'transfer_out',
    'internal_transfer_in': 'transfer_in'
  };

  return typeMap[legacyType] || legacyType;
}

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

// Define interface for the transaction creation request
interface CreateTransactionRequest {
  // Canonical field names
  partId: string;
  warehouseId: string;
  transactionType?: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment';
  quantity?: number;
  transactionDate?: Date;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
  userId: string;
  notes?: string;

  // Legacy field names for backward compatibility
  type?: 'purchase_receipt' | 'production_output' | 'production_consumption' | 'sales_shipment' | 'stock_adjustment' | 'internal_transfer_out' | 'internal_transfer_in';
  quantityChanged?: number;
  referenceId?: string;
  referenceModel?: string;
}

/**
 * GET handler for inventory transactions
 * Fetches a list of inventory transactions with pagination and filtering options
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/inventory-transactions - Fetching transactions');
    await connectToDatabase();

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10', 10), MAX_LIMIT);
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    // Filter by part ID if provided
    const partId = url.searchParams.get('partId');
    if (partId) {
      try {
        filter.partId = new mongoose.Types.ObjectId(partId);
      } catch (err) {
        console.error('Invalid partId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid partId format' },
          { status: 400 }
        );
      }
    }

    // Filter by warehouse ID if provided
    const warehouseId = url.searchParams.get('warehouseId');
    if (warehouseId) {
      try {
        filter.warehouseId = new mongoose.Types.ObjectId(warehouseId);
      } catch (err) {
        console.error('Invalid warehouseId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid warehouseId format' },
          { status: 400 }
        );
      }
    }

    // Filter by transaction type if provided
    const type = url.searchParams.get('type');
    if (type) {
      filter.type = type;
    }

    // Filter by date range if provided
    const startDateParam = url.searchParams.get('startDate');
    const endDateParam = url.searchParams.get('endDate');

    if (startDateParam && endDateParam) {
      filter.transactionDate = {
        $gte: new Date(startDateParam),
        $lte: new Date(endDateParam)
      };
    } else if (startDateParam) {
      filter.transactionDate = { $gte: new Date(startDateParam) };
    } else if (endDateParam) {
      filter.transactionDate = { $lte: new Date(endDateParam) };
    } else {
      // If no specific date range is provided, check for period filter
      const period = url.searchParams.get('period');
      let startDate: Date | null = null;
      const now = new Date();

      switch (period) {
        case 'day':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = null;
      }

      if (startDate) {
        filter.transactionDate = { $gte: startDate };
      }
    }

    // Execute query with population
    const transactions = await InventoryTransaction.find(filter)
      .sort({ transactionDate: -1 })
      .skip(skip)
      .limit(limit)
      .populate('partId', 'partNumber name')
      .populate('warehouseId', 'warehouseCode name')
      .populate('userId', 'username fullName')
      .lean();

    // Get total count for pagination
    const total = await InventoryTransaction.countDocuments(filter);

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transactions query completed in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      meta: { duration }
    });
  } catch (error: any) {
    console.error('Error fetching inventory transactions:', error);
    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to fetch inventory transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for inventory transactions
 * Creates a new inventory transaction and updates inventory levels
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/inventory-transactions - Creating new transaction');
    await connectToDatabase();
    const data = await request.json() as CreateTransactionRequest;

    // Validate required fields
    if (!data.partId || !data.warehouseId || !data.userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: partId, warehouseId, and userId are required' },
        { status: 400 }
      );
    }

    // Ensure either canonical or legacy transaction type is provided
    if (!data.transactionType && !data.type) {
      return NextResponse.json(
        { success: false, error: 'Missing required field: transactionType or type is required' },
        { status: 400 }
      );
    }

    // Ensure either canonical or legacy quantity is provided
    if (data.quantity === undefined && data.quantityChanged === undefined) {
      return NextResponse.json(
        { success: false, error: 'Missing required field: quantity or quantityChanged is required' },
        { status: 400 }
      );
    }

    // Start a session for transaction
    const session = await mongoose.startSession();
    let newTransaction;
    let updatedInventory;

    try {
      session.startTransaction();

      // Find part to verify it exists
      const part = await Part.findById(data.partId).session(session);
      if (!part) {
        throw new Error(`Part with ID ${data.partId} not found`);
      }

      // Find warehouse to verify it exists
      const warehouse = await Warehouse.findById(data.warehouseId).session(session);
      if (!warehouse) {
        throw new Error(`Warehouse with ID ${data.warehouseId} not found`);
      }

      // Find or create inventory record
      const { Inventory } = await import('@/app/models');
      const inventory = await Inventory.findOne({
        partId: data.partId,
        warehouseId: data.warehouseId
      }).session(session);

      if (!inventory) {
        // If no inventory record exists, create one with 0 quantity
        const newInventory = new Inventory({
          partId: data.partId,
          warehouseId: data.warehouseId,
          quantity: 0
        });
        await newInventory.save({ session });

        // Refetch to make sure we have the latest state
        const createdInventory = await Inventory.findOne({
          partId: data.partId,
          warehouseId: data.warehouseId
        }).session(session);

        if (!createdInventory) {
          throw new Error('Failed to create inventory record');
        }

        // Use the newly created inventory
        const previousStock = 0;
        // Use canonical field names with fallback to legacy names
        const quantity = data.quantity !== undefined ? data.quantity : data.quantityChanged;
        const newStock = previousStock + quantity;

        // Get transaction type (canonical or legacy)
        const transactionType = data.transactionType || data.type;

        // Check if transaction would result in negative stock
        if (newStock < 0 && transactionType !== 'adjustment_cycle_count' && transactionType !== 'stock_adjustment') {
          throw new Error('Insufficient stock. The operation would result in negative stock.');
        }

        // Create transaction with canonical field names
        newTransaction = new InventoryTransaction({
          partId: data.partId,
          warehouseId: data.warehouseId,
          transactionType: data.transactionType || mapLegacyToCanonicalType(data.type),
          quantity,
          previousStock,
          newStock,
          transactionDate: data.transactionDate || new Date(),
          referenceNumber: data.referenceNumber || data.referenceId || null,
          referenceType: data.referenceType || data.referenceModel || null,
          userId: data.userId,
          notes: data.notes || '',
        });

        await newTransaction.save({ session });

        // Update inventory with new quantity
        updatedInventory = await Inventory.findOneAndUpdate(
          { partId: data.partId, warehouseId: data.warehouseId },
          { $set: { quantity: newStock, lastRestockedDate: new Date() } },
          { new: true, session }
        );
      } else {
        // Use existing inventory
        const previousStock = inventory.quantity;
        // Use canonical field names with fallback to legacy names
        const quantity = data.quantity !== undefined ? data.quantity : data.quantityChanged;
        const newStock = previousStock + quantity;

        // Get transaction type (canonical or legacy)
        const transactionType = data.transactionType || data.type;

        // Check if transaction would result in negative stock
        if (newStock < 0 && transactionType !== 'adjustment_cycle_count' && transactionType !== 'stock_adjustment') {
          throw new Error('Insufficient stock. The operation would result in negative stock.');
        }

        // Create transaction with canonical field names
        newTransaction = new InventoryTransaction({
          partId: data.partId,
          warehouseId: data.warehouseId,
          transactionType: data.transactionType || mapLegacyToCanonicalType(data.type),
          quantity,
          previousStock,
          newStock,
          transactionDate: data.transactionDate || new Date(),
          referenceNumber: data.referenceNumber || data.referenceId || null,
          referenceType: data.referenceType || data.referenceModel || null,
          userId: data.userId,
          notes: data.notes || '',
        });

        await newTransaction.save({ session });

        // Update inventory with new quantity
        updatedInventory = await Inventory.findOneAndUpdate(
          { partId: data.partId, warehouseId: data.warehouseId },
          { $set: { quantity: newStock, lastRestockedDate: new Date() } },
          { new: true, session }
        );
      }

      await session.commitTransaction();

    } catch (error: any) {
      await session.abortTransaction();
      console.error('Error in inventory transaction:', error);
      return NextResponse.json(
        { success: false, error: error.message || 'Failed to create inventory transaction' },
        { status: 500 }
      );
    } finally {
      session.endSession();
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transaction created in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: {
        transaction: newTransaction,
        inventory: updatedInventory
      },
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/inventory-transactions (${duration}ms):`, error);
    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to create inventory transaction' },
      { status: 500 }
    );
  }
}