import { NextRequest, NextResponse } from 'next/server';
import { checkDatabaseHealth } from '@/app/lib/mongodb';
import { getRecentMetrics, getDatabaseStats } from '@/app/lib/mongodb-monitoring';
import withDatabase from '@/app/middlewares/withDatabase';

/**
 * GET handler for checking database health and retrieving monitoring metrics
 * @param request - The incoming request
 * @returns JSON response with database status information
 */
async function handleGET(request: NextRequest) {
  try {
    console.log('[API] GET /api/db-status - Checking database status');
    
    // Parse query parameters
    const url = new URL(request.url);
    const includeMetrics = url.searchParams.get('metrics') === 'true';
    const includeStats = url.searchParams.get('stats') === 'true';
    
    // No need to explicitly call connectToDatabase here as withDatabase middleware handles it
    
    // Get database health status
    const healthStatus = await checkDatabaseHealth();
    
    // Prepare response data
    const response: any = {
      status: healthStatus.status,
      message: healthStatus.message,
      readyState: healthStatus.readyState,
      timestamp: new Date().toISOString()
    };
    
    // Include additional metrics if requested
    if (includeMetrics) {
      response.metrics = getRecentMetrics();
    }
    
    // Include database stats if requested
    if (includeStats) {
      response.stats = await getDatabaseStats();
    }
    
    // Use appropriate status code based on database health
    const statusCode = healthStatus.status === 'healthy' ? 200 : 
                      healthStatus.status === 'warning' ? 200 : 503;
    
    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    console.error('[API] Error checking database status:', error);
    return NextResponse.json({
      status: 'error',
      message: error instanceof Error ? error.message : 'An unknown error occurred',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Wrap the handler with the withDatabase middleware to ensure connection is established
export const GET = withDatabase(handleGET); 