import { NextRequest, NextResponse } from 'next/server';
import { searchProducts, handleMongoDBError } from '@/app/services/product.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for searching products with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with products data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/products/search - Searching products');
    const url = new URL(request.url);

    // Get search query
    const query = url.searchParams.get('query') || '';

    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Sorting parameters
    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // Additional filters
    const filter: any = {};
    
    // Category filter
    const categoryId = url.searchParams.get('category_id');
    if (categoryId) {
      filter.category_id = categoryId;
    }
    
    // Status filter
    const status = url.searchParams.get('status');
    if (status) {
      filter.status = status;
    }
    
    // Price range filter
    const minPrice = url.searchParams.get('minPrice');
    const maxPrice = url.searchParams.get('maxPrice');
    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = parseFloat(minPrice);
      if (maxPrice) filter.price.$lte = parseFloat(maxPrice);
    }

    // Prepare options for service function
    const options = {
      query,
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling searchProducts service with options: ${JSON.stringify(options)}`);

    // Call service function
    const result = await searchProducts(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service searchProducts completed in ${duration}ms with ${result.products.length} results`);

    // Return response
    return NextResponse.json({
      data: result.products,
      pagination: result.pagination,
      error: null,
      meta: { duration, query }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/products/search (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
} 