import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler from the new product service
import { 
  getProductById, 
  getProductByProductId,
  updateProductById,
  updateProductByProductId,
  deleteProductById,
  deleteProductByProductId,
  handleMongoDBError 
} from '@/app/services/product.service';
import mongoose from 'mongoose';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to either _id or product_id
}

/**
 * GET handler for fetching a specific product by its ID or product_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the product ID
 * @returns JSON response with product data or error
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const id = params.id; // Could be MongoDB ObjectId or product_id
  try {
    console.log(`[API] GET /api/products/${id} - Fetching product`);

    let product;
    // Check if it's a valid MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      product = await getProductById(id);
    }
    
    // If not found by ObjectId or not a valid ObjectId, try product_id
    if (!product) {
      product = await getProductByProductId(id);
    }

    const duration = Date.now() - startTime;

    if (!product) {
      console.log(`[API] Product ${id} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Product with ID ${id} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched product ${id} successfully (${duration}ms)`);
    return NextResponse.json({ data: product, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching product ${id} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific product by its ID or product_id
 * @param request - The incoming request with updated product data
 * @param params - Route parameters including the product ID
 * @returns JSON response with updated product data or error
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const id = params.id;
  try {
    console.log(`[API] PUT /api/products/${id} - Updating product`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    let updatedProduct;
    // Check if it's a valid MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      updatedProduct = await updateProductById(id, updateData);
    } 
    
    // If not found by ObjectId or not a valid ObjectId, try product_id
    if (!updatedProduct) {
      updatedProduct = await updateProductByProductId(id, updateData);
    }

    const duration = Date.now() - startTime;
    
    if (!updatedProduct) {
      console.log(`[API] Product ${id} not found for update (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Product with ID ${id} not found`, meta: { duration } },
        { status: 404 }
      );
    }
    
    console.log(`[API] Updated product ${id} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedProduct, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating product ${id} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific product by its ID or product_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the product ID
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: RouteParams }
) {
  const startTime = Date.now();
  const id = params.id;
  try {
    console.log(`[API] DELETE /api/products/${id} - Deleting product`);

    let result;
    // Check if it's a valid MongoDB ObjectId first
    if (mongoose.Types.ObjectId.isValid(id)) {
      result = await deleteProductById(id);
    } 
    
    // If not a valid ObjectId, try product_id
    if (!result) {
      result = await deleteProductByProductId(id);
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Deleted product ${id} successfully (${duration}ms)`);
    return NextResponse.json({ 
      data: result, 
      error: null, 
      meta: { duration } 
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting product ${id} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
