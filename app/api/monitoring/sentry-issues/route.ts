import { NextRequest, NextResponse } from 'next/server';
import { captureException } from '@/app/lib/sentry-utils';

/**
 * API route to fetch issues from Sentry
 * This acts as a proxy to the Sentry API to avoid exposing API keys in the frontend
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('projectId') || '4509213084418048';
    const query = searchParams.get('query') || 'is:unresolved';
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    // Validate parameters
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Invalid limit parameter. Must be between 1 and 100.' },
        { status: 400 }
      );
    }

    // In a production environment, you would use the Sentry API token from environment variables
    // const sentryToken = process.env.SENTRY_API_TOKEN;
    // if (!sentryToken) {
    //   throw new Error('Sentry API token not configured');
    // }

    // For now, we'll return mock data since we don't want to make actual API calls in this example
    // In a real implementation, you would make a fetch request to the Sentry API
    
    // Example of how the actual API call would look:
    // const response = await fetch(
    //   `https://sentry.io/api/0/projects/trendtech-innovations/trend_ims/issues/?query=${encodeURIComponent(query)}&limit=${limit}`,
    //   {
    //     headers: {
    //       Authorization: `Bearer ${sentryToken}`,
    //       'Content-Type': 'application/json',
    //     },
    //   }
    // );
    // 
    // if (!response.ok) {
    //   throw new Error(`Sentry API returned ${response.status}: ${response.statusText}`);
    // }
    // 
    // const data = await response.json();

    // Mock data for demonstration
    const mockIssues = [
      {
        id: 'IMS-TEJ-H',
        title: 'Error fetching analytics data',
        culprit: 'GET /api/analytics',
        status: 'unresolved',
        level: 'error',
        count: 24,
        lastSeen: new Date().toISOString(),
        project: { name: 'Trend_IMS' }
      },
      {
        id: 'IMS-TEJ-G',
        title: 'Failed to load inventory data',
        culprit: 'GET /api/inventory',
        status: 'unresolved',
        level: 'error',
        count: 12,
        lastSeen: new Date(Date.now() - 3600000).toISOString(),
        project: { name: 'Trend_IMS' }
      },
      {
        id: 'IMS-TEJ-3',
        title: 'SentryExampleAPIError',
        culprit: '/api/sentry-example-api',
        status: 'unresolved',
        level: 'error',
        count: 5,
        lastSeen: new Date(Date.now() - 86400000).toISOString(),
        project: { name: 'Trend_IMS' }
      }
    ];

    // Return the mock data
    return NextResponse.json({ issues: mockIssues });
  } catch (error) {
    console.error('Error fetching Sentry issues:', error);
    captureException(error, {
      tags: {
        component: 'sentry-issues-api',
      },
    });

    return NextResponse.json(
      { error: 'Failed to fetch Sentry issues' },
      { status: 500 }
    );
  }
}