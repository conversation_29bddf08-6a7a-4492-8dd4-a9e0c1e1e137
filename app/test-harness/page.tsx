"use client";

import React from 'react';
import { ThemeProvider, useTheme } from '@/app/context/ThemeContext';
import ThemeToggle from '@/app/components/ui/ThemeToggle';

// Import UI Components
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Input } from '@/app/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Textarea } from '@/app/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/app/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/app/components/ui/dropdown-menu';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { Badge } from '@/app/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/ui/tooltip';
import CalendarComponent from '@/app/components/charts/CalendarComponent'; // Assuming path
import ErrorDisplay from '@/app/components/ui/ErrorDisplay';
import StatusCard from '@/app/components/ui/StatusCard';
import { AlertCircle, Package } from 'lucide-react';

const TestHarnessContent: React.FC = () => {
  const { theme } = useTheme();

  return (
    <div className="p-8 space-y-8 bg-background text-foreground">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">UI Component Test Harness</h1>
        <ThemeToggle />
      </div>

      <p className="text-muted-foreground">
        This page displays various UI components to test their appearance and functionality in both light and dark themes.
      </p>

      {/* Section for Buttons */}
      <Card>
        <CardHeader><CardTitle>Buttons</CardTitle></CardHeader>
        <CardContent className="space-x-2 space-y-2">
          <Button>Default</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="destructive">Destructive</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="ghost">Ghost</Button>
          <Button variant="link">Link</Button>
          <Button disabled>Disabled</Button>
        </CardContent>
      </Card>

      {/* Section for Cards */}
      <Card>
        <CardHeader><CardTitle>Cards</CardTitle></CardHeader>
        <CardContent>
          This is the content inside a standard Card.
        </CardContent>
      </Card>
      
      {/* Status Card Example */}
      <StatusCard
        title="Sample Status"
        mainStat={{ value: 123, label: 'Total' }}
        data={{ 'Active': 80, 'Inactive': 43 }}
        icon={<Package />}
        color="blue"
      />

      {/* Section for Form Elements */}
      <Card>
        <CardHeader><CardTitle>Form Elements</CardTitle></CardHeader>
        <CardContent className="space-y-4">
          <Input placeholder="Input Field" />
          <Select>
            <SelectTrigger><SelectValue placeholder="Select Option" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="option1">Option 1</SelectItem>
              <SelectItem value="option2">Option 2</SelectItem>
            </SelectContent>
          </Select>
          <Textarea placeholder="Textarea Field" />
        </CardContent>
      </Card>

      {/* Section for Table */}
      <Card>
        <CardHeader><CardTitle>Table</CardTitle></CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Header 1</TableHead>
                <TableHead>Header 2</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>Data 1</TableCell>
                <TableCell>Data 2</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Data 3</TableCell>
                <TableCell>Data 4</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Section for Tabs */}
      <Card>
        <CardHeader><CardTitle>Tabs</CardTitle></CardHeader>
        <CardContent>
          <Tabs defaultValue="tab1">
            <TabsList>
              <TabsTrigger value="tab1">Tab 1</TabsTrigger>
              <TabsTrigger value="tab2">Tab 2</TabsTrigger>
            </TabsList>
            <TabsContent value="tab1">Content for Tab 1.</TabsContent>
            <TabsContent value="tab2">Content for Tab 2.</TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Section for Dropdown Menu */}
      <Card>
        <CardHeader><CardTitle>Dropdown Menu</CardTitle></CardHeader>
        <CardContent>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">Open Menu</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>Item 1</DropdownMenuItem>
              <DropdownMenuItem>Item 2</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardContent>
      </Card>

      {/* Section for Alert */}
      <Card>
        <CardHeader><CardTitle>Alert</CardTitle></CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Alert Title</AlertTitle>
            <AlertDescription>This is an alert description.</AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Section for Badge */}
      <Card>
        <CardHeader><CardTitle>Badge</CardTitle></CardHeader>
        <CardContent className="space-x-2">
          <Badge>Default</Badge>
          <Badge variant="secondary">Secondary</Badge>
          <Badge variant="destructive">Destructive</Badge>
          <Badge variant="outline">Outline</Badge>
        </CardContent>
      </Card>

      {/* Section for Tooltip */}
      <Card>
        <CardHeader><CardTitle>Tooltip</CardTitle></CardHeader>
        <CardContent>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline">Hover Me</Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>This is a tooltip</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardContent>
      </Card>

      {/* Section for Calendar */}
      <Card>
        <CardHeader><CardTitle>Calendar Component</CardTitle></CardHeader>
        <CardContent>
          <CalendarComponent />
        </CardContent>
      </Card>

      {/* Section for Error Display */}
      <Card>
        <CardHeader><CardTitle>Error Display</CardTitle></CardHeader>
        <CardContent>
          <ErrorDisplay error={new Error("This is a sample error message.")} message="Failed to load data" />
        </CardContent>
      </Card>

    </div>
  );
};

// Wrap the content with ThemeProvider to ensure context is available
const TestHarnessPage: React.FC = () => {
  return (
    <ThemeProvider>
      <TestHarnessContent />
    </ThemeProvider>
  );
};

export default TestHarnessPage; 