"use client";

import React, { useState } from 'react';
import { SentryTest } from '@/app/components/features';
import ErrorBoundary from '@/components/ui/ErrorBoundary';
import { Button } from '@/components/ui/button';

/**
 * Component that throws an error during rendering
 */
interface ErrorThrowerProps {
  shouldThrow: boolean;
}

const ErrorThrower: React.FC<ErrorThrowerProps> = ({ shouldThrow }) => {
  if (shouldThrow) {
    // Create a test error with a clearer message
    const testError = new Error('[TEST ERROR] This is an intentional test error triggered by the button');

    // Add a custom property to identify it as a test
    (testError as any).isTestError = true;

    // Throw the test error during rendering
    throw testError;
  }

  return null;
};

/**
 * Test page for Sentry integration
 */
const SentryTestPage: React.FC = () => {
  // State to control when to throw the error
  const [shouldThrowError, setShouldThrowError] = useState(false);

  // Function to trigger an error for testing the error boundary
  const triggerError = () => {
    // Set state to trigger the error during next render
    setShouldThrowError(true);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Sentry Integration Test</h1>

      <div className="grid gap-8 md:grid-cols-2">
        <div>
          <h2 className="text-xl font-semibold mb-4">Sentry Connection Test</h2>
          <SentryTest />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Error Boundary Test</h2>
          <ErrorBoundary>
            {/* This component will throw an error during rendering if shouldThrowError is true */}
            <ErrorThrower shouldThrow={shouldThrowError} />

            <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
              <div className="mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                <h3 className="text-amber-800 dark:text-amber-300 font-medium mb-1">Test Feature</h3>
                <p className="text-amber-700 dark:text-amber-400 text-sm">
                  This button intentionally triggers an error to test the ErrorBoundary component.
                  The error is expected and will be caught and reported to Sentry.
                </p>
              </div>

              <p className="mb-4">
                Click the button below to trigger a test error that will be caught by the ErrorBoundary
                and reported to Sentry.
              </p>

              <Button
                variant="destructive"
                onClick={triggerError}
                title="This will intentionally generate a test error to verify the ErrorBoundary component"
              >
                Trigger Test Error (Safe)
              </Button>

              <p className="mt-4 text-xs text-gray-500 dark:text-gray-400 italic">
                Note: This is a controlled test. The error is intentional and helps verify that
                the ErrorBoundary component is working correctly.
              </p>
            </div>
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
};

export default SentryTestPage;
