"use client";

/**
 * Theme Context Module
 * Provides theme management functionality for the application
 */
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

/** Valid theme types for the application */
type Theme = 'light' | 'dark';

/**
 * Interface for the theme context
 */
interface ThemeContextType {
  /** Current theme ('light' or 'dark') */
  theme: Theme;
  /** Function to toggle between light and dark themes */
  toggleTheme: () => void;
  /** Function to explicitly set the theme */
  setTheme: (theme: Theme) => void;
}

/**
 * Default theme context used before provider is mounted
 */
const defaultThemeContext: ThemeContextType = {
  theme: 'light', // Default theme
  toggleTheme: () => console.warn('ThemeProvider not mounted'),
  setTheme: () => console.warn('ThemeProvider not mounted'),
};

/** Theme context for providing theme information throughout the app */
const ThemeContext = createContext<ThemeContextType>(defaultThemeContext); // Provide default context

/**
 * Theme Provider component that manages theme state and preferences
 * Persists theme choice in localStorage and syncs with system preferences
 * @param children - Child components that will have access to the theme context
 */
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize with a default theme for SSR
  const [theme, setThemeState] = useState<Theme>('light');
  const [mounted, setMounted] = useState(false);

  /**
   * Initialize theme from localStorage or system preference after mounting
   */
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme;
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = savedTheme || (prefersDark ? 'dark' : 'light');
    setThemeState(initialTheme);
    setMounted(true);
  }, []);

  /**
   * Set theme and update localStorage
   * @param newTheme - The theme to set
   */
  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    if (mounted) {
      localStorage.setItem('theme', newTheme);
    }
  }, [mounted]);

  /**
   * Apply theme classes to document when theme changes
   */
  useEffect(() => {
    if (!mounted) return;
    
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    document.documentElement.setAttribute('data-theme', theme);
  }, [theme, mounted]);

  /**
   * Toggle between light and dark mode
   */
  const toggleTheme = useCallback(() => {
    setThemeState(prevTheme => {
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';
      if (mounted) {
        localStorage.setItem('theme', newTheme);
      }
      return newTheme;
    });
  }, [mounted]);

  // Always render the provider, use dynamic values once mounted
  const value = mounted ? { theme, toggleTheme, setTheme } : defaultThemeContext;

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Custom hook to use the theme context
 * @returns The theme context with current theme and theme management functions
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  // No need to check for undefined anymore if default context is provided
  // if (context === undefined) {
  //   throw new Error('useTheme must be used within a ThemeProvider');
  // }
  return context;
}; 