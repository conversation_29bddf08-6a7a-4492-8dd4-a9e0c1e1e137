/**
 * Utility functions for Sentry error tracking and monitoring
 */
import * as Sentry from '@sentry/nextjs';

/**
 * Severity levels for Sentry events
 */
export type SeverityLevel = 'fatal' | 'error' | 'warning' | 'info' | 'debug';

/**
 * Options for capturing exceptions and messages
 */
export interface CaptureOptions {
  /** Tags to add to the event */
  tags?: Record<string, string>;
  /** Extra context to add to the event */
  extra?: Record<string, any>;
  /** User information to associate with the event */
  user?: {
    id?: string;
    email?: string;
    username?: string;
    [key: string]: any;
  };
  /** Level of the event */
  level?: SeverityLevel;
  /** Fingerprint to group events */
  fingerprint?: string[];
  /** Whether to sample the event */
  sample?: boolean;
}

/**
 * Capture an exception with Sentry
 *
 * @param error - The error to capture
 * @param options - Additional options for the event
 * @returns The event ID
 *
 * @example
 * try {
 *   // Some code that might throw an error
 * } catch (error) {
 *   captureException(error, {
 *     tags: { component: 'UserProfile' },
 *     extra: { userId: '123' }
 *   });
 * }
 */
export const captureException = (error: Error | unknown, options?: CaptureOptions | Record<string, any>) => {
  // Check if this is a test error
  const isTestError =
    (error instanceof Error && (error as any).isTestError) ||
    (options?.tags?.test === 'true') ||
    (options?.extra?.isTest === true);

  // Use different log level for test errors
  if (isTestError) {
    console.log('[Test Error]', error);
  } else {
    console.error('[Error]', error);
  }

  // Handle legacy context parameter
  if (options && !options.tags && !options.extra && !options.user) {
    return Sentry.captureException(error, {
      extra: options
    });
  }

  // Handle new options parameter
  return Sentry.withScope((scope) => {
    // Add tags
    if (options?.tags) {
      Object.entries(options.tags).forEach(([key, value]) => {
        scope.setTag(key, value);
      });
    }

    // Always tag test errors
    if (isTestError) {
      scope.setTag('test', 'true');
      scope.setTag('intentional', 'true');
    }

    // Add extra context
    if (options?.extra) {
      Object.entries(options.extra).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
    }

    // Add test context for test errors
    if (isTestError) {
      scope.setExtra('isTest', true);
      scope.setExtra('description', 'This error was intentionally triggered for testing purposes');
    }

    // Add user information
    if (options?.user) {
      scope.setUser(options.user);
    }

    // Set level
    if (options?.level) {
      scope.setLevel(options.level as Sentry.SeverityLevel);
    } else if (isTestError) {
      // Use info level for test errors
      scope.setLevel('info');
    }

    // Set fingerprint
    if (options?.fingerprint) {
      scope.setFingerprint(options.fingerprint);
    } else if (isTestError) {
      // Group all test errors together
      scope.setFingerprint(['test-error']);
    }

    // Capture the exception
    return Sentry.captureException(error);
  });
};

/**
 * Capture a message with Sentry
 *
 * @param message - The message to capture
 * @param level - The severity level of the message
 * @param options - Additional options for the event
 * @returns The event ID
 *
 * @example
 * captureMessage('User logged in', 'info', {
 *   tags: { component: 'LoginForm' },
 *   extra: { userId: '123' }
 * });
 */
export const captureMessage = (
  message: string,
  level: SeverityLevel = 'info',
  options?: CaptureOptions | Record<string, any>
) => {
  console.log(`[${level}] ${message}`);

  // Handle legacy context parameter
  if (options && !options.tags && !options.extra && !options.user) {
    return Sentry.captureMessage(message, {
      level: level as Sentry.SeverityLevel,
      extra: options
    });
  }

  // Handle new options parameter
  return Sentry.withScope((scope) => {
    // Add tags
    if (options?.tags) {
      Object.entries(options.tags).forEach(([key, value]) => {
        scope.setTag(key, value);
      });
    }

    // Add extra context
    if (options?.extra) {
      Object.entries(options.extra).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
    }

    // Add user information
    if (options?.user) {
      scope.setUser(options.user);
    }

    // Set level
    scope.setLevel(level as Sentry.SeverityLevel);

    // Set fingerprint
    if (options?.fingerprint) {
      scope.setFingerprint(options.fingerprint);
    }

    // Capture the message
    return Sentry.captureMessage(message);
  });
};

/**
 * Start a transaction for performance monitoring
 *
 * @param name - The name of the transaction
 * @param op - The operation type
 * @param options - Additional options for the transaction
 * @returns The transaction span
 *
 * @example
 * const transaction = startTransaction('Load User Profile', 'navigation');
 * // Do some work
 * transaction.end();
 */
export const startTransaction = (
  name: string,
  op: string,
  options?: {
    tags?: Record<string, string>;
    data?: Record<string, any>;
  }
) => {
  // Use the new startInactiveSpan API with forceTransaction to create a transaction
  const transaction = Sentry.startInactiveSpan({
    name,
    op,
    forceTransaction: true
  });

  // Add tags and data if provided
  if (options?.tags) {
    Object.entries(options.tags).forEach(([key, value]) => {
      transaction.setTag(key, value);
    });
  }

  if (options?.data) {
    Object.entries(options.data).forEach(([key, value]) => {
      transaction.setData(key, value);
    });
  }

  return transaction;
};

/**
 * Set user information for Sentry events
 *
 * @param user - User information to associate with events
 *
 * @example
 * setUser({
 *   id: '123',
 *   email: '<EMAIL>',
 *   username: 'johndoe',
 *   role: 'admin'
 * });
 */
export const setUser = (user: CaptureOptions['user'] | null) => {
  Sentry.setUser(user);
};

/**
 * Set extra context for Sentry events
 *
 * @param key - Context key
 * @param value - Context value
 *
 * @example
 * setExtra('serverName', 'prod-1');
 */
export const setExtra = (key: string, value: any) => {
  Sentry.setExtra(key, value);
};

/**
 * Set tag for Sentry events
 *
 * @param key - Tag key
 * @param value - Tag value
 *
 * @example
 * setTag('environment', 'production');
 */
export const setTag = (key: string, value: string) => {
  Sentry.setTag(key, value);
};

/**
 * Add breadcrumb to the current scope
 *
 * @param breadcrumb - Breadcrumb data
 *
 * @example
 * addBreadcrumb({
 *   category: 'auth',
 *   message: 'User logged in',
 *   level: 'info'
 * });
 */
export const addBreadcrumb = (breadcrumb: Sentry.Breadcrumb) => {
  Sentry.addBreadcrumb(breadcrumb);
};

/**
 * Check if Sentry is enabled
 *
 * @returns True if Sentry is enabled
 */
export const isSentryEnabled = (): boolean => {
  return Sentry.getCurrentScope().getClient() !== undefined;
};

/**
 * Wrap a function with Sentry error tracking
 *
 * @param fn - The function to wrap
 * @param options - Options for error tracking
 * @returns The wrapped function
 *
 * @example
 * const riskyFunction = withErrorTracking(
 *   (id: string) => {
 *     // Some code that might throw an error
 *     return fetchData(id);
 *   },
 *   {
 *     name: 'fetchUserData',
 *     tags: { component: 'UserProfile' }
 *   }
 * );
 */
export const withErrorTracking = <T extends (...args: any[]) => any>(
  fn: T,
  options: {
    name?: string;
    tags?: Record<string, string>;
    extras?: Record<string, any>;
    level?: SeverityLevel;
    fingerprint?: string[];
    transaction?: string;
    capturePerformance?: boolean;
  } = {}
): ((...args: Parameters<T>) => ReturnType<T>) => {
  return (...args: Parameters<T>): ReturnType<T> => {
    // Start transaction if performance tracking is enabled
    const transaction = options.capturePerformance
      ? startTransaction(
          options.transaction || options.name || fn.name || 'anonymous function',
          'function',
          { tags: options.tags }
        )
      : null;

    try {
      const result = fn(...args);

      // Handle promises
      if (result instanceof Promise) {
        return result
          .then((value) => {
            // Finish transaction if it exists
            if (transaction) {
              transaction.setStatus('ok');
              transaction.end();
            }
            return value;
          })
          .catch((error) => {
            // Finish transaction with error status
            if (transaction) {
              transaction.setStatus('internal_error');
              transaction.end();
            }

            const name = options.name || fn.name || 'anonymous function';
            captureException(error, {
              tags: {
                function: name,
                ...options.tags
              },
              extra: {
                arguments: args,
                ...options.extras
              },
              level: options.level,
              fingerprint: options.fingerprint
            });

            // Re-throw the error
            throw error;
          }) as ReturnType<T>;
      }

      // Finish transaction if it exists
      if (transaction) {
        transaction.setStatus('ok');
        transaction.end();
      }

      return result;
    } catch (error) {
      // Finish transaction with error status
      if (transaction) {
        transaction.setStatus('internal_error');
        transaction.end();
      }

      const name = options.name || fn.name || 'anonymous function';
      captureException(error, {
        tags: {
          function: name,
          ...options.tags
        },
        extra: {
          arguments: args,
          ...options.extras
        },
        level: options.level,
        fingerprint: options.fingerprint
      });

      // Re-throw the error
      throw error;
    }
  };
};

/**
 * Flush all events to Sentry
 *
 * @param timeout - Maximum time in ms the client should wait for events to be flushed
 * @returns A promise that resolves when all events are flushed
 */
export const flush = (timeout?: number): Promise<boolean> => {
  return Sentry.flush(timeout);
};

/**
 * Close Sentry SDK
 *
 * @returns A promise that resolves when the SDK is closed
 */
export const close = (): Promise<boolean> => {
  return Sentry.close();
};
