import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { format } from 'date-fns';

/**
 * MongoDB Monitoring Configuration
 *
 * This module provides utilities for monitoring MongoDB performance,
 * logging slow queries, and tracking database operations.
 */

// --- Configuration ---
const LOGS_DIR = path.join(process.cwd(), 'logs');
const SLOW_QUERY_THRESHOLD_MS = 500; // Log queries that take longer than 500ms
const ENABLE_PROFILING = process.env.NODE_ENV !== 'production' || process.env.ENABLE_DB_PROFILING === 'true';
const LOG_TO_CONSOLE = process.env.LOG_TO_CONSOLE === 'true';
const LOG_TO_FILE = process.env.LOG_TO_FILE !== 'false';

// Ensure logs directory exists
if (LOG_TO_FILE && !fs.existsSync(LOGS_DIR)) {
  fs.mkdirSync(LOGS_DIR, { recursive: true });
}

interface QueryMetric {
  operation: string;
  collection: string;
  query: any;
  options: any;
  duration: number;
  timestamp: Date;
}

interface ConnectionMetric {
  event: string;
  timestamp: Date;
  details?: any;
}

// In-memory cache for recent metrics (to avoid excessive file I/O)
const queryMetricsCache: QueryMetric[] = [];
const connectionMetricsCache: ConnectionMetric[] = [];
const MAX_CACHE_SIZE = 100;

/**
 * Logs a message to either console, file, or both based on configuration
 */
function logMessage(message: string, type: 'query' | 'connection' | 'error') {
  const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
  const formattedMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;

  if (LOG_TO_CONSOLE) {
    if (type === 'error') {
      console.error(formattedMessage);
    } else {
      console.log(formattedMessage);
    }
  }

  if (LOG_TO_FILE) {
    const today = format(new Date(), 'yyyy-MM-dd');
    const logFile = path.join(LOGS_DIR, `mongodb-${type}-${today}.log`);

    fs.appendFile(logFile, formattedMessage + '\n', (err) => {
      if (err) console.error(`Failed to write to log file: ${err.message}`);
    });
  }
}

/**
 * Logs slow query metrics
 */
function logQueryMetric(metric: QueryMetric) {
  // Add to in-memory cache
  queryMetricsCache.push(metric);

  // Keep cache size in check
  if (queryMetricsCache.length > MAX_CACHE_SIZE) {
    queryMetricsCache.shift();
  }

  // Only log slow queries
  if (metric.duration >= SLOW_QUERY_THRESHOLD_MS) {
    const message = `Slow query: ${metric.operation} on ${metric.collection} took ${metric.duration}ms - Query: ${JSON.stringify(metric.query)}`;
    logMessage(message, 'query');
  }
}

/**
 * Logs connection events
 */
function logConnectionMetric(metric: ConnectionMetric) {
  // Add to in-memory cache
  connectionMetricsCache.push(metric);

  // Keep cache size in check
  if (connectionMetricsCache.length > MAX_CACHE_SIZE) {
    connectionMetricsCache.shift();
  }

  const message = `Connection ${metric.event}${metric.details ? ': ' + JSON.stringify(metric.details) : ''}`;
  logMessage(message, 'connection');
}

/**
 * Initializes MongoDB monitoring for a mongoose connection
 */
export function setupMongoDBMonitoring() {
  if (!ENABLE_PROFILING) {
    console.log('[MongoDB Monitoring] Profiling disabled');
    return;
  }

  console.log('[MongoDB Monitoring] Setting up monitoring');

  // Monitor connection events
  mongoose.connection.on('connected', () => {
    logConnectionMetric({
      event: 'connected',
      timestamp: new Date(),
      details: {
        host: mongoose.connection.host,
        name: mongoose.connection.name
      }
    });
  });

  mongoose.connection.on('disconnected', () => {
    logConnectionMetric({
      event: 'disconnected',
      timestamp: new Date()
    });
  });

  mongoose.connection.on('error', (err) => {
    logConnectionMetric({
      event: 'error',
      timestamp: new Date(),
      details: {
        message: err.message,
        code: err.code
      }
    });
    logMessage(`Connection error: ${err.message}`, 'error');
  });

  // Monitor queries using Mongoose hooks
  if (ENABLE_PROFILING) {
    // Use mongoose middleware instead of overriding collection methods
    // This avoids TypeScript signature errors and is more compatible with mongoose
    mongoose.plugin(schema => {
      // Monitor all find operations
      schema.pre('find', function() {
        (this as any)._startTime = Date.now();
      });

      schema.post('find', function(result, next) {
        const duration = Date.now() - ((this as any)._startTime || Date.now());

        logQueryMetric({
          operation: 'find',
          collection: (this as any).model.collection.name,
          query: (this as any).getQuery(),
          options: (this as any).getOptions(),
          duration,
          timestamp: new Date()
        });

        next();
      });

      // Monitor all findOne operations
      schema.pre('findOne', function() {
        (this as any)._startTime = Date.now();
      });

      schema.post('findOne', function(result, next) {
        const duration = Date.now() - ((this as any)._startTime || Date.now());

        logQueryMetric({
          operation: 'findOne',
          collection: (this as any).model.collection.name,
          query: (this as any).getQuery(),
          options: (this as any).getOptions(),
          duration,
          timestamp: new Date()
        });

        next();
      });

      // Monitor all insert operations
      schema.pre('save', function() {
        (this as any)._startTime = Date.now();
      });

      schema.post('save', function(doc, next) {
        const duration = Date.now() - ((this as any)._startTime || Date.now());

        logQueryMetric({
          operation: 'save',
          collection: (this as any).constructor.collection.name,
          query: { _id: (this as any)._id },
          options: {},
          duration,
          timestamp: new Date()
        });

        next();
      });

      // Monitor all update operations
      schema.pre('updateOne', function() {
        (this as any)._startTime = Date.now();
      });

      schema.post('updateOne', function(result, next) {
        const duration = Date.now() - ((this as any)._startTime || Date.now());

        logQueryMetric({
          operation: 'updateOne',
          collection: (this as any).model.collection.name,
          query: (this as any).getQuery(),
          options: (this as any).getOptions(),
          duration,
          timestamp: new Date()
        });

        next();
      });

      // Monitor all delete operations
      schema.pre('deleteOne', function() {
        (this as any)._startTime = Date.now();
      });

      schema.post('deleteOne', function(result, next) {
        const duration = Date.now() - ((this as any)._startTime || Date.now());

        logQueryMetric({
          operation: 'deleteOne',
          collection: (this as any).model.collection.name,
          query: (this as any).getQuery(),
          options: (this as any).getOptions(),
          duration,
          timestamp: new Date()
        });

        next();
      });
    });
  }

  console.log('[MongoDB Monitoring] Monitoring setup complete');
}

/**
 * Starts MongoDB profiler at database level if running against a MongoDB instance
 * that supports it (not available in serverless environments)
 */
export async function enableMongoDBProfiler() {
  // Skip profiler enablement to avoid CMD_NOT_ALLOWED_profile errors
  // This is a common issue with MongoDB Atlas and other managed services
  // where the user doesn't have sufficient permissions
  console.log('[MongoDB Monitoring] Using Mongoose monitoring only (skipping database profiler)');

  // The following code is kept but commented out for reference
  /*
  try {
    if (!mongoose.connection.db) {
      console.log('[MongoDB Monitoring] No active database connection');
      return;
    }

    // Set profiling level to 1 (log slow queries only)
    await mongoose.connection.db.command({
      profile: 1,
      slowms: SLOW_QUERY_THRESHOLD_MS
    });

    console.log(`[MongoDB Monitoring] Profiler enabled (slowms: ${SLOW_QUERY_THRESHOLD_MS}ms)`);
  } catch (error) {
    console.error('[MongoDB Monitoring] Failed to enable profiler:',
      error instanceof Error ? error.message : error);

    if (error instanceof Error && error.message.includes('not authorized')) {
      console.error('[MongoDB Monitoring] Profiler requires admin privileges. Using Mongoose monitoring instead.');
    }
  }
  */
}

/**
 * Gets recent metrics for display in monitoring dashboard
 */
export function getRecentMetrics() {
  return {
    queries: [...queryMetricsCache],
    connections: [...connectionMetricsCache]
  };
}

/**
 * Gets performance statistics for the database
 */
export async function getDatabaseStats() {
  try {
    if (!mongoose.connection.db) {
      return { error: 'No active database connection' };
    }

    const serverStatus = await mongoose.connection.db.command({ serverStatus: 1 });
    const dbStats = await mongoose.connection.db.command({ dbStats: 1 });

    return {
      connections: serverStatus.connections,
      network: serverStatus.network,
      opcounters: serverStatus.opcounters,
      memory: serverStatus.mem,
      storage: {
        dataSize: dbStats.dataSize,
        storageSize: dbStats.storageSize,
        indexes: dbStats.indexes,
        indexSize: dbStats.indexSize
      }
    };
  } catch (error) {
    console.error('[MongoDB Monitoring] Failed to get database stats:',
      error instanceof Error ? error.message : error);
    return { error: 'Failed to get database stats' };
  }
}

export default setupMongoDBMonitoring;