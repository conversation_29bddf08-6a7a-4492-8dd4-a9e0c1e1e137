/**
 * Parts data functions
 */

/**
 * Fetches all parts from the API with pagination support
 * @param page - Page number (default: 1)
 * @param limit - Items per page (default: 50)
 * @param search - Optional search term
 * @returns Object containing parts array and pagination info
 */
export async function getAllParts(options = { page: 1, limit: 50, search: '' }) {
  try {
    const { page, limit, search } = options;
    
    // Build query parameters
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    if (search) {
      params.append('search', search);
      // Use search endpoint if a search term is provided
      const response = await fetch(`/api/parts/search?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to search parts');
      }
      const data = await response.json();
      return {
        parts: data.data || [],
        pagination: data.pagination || { 
          total: 0,
          page,
          limit,
          pages: 0
        }
      };
    }
    
    // Use regular parts endpoint if no search term
    const response = await fetch(`/api/parts?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch parts');
    }
    const data = await response.json();
    return {
      parts: data.data || [],
      pagination: data.pagination || { 
        total: 0,
        page,
        limit,
        pages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching parts:', error);
    return {
      parts: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 50,
        pages: 0
      }
    };
  }
} 