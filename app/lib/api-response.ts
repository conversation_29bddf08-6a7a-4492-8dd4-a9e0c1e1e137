import { NextResponse } from 'next/server';

/**
 * Helper function to create a standardized success response
 * @param data - The response data
 * @param message - Optional success message
 * @param metadata - Optional metadata like pagination info
 * @param statusCode - HTTP status code (default: 200)
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized success response.
 *
 * @param {any} data - The main data payload for the response.
 * @param {string} [message] - An optional descriptive message for the success.
 * @param {any} [metadata] - Optional metadata, such as pagination information.
 * @param {number} [statusCode=200] - The HTTP status code for the response (default is 200).
 * @returns {NextResponse} A NextResponse object formatted for a successful API call.
 */
export function successResponse(
  data: any, 
  message?: string, 
  metadata?: any, 
  statusCode: number = 200
) {
  return NextResponse.json({
    success: true,
    data,
    message,
    metadata
  }, {
    status: statusCode
  });
}

/**
 * Helper function to create a standardized error response
 * @param code - Error code
 * @param message - Human-readable error message
 * @param details - Optional array of detailed error information
 * @param statusCode - HTTP status code (default: 400)
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized error response.
 *
 * @param {string} code - A specific error code string (e.g., 'VALIDATION_ERROR', 'RESOURCE_NOT_FOUND').
 * @param {string} message - A human-readable message describing the error.
 * @param {any[]} [details] - An optional array of more detailed error information (e.g., field-specific validation errors).
 * @param {number} [statusCode=400] - The HTTP status code for the error response (default is 400).
 * @returns {NextResponse} A NextResponse object formatted for a failed API call.
 */
export function errorResponse(
  code: string, 
  message: string, 
  details?: any[], 
  statusCode: number = 400
) {
  return NextResponse.json({
    success: false,
    error: {
      code,
      message,
      details
    }
  }, {
    status: statusCode
  });
}

/**
 * Helper function to create a validation error response
 * @param errors - Array of validation errors
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized validation error response (HTTP 422).
 *
 * @param {{ field: string, message: string }[]} errors - An array of validation error objects, each specifying the field and message.
 * @returns {NextResponse} A NextResponse object for validation failures.
 */
export function validationErrorResponse(
  errors: { field: string, message: string }[]
) {
  return errorResponse(
    'VALIDATION_ERROR',
    'Validation failed',
    errors,
    422
  );
}

/**
 * Helper function to create a not found error response
 * @param resourceType - Type of resource that was not found
 * @param identifier - Identifier that was used to look up the resource
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized resource not found error response (HTTP 404).
 *
 * @param {string} resourceType - The type of the resource that was not found (e.g., 'Product', 'User').
 * @param {string} identifier - The identifier used to look up the resource (e.g., an ID or name).
 * @returns {NextResponse} A NextResponse object for resource not found errors.
 */
export function notFoundResponse(
  resourceType: string,
  identifier: string
) {
  return errorResponse(
    'RESOURCE_NOT_FOUND',
    `${resourceType} with identifier ${identifier} not found`,
    [],
    404
  );
}

/**
 * Helper function to create a duplicate resource error response
 * @param resourceType - Type of resource that already exists
 * @param identifier - Identifier that caused the conflict
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized duplicate resource error response (HTTP 409).
 *
 * @param {string} resourceType - The type of the resource that already exists.
 * @param {string} identifier - The identifier that caused the conflict.
 * @returns {NextResponse} A NextResponse object for duplicate resource errors.
 */
export function duplicateResourceResponse(
  resourceType: string,
  identifier: string
) {
  return errorResponse(
    'DUPLICATE_RESOURCE',
    `${resourceType} with identifier ${identifier} already exists`,
    [],
    409
  );
}

/**
 * Helper function to create a database error response
 * @param message - Error message
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized database error response (HTTP 500).
 *
 * @param {string} message - A message describing the database error.
 * @returns {NextResponse} A NextResponse object for database errors.
 */
export function databaseErrorResponse(
  message: string
) {
  return errorResponse(
    'DATABASE_ERROR',
    message,
    [],
    500
  );
}

/**
 * Helper function to create an internal error response
 * @param message - Error message
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized internal server error response (HTTP 500).
 *
 * @param {string} message - A message describing the internal error.
 * @returns {NextResponse} A NextResponse object for internal server errors.
 */
export function internalErrorResponse(
  message: string
) {
  return errorResponse(
    'INTERNAL_ERROR',
    message,
    [],
    500
  );
}

/**
 * Helper function to create a bad request error response
 * @param message - Error message
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized bad request error response (HTTP 400).
 *
 * @param {string} message - A message describing why the request was bad.
 * @returns {NextResponse} A NextResponse object for bad requests.
 */
export function badRequestResponse(
  message: string
) {
  return errorResponse(
    'BAD_REQUEST',
    message,
    [],
    400
  );
}

/**
 * Helper function to create a created response
 * @param data - The created resource
 * @param message - Optional success message
 * @returns NextResponse with standardized format
 */
/**
 * Creates a standardized resource created response (HTTP 201).
 *
 * @param {any} data - The data of the newly created resource.
 * @param {string} [message] - An optional message, defaults to 'Resource created successfully'.
 * @returns {NextResponse} A NextResponse object for successful resource creation.
 */
export function createdResponse(
  data: any,
  message?: string
) {
  return successResponse(
    data,
    message || 'Resource created successfully',
    null,
    201
  );
}
