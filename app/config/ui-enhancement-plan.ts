/
**
 * UI Enhancement Implementation Plan
 *
 * This configuration defines the phased approach for implementing Magic UI enhancements
 * to the Trend IMS application, focusing on assemblies components first.
 */

import { UIEnhancementConfig } from '../models/ui-enhancement.model';

/**
 * UI Enhancement Configuration
 *
 * Defines the complete plan for enhancing the application UI with Magic UI components
 * organized in implementation phases with specific component enhancements.
 */
export const uiEnhancementConfig: UIEnhancementConfig = {
  version: '1.0.0',
  lastUpdated: new Date(),

  // Define UI mappings for each entity type
  entityMappings: [
    {
      entityType: 'assembly',
      cardComponent: {
        id: 'assembly-card',
        type: 'card',
        effectType: 'magic-card',
        hoverAnimation: true,
        backgroundEffect: 'shine-border',
        animationEnabled: true
      },
      rowComponent: {
        id: 'assembly-expandable-row',
        type: 'expandable-row',
        expandAnimation: 'slide',
        highlightOnHover: true,
        detailsBackgroundEffect: 'grid',
        animationEnabled: true
      },
      backgroundComponent: {
        id: 'assembly-list-background',
        type: 'background',
        patternType: 'interactive-grid',
        interactiveMode: true,
        colorScheme: 'system',
        animationEnabled: true
      },
      buttonComponents: [
        {
          id: 'assembly-action-button',
          type: 'button',
          effectType: 'shimmer',
          size: 'md',
          variant: 'default',
          animationEnabled: true
        },
        {
          id: 'assembly-view-button',
          type: 'button',
          effectType: 'ripple',
          size: 'sm',
          variant: 'outline',
          animationEnabled: true
        }
      ],
      textComponents: [
        {
          id: 'assembly-title',
          type: 'text',
          animationType: 'gradient',
          importance: 'high',
          animationEnabled: true
        },
        {
          id: 'assembly-status',
          type: 'text',
          animationType: 'shine',
          importance: 'medium',
          animationEnabled: true
        }
      ]
    },
    {
      entityType: 'part',
      cardComponent: {
        id: 'part-card',
        type: 'card',
        effectType: 'neon-gradient',
        hoverAnimation: true,
        backgroundEffect: 'shine-border',
        animationEnabled: true
      },
      // Additional part entity mappings would go here
    }
  ],

  // Define implementation phases
  implementationPhases: [
    {
      phaseNumber: 1,
      name: 'Assembly Card Enhancement',
      components: ['assembly-card', 'assembly-title', 'assembly-status'],
      completed: false,
      startDate: new Date(),
    },
    {
      phaseNumber: 2,
      name: 'Assembly Table Enhancement',
      components: ['assembly-expandable-row', 'assembly-action-button', 'assembly-view-button'],
      completed: false,
    },
    {
      phaseNumber: 3,
      name: 'Background and Layout Enhancement',
      components: ['assembly-list-background'],
      completed: false,
    },
    {
      phaseNumber: 4,
      name: 'Part Components Enhancement',
      components: ['part-card'],
      completed: false,
    }
  ],

  // Global settings for UI enhancements
  globalSettings: {
    animationsEnabled: true,
    performanceMode: false,
    accessibilityMode: true // Enable accessibility mode
  }
};

/**
 * Get current implementation phase
 * @returns The current active implementation phase
 */
export function getCurrentPhase() {
  const currentPhase = uiEnhancementConfig.implementationPhases.find(phase => !phase.completed);
  return currentPhase || uiEnhancementConfig.implementationPhases[uiEnhancementConfig.implementationPhases.length - 1];
}

/**
 * Get components for current phase
 * @returns Array of component IDs for the current phase
 */
export function getCurrentPhaseComponents() {
  const phase = getCurrentPhase();
  return phase ? phase.components : [];
}

/**
 * Check if a component should be enhanced in the current phase
 * @param componentId The component ID to check
 * @returns Boolean indicating if the component should be enhanced
 */
export function shouldEnhanceComponent(componentId: string) {
  const currentComponents = getCurrentPhaseComponents();
  return currentComponents.includes(componentId);
}