/**
 * Utility functions for API calls
 */

// Get the base API URL based on the environment
export const getApiBaseUrl = (): string => {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    // Use the current window location to determine the API base URL
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;

    // Return the full base URL including port if it exists
    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;
  }

  // Default fallback for server-side rendering - Updated to use correct port 5174
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5174';
};

// Create a full API URL by combining the base URL with the endpoint
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  
  // Ensure endpoint starts with a slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  return `${baseUrl}${formattedEndpoint}`;
};
