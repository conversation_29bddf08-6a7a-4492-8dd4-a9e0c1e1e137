import mongoose, { Types } from 'mongoose';
import { Product, IProduct } from '../models/product.model';
import { Category } from '../models/category.model';
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[ProductService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[ProductService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'product');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    // Check which field caused the duplicate key error
    const keyPattern = error.keyPattern || {};
    const keyValue = error.keyValue || {};
    
    if (keyPattern.product_id) {
      errorMessage = `Duplicate entry: A product with ID ${keyValue.product_id} already exists`;
    } else if (keyPattern.sku) {
      errorMessage = `Duplicate entry: A product with SKU ${keyValue.sku} already exists`;
    } else if (keyPattern.barcode) {
      errorMessage = `Duplicate entry: A product with barcode ${keyValue.barcode} already exists`;
    } else {
      errorMessage = `Duplicate entry: This product already exists`;
    }
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// Interface for the service
export interface IProductService extends IProduct {}

// Define DTOs for product operations
export interface IImage {
  url: string;
  alt_text?: string | null;
}

export interface IDimension {
  length: number;
  width: number;
  height: number;
  unit: string;
}

export interface IWeight {
  value: number;
  unit: string;
}

export interface IAttribute {
  name: string;
  value: string;
}

export interface IBomComponent {
  item_id: Types.ObjectId;
  item_type: 'Part' | 'Assembly';
  quantity: number;
  unit_of_measure: string;
}

export interface CreateProductDto {
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  assemblyId?: string | null;
  partId?: string | null;
}

export interface UpdateProductDto {
  name?: string;
  description?: string;
  categoryId?: string;
  status?: 'active' | 'discontinued' | 'in_development';
  sellingPrice?: number;
  assemblyId?: string | null;
  partId?: string | null;
}

/**
 * Fetches products with pagination, sorting, and filtering.
 */
export async function getAllProducts(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Create the aggregation pipeline
    const pipeline = [
      { $match: filter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information if needed
      {
        $lookup: {
          from: 'categories',
          localField: 'category_id',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];

    const products = await Product.aggregate(pipeline);
    const totalCount = await Product.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', {
      count: products.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      products,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch products');
  }
}

/**
 * Gets a single product by its MongoDB ObjectId.
 */
export async function getProductById(id: string): Promise<IProductService | null> {
  logOperation('GET_BY_ID', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    const product = await Product.findById(id)
      .populate({
        path: 'category_id',
        model: 'Category',
        select: '_id name'
      })
      .lean() as IProductService | null;
    
    if (!product) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', { id });
    return product;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get product by ID ${id}`);
  }
}

/**
 * Gets a single product by its product_id.
 */
export async function getProductByProductId(productId: string): Promise<IProductService | null> {
  logOperation('GET_BY_PRODUCT_ID', { productId });
  try {
    await connectToMongoose();

    const product = await Product.findOne({ product_id: productId })
      .populate({
        path: 'category_id',
        model: 'Category',
        select: '_id name'
      })
      .lean() as IProductService | null;
    
    if (!product) {
      logOperation('GET_BY_PRODUCT_ID_NOT_FOUND', { productId });
      return null;
    }
    
    logOperation('GET_BY_PRODUCT_ID_SUCCESS', { productId });
    return product;
  } catch (error: any) {
    logOperation('GET_BY_PRODUCT_ID_ERROR', { productId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get product by product_id ${productId}`);
  }
}

/**
 * Gets a single product by its SKU.
 */
export async function getProductBySku(sku: string): Promise<IProductService | null> {
  logOperation('GET_BY_SKU', { sku });
  try {
    await connectToMongoose();

    const product = await Product.findOne({ sku })
      .populate({
        path: 'category_id',
        model: 'Category',
        select: '_id name'
      })
      .lean() as IProductService | null;
    
    if (!product) {
      logOperation('GET_BY_SKU_NOT_FOUND', { sku });
      return null;
    }
    
    logOperation('GET_BY_SKU_SUCCESS', { sku });
    return product;
  } catch (error: any) {
    logOperation('GET_BY_SKU_ERROR', { sku, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get product by SKU ${sku}`);
  }
}

/**
 * Creates a new product.
 */
export async function createProduct(productData: CreateProductDto): Promise<IProductService> {
  logOperation('CREATE', { 
    productCode: productData.productCode,
    name: productData.name 
  });
  
  try {
    await connectToMongoose();

    // Check if category exists if provided
    if (productData.categoryId) {
      if (!Types.ObjectId.isValid(productData.categoryId)) {
        throw new Error('Invalid category ID format');
      }
      
      const categoryExists = await Category.findById(productData.categoryId);
      if (!categoryExists) {
        throw new Error(`Category with ID ${productData.categoryId} does not exist`);
      }
    }

    // Create product based on the schema
    const newProduct = new Product({
      productCode: productData.productCode,
      name: productData.name,
      description: productData.description,
      categoryId: productData.categoryId ? new Types.ObjectId(productData.categoryId) : null,
      status: productData.status,
      sellingPrice: productData.sellingPrice,
      assemblyId: productData.assemblyId ? new Types.ObjectId(productData.assemblyId) : null,
      partId: productData.partId ? new Types.ObjectId(productData.partId) : null
    });

    const savedProduct = await newProduct.save();
    
    logOperation('CREATE_SUCCESS', {
      _id: savedProduct._id,
      productCode: savedProduct.productCode
    });

    return savedProduct.toObject() as IProductService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create product');
  }
}

/**
 * Updates an existing product by its MongoDB ObjectId.
 */
export async function updateProductById(id: string, updateData: UpdateProductDto): Promise<IProductService | null> {
  logOperation('UPDATE_BY_ID', { id, data: updateData });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    // Check if product exists
    const existingProduct = await Product.findById(id);
    if (!existingProduct) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    // Check if category exists if provided
    if (updateData.categoryId) {
      if (!Types.ObjectId.isValid(updateData.categoryId)) {
        throw new Error('Invalid category ID format');
      }
      
      const categoryExists = await Category.findById(updateData.categoryId);
      if (!categoryExists) {
        throw new Error(`Category with ID ${updateData.categoryId} does not exist`);
      }
    }
    
    // Prepare update payload, converting IDs to ObjectIds as needed
    const updatePayload: any = { ...updateData };
    if (updateData.categoryId) {
      updatePayload.categoryId = new Types.ObjectId(updateData.categoryId);
    }
    if (updateData.assemblyId) {
      updatePayload.assemblyId = new Types.ObjectId(updateData.assemblyId);
    }
    if (updateData.partId) {
      updatePayload.partId = new Types.ObjectId(updateData.partId);
    }
    
    const product = await Product.findByIdAndUpdate(
      id,
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as IProductService | null;

    if (!product) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('UPDATE_BY_ID_SUCCESS', { id });
    return product;
  } catch (error: any) {
    logOperation('UPDATE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update product with ID ${id}`);
  }
}

/**
 * Updates an existing product by its product_id field.
 */
export async function updateProductByProductId(productId: string, updateData: UpdateProductDto): Promise<IProductService | null> {
  logOperation('UPDATE_BY_PRODUCT_ID', { productId, data: updateData });
  
  try {
    await connectToMongoose();
    
    // Check if product exists
    const existingProduct = await Product.findOne({ productCode: productId });
    if (!existingProduct) {
      logOperation('UPDATE_BY_PRODUCT_ID_NOT_FOUND', { productId });
      return null;
    }
    
    // Check if category exists if provided
    if (updateData.categoryId) {
      if (!Types.ObjectId.isValid(updateData.categoryId)) {
        throw new Error('Invalid category ID format');
      }
      
      const categoryExists = await Category.findById(updateData.categoryId);
      if (!categoryExists) {
        throw new Error(`Category with ID ${updateData.categoryId} does not exist`);
      }
    }
    
    // Prepare update payload, converting IDs to ObjectIds as needed
    const updatePayload: any = { ...updateData };
    if (updateData.categoryId) {
      updatePayload.categoryId = new Types.ObjectId(updateData.categoryId);
    }
    if (updateData.assemblyId) {
      updatePayload.assemblyId = new Types.ObjectId(updateData.assemblyId);
    }
    if (updateData.partId) {
      updatePayload.partId = new Types.ObjectId(updateData.partId);
    }
    
    const productByProductId = await Product.findOneAndUpdate(
      { productCode: productId },
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as IProductService | null;
    if (!productByProductId) {
      logOperation('UPDATE_BY_PRODUCT_ID_NOT_FOUND', { productId });
      return null;
    }
    logOperation('UPDATE_BY_PRODUCT_ID_SUCCESS', { productId });
    return productByProductId;
  } catch (error: any) {
    logOperation('UPDATE_BY_PRODUCT_ID_ERROR', { productId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update product with product_id ${productId}`);
  }
}

/**
 * Deletes a product by its MongoDB ObjectId.
 */
export async function deleteProductById(id: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE_BY_ID', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    // Could add additional checks here, like:
    // - Check if product is referenced in sales orders
    // - Check if product is part of an active work order
    // - etc.
    
    const result = await Product.findByIdAndDelete(id);
    
    if (!result) {
      logOperation('DELETE_BY_ID_NOT_FOUND', { id });
      throw new Error(`Product with ID ${id} not found or already deleted`);
    }
    
    logOperation('DELETE_BY_ID_SUCCESS', { id });
    return { success: true, message: `Product ${id} deleted` };
  } catch (error: any) {
    logOperation('DELETE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete product with ID ${id}`);
  }
}

/**
 * Deletes a product by its product_id field.
 */
export async function deleteProductByProductId(productId: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE_BY_PRODUCT_ID', { productId });
  
  try {
    await connectToMongoose();
    
    // Could add additional checks here
    
    const result = await Product.findOneAndDelete({ product_id: productId });
    
    if (!result) {
      logOperation('DELETE_BY_PRODUCT_ID_NOT_FOUND', { productId });
      throw new Error(`Product with product_id ${productId} not found or already deleted`);
    }
    
    logOperation('DELETE_BY_PRODUCT_ID_SUCCESS', { productId });
    return { success: true, message: `Product ${productId} deleted` };
  } catch (error: any) {
    logOperation('DELETE_BY_PRODUCT_ID_ERROR', { productId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete product with product_id ${productId}`);
  }
}

/**
 * Searches products based on a text query string.
 */
export async function searchProducts(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { product_id: new RegExp(query, 'i') },
        { description: new RegExp(query, 'i') },
        { sku: new RegExp(query, 'i') },
        { barcode: new RegExp(query, 'i') }
      ];
      
      // If query looks like a tag, search in tags array too
      searchFilter.$or.push({ tags: new RegExp(query, 'i') });
    }

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information
      {
        $lookup: {
          from: 'categories',
          localField: 'category_id',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];

    const [products, totalCount] = await Promise.all([
      Product.aggregate(pipeline),
      Product.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: products.length, pagination });
    return { products, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search products');
  }
}

/**
 * Gets products by category_id with pagination.
 */
export async function getProductsByCategory(categoryId: string, options: any = {}) {
  logOperation('GET_BY_CATEGORY', { categoryId });
  
  if (!Types.ObjectId.isValid(categoryId)) {
    logOperation('GET_BY_CATEGORY_INVALID_FORMAT', { categoryId });
    throw new Error('Invalid category ID format');
  }
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Combine category filter with additional filters
    const searchFilter = { 
      ...filter,
      category_id: new Types.ObjectId(categoryId)
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information
      {
        $lookup: {
          from: 'categories',
          localField: 'category_id',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];
    
    const [products, totalCount] = await Promise.all([
      Product.aggregate(pipeline),
      Product.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_CATEGORY_SUCCESS', { categoryId, count: products.length, pagination });
    return { products, pagination };
  } catch (error: any) {
    logOperation('GET_BY_CATEGORY_ERROR', { categoryId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get products for category ${categoryId}`);
  }
}

/**
 * Gets products by status with pagination.
 */
export async function getProductsByStatus(status: 'active' | 'inactive' | 'draft' | 'archived', options: any = {}) {
  logOperation('GET_BY_STATUS', { status });
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Combine status filter with additional filters
    const searchFilter = { 
      ...filter,
      status
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information
      {
        $lookup: {
          from: 'categories',
          localField: 'category_id',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];
    
    const [products, totalCount] = await Promise.all([
      Product.aggregate(pipeline),
      Product.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_STATUS_SUCCESS', { status, count: products.length, pagination });
    return { products, pagination };
  } catch (error: any) {
    logOperation('GET_BY_STATUS_ERROR', { status, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get products with status ${status}`);
  }
} 