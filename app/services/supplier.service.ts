import mongoose, { Types } from 'mongoose';
import { Supplier, ISupplier } from '../models/supplier.model';
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';
import { v4 as uuidv4 } from 'uuid';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[SupplierService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[SupplierService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'supplier');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// Interface for the service
export interface ISupplierService extends ISupplier {}

// Define DTOs for supplier operations
export interface CreateSupplierDto {
  supplier_id?: string; // Optional, generated if not provided
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  specialty?: string[];
  rating?: number | null;
  payment_terms?: string | null;
  delivery_terms?: string | null;
  is_active?: boolean;
}

export interface UpdateSupplierDto extends Partial<CreateSupplierDto> {}

/**
 * Fetches suppliers with pagination, sorting, and filtering.
 */
export async function getAllSuppliers(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {}
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch suppliers with pagination
    const suppliers = await Supplier.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const totalCount = await Supplier.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', {
      count: suppliers.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      suppliers,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch suppliers');
  }
}

/**
 * Gets a single supplier by its supplier_id.
 */
export async function getSupplierBySupplierId(supplierId: string): Promise<ISupplierService | null> {
  logOperation('GET_BY_SUPPLIER_ID', { supplierId });
  try {
    await connectToMongoose();

    const supplier = await Supplier.findOne({ supplier_id: supplierId }).lean() as ISupplierService | null;
    
    if (!supplier) {
      logOperation('GET_BY_SUPPLIER_ID_NOT_FOUND', { supplierId });
      return null;
    }
    
    logOperation('GET_BY_SUPPLIER_ID_SUCCESS', { supplierId });
    return supplier;
  } catch (error: any) {
    logOperation('GET_BY_SUPPLIER_ID_ERROR', { supplierId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get supplier by ID ${supplierId}`);
  }
}

/**
 * Gets a single supplier by its MongoDB ObjectId.
 */
export async function getSupplierById(id: string): Promise<ISupplierService | null> {
  logOperation('GET_BY_ID', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    const supplier = await Supplier.findById(id).lean() as ISupplierService | null;
    
    if (!supplier) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', { id });
    return supplier;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get supplier by ID ${id}`);
  }
}

/**
 * Creates a new supplier.
 */
export async function createSupplier(supplierData: CreateSupplierDto): Promise<ISupplierService> {
  logOperation('CREATE', { name: supplierData.name });
  try {
    await connectToMongoose();

    // Generate supplier_id if not provided
    const supplierId = supplierData.supplier_id || `SUP-${uuidv4().substring(0, 8).toUpperCase()}`;
    
    // Check if supplier_id already exists
    const existingSupplier = await Supplier.findOne({ supplier_id: supplierId }).exec();
    if (existingSupplier) {
      throw new Error(`Supplier with ID ${supplierId} already exists`);
    }

    // Check if email already exists (since it's unique)
    if (supplierData.email) {
      const existingEmail = await Supplier.findOne({ email: supplierData.email }).exec();
      if (existingEmail) {
        throw new Error(`Supplier with email ${supplierData.email} already exists`);
      }
    }

    const supplierToSave = new Supplier({
      ...supplierData,
      supplier_id: supplierId,
      is_active: supplierData.is_active !== undefined ? supplierData.is_active : true
    });

    const savedSupplier = await supplierToSave.save();
    logOperation('CREATE_SUCCESS', { _id: savedSupplier._id, supplier_id: savedSupplier.supplier_id });
    return savedSupplier.toObject() as ISupplierService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create supplier');
  }
}

/**
 * Updates an existing supplier by its supplier_id.
 */
export async function updateSupplierBySupplierId(supplierId: string, updateData: UpdateSupplierDto): Promise<ISupplierService | null> {
  logOperation('UPDATE_BY_SUPPLIER_ID', { supplierId, data: updateData });
  try {
    await connectToMongoose();
    
    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...updateData };
    delete updatePayload.supplier_id; // Don't update the unique identifier
    
    // Check if supplier exists
    const existingSupplier = await Supplier.findOne({ supplier_id: supplierId }).exec();
    if (!existingSupplier) {
      logOperation('UPDATE_BY_SUPPLIER_ID_NOT_FOUND', { supplierId });
      return null;
    }

    // Check for email uniqueness if email is being updated
    if (updatePayload.email && updatePayload.email !== existingSupplier.email) {
      const existingEmail = await Supplier.findOne({ 
        email: updatePayload.email,
        _id: { $ne: existingSupplier._id } // Exclude current supplier
      }).exec();
      
      if (existingEmail) {
        throw new Error(`Supplier with email ${updatePayload.email} already exists`);
      }
    }

    const updatedSupplier = await Supplier.findOneAndUpdate(
      { supplier_id: supplierId },
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as ISupplierService | null;

    if (!updatedSupplier) {
      logOperation('UPDATE_BY_SUPPLIER_ID_FAILED', { supplierId });
      return null;
    }
    
    logOperation('UPDATE_BY_SUPPLIER_ID_SUCCESS', { supplierId });
    return updatedSupplier;
  } catch (error: any) {
    logOperation('UPDATE_BY_SUPPLIER_ID_ERROR', { supplierId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update supplier ${supplierId}`);
  }
}

/**
 * Updates an existing supplier by its MongoDB ObjectId.
 */
export async function updateSupplierById(id: string, updateData: UpdateSupplierDto): Promise<ISupplierService | null> {
  logOperation('UPDATE_BY_ID', { id, data: updateData });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...updateData };
    delete updatePayload.supplier_id; // Don't update the unique identifier
    
    // Find the supplier first to check if it exists
    const existingSupplier = await Supplier.findById(id).exec();
    if (!existingSupplier) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }

    // Check for email uniqueness if email is being updated
    if (updatePayload.email && updatePayload.email !== existingSupplier.email) {
      const existingEmail = await Supplier.findOne({ 
        email: updatePayload.email,
        _id: { $ne: id } // Exclude current supplier
      }).exec();
      
      if (existingEmail) {
        throw new Error(`Supplier with email ${updatePayload.email} already exists`);
      }
    }
    
    const updatedSupplier = await Supplier.findByIdAndUpdate(
      new Types.ObjectId(id),
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as ISupplierService | null;

    if (!updatedSupplier) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('UPDATE_BY_ID_SUCCESS', { id });
    return updatedSupplier;
  } catch (error: any) {
    logOperation('UPDATE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update supplier with ID ${id}`);
  }
}

/**
 * Deletes a supplier by its supplier_id.
 */
export async function deleteSupplierBySupplierId(supplierId: string): Promise<ISupplierService | null> {
  logOperation('DELETE_BY_SUPPLIER_ID', { supplierId });
  try {
    await connectToMongoose();

    const deletedSupplier = await Supplier.findOneAndDelete({ supplier_id: supplierId }).lean() as ISupplierService | null;
    
    if (!deletedSupplier) {
      logOperation('DELETE_BY_SUPPLIER_ID_NOT_FOUND', { supplierId });
      return null;
    }
    
    logOperation('DELETE_BY_SUPPLIER_ID_SUCCESS', { supplierId });
    return deletedSupplier;
  } catch (error: any) {
    logOperation('DELETE_BY_SUPPLIER_ID_ERROR', { supplierId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete supplier ${supplierId}`);
  }
}

/**
 * Deletes a supplier by its MongoDB ObjectId.
 */
export async function deleteSupplierById(id: string): Promise<ISupplierService | null> {
  logOperation('DELETE_BY_ID', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    const deletedSupplier = await Supplier.findByIdAndDelete(new Types.ObjectId(id)).lean() as ISupplierService | null;
    
    if (!deletedSupplier) {
      logOperation('DELETE_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('DELETE_BY_ID_SUCCESS', { id });
    return deletedSupplier;
  } catch (error: any) {
    logOperation('DELETE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete supplier with ID ${id}`);
  }
}

/**
 * Searches suppliers based on a text query string.
 */
export async function searchSuppliers(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { contactPerson: new RegExp(query, 'i') },
        { email: new RegExp(query, 'i') },
        { phone: new RegExp(query, 'i') },
        { address: new RegExp(query, 'i') },
        { supplier_id: new RegExp(query, 'i') }
      ];
    }

    const suppliersQuery = Supplier.find(searchFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const [suppliers, totalCount] = await Promise.all([
      suppliersQuery.exec() as any as Promise<ISupplierService[]>,
      Supplier.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: suppliers.length, pagination });
    return { suppliers, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search suppliers');
  }
}

/**
 * Gets active suppliers (is_active = true)
 */
export async function getActiveSuppliers(options: any = {}) {
  logOperation('GET_ACTIVE');
  
  // Add is_active filter to options
  const activeOptions = {
    ...options,
    filter: { 
      ...options.filter,
      is_active: true 
    }
  };
  
  return getAllSuppliers(activeOptions);
}

/**
 * Updates a supplier's active status
 */
export async function updateSupplierActiveStatus(supplierId: string, isActive: boolean): Promise<ISupplierService | null> {
  logOperation('UPDATE_ACTIVE_STATUS', { supplierId, isActive });
  return updateSupplierBySupplierId(supplierId, { is_active: isActive });
} 