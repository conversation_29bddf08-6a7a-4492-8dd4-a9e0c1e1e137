import { Types } from 'mongoose';
import Transaction from '@/app/models/transaction.model';
import Batch from '@/app/models/batch.model';
import Part from '@/app/models/part.model';

/**
 * Get inventory summary for a specific batch
 * @param batchId - The ID of the batch
 * @returns Inventory summary for the batch
 */
export async function getBatchInventorySummary(batchId: string) {
  try {
    // Validate that the ID is a valid ObjectId
    if (!Types.ObjectId.isValid(batchId)) {
      throw new Error('Invalid batch ID format');
    }

    // Get the batch details
    const batch = await Batch.findById(batchId)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced status')
      .lean();

    if (!batch) {
      throw new Error(`Batch with ID ${batchId} not found`);
    }

    // Get all transactions related to this batch
    const transactions = await Transaction.find({ batchId })
      .select('partId transactionType quantity previousStock newStock transactionDate')
      .sort({ transactionDate: 1 })
      .lean();

    // Get part details if this batch is for a part
    let part = null;
    if (batch.partId) {
      part = await Part.findById(batch.partId)
        .select('_id name description inventory')
        .lean();
    }

    // Calculate inventory metrics
    const inventoryMetrics = {
      totalStockIn: 0,
      totalStockOut: 0,
      netChange: 0,
      lastTransactionDate: null as Date | null,
      currentStock: part?.inventory?.currentStock || 0,
      transactionCount: transactions.length
    };

    // Process transactions to calculate metrics
    transactions.forEach(transaction => {
      if (transaction.transactionType === 'stock_in') {
        inventoryMetrics.totalStockIn += transaction.quantity;
        inventoryMetrics.netChange += transaction.quantity;
      } else if (transaction.transactionType === 'stock_out') {
        inventoryMetrics.totalStockOut += Math.abs(transaction.quantity);
        inventoryMetrics.netChange += transaction.quantity; // quantity is negative for stock_out
      } else if (transaction.transactionType === 'adjustment') {
        inventoryMetrics.netChange += transaction.quantity;
      }

      // Update last transaction date
      if (!inventoryMetrics.lastTransactionDate || 
          (transaction.transactionDate && transaction.transactionDate > inventoryMetrics.lastTransactionDate)) {
        inventoryMetrics.lastTransactionDate = transaction.transactionDate;
      }
    });

    return {
      batch,
      part,
      inventoryMetrics,
      transactions: transactions.slice(0, 5) // Return only the 5 most recent transactions
    };
  } catch (error: any) {
    console.error(`Error getting batch inventory summary: ${error.message}`);
    throw error;
  }
}

/**
 * Get inventory transactions for a specific batch with pagination
 * @param batchId - The ID of the batch
 * @param options - Options for pagination and sorting
 * @returns Transactions for the batch with pagination
 */
export async function getBatchTransactions(batchId: string, options: any = {}) {
  try {
    const {
      page = 1,
      limit = 20,
      sort = { transactionDate: -1 } // Default sort by date descending
    } = options;

    // Validate that the ID is a valid ObjectId
    if (!Types.ObjectId.isValid(batchId)) {
      throw new Error('Invalid batch ID format');
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find transactions for this batch
    const transactions = await Transaction.find({ batchId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('partId transactionType quantity previousStock newStock transactionDate referenceNumber notes createdAt')
      .populate({
        path: 'partId',
        model: 'Part',
        select: '_id name description'
      })
      .lean();

    // Get total count for pagination
    const totalCount = await Transaction.countDocuments({ batchId });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    console.error(`Error getting batch transactions: ${error.message}`);
    throw error;
  }
}
