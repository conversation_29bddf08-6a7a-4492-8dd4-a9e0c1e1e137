import mongoose, { Types } from 'mongoose';
import { Category, ICategory } from '../models/category.model';
import { Part } from '../models/part.model';
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[CategoryService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[CategoryService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'category');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A category with this name already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// Interface for the service
export interface ICategoryService extends ICategory {}

// Define DTOs for category operations
export interface CreateCategoryDto {
  name: string;
  description?: string | null;
  parentCategory?: string | null; // Optional parent category ID
}

export interface UpdateCategoryDto extends Partial<CreateCategoryDto> {}

/**
 * Fetches categories with pagination, sorting, and filtering.
 */
export async function getAllCategories(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    includeParts = false, // Option to include parts for each category
    partsLimit = 10 // Limit for parts per category
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    let categories;
    
    if (includeParts) {
      // Use aggregation with $lookup to avoid N+1 queries
      const sortStage: any = {};
      Object.entries(sort).forEach(([key, value]) => {
        sortStage[key] = value;
      });

      // Create the aggregation pipeline
      const pipeline = [
        { $match: filter },
        { $sort: sortStage },
        { $skip: skip },
        { $limit: limit },
        // Lookup to get parent category information
        {
          $lookup: {
            from: 'categories', // The collection to join with
            localField: 'parentCategory', // Field from the categories collection
            foreignField: '_id', // Field from the parentCategory collection
            as: 'parentCategoryData' // Output array field
          }
        },
        // Lookup to get parts for each category 
        {
          $lookup: {
            from: 'parts', // The collection to join with
            let: { categoryId: '$_id' }, // Variable to use in the pipeline
            pipeline: [
              { 
                $match: { 
                  $expr: { $eq: ['$category', '$$categoryId'] } 
                } 
              },
              { $limit: partsLimit },
              { 
                $project: { 
                  _id: 1, 
                  name: 1, 
                  description: 1 
                } 
              }
            ],
            as: 'parts' // Output array field
          }
        },
        // Unwind and regroup parentCategory to get single object instead of array
        { 
          $addFields: { 
            parentCategory: { 
              $cond: { 
                if: { $gt: [{ $size: '$parentCategoryData' }, 0] }, 
                then: { 
                  _id: { $arrayElemAt: ['$parentCategoryData._id', 0] },
                  name: { $arrayElemAt: ['$parentCategoryData.name', 0] }
                }, 
                else: null 
              } 
            } 
          } 
        },
        // Remove the parentCategoryData array
        { $project: { parentCategoryData: 0 } }
      ];

      categories = await Category.aggregate(pipeline);
    } else {
      // If parts are not needed, use regular find query with populate
      categories = await Category.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .select('_id name description parentCategory createdAt updatedAt')
        .populate({
          path: 'parentCategory',
          model: 'Category',
          select: '_id name' // Only select essential fields from parent
        })
        .lean();
    }

    const totalCount = await Category.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', {
      count: categories.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      categories,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch categories');
  }
}

/**
 * Gets a single category by its MongoDB ObjectId.
 */
export async function getCategoryById(id: string): Promise<ICategoryService | null> {
  logOperation('GET_BY_ID', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    const category = await Category.findById(id)
      .select('_id name description parentCategory createdAt updatedAt')
      .populate({
        path: 'parentCategory',
        model: 'Category',
        select: '_id name' // Only select essential fields from parent
      })
      .lean() as ICategoryService | null;
    
    if (!category) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', { id });
    return category;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get category by ID ${id}`);
  }
}

/**
 * Gets a single category by its name (case-insensitive).
 */
export async function getCategoryByName(name: string): Promise<ICategoryService | null> {
  logOperation('GET_BY_NAME', { name });
  try {
    await connectToMongoose();

    const category = await Category.findOne({ 
      name: { $regex: new RegExp(`^${name}$`, 'i') } 
    })
    .select('_id name description parentCategory createdAt updatedAt')
    .populate({
      path: 'parentCategory',
      model: 'Category',
      select: '_id name' // Only select essential fields from parent
    })
    .lean() as ICategoryService | null;
    
    if (!category) {
      logOperation('GET_BY_NAME_NOT_FOUND', { name });
      return null;
    }
    
    logOperation('GET_BY_NAME_SUCCESS', { name });
    return category;
  } catch (error: any) {
    logOperation('GET_BY_NAME_ERROR', { name, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get category by name ${name}`);
  }
}

/**
 * Creates a new category.
 */
export async function createCategory(categoryData: CreateCategoryDto): Promise<ICategoryService> {
  logOperation('CREATE', { name: categoryData.name });
  try {
    await connectToMongoose();

    // Create category based on the schema
    const newCategory = new Category({
      name: categoryData.name,
      description: categoryData.description,
      parentCategory: categoryData.parentCategory
    });

    const savedCategory = await newCategory.save();
    logOperation('CREATE_SUCCESS', {
      _id: savedCategory._id,
      name: savedCategory.name
    });

    return savedCategory.toObject() as ICategoryService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create category');
  }
}

/**
 * Updates an existing category by its MongoDB ObjectId.
 */
export async function updateCategoryById(id: string, updateData: UpdateCategoryDto): Promise<ICategoryService | null> {
  logOperation('UPDATE_BY_ID', { id, data: updateData });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...updateData };
    
    const category = await Category.findByIdAndUpdate(
      id,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    )
    .populate({
      path: 'parentCategory',
      model: 'Category',
      select: '_id name' // Only select essential fields from parent
    })
    .lean() as ICategoryService | null;

    if (!category) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('UPDATE_BY_ID_SUCCESS', { id });
    return category;
  } catch (error: any) {
    logOperation('UPDATE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update category with ID ${id}`);
  }
}

/**
 * Deletes a category by its MongoDB ObjectId.
 */
export async function deleteCategoryById(id: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE_BY_ID', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    // Check if this category is a parent for any other categories
    const childCategories = await Category.findOne({ parentCategory: new Types.ObjectId(id) });
    if (childCategories) {
      logOperation('DELETE_BY_ID_FAILED', { id, reason: 'has_children' });
      throw new Error(`Cannot delete category with ID ${id} because it has child categories`);
    }

    // Check if this category is used by any parts
    const parts = await Part.findOne({ category: new Types.ObjectId(id) });
    if (parts) {
      logOperation('DELETE_BY_ID_FAILED', { id, reason: 'used_by_parts' });
      throw new Error(`Cannot delete category with ID ${id} because it is used by parts`);
    }
    
    const result = await Category.findByIdAndDelete(id);
    
    if (!result) {
      logOperation('DELETE_BY_ID_NOT_FOUND', { id });
      throw new Error(`Category with ID ${id} not found or already deleted`);
    }
    
    logOperation('DELETE_BY_ID_SUCCESS', { id });
    return { success: true, message: `Category ${id} deleted` };
  } catch (error: any) {
    logOperation('DELETE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete category with ID ${id}`);
  }
}

/**
 * Searches categories based on a text query string.
 */
export async function searchCategories(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { description: new RegExp(query, 'i') }
      ];
    }

    const categoriesQuery = Category.find(searchFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate({
        path: 'parentCategory',
        model: 'Category',
        select: '_id name' // Only select essential fields from parent
      })
      .lean();

    const [categories, totalCount] = await Promise.all([
      categoriesQuery.exec() as any as Promise<ICategoryService[]>,
      Category.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: categories.length, pagination });
    return { categories, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search categories');
  }
}

/**
 * Gets all categories that have the specified parent category.
 */
export async function getChildCategories(parentId: string | null, options: any = {}) {
  logOperation('GET_CHILD_CATEGORIES', { parentId });
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }
  } = options;
  
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    let filter: any = {};
    
    if (parentId === null) {
      // Get top-level categories (no parent)
      filter.parentCategory = null;
    } else if (Types.ObjectId.isValid(parentId)) {
      // Get children of a specific parent
      filter.parentCategory = new Types.ObjectId(parentId);
    } else {
      throw new Error('Invalid parent category ID format');
    }
    
    const categories = await Category.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
      
    const totalCount = await Category.countDocuments(filter);
    
    logOperation('GET_CHILD_CATEGORIES_SUCCESS', { 
      parentId, 
      count: categories.length,
      totalCount
    });
    
    return {
      categories,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('GET_CHILD_CATEGORIES_ERROR', { parentId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get child categories for parent ${parentId}`);
  }
}

/**
 * Gets all top-level categories (categories with no parent).
 */
export async function getTopLevelCategories(options: any = {}) {
  logOperation('GET_TOP_LEVEL_CATEGORIES');
  return getChildCategories(null, options);
} 