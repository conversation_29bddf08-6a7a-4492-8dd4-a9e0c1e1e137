import mongoose, { Types } from 'mongoose';
import { Inventory, IInventory } from '../models/inventory.model';
import { InventoryLevel, IInventoryLevel } from '../models/inventory.model';
import { Part } from '../models/part.model';
import { Assembly } from '../models/assembly.model';
import { Product } from '../models/product.model';
import { Warehouse } from '../models/warehouse.model';
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[InventoryService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[InventoryService Error]', error);

  // Set Sentry tags for better filtering
  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'inventory');

  // Determine error type and set appropriate tags
  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  // Check for specific MongoDB error types
  if (error.name === 'ValidationError') {
    // Handle validation errors
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;

    // Set Sentry tags for validation errors
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    // Handle duplicate key errors
    errorType = 'duplicate';
    errorStatus = 409;
    // Check which field caused the duplicate key error
    const keyPattern = error.keyPattern || {};

    if (keyPattern['item_id'] && keyPattern['item_type'] && keyPattern['warehouse_id']) {
      errorMessage = `Duplicate entry: Inventory record for this item and warehouse already exists`;
    } else {
      errorMessage = `Duplicate entry: This inventory record already exists`;
    }

    // Set Sentry tags for duplicate key errors
    setTag('error.subtype', 'duplicate_key');
  } else {
    // Handle other errors
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;

    // Set Sentry tags for general database errors
    setTag('error.subtype', 'general');
  }

  // Capture the exception in Sentry
  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  // Return standardized error response
  return { message: errorMessage, status: errorStatus };
};

/**
 * Fetch inventory items with pagination, sorting, and filtering options
 * @param options Pagination, sorting, and filtering options
 * @returns Inventory array and pagination information
 */
export async function fetchInventory(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {}
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Get parts with inventory data instead of separate inventory records
    const parts = await Part.aggregate([
      // Optional filtering stage if filter is provided
      ...(Object.keys(filter).length > 0 ? [{ $match: filter }] : []),
      
      // Lookup inventory data for each part (optional if inventory is embedded)
      // This could be needed if inventory data is in a separate collection
      {
        $lookup: {
          from: 'inventory_levels',
          let: { partId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$item_id', '$$partId'] },
                    { $eq: ['$item_type', 'Part'] }
                  ]
                }
              }
            }
          ],
          as: 'inventoryData'
        }
      },
      
      // Add supplier information
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplierInfo'
        }
      },
      
      // Add warehouse information for inventory location
      {
        $lookup: {
          from: 'warehouses',
          localField: 'inventory.warehouseId',
          foreignField: '_id',
          as: 'warehouseInfo'
        }
      },
      
      // Project fields to shape the response
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          description: 1,
          technicalSpecs: '$technical_specs',
          isManufactured: '$is_manufactured',
          reorderLevel: '$reorder_level',
          status: 1,
          inventory: 1, // This should contain currentStock, safetyStockLevel etc.
          supplierId: 1,
          unitOfMeasure: 1,
          cost: '$costPrice', // Aliasing costPrice to cost
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          // Virtual fields / Fallbacks
          currentStock: { $ifNull: ['$inventory.currentStock', 0] }, // Top-level currentStock as a fallback
          supplierName: { $arrayElemAt: ['$supplierInfo.name', 0] }, // For easier access on the frontend
          supplierMongoId: { $arrayElemAt: ['$supplierInfo._id', 0] } // Providing supplier's _id as well
        }
      },
      
      // Apply sorting
      { $sort: sort },
      
      // Apply pagination
      { $skip: skip },
      { $limit: limit }
    ]);

    // Count total documents for pagination
    const totalCount = await Part.countDocuments(filter);
    
    // Log successful operation
    logOperation('FETCH_ALL_SUCCESS', {
      count: parts.length,
      totalCount
    });

    // Return data and pagination info
    return {
      data: parts,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    };
  } catch (error) {
    logOperation('FETCH_ALL_ERROR', { error: error instanceof Error ? error.message : String(error) });
    throw error;
  }
}

/**
 * Get inventory by part ID and warehouse ID
 * @param partId The ObjectId of the part
 * @param warehouseId The ObjectId of the warehouse
 * @returns The inventory document
 */
export async function getInventoryByPartAndWarehouse(partId: string, warehouseId: string) {
  logOperation('GET_BY_PART_AND_WAREHOUSE', { partId, warehouseId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(partId) || !Types.ObjectId.isValid(warehouseId)) {
      throw new Error('Invalid ID format');
    }

    // Find the inventory by partId and warehouseId
    const inventory = await Inventory.findOne({
      partId: new Types.ObjectId(partId),
      warehouseId: new Types.ObjectId(warehouseId)
    })
      .populate('partId', 'partNumber name') // Populate related part basic info
      .populate('warehouseId', 'warehouseCode name') // Populate related warehouse basic info
      .lean();

    if (!inventory) {
      logOperation('NOT_FOUND', { partId, warehouseId });
      return null;
    }

    logOperation('SUCCESS', { partId, warehouseId });
    return inventory;
  } catch (error: any) {
    logOperation('ERROR', { partId, warehouseId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory for part ID ${partId} in warehouse ID ${warehouseId}`);
  }
}

/**
 * Get inventory by ID
 * @param inventoryId The ObjectId of the inventory record
 * @returns The inventory document
 */
export async function getInventoryById(inventoryId: string) {
  logOperation('GET_BY_ID', { inventoryId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(inventoryId)) {
      throw new Error('Invalid inventory ID format');
    }

    // Find the inventory by ID
    const inventory = await Inventory.findById(inventoryId)
      .populate('partId', 'partNumber name') // Populate related part basic info
      .populate('warehouseId', 'warehouseCode name') // Populate related warehouse basic info
      .lean();

    if (!inventory) {
      logOperation('NOT_FOUND', { inventoryId });
      return null;
    }

    logOperation('SUCCESS', { inventoryId });
    return inventory;
  } catch (error: any) {
    logOperation('ERROR', { inventoryId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory with ID ${inventoryId}`);
  }
}

/**
 * Add a new inventory record
 * @param inventoryData The inventory data to add
 * @returns The created inventory record
 */
export async function addInventory(inventoryData: any) {
  logOperation('ADD', inventoryData);

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Create a new inventory record
    const newInventory = new Inventory(inventoryData);
    const savedInventory = await newInventory.save();

    logOperation('SUCCESS', { _id: savedInventory._id, partId: savedInventory.partId, warehouseId: savedInventory.warehouseId });
    return savedInventory.toObject();
  } catch (error: any) {
    logOperation('ERROR', { error: error.message, data: inventoryData });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to add inventory record');
  }
}

/**
 * Update an inventory record by ID
 * @param inventoryId The ObjectId of the inventory to update
 * @param inventoryData The updated inventory data
 * @returns The updated inventory record
 */
export async function updateInventory(inventoryId: string, inventoryData: any) {
  logOperation('UPDATE', { inventoryId, data: inventoryData });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(inventoryId)) {
      throw new Error('Invalid inventory ID format');
    }

    // Find and update the inventory
    const updatedInventory = await Inventory.findByIdAndUpdate(
      inventoryId,
      { $set: inventoryData },
      { new: true, runValidators: true }
    )
      .populate('partId', 'partNumber name')
      .populate('warehouseId', 'warehouseCode name');

    if (!updatedInventory) {
      logOperation('NOT_FOUND', { inventoryId });
      return null;
    }

    logOperation('SUCCESS', { inventoryId });
    return updatedInventory.toObject();
  } catch (error: any) {
    logOperation('ERROR', { inventoryId, error: error.message, data: inventoryData });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update inventory with ID ${inventoryId}`);
  }
}

/**
 * Delete an inventory record by ID
 * @param inventoryId The ObjectId of the inventory to delete
 * @returns True if the inventory was deleted, false if not found
 */
export async function deleteInventory(inventoryId: string) {
  logOperation('DELETE', { inventoryId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(inventoryId)) {
      throw new Error('Invalid inventory ID format');
    }

    // Find and delete the inventory
    const result = await Inventory.findByIdAndDelete(inventoryId);

    if (!result) {
      logOperation('NOT_FOUND', { inventoryId });
      return false;
    }

    logOperation('SUCCESS', { inventoryId });
    return true;
  } catch (error: any) {
    logOperation('ERROR', { inventoryId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete inventory with ID ${inventoryId}`);
  }
}

/**
 * Update stock level for a part in a warehouse
 * @param partId The ObjectId of the part
 * @param warehouseId The ObjectId of the warehouse
 * @param quantityChange The amount to change the stock by (positive for increase, negative for decrease)
 * @returns The updated inventory record
 */
export async function updateStockLevel(partId: string, warehouseId: string, quantityChange: number) {
  logOperation('UPDATE_STOCK', { partId, warehouseId, quantityChange });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(partId) || !Types.ObjectId.isValid(warehouseId)) {
      throw new Error('Invalid ID format');
    }

    // Validate quantity change is an integer
    if (!Number.isInteger(quantityChange)) {
      throw new Error('Quantity change must be a whole number');
    }

    // Start a session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Find existing inventory record or create if it doesn't exist
      let inventory = await Inventory.findOne(
        { partId, warehouseId },
        null,
        { session }
      );

      if (!inventory) {
        // Create new inventory record if it doesn't exist
        inventory = new Inventory({
          partId,
          warehouseId,
          quantity: 0,
          status: 'out_of_stock'
        });
      }

      // Calculate new quantity
      const newQuantity = inventory.quantity + quantityChange;

      // Validate new quantity is not negative
      if (newQuantity < 0) {
        throw new Error('Cannot reduce stock below zero');
      }

      // Update quantity and last restocked date if adding stock
      inventory.quantity = newQuantity;
      if (quantityChange > 0) {
        inventory.lastRestockedDate = new Date();
      }

      // Save the updated inventory
      await inventory.save({ session });

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      logOperation('SUCCESS', { partId, warehouseId, newQuantity });
      return inventory.toObject();
    } catch (error) {
      // Abort transaction on error
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error: any) {
    logOperation('ERROR', { partId, warehouseId, quantityChange, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update stock level for part ID ${partId} in warehouse ID ${warehouseId}`);
  }
}

/**
 * Get current stock level for a part across all warehouses
 * @param partId The ObjectId of the part
 * @returns Array of inventory records for the part across different warehouses
 */
export async function getPartStockLevels(partId: string) {
  logOperation('GET_PART_STOCK', { partId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(partId)) {
      throw new Error('Invalid part ID format');
    }

    // Find all inventory records for the part
    const inventoryItems = await Inventory.find({ partId })
      .populate('warehouseId', 'warehouseCode name location')
      .lean();

    logOperation('SUCCESS', { partId, count: inventoryItems.length });
    return inventoryItems;
  } catch (error: any) {
    logOperation('ERROR', { partId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get stock levels for part ID ${partId}`);
  }
}

/**
 * Get total stock count for a part across all warehouses
 * @param partId The ObjectId of the part
 * @returns The total quantity available across all warehouses
 */
export async function getTotalStockForPart(partId: string) {
  logOperation('GET_TOTAL_STOCK', { partId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(partId)) {
      throw new Error('Invalid part ID format');
    }

    // Aggregate to get the total quantity
    const result = await Inventory.aggregate([
      { $match: { partId: new Types.ObjectId(partId) } },
      { $group: { _id: null, totalQuantity: { $sum: '$quantity' } } }
    ]);

    const totalQuantity = result.length > 0 ? result[0].totalQuantity : 0;

    logOperation('SUCCESS', { partId, totalQuantity });
    return totalQuantity;
  } catch (error: any) {
    logOperation('ERROR', { partId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get total stock for part ID ${partId}`);
  }
}

// Type for inventory service operations
export interface IInventoryService extends IInventoryLevel { }

// DTO interfaces for inventory operations
export interface CreateInventoryDto {
  item_id: string; // String version for API/client consumption
  item_type: 'Part' | 'Assembly' | 'Product';
  warehouse_id: string; // String version for API/client consumption
  quantity_on_hand: number;
  quantity_allocated?: number;
  location_in_warehouse?: string | null;
  reorder_level?: number | null;
  safety_stock_level?: number | null;
  maximum_stock_level?: number | null;
  average_daily_usage?: number | null;
  abc_classification?: string | null;
  notes?: string | null;
}

export interface UpdateInventoryDto extends Partial<Omit<CreateInventoryDto, 'item_id' | 'item_type' | 'warehouse_id'>> { }

export interface InventoryAdjustmentDto {
  adjustment_quantity: number; // Positive for increase, negative for decrease
  reason: string;
  reference_number?: string | null;
  notes?: string | null;
}

export interface InventoryAllocationDto {
  allocation_quantity: number; // Positive to allocate, negative to deallocate
  reference_number?: string | null;
  notes?: string | null;
}

/**
 * Get all inventory records with pagination, sorting, and filtering
 */
export async function getAllInventory(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { last_stock_update: -1 }, // Default sort by last update (newest first)
    filter = {},
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Create the aggregation pipeline
    const pipeline = [
      { $match: filter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const inventoryRecords = await InventoryLevel.aggregate(pipeline);
    const totalCount = await InventoryLevel.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', {
      count: inventoryRecords.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      inventoryRecords,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch inventory records');
  }
}

/**
 * Get a single inventory record by its MongoDB ObjectId
 */
export async function getInventoryById(id: string): Promise<IInventoryService | null> {
  logOperation('GET_BY_ID', { id });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }

  try {
    await connectToMongoose();

    const inventoryRecord = await InventoryLevel.findById(id).lean() as IInventoryService | null;

    if (!inventoryRecord) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }

    logOperation('GET_BY_ID_SUCCESS', { id });
    return inventoryRecord;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory record by ID ${id}`);
  }
}

/**
 * Get inventory record by item and warehouse
 */
export async function getInventoryByItemAndWarehouse(
  itemId: string,
  itemType: 'Part' | 'Assembly' | 'Product',
  warehouseId: string
): Promise<IInventoryService | null> {
  logOperation('GET_BY_ITEM_AND_WAREHOUSE', { itemId, itemType, warehouseId });

  if (!Types.ObjectId.isValid(itemId) || !Types.ObjectId.isValid(warehouseId)) {
    logOperation('GET_BY_ITEM_AND_WAREHOUSE_INVALID_FORMAT', { itemId, warehouseId });
    throw new Error('Invalid ID format for item_id or warehouse_id');
  }

  try {
    await connectToMongoose();

    const inventoryRecord = await InventoryLevel.findOne({
      item_id: new Types.ObjectId(itemId),
      item_type: itemType,
      warehouse_id: new Types.ObjectId(warehouseId)
    }).lean() as IInventoryService | null;

    if (!inventoryRecord) {
      logOperation('GET_BY_ITEM_AND_WAREHOUSE_NOT_FOUND', { itemId, itemType, warehouseId });
      return null;
    }

    logOperation('GET_BY_ITEM_AND_WAREHOUSE_SUCCESS', { itemId, itemType, warehouseId });
    return inventoryRecord;
  } catch (error: any) {
    logOperation('GET_BY_ITEM_AND_WAREHOUSE_ERROR', { itemId, itemType, warehouseId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory record for ${itemType} ${itemId} in warehouse ${warehouseId}`);
  }
}

/**
 * Create a new inventory record
 */
export async function createInventory(inventoryData: CreateInventoryDto): Promise<IInventoryService> {
  logOperation('CREATE', {
    item_id: inventoryData.item_id,
    item_type: inventoryData.item_type,
    warehouse_id: inventoryData.warehouse_id
  });

  try {
    await connectToMongoose();

    // Validate item_id and warehouse_id are valid ObjectIds
    if (!Types.ObjectId.isValid(inventoryData.item_id) || !Types.ObjectId.isValid(inventoryData.warehouse_id)) {
      throw new Error('Invalid ID format for item_id or warehouse_id');
    }

    // Check if the referenced item exists based on item_type
    let itemExists = false;
    if (inventoryData.item_type === 'Part') {
      itemExists = await Part.exists({ _id: new Types.ObjectId(inventoryData.item_id) });
    } else if (inventoryData.item_type === 'Assembly') {
      itemExists = await Assembly.exists({ _id: new Types.ObjectId(inventoryData.item_id) });
    } else if (inventoryData.item_type === 'Product') {
      itemExists = await Product.exists({ _id: new Types.ObjectId(inventoryData.item_id) });
    }

    if (!itemExists) {
      throw new Error(`${inventoryData.item_type} with ID ${inventoryData.item_id} does not exist`);
    }

    // Check if the referenced warehouse exists
    const warehouseExists = await Warehouse.exists({ _id: new Types.ObjectId(inventoryData.warehouse_id) });
    if (!warehouseExists) {
      throw new Error(`Warehouse with ID ${inventoryData.warehouse_id} does not exist`);
    }

    // Check if inventory record already exists for this item and warehouse
    const existingRecord = await InventoryLevel.findOne({
      item_id: new Types.ObjectId(inventoryData.item_id),
      item_type: inventoryData.item_type,
      warehouse_id: new Types.ObjectId(inventoryData.warehouse_id)
    });

    if (existingRecord) {
      throw new Error(`Inventory record already exists for this ${inventoryData.item_type} in the specified warehouse`);
    }

    // Prepare the inventory record to create
    const inventoryToCreate: any = {
      ...inventoryData,
      item_id: new Types.ObjectId(inventoryData.item_id),
      warehouse_id: new Types.ObjectId(inventoryData.warehouse_id),
      quantity_allocated: inventoryData.quantity_allocated || 0,
      last_stock_update: new Date()
    };

    // Create the inventory record
    const newInventory = new InventoryLevel(inventoryToCreate);
    const savedInventory = await newInventory.save();

    logOperation('CREATE_SUCCESS', {
      _id: savedInventory._id,
      item_type: savedInventory.item_type,
      item_id: savedInventory.item_id,
      warehouse_id: savedInventory.warehouse_id
    });

    return savedInventory.toObject() as IInventoryService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create inventory record');
  }
}

/**
 * Update an inventory record by ID
 */
export async function updateInventoryById(id: string, updateData: UpdateInventoryDto): Promise<IInventoryService | null> {
  logOperation('UPDATE_BY_ID', { id, data: updateData });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }

  try {
    await connectToMongoose();

    // Check if inventory record exists
    const existingInventory = await InventoryLevel.findById(id);
    if (!existingInventory) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }

    // Prepare update payload
    const updatePayload: any = {
      ...updateData,
      last_stock_update: new Date() // Update timestamp
    };

    const inventoryRecord = await InventoryLevel.findByIdAndUpdate(
      id,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean() as IInventoryService | null;

    if (!inventoryRecord) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', { id });
      return null;
    }

    logOperation('UPDATE_BY_ID_SUCCESS', { id });
    return inventoryRecord;
  } catch (error: any) {
    logOperation('UPDATE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update inventory record with ID ${id}`);
  }
}

/**
 * Delete an inventory record by ID
 */
export async function deleteInventoryById(id: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE_BY_ID', { id });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }

  try {
    await connectToMongoose();

    const result = await InventoryLevel.findByIdAndDelete(id);

    if (!result) {
      logOperation('DELETE_BY_ID_NOT_FOUND', { id });
      throw new Error(`Inventory record with ID ${id} not found or already deleted`);
    }

    logOperation('DELETE_BY_ID_SUCCESS', { id });
    return { success: true, message: `Inventory record ${id} deleted` };
  } catch (error: any) {
    logOperation('DELETE_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete inventory record with ID ${id}`);
  }
}

/**
 * Adjust inventory quantity (increase or decrease actual stock)
 */
export async function adjustInventoryQuantity(
  id: string,
  adjustmentData: InventoryAdjustmentDto
): Promise<IInventoryService | null> {
  logOperation('ADJUST_QUANTITY', { id, adjustment: adjustmentData });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('ADJUST_QUANTITY_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }

  try {
    await connectToMongoose();

    // Check if inventory record exists
    const existingInventory = await InventoryLevel.findById(id);
    if (!existingInventory) {
      logOperation('ADJUST_QUANTITY_NOT_FOUND', { id });
      return null;
    }

    // Calculate new quantity on hand
    const newQuantityOnHand = existingInventory.quantity_on_hand + adjustmentData.adjustment_quantity;

    // Prevent negative inventory if adjustment would result in negative stock
    if (newQuantityOnHand < 0) {
      throw new Error('Adjustment would result in negative inventory');
    }

    // Update inventory record
    const inventoryRecord = await InventoryLevel.findByIdAndUpdate(
      id,
      {
        $set: {
          quantity_on_hand: newQuantityOnHand,
          last_stock_update: new Date()
        },
        $push: {
          // Could log adjustment history if schema supports it
        }
      },
      { new: true, runValidators: true }
    ).lean() as IInventoryService | null;

    if (!inventoryRecord) {
      logOperation('ADJUST_QUANTITY_NOT_FOUND', { id });
      return null;
    }

    logOperation('ADJUST_QUANTITY_SUCCESS', {
      id,
      prevQuantity: existingInventory.quantity_on_hand,
      newQuantity: newQuantityOnHand,
      adjustment: adjustmentData.adjustment_quantity
    });

    return inventoryRecord;
  } catch (error: any) {
    logOperation('ADJUST_QUANTITY_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to adjust inventory quantity for record with ID ${id}`);
  }
}

/**
 * Manage allocation of inventory (reserve or release stock)
 */
export async function manageInventoryAllocation(
  id: string,
  allocationData: InventoryAllocationDto
): Promise<IInventoryService | null> {
  logOperation('MANAGE_ALLOCATION', { id, allocation: allocationData });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('MANAGE_ALLOCATION_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }

  try {
    await connectToMongoose();

    // Check if inventory record exists
    const existingInventory = await InventoryLevel.findById(id);
    if (!existingInventory) {
      logOperation('MANAGE_ALLOCATION_NOT_FOUND', { id });
      return null;
    }

    // Calculate new quantity allocated
    const newQuantityAllocated = existingInventory.quantity_allocated + allocationData.allocation_quantity;

    // Validate the allocation
    if (newQuantityAllocated < 0) {
      throw new Error('Allocation adjustment would result in negative allocated quantity');
    }

    if (newQuantityAllocated > existingInventory.quantity_on_hand) {
      throw new Error('Cannot allocate more than available quantity on hand');
    }

    // Update inventory record
    const inventoryRecord = await InventoryLevel.findByIdAndUpdate(
      id,
      {
        $set: {
          quantity_allocated: newQuantityAllocated,
          last_stock_update: new Date()
        }
      },
      { new: true, runValidators: true }
    ).lean() as IInventoryService | null;

    if (!inventoryRecord) {
      logOperation('MANAGE_ALLOCATION_NOT_FOUND', { id });
      return null;
    }

    logOperation('MANAGE_ALLOCATION_SUCCESS', {
      id,
      prevAllocated: existingInventory.quantity_allocated,
      newAllocated: newQuantityAllocated,
      allocation: allocationData.allocation_quantity
    });

    return inventoryRecord;
  } catch (error: any) {
    logOperation('MANAGE_ALLOCATION_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to manage allocation for inventory record with ID ${id}`);
  }
}

/**
 * Get inventory by item (get all warehouse inventory for a specific item)
 */
export async function getInventoryByItem(
  itemId: string,
  itemType: 'Part' | 'Assembly' | 'Product'
): Promise<IInventoryService[]> {
  logOperation('GET_BY_ITEM', { itemId, itemType });

  if (!Types.ObjectId.isValid(itemId)) {
    logOperation('GET_BY_ITEM_INVALID_FORMAT', { itemId });
    throw new Error('Invalid ID format for item_id');
  }

  try {
    await connectToMongoose();

    const inventoryRecords = await InventoryLevel.find({
      item_id: new Types.ObjectId(itemId),
      item_type: itemType
    }).lean() as IInventoryService[];

    logOperation('GET_BY_ITEM_SUCCESS', {
      itemId,
      itemType,
      count: inventoryRecords.length
    });

    return inventoryRecords;
  } catch (error: any) {
    logOperation('GET_BY_ITEM_ERROR', { itemId, itemType, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory records for ${itemType} ${itemId}`);
  }
}

/**
 * Get inventory by warehouse (get all inventory in a specific warehouse)
 */
export async function getInventoryByWarehouse(warehouseId: string, options: any = {}): Promise<any> {
  const {
    page = 1,
    limit = 20,
    sort = { last_stock_update: -1 },
    filter = {},
  } = options;

  logOperation('GET_BY_WAREHOUSE', { warehouseId, options });

  if (!Types.ObjectId.isValid(warehouseId)) {
    logOperation('GET_BY_WAREHOUSE_INVALID_FORMAT', { warehouseId });
    throw new Error('Invalid ID format for warehouse_id');
  }

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Combine warehouse filter with additional filters
    const warehouseFilter = {
      ...filter,
      warehouse_id: new Types.ObjectId(warehouseId)
    };

    // Create the aggregation pipeline
    const pipeline = [
      { $match: warehouseFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const inventoryRecords = await InventoryLevel.aggregate(pipeline);
    const totalCount = await InventoryLevel.countDocuments(warehouseFilter);

    logOperation('GET_BY_WAREHOUSE_SUCCESS', {
      warehouseId,
      count: inventoryRecords.length,
      totalCount
    });

    return {
      inventoryRecords,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('GET_BY_WAREHOUSE_ERROR', { warehouseId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get inventory records for warehouse ${warehouseId}`);
  }
}

/**
 * Search inventory with advanced filtering
 */
export async function searchInventory(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { last_stock_update: -1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Build search filter
    let searchFilter: any = { ...filter };

    // Add text search if query is provided
    if (query && query.trim().length > 0) {
      // Since we can't directly search on referenced data, we'll look for matching in notes
      // or allow searching by item_type or location_in_warehouse
      searchFilter.$or = [
        { notes: new RegExp(query, 'i') },
        { item_type: new RegExp(query, 'i') },
        { location_in_warehouse: new RegExp(query, 'i') },
        { abc_classification: new RegExp(query, 'i') }
      ];
    }

    // Add filter for low stock if specified
    if (filter.lowStock === true) {
      searchFilter.$expr = {
        $lte: [
          '$quantity_on_hand',
          { $ifNull: ['$reorder_level', Number.MAX_SAFE_INTEGER] }
        ]
      };
      delete searchFilter.lowStock;
    }

    // Add filter for stock status
    if (filter.stockStatus) {
      switch (filter.stockStatus) {
        case 'out_of_stock':
          searchFilter.quantity_on_hand = 0;
          break;
        case 'low_stock':
          searchFilter.$expr = {
            $and: [
              { $gt: ['$quantity_on_hand', 0] },
              { $lte: ['$quantity_on_hand', { $ifNull: ['$reorder_level', Number.MAX_SAFE_INTEGER] }] }
            ]
          };
          break;
        case 'in_stock':
          searchFilter.quantity_on_hand = { $gt: 0 };
          break;
        case 'overstocked':
          searchFilter.$expr = {
            $gt: [
              '$quantity_on_hand',
              { $ifNull: ['$maximum_stock_level', 0] }
            ]
          };
          break;
      }
      delete searchFilter.stockStatus;
    }

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const [inventoryRecords, totalCount] = await Promise.all([
      InventoryLevel.aggregate(pipeline),
      InventoryLevel.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: inventoryRecords.length, pagination });
    return { inventoryRecords, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search inventory records');
  }
}

/**
 * Get inventory records with levels below reorder point
 */
export async function getLowStockInventory(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { quantity_on_hand: 1 }, // Sort by quantity ascending (lowest first)
    filter = {}
  } = options;

  logOperation('GET_LOW_STOCK', { options });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Build filter for low stock items
    const lowStockFilter = {
      ...filter,
      $expr: {
        $and: [
          // Only include items with reorder_level set
          { $ne: ['$reorder_level', null] },
          // Where quantity_on_hand is less than or equal to reorder_level
          { $lte: ['$quantity_on_hand', '$reorder_level'] }
        ]
      }
    };

    const pipeline = [
      { $match: lowStockFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const [inventoryRecords, totalCount] = await Promise.all([
      InventoryLevel.aggregate(pipeline),
      InventoryLevel.countDocuments(lowStockFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('GET_LOW_STOCK_SUCCESS', { count: inventoryRecords.length, pagination });
    return { inventoryRecords, pagination };
  } catch (error: any) {
    logOperation('GET_LOW_STOCK_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to retrieve low stock inventory');
  }
}