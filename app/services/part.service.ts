import mongoose, { Model, Types } from 'mongoose';
// import { InjectModel } from '@nestjs/mongoose'; // Example if using NestJS
import { Part, IPart as IPartModel, IInventory } from '../models/part.model'; // Importing IInventory as well
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[PartService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[PartService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'part');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// import { PartNotFoundException, DuplicatePartNumberException, InvalidStockOperationException } from '../common/exceptions/part.exceptions.ts'; // Custom exceptions to be defined

// Updated interface to match the new schema in part.model.ts
export interface IPart extends IPartModel {}

// SCHEMA ALIGNMENT: Updated all DTOs and logic to use canonical field names from database_schema_updated.md. Legacy/incorrect field names removed.
// Updated DTO to match the new schema
export interface CreatePartDto {
  partNumber: string;
  name: string;
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'obsolete' | 'in_development' | 'pending_approval' | 'inactive';
  inventory: {
    currentStock: number;
    warehouseId: string;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null;
  };
  supplierId?: string | null;
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: string | null;
}

export interface UpdatePartDto extends Partial<Omit<CreatePartDto, 'partNumber'>> {
  partNumber?: string;
}

// This structure uses exported functions rather than a class, 
// aligning with the existing code snippet provided for this file.
// If a class-based service is preferred, that can be adjusted.

/**
 * Creates a new part.
 */
export async function createPart(partData: CreatePartDto): Promise<IPart> {
  logOperation('CREATE', { partNumber: partData.partNumber });
  await connectToMongoose();
  try {
    const existingPart = await Part.findOne({ partNumber: partData.partNumber }).exec();
    if (existingPart) {
      throw new Error(`Part with number ${partData.partNumber} already exists.`); // Consider custom error
    }

    const partToSave = new Part({
      ...partData,
      inventory: {
        ...partData.inventory,
        warehouseId: new Types.ObjectId(partData.inventory.warehouseId),
        lastStockUpdate: new Date(),
      },
      supplierId: partData.supplierId ? new Types.ObjectId(partData.supplierId) : null,
      categoryId: partData.categoryId ? new Types.ObjectId(partData.categoryId) : null,
    });

    const savedPart = await partToSave.save();
    logOperation('CREATE_SUCCESS', { _id: savedPart._id });
    return savedPart.toObject() as IPart;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create part');
  }
}

/**
 * Retrieves a part by its MongoDB ObjectId.
 */
export async function getPartById(id: string): Promise<IPart | null> {
  logOperation('GET_BY_ID', { id });
  await connectToMongoose();
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format'); // Consider custom error
  }
  try {
    const part = await Part.findById(id).lean().exec() as IPart | null;
    if (!part) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    logOperation('GET_BY_ID_SUCCESS', { id });
    return part;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get part by ID ${id}`);
  }
}

/**
 * Retrieves a part by its unique partNumber.
 */
export async function getPartByPartNumberService(partNumber: string): Promise<IPart | null> {
  logOperation('GET_BY_PARTNUMBER', { partNumber });
  await connectToMongoose();
  try {
    const part = await Part.findOne({ partNumber }).lean().exec() as IPart | null;
    if (!part) {
      logOperation('GET_BY_PARTNUMBER_NOT_FOUND', { partNumber });
      return null;
    }
    logOperation('GET_BY_PARTNUMBER_SUCCESS', { partNumber });
    return part;
  } catch (error: any) {
    logOperation('GET_BY_PARTNUMBER_ERROR', { partNumber, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get part by part number ${partNumber}`);
  }
}

/**
 * Retrieves all parts, possibly with filters and pagination.
 */
export async function getAllParts(options: any = {}): Promise<{ parts: IPart[], pagination: any }> {
  const {
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {}
  } = options;
  logOperation('GET_ALL', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });
  await connectToMongoose();
  try {
    const skip = (page - 1) * limit;
    const partsQuery = Part.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    const [parts, totalCount] = await Promise.all([
      partsQuery.exec() as any as Promise<IPart[]>,
      Part.countDocuments(filter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    logOperation('GET_ALL_SUCCESS', { count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    logOperation('GET_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch all parts');
  }
}

/**
 * Updates an existing part.
 */
export async function updatePartService(id: string, updateData: UpdatePartDto): Promise<IPart | null> {
  logOperation('UPDATE', { id, data: updateData });
  await connectToMongoose();
  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_INVALID_ID_FORMAT', { id });
    throw new Error('Invalid ID format'); // Consider custom error
  }

  try {
    if (updateData.partNumber) {
      const existingPart = await Part.findOne({ partNumber: updateData.partNumber, _id: { $ne: new Types.ObjectId(id) } }).exec();
      if (existingPart) {
        throw new Error(`Another part with number ${updateData.partNumber} already exists.`); // Consider custom error
      }
    }
    
    // Prepare update payload while handling ObjectId conversions and nested fields
    const updatePayload: any = { ...updateData };
    
    // Handle inventory subdocument updates
    if (updateData.inventory) {
      if (updateData.inventory.warehouseId) {
        updatePayload.inventory.warehouseId = new Types.ObjectId(updateData.inventory.warehouseId);
      }
      updatePayload.inventory.lastStockUpdate = new Date();
    }
    
    // Handle other ObjectId references
    if (updateData.supplierId) {
      updatePayload.supplierId = new Types.ObjectId(updateData.supplierId);
    }
    if (updateData.categoryId) {
      updatePayload.categoryId = new Types.ObjectId(updateData.categoryId);
    }

    const updatedPart = await Part.findByIdAndUpdate(
      new Types.ObjectId(id),
      { $set: updatePayload }, 
      { new: true, runValidators: true }
    ).lean().exec() as IPart | null;

    if (!updatedPart) {
      logOperation('UPDATE_NOT_FOUND', { id });
      return null;
    }
    logOperation('UPDATE_SUCCESS', { id });
    return updatedPart;
  } catch (error: any) {
    logOperation('UPDATE_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update part ${id}`);
  }
}

/**
 * Deletes a part by its ObjectId.
 */
export async function deletePartService(id: string): Promise<IPart | null> {
  logOperation('DELETE', { id });
  await connectToMongoose();
  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_INVALID_ID_FORMAT', { id });
    throw new Error('Invalid ID format'); // Consider custom error
  }
  try {
    const deletedPart = await Part.findByIdAndDelete(new Types.ObjectId(id)).lean().exec() as IPart | null;
    if (!deletedPart) {
      logOperation('DELETE_NOT_FOUND', { id });
      return null;
    }
    logOperation('DELETE_SUCCESS', { id });
    return deletedPart;
  } catch (error: any) {
    logOperation('DELETE_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete part ${id}`);
  }
}

/**
 * Updates the stock level of a specific part's inventory sub-document.
 */
export async function updateStockLevel(partId: string, quantityChange: number, warehouseIdFromTransaction: string): Promise<IPart | null> {
  logOperation('UPDATE_STOCK', { partId, quantityChange, warehouseIdFromTransaction });
  await connectToMongoose();
  if (!Types.ObjectId.isValid(partId) || !Types.ObjectId.isValid(warehouseIdFromTransaction)) {
    logOperation('UPDATE_STOCK_INVALID_ID', { partId, warehouseIdFromTransaction });
    throw new Error('Invalid Part ID or Warehouse ID format for stock update'); // Consider custom error
  }

  try {
    const part = await Part.findById(partId).exec();
    if (!part) {
      logOperation('UPDATE_STOCK_PART_NOT_FOUND', { partId });
      throw new Error(`Part not found with id ${partId} for stock update`); // Consider custom error
    }

    if (part.inventory.warehouseId.toString() !== warehouseIdFromTransaction) {
      logOperation('UPDATE_STOCK_WAREHOUSE_MISMATCH', { partId, partWarehouse: part.inventory.warehouseId.toString(), txWarehouse: warehouseIdFromTransaction });
      throw new Error(`Warehouse ID mismatch: Part ${partId} is stocked in ${part.inventory.warehouseId}, but transaction is for ${warehouseIdFromTransaction}.`); // Consider custom error
    }

    part.inventory.currentStock += quantityChange;
    part.inventory.lastStockUpdate = new Date();
    
    const updatedPart = await part.save();
    logOperation('UPDATE_STOCK_SUCCESS', { partId, newStock: updatedPart.inventory.currentStock });
    return updatedPart.toObject() as IPart;
  } catch (error: any) {
    logOperation('UPDATE_STOCK_ERROR', { partId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update stock for part ${partId}`);
  }
}

/**
 * Checks if a part is below its reorder level.
 * Updated to use the new schema structure with inventory sub-document
 */
export async function checkReorderPoint(partId: string): Promise<boolean> {
  logOperation('CHECK_REORDER_POINT', { partId });
  const part = await getPartById(partId); // Uses the service function already defined
  if (!part || part.reorderLevel === null || part.reorderLevel === undefined || typeof part.inventory?.currentStock !== 'number') {
    logOperation('CHECK_REORDER_POINT_NOT_APPLICABLE', { partId, reorderLevel: part?.reorderLevel, currentStock: part?.inventory?.currentStock });
    return false;
  }
  const needsReorder = part.inventory.currentStock < part.reorderLevel;
  logOperation('CHECK_REORDER_POINT_RESULT', { partId, needsReorder });
  return needsReorder;
}

/**
 * Search for parts with text search and filtering options
 */
export async function searchParts(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    let searchFilter: any = { ...filter };
    
    if (query) {
      searchFilter.$text = { $search: query };
    }

    const partsQuery = Part.find(searchFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const [parts, totalCount] = await Promise.all([
      partsQuery.exec() as any as Promise<IPart[]>,
      Part.countDocuments(searchFilter)
    ]);

    const pagination = {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search parts');
  }
} 

// Unit Test Considerations:
// - Mock connectToMongoose, Part model methods (findOne, findById, save, etc.)
// - Test successful CRUD operations.
// - Test error handling: duplicate partNumber, validation errors, part not found, invalid IDs.
// - Test business logic: stock updates, reorder point checks.
// - Test pagination and filtering in getAllParts and searchParts.

// Export an instance of the service if not using DI framework like NestJS, or export the class itself.
// export const partService = new PartService(); 