import mongoose from 'mongoose';
import connectToMongoose from '../lib/mongodb';
import Part from '../models/part.model';
import Supplier from '../models/supplier.model';
import PurchaseOrder from '../models/purchaseOrder.model';
import Transaction from '../models/transaction.model';
import WorkOrder from '../models/workOrder.model';
import User from '../models/user.model'; // Ensure User model is imported
import Assembly from '../models/assembly.model';
import Warehouse from '../models/warehouse.model'; // Ensure Warehouse model is imported
import Batch from '../models/batch.model'; // Added
import BatchLog from '../models/batchLog.model'; // Added
import Delivery from '../models/delivery.model'; // Added
import Setting from '../models/settings.model'; // Added
import Category from '../models/category.model'; // Added
import Product from '../models/product.model'; // Added Product import
import SystemLog from '../models/systemLog.model'; // Added
import { v4 as uuidv4 } from 'uuid';
import { format } from 'date-fns';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, collection: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[MongoDB][${timestamp}] ${operation} on ${collection}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @throws Error with a standardized message
 */
export const handleMongoDBError = (error: any) => { // Added export keyword
  console.error('[MongoDB Error]', error);

  // Set Sentry tags for better filtering
  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');

  // Determine error type and set appropriate tags
  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  // Check for specific MongoDB error types
  if (error.name === 'ValidationError') {
    // Handle validation errors
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;

    // Set Sentry tags for validation errors
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    // Handle duplicate key errors
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;

    // Set Sentry tags for duplicate key errors
    setTag('error.subtype', 'duplicate_key');
  } else {
    // Handle other errors
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;

    // Set Sentry tags for general database errors
    setTag('error.subtype', 'general');
  }

  // Capture the exception in Sentry
  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  // Return standardized error response
  return { message: errorMessage, status: errorStatus };
};

// Batch Log Management Functions
export async function addBatchLog(batchLogData: { batchId: string; event: string; userId: string; details?: any; timestamp?: Date }) {
  logOperation('ADD', 'batchLog', { batchId: batchLogData.batchId, event: batchLogData.event, userId: batchLogData.userId });
  try {
    await connectToMongoose();

    const newBatchLog = new BatchLog({
      ...batchLogData,
      timestamp: batchLogData.timestamp || new Date(), // Default to now if not provided
    });

    const savedBatchLog = await newBatchLog.save();
    logOperation('SUCCESS', 'batchLog_creation', { _id: savedBatchLog._id });
    return savedBatchLog.toObject(); // Return plain object for consistency
  } catch (error: any) {
    logOperation('ERROR', 'batchLog_creation', { error: error.message, data: batchLogData });
    // Capture with Sentry via handleMongoDBError and rethrow a simple message for the API layer
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to add batch log.');
  }
}

export async function getBatchLogs(batchId: string, options: { page?: number; limit?: number; sort?: any } = {}) {
  const { page = 1, limit = 20, sort = { timestamp: -1 } } = options;
  logOperation('FETCH_ALL', 'batchLogs', { batchId, page, limit, sort: JSON.stringify(sort) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    const logs = await BatchLog.find({ batchId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const totalCount = await BatchLog.countDocuments({ batchId });

    logOperation('SUCCESS', 'batchLogs_fetch', {
      batchId,
      count: logs.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit),
    });

    return {
      logs,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', 'batchLogs_fetch', { batchId, error: error.message });
    const errDetails = handleMongoDBError(error);
    // Return a structure consistent with successful calls but with empty data
    return {
      logs: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit,
      },
      error: errDetails.message || 'Failed to fetch batch logs.',
    };
  }
}

// Parts Management Functions
export async function fetchParts(options: {
  page?: number,
  limit?: number,
  sort?: any,
  filter?: any,
  includeSubParts?: boolean, // Changed from includeSub_parts
  includeInventory?: boolean
}) {
  const { // Destructure options with defaults at the beginning of the function
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {},
    includeSubParts = false, // Changed from includeSub_parts
    includeInventory = false
  } = options;

  try {
    // Assuming Part model is imported at the top of the file like other models
    // const { Part } = await import('@/app/models'); // Removed dynamic import

    const skip = (page - 1) * limit;

    let selection = '_id partNumber name description technicalSpecs isManufactured reorderLevel status supplierId unitOfMeasure costPrice categoryId createdAt updatedAt inventory';

    const query = Part.find(filter)
      .select(selection)
      .populate('supplierId', 'name contactPerson email phone address')
      .populate('categoryId', 'name description')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    if (includeInventory) {
      query.populate({
        path: 'inventory.warehouseId',
        select: 'name warehouseCode location',
        model: 'Warehouse' // Assumes Warehouse model is imported and available
      });
    }

    if (includeSubParts) {
        query.populate('subParts'); // Changed from sub_parts to subParts
    }

    const partsFromDb = await query.lean();

    const processedParts = partsFromDb.map((part: any) => {
      const newPart: any = { ...part };

      if (newPart.supplierId && typeof newPart.supplierId === 'object') {
        newPart.supplier = newPart.supplierId;
        newPart.supplierName = (newPart.supplierId as any).name || null;
      } else {
        newPart.supplier = null;
        newPart.supplierName = null;
      }

      if (newPart.categoryId && typeof newPart.categoryId === 'object') {
        newPart.category = newPart.categoryId;
        newPart.categoryName = (newPart.categoryId as any).name || null;
      } else {
        newPart.category = null;
        newPart.categoryName = null;
      }

      if (includeInventory && newPart.inventory && newPart.inventory.warehouseId && typeof newPart.inventory.warehouseId === 'object') {
        newPart.inventory.warehouse = newPart.inventory.warehouseId;
      } else if (newPart.inventory) { // Check if inventory object exists
        newPart.inventory.warehouse = null;
      }

      return newPart;
    });

    const total = await Part.countDocuments(filter);
    const pages = Math.ceil(total / limit);

    return {
      parts: processedParts,
      pagination: {
        total,
        page,
        limit,
        pages
      }
    };
  } catch (error: unknown) { // Typed error as unknown for better type safety
    console.error('[Service] Error in fetchParts:', error); // Simplified log message
    let errorMessage = 'Failed to fetch parts';
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    // Use page and limit from the destructured options at the function start
    return {
        parts: [],
        pagination: { total: 0, page, limit, pages: 0 },
        error: errorMessage
    };
  }
}

export async function getPart(partId: string, options: any = {}) { // partId is the _id (string)
  const { includeSubParts = false } = options; // Changed from includeSub_parts

  logOperation('GET', 'part', { _id: partId, includeSubParts }); // Changed from includeSub_parts
  try {
    await connectToMongoose();

    logOperation('FIND_BY_ID', 'part', { _id: partId, includeSubParts });

    // Build the query
    let query = Part.findById(partId);

    // Select fields based on the updated Part schema using camelCase
    let selection = '_id partNumber name description technicalSpecs isManufactured reorderLevel status inventory supplierId unitOfMeasure costPrice categoryId createdAt updatedAt';
    if (includeSubParts) { // Changed from includeSub_parts
      selection += ' subParts isAssembly'; // Add subParts and isAssembly if requested
      query = query.select(selection);

      // Populate subParts to avoid N+1 queries
      query = query.populate({
        path: 'subParts.partId', // Updated path
        model: 'Part',
        select: '_id partNumber name description technicalSpecs isManufactured reorderLevel status inventory supplierId unitOfMeasure costPrice categoryId createdAt updatedAt' // Updated selection for populated parts
      });
    } else {
      // Default selection without subParts
      query = query.select(selection);
    }

    // Execute the query
    const part = await query.lean();

    if (!part) {
      logOperation('NOT_FOUND', 'part', { _id: partId });
      return null;
    }

    logOperation('SUCCESS', 'get_part', { _id: partId, includeSubParts }); // Changed from includeSub_parts
    return part;
  } catch (error: any) {
    logOperation('ERROR', 'get_part', { _id: partId, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function addPart(partData: any) {
  logOperation('ADD', 'part', { name: partData.name });
  try {
    // Connect using Mongoose for model operations
    logOperation('CONNECT', 'mongoose');
    await connectToMongoose();

    // Use provided _id or generate one if necessary (schema uses String _id)
    const finalId = partData._id || uuidv4();
    logOperation('PREPARE_CREATE', 'part', { _id: finalId });

    // partData is expected to come from the API layer already structured according to IPart and IInventory (using camelCase)
    // The API layer should ensure `partData.inventory` contains all required fields for IInventory.

    logOperation('CREATE', 'part', { _id: finalId, name: partData.name });
    const newPart = new Part({
      _id: finalId, // Explicitly set the String _id
      partNumber: partData.partNumber, // Added partNumber
      name: partData.name,
      description: partData.description,
      technicalSpecs: partData.technicalSpecs, // Changed from technical_specs
      isManufactured: partData.isManufactured ?? false, // Changed from is_manufactured
      reorderLevel: partData.reorderLevel, // Changed from reorder_level
      status: partData.status || 'active',
      inventory: partData.inventory, // Directly use the validated inventory object from partData
      supplierId: partData.supplierId, // Changed from supplier_id
      unitOfMeasure: partData.unitOfMeasure, // Added
      costPrice: partData.costPrice, // Added
      categoryId: partData.categoryId, // Changed from category
      isAssembly: partData.isAssembly || false, // Kept for now, though not in the latest schema for 'parts'
      subParts: partData.subParts || [], // Changed from sub_parts, though not in latest 'parts' schema
      schemaVersion: partData.schemaVersion || 1 // Kept for now
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedPart = await newPart.save();
    logOperation('SUCCESS', 'part_creation', { _id: savedPart._id });

    return savedPart;
  } catch (error: any) {
    logOperation('ERROR', 'part_creation', { error: error.message, stack: error.stack });
    return handleMongoDBError(error);
  }
}

export async function updatePart(partId: string, partData: any) { // partId is the _id (string)
  logOperation('UPDATE', 'part', { _id: partId });
  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // partData is expected to come from the API layer already structured according to IPart and IInventory (using camelCase)
    const updatePayload: any = { ...partData };
    delete updatePayload._id; // Don't try to update _id

    // Ensure field names in updatePayload match the Mongoose schema (camelCase)
    if (updatePayload.technical_specs !== undefined) {
      updatePayload.technicalSpecs = updatePayload.technical_specs;
      delete updatePayload.technical_specs;
    }
    if (updatePayload.is_manufactured !== undefined) {
      updatePayload.isManufactured = updatePayload.is_manufactured;
      delete updatePayload.is_manufactured;
    }
    if (updatePayload.reorder_level !== undefined) {
      updatePayload.reorderLevel = updatePayload.reorder_level;
      delete updatePayload.reorder_level;
    }
    if (updatePayload.supplier_id !== undefined) {
      updatePayload.supplierId = updatePayload.supplier_id;
      delete updatePayload.supplier_id;
    }
    if (updatePayload.category !== undefined) { // Assuming 'category' might be old field for categoryId
        updatePayload.categoryId = updatePayload.category;
        delete updatePayload.category;
    }

    // Inventory is expected to be passed as a complete object if it's being updated.
    // The Part model (IPart) defines inventory as IInventory.
    // The API layer should send partData.inventory structured correctly.
    if (partData.inventory) {
        updatePayload.inventory = partData.inventory; // Directly assign if provided
    }

    // Remove any legacy flat inventory fields if they were somehow passed
    delete updatePayload.currentStock;
    delete updatePayload.location;
    delete updatePayload.lastCountDate;
    delete updatePayload.current_stock; // also remove snake_case version

    // updatedAt is handled by timestamps: true

    // Update part using its _id
    const part = await Part.findByIdAndUpdate(
      partId,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run schema validators
    ).lean();

    if (!part) {
      logOperation('UPDATE_NOT_FOUND', 'part', { _id: partId });
      throw new Error(`Part with ID ${partId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'part', { _id: partId });
    return part;
  } catch (error: any) {
    logOperation('ERROR', 'part', { _id: partId, error: error.message });
    handleMongoDBError(error);
    // This function's success response is a single part object, not paginated.
    // So, the error should not include pagination. Throwing or returning simple error object.
    throw new Error(handleMongoDBError(error).message || `Failed to update part ${partId}`);
    // Or: return { success: false, message: handleMongoDBError(error).message || `Failed to update part ${partId}`, error: handleMongoDBError(error) };
  }
}

export async function deletePart(partId: string) { // partId is the _id (string)
  logOperation('DELETE', 'part', { _id: partId });
  try {
    await connectToMongoose();

    const result = await Part.findByIdAndDelete(partId);
    if (!result) {
      logOperation('NOT_FOUND', 'part', { _id: partId });
      return { success: false, message: `Part with ID ${partId} not found` };
    }

    logOperation('SUCCESS', 'delete_part', { _id: partId });
    return { success: true, message: `Part with ID ${partId} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'part', { _id: partId, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting part: ${error.message}` }; // Return error message
  }
}

// Define an interface for the structure returned by searchParts
interface ISearchPartsResult {
  parts: {
    _id: string;
    partNumber?: string;
    name: string;
    description?: string | null;
    technicalSpecs?: string | null; // Using camelCase
    status?: string;
    reorderLevel?: number | null; // Using camelCase
    isManufactured?: boolean;
    isAssembly?: boolean; // Added field to identify if part is an assembly
    supplierId?: string;
    unitOfMeasure?: string;
    costPrice?: number;
    categoryId?: string;
    inventory?: {
      currentStock?: number;
      warehouseId?: string;
      safetyStockLevel?: number;
      maximumStockLevel?: number;
      averageDailyUsage?: number;
      abcClassification?: string;
      lastStockUpdate?: Date;
      // Not including location as it's not in the schema
    };
    createdAt?: Date;
    updatedAt?: Date;
  }[];
  pagination: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

// Helper function to escape special characters for RegExp
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
}

// Add explicit return type annotation
export async function searchParts(options: any = {}): Promise<ISearchPartsResult> {
  const {
    query = '', // General search query string
    page = 1,
    limit = 20,
    filter = {}, // Specific field filters
    sort = { updatedAt: -1 } // Default sort
    // Removed 'fields' option as search logic is now more specific
  } = options;

  logOperation('SEARCH', 'parts', { query, page, limit, filter: JSON.stringify(filter), sort: JSON.stringify(sort) });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // --- Build Combined Query ---
    let finalQuery: any = {};
    const conditions = [];

    // Add specific field filters if any
    if (filter && Object.keys(filter).length > 0) {
      conditions.push(filter);
    }

    // Trim the query and check if it's non-empty
    const trimmedQuery = query ? query.trim() : '';

    // Add regex-based search if trimmedQuery is provided
    if (trimmedQuery) {
      const escapedQuery = escapeRegExp(trimmedQuery);
      const searchRegex = new RegExp(escapedQuery, 'i'); // Case-insensitive regex with escaped query
      const searchableFields = ['partNumber', 'name', 'description', 'technicalSpecs'];
      const regexConditions = searchableFields.map((field: string) => ({ [field]: searchRegex }));
      conditions.push({ $or: regexConditions }); // Search for query in any of the specified fields
    }

    // Construct the final query based on the conditions
    if (conditions.length > 1) {
      finalQuery = { $and: conditions }; // Combine all conditions if multiple exist
    } else if (conditions.length === 1) {
      finalQuery = conditions[0]; // Use the single condition
    } else {
      // No query and no filter, or empty query and no filter, fetch all documents (respecting pagination)
      finalQuery = {};
    }

    logOperation('DEBUG', 'search_parts_query', { finalQuery: JSON.stringify(finalQuery) });

    // Use a more optimized query approach
    // Execute search with pagination, combined query, and sorting
    const parts = await Part.find(finalQuery) // Use finalQuery
      .sort(sort)
      .skip(skip)
      .limit(limit)
      // Select fields based on the updated Part schema with camelCase field names
      .select('_id partNumber name description technicalSpecs status inventory reorderLevel createdAt updatedAt isManufactured supplierId unitOfMeasure costPrice categoryId isAssembly')
      // Ensure lean() is used to improve performance
      .lean() as unknown as ISearchPartsResult['parts']; // Explicitly cast lean result

    // Get total count for pagination info using the combined query - more efficiently
    const totalCount = await Part.countDocuments(finalQuery).exec(); // Use finalQuery

    // Enhanced logging for debugging search results
    console.log(`[MongoDBService-searchParts] Executed query: ${JSON.stringify(finalQuery)}`);
    console.log(`[MongoDBService-searchParts] Found ${parts.length} parts with this query.`);
    console.log(`[MongoDBService-searchParts] Total matching documents in collection (ignoring pagination): ${totalCount}`);
    if (parts.length > 0 && parts.length <= 5) { // Log first few parts if found, up to 5
        console.log(`[MongoDBService-searchParts] First few parts returned (up to 5): ${JSON.stringify(parts.slice(0,5).map(p => ({ _id: p._id, name: p.name, partNumber: p.partNumber }))) }`);
    } else if (parts.length > 5) {
        console.log(`[MongoDBService-searchParts] More than 5 parts found, logging only the count.`);
    }

    logOperation('SEARCH_SUCCESS', 'parts', {
      query,
      filter: JSON.stringify(filter),
      count: parts.length,
      totalCount
    });

    return {
      parts,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'search_parts', { query, filter: JSON.stringify(filter), error: error.message });
    handleMongoDBError(error);
    // Ensure function returns in case of error with the correct structure
    return { parts: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}

// Product Management Functions
export async function getProduct(productCode: string) {
  logOperation('GET', 'product', { productCode });
  try {
    await connectToMongoose();

    // Try to find by _id first if it looks like a MongoDB ObjectId
    if (mongoose.Types.ObjectId.isValid(productCode)) {
      const product = await Product.findById(productCode)
        .populate('main_assembly_id')
        .lean();

      if (product) {
        logOperation('SUCCESS', 'get_product_by_id', { _id: productCode });
        return product;
      }
    }

    // Otherwise search by product_id field
    const product = await Product.findOne({ product_id: productCode })
      .populate('main_assembly_id')
      .lean();

    if (!product) {
      logOperation('NOT_FOUND', 'product', { productCode });
      return null;
    }

    logOperation('SUCCESS', 'get_product', { productCode });
    return product;
  } catch (error: any) {
    logOperation('ERROR', 'get_product', { productCode, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function updateProduct(productCode: string, updateData: any) {
  logOperation('UPDATE', 'product', { productCode });
  try {
    await connectToMongoose();

    // Try to find by _id first if it looks like a MongoDB ObjectId
    let product;
    if (mongoose.Types.ObjectId.isValid(productCode)) {
      product = await Product.findById(productCode);
      if (!product) {
        product = await Product.findOne({ product_id: productCode });
      }
    } else {
      product = await Product.findOne({ product_id: productCode });
    }

    if (!product) {
      logOperation('UPDATE_NOT_FOUND', 'product', { productCode });
      throw new Error(`Product with code ${productCode} not found`);
    }

    // Update the product fields
    Object.keys(updateData).forEach(key => {
      product[key] = updateData[key];
    });

    // Save and return the updated product
    const updatedProduct = await product.save();
    logOperation('UPDATE_SUCCESS', 'product', { productCode });
    return updatedProduct;
  } catch (error: any) {
    logOperation('ERROR', 'update_product', { productCode, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function deleteProduct(productCode: string) {
  logOperation('DELETE', 'product', { productCode });
  try {
    await connectToMongoose();

    // Try to delete by _id first if it looks like a MongoDB ObjectId
    let result;
    if (mongoose.Types.ObjectId.isValid(productCode)) {
      result = await Product.findByIdAndDelete(productCode);
      if (result) {
        logOperation('DELETE_SUCCESS', 'product', { _id: productCode });
        return { success: true, message: `Product ${productCode} deleted successfully` };
      }
    }

    // Otherwise delete by product_id field
    result = await Product.findOneAndDelete({ product_id: productCode });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'product', { productCode });
      throw new Error(`Product with code ${productCode} not found`);
    }

    logOperation('DELETE_SUCCESS', 'product', { productCode });
    return { success: true, message: `Product ${productCode} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_product', { productCode, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting product: ${error.message}` }; // Return error message
  }
}

export async function fetchProducts(options: any = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = -1,
    search = '',
    filters = {}
  } = options;

  logOperation('FETCH_ALL', 'products', { page, limit, sortBy, sortOrder, search });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Build the query
    let query: any = {};

    // Add search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { product_id: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Add any additional filters
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        query[key] = filters[key];
      }
    });

    // Execute the query
    const products = await Product.find(query)
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit)
      .populate('main_assembly_id')
      .lean();

    // Get total count for pagination
    const total = await Product.countDocuments(query);

    logOperation('SUCCESS', 'fetch_products', { count: products.length, total });

    return {
      products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error: any) {
    logOperation('ERROR', 'fetch_products', { error: error.message });
    handleMongoDBError(error);
    return { categories: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } }; // Ensure return
  }
}

export async function addProduct(productData: any) {
  logOperation('ADD', 'product', { name: productData.name });
  try {
    await connectToMongoose();

    // Create a new product using the provided data
    const newProduct = new Product(productData);
    const savedProduct = await newProduct.save();

    logOperation('SUCCESS', 'add_product', { _id: savedProduct._id });
    return savedProduct;
  } catch (error: any) {
    logOperation('ERROR', 'add_product', { error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

// Supplier Management Functions
export async function fetchSuppliers(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    includeParts = false, // New option to include parts
    partsLimit = 10 // Limit for parts per category, adjust as needed
  } = options;
  logOperation('FETCH_ALL', 'suppliers', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });
  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch suppliers with pagination and select fields based on updated schema
    const suppliers = await Supplier.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('supplier_id name contactPerson email phone address specialty rating createdAt') // Select updated fields
      .lean();

    const totalCount = await Supplier.countDocuments(filter);

    logOperation('SUCCESS', 'suppliers', { count: suppliers.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });

    return {
      suppliers,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'suppliers', { error: error.message });
    handleMongoDBError(error);
    return { categories: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } }; // Ensure return
  }
}

export async function addSupplier(supplierData: any) {
  logOperation('ADD', 'supplier', { name: supplierData.name });
  try { // Corrected: Removed the nested try block which was causing syntax error
    await connectToMongoose();

    // Use provided supplier_id or generate one
    const supplier_id = supplierData.supplier_id || uuidv4();
    logOperation('PREPARE_CREATE', 'supplier', { supplier_id });

    // Create supplier based on the new schema
    const newSupplier = new Supplier({
      supplier_id: supplier_id, // Ensure unique identifier is set
      name: supplierData.name,
      contactPerson: supplierData.contactPerson,
      email: supplierData.email,
      phone: supplierData.phone,
      address: supplierData.address,
      specialty: supplierData.specialty || [], // Default to empty array
      rating: supplierData.rating,
      // createdAt is handled by timestamps: true
      // Removed is_active field
    });

    const savedSupplier = await newSupplier.save();
    logOperation('SUCCESS', 'supplier_creation', { _id: savedSupplier._id, supplier_id: savedSupplier.supplier_id });
    return savedSupplier;
  } catch (error) {
    handleMongoDBError(error);
  } // Corrected: Removed extra closing brace if any, ensured try has a catch
}

export async function updateSupplier(supplierId: string, supplierData: any) { // supplierId is the unique string identifier
  logOperation('UPDATE', 'supplier', { supplier_id: supplierId });
  try {
    await connectToMongoose();

    // Find the supplier by ID (could be ObjectId or supplier_id field)
    let supplier;
    if (mongoose.Types.ObjectId.isValid(supplierId)) {
      supplier = await Supplier.findById(supplierId);
      if (!supplier) {
        supplier = await Supplier.findOne({ supplier_id: supplierId });
      }
    } else {
      supplier = await Supplier.findOne({ supplier_id: supplierId });
    }

    if (!supplier) {
      logOperation('NOT_FOUND', 'supplier', { supplier_id: supplierId });
      throw new Error(`Supplier with ID ${supplierId} not found`);
    }

    // Update only the fields that are provided
    Object.keys(supplierData).forEach(key => {
      if (supplierData[key] !== undefined) {
        supplier[key] = supplierData[key];
      }
    });

    // Save the updated supplier
    const updatedSupplier = await supplier.save();
    logOperation('SUCCESS', 'update_supplier', { supplier_id: supplierId });
    return updatedSupplier;
  } catch (error: any) {
    logOperation('ERROR', 'supplier', { supplier_id: supplierId, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function deleteSupplier(supplierId: string) { // supplierId is the unique string identifier
  logOperation('DELETE', 'supplier', { supplier_id: supplierId });
  try {
    await connectToMongoose();

    // Find and delete the supplier by ID
    let result;
    if (mongoose.Types.ObjectId.isValid(supplierId)) {
      result = await Supplier.findByIdAndDelete(supplierId);
      if (result) {
        logOperation('SUCCESS', 'delete_supplier', { _id: supplierId });
        return { success: true, message: `Supplier ${supplierId} deleted successfully` };
      }
    }

    // Try by supplier_id field if not found by _id or not a valid ObjectId
    result = await Supplier.findOneAndDelete({ supplier_id: supplierId });

    if (!result) {
      logOperation('NOT_FOUND', 'supplier', { supplier_id: supplierId });
      throw new Error(`Supplier with ID ${supplierId} not found`);
    }

    logOperation('SUCCESS', 'delete_supplier', { supplier_id: supplierId });
    return { success: true, message: `Supplier ${supplierId} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_supplier', { supplier_id: supplierId, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting supplier: ${error.message}` }; // Return error message
  }
}

export async function getSupplier(supplierId: string) { // supplierId is the unique string identifier
  logOperation('GET', 'supplier', { supplier_id: supplierId });
  try {
    await connectToMongoose();
    const supplier = await Supplier.findOne({ supplier_id: supplierId })
      // Select fields based on updated Supplier schema
      .select('supplier_id name contactPerson email phone address specialty rating createdAt updatedAt')
      .lean();

    if (!supplier) {
      logOperation('NOT_FOUND', 'supplier', { supplier_id: supplierId });
      // Return null if not found, let the API layer handle the 404
      return null;
    }
    logOperation('SUCCESS', 'get_supplier', { supplier_id: supplierId });
    return supplier;
  } catch (error: any) {
    logOperation('ERROR', 'get_supplier', { supplier_id: supplierId, error: error.message });
    // Re-throw error after logging and standardizing (handleMongoDBError throws)
    handleMongoDBError(error);
    // This line might not be reached if handleMongoDBError always throws, but added for type safety
    throw error;
  }
}

// Purchase Order Functions
export async function fetchPurchaseOrders(options: any = {}) {
  const { // Destructure options with defaults
    page = 1,
    limit = 20,
    sort = { order_date: -1 },
    filter = {}
  } = options;
  logOperation('FETCH_ALL', 'purchaseOrders', options);
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    const purchaseOrders = await PurchaseOrder.find(filter)
      .populate('supplier_id', 'name contactPerson email')
      .populate('created_by', 'username name email')
      .populate({ path: 'items.item_id', model: 'Part', select: 'partNumber name description unitOfMeasure' })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    const totalCount = await PurchaseOrder.countDocuments(filter);
    logOperation('SUCCESS', 'purchaseorders', { count: purchaseOrders.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });
    return {
      purchaseOrders,
      pagination: { totalCount, totalPages: Math.ceil(totalCount / limit), currentPage: page, limit }
    };
  } catch (error: any) {
    logOperation('ERROR', 'purchaseorders', { error: error.message });
    const errDetails = handleMongoDBError(error);
    // Use destructured page and limit for consistent error return
    return { purchaseOrders: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit }, error: errDetails.message || 'Failed to fetch purchase orders' };
  }
}

export async function getPurchaseOrder(poNumber: string) { // Find by poNumber (string)
  logOperation('GET', 'purchase_order', { poNumber });
  try {
    await connectToMongoose();

    // Try by _id first if it looks like a MongoDB ObjectId
    if (mongoose.Types.ObjectId.isValid(poNumber)) {
      const po = await PurchaseOrder.findById(poNumber)
        .populate('supplier_id')
        .populate('items.part_id')
        .lean();

      if (po) {
        logOperation('SUCCESS', 'get_purchaseorder_by_id', { _id: poNumber });
        return po;
      }
    }

    // Otherwise try by poNumber field
    const po = await PurchaseOrder.findOne({ poNumber })
      .populate('supplierId')
      .populate('items.partId')
      .lean();

    if (!po) {
      logOperation('NOT_FOUND', 'purchase_order', { poNumber });
      return null;
    }

    logOperation('SUCCESS', 'get_purchaseorder', { poNumber });
    return po;
  } catch (error: any) {
    logOperation('ERROR', 'get_purchaseorder', { poNumber: poNumber, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function createPurchaseOrder(poData: any) {
  logOperation('CREATE', 'purchase_order', { poNumber: poData.poNumber });
  try {
    await connectToMongoose();

    // Create the purchase order with the provided data
    const newPO = new PurchaseOrder(poData);
    const savedPO = await newPO.save();

    logOperation('SUCCESS', 'purchase_order_creation', { poNumber: savedPO.poNumber });
    return savedPO;
  } catch (error: any) {
    logOperation('ERROR', 'purchaseorder_creation', { poNumber: poData.poNumber, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

// Alias for createPurchaseOrder to maintain backward compatibility
export const addPurchaseOrder = createPurchaseOrder;

export async function updatePurchaseOrder(poNumber: string, poData: any) { // Find by poNumber
  logOperation('UPDATE', 'purchase_order', { poNumber });
  try {
    await connectToMongoose();

    // Find the purchase order
    let po;
    if (mongoose.Types.ObjectId.isValid(poNumber)) {
      po = await PurchaseOrder.findById(poNumber);
      if (!po) {
        po = await PurchaseOrder.findOne({ poNumber });
      }
    } else {
      po = await PurchaseOrder.findOne({ poNumber });
    }

    if (!po) {
      logOperation('NOT_FOUND', 'purchase_order', { poNumber });
      throw new Error(`Purchase order ${poNumber} not found`);
    }

    // Update fields
    Object.keys(poData).forEach(key => {
      po[key] = poData[key];
    });

    // Save updated PO
    const updatedPO = await po.save();
    logOperation('SUCCESS', 'update_purchaseorder', { poNumber });
    return updatedPO;
  } catch (error: any) {
    logOperation('ERROR', 'update_purchaseorder', { poNumber: poNumber, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function deletePurchaseOrder(poNumber: string) { // Find by poNumber
  logOperation('DELETE', 'purchase_order', { poNumber });
  try {
    await connectToMongoose();

    // Try delete by _id first
    let result;
    if (mongoose.Types.ObjectId.isValid(poNumber)) {
      result = await PurchaseOrder.findByIdAndDelete(poNumber);
      if (result) {
        logOperation('SUCCESS', 'delete_purchaseorder', { _id: poNumber });
        return { success: true, message: `Purchase order ${poNumber} deleted successfully` };
      }
    }

    // Try by poNumber field
    result = await PurchaseOrder.findOneAndDelete({ poNumber });

    if (!result) {
      logOperation('NOT_FOUND', 'purchase_order', { poNumber });
      throw new Error(`Purchase order ${poNumber} not found`);
    }

    logOperation('SUCCESS', 'delete_purchaseorder', { poNumber });
    return { success: true, message: `Purchase order ${poNumber} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_purchaseorder', { poNumber: poNumber, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting purchase order: ${error.message}` }; // Return error message
  }
}

// Inventory Transactions
export async function recordTransaction(transactionData: {
  partId: string;
  transactionType: 'stock_in' | 'stock_out' | 'adjustment' | 'initial';
  quantity: number; // The actual change amount (+ve for in, -ve for out, absolute for adjustment/initial)
  transactionDate?: Date;
  referenceNumber: string;
  notes?: string;
  originalSqlId?: any;
  session?: mongoose.ClientSession; // Optional session for atomicity
}) {
  const { partId, transactionType, quantity, referenceNumber } = transactionData;
  logOperation('RECORD_TRANSACTION', 'transaction', { partId, transactionType, quantity, referenceNumber });

  const session = transactionData.session || await mongoose.startSession();
  const ownsSession = !transactionData.session; // Track if we created the session

  try {
    await connectToMongoose(); // Ensure connection

    // Start transaction if we own the session
    if (ownsSession) {
      session.startTransaction();
      logOperation('START_TRANSACTION', 'transaction', { partId });
    }

    // 1. Find the part
    const part = await Part.findById(partId).session(session);
    if (!part) {
      throw new Error(`Part with ID ${partId} not found`);
    }

    // 2. Determine previous and new stock levels
    const previousStock = part.inventory?.currentStock ?? 0; // Assuming 'currentStock' field in inventory object
    let newStock: number;
    let changeQuantity: number; // The quantity value to store in the transaction record

    switch (transactionType) {
      case 'stock_in':
        newStock = previousStock + quantity;
        changeQuantity = quantity; // Positive quantity for stock in
        break;
      case 'stock_out':
        newStock = previousStock - quantity; // Subtract positive quantity for stock out
        changeQuantity = -quantity; // Store negative quantity for stock out
        if (newStock < 0) {
            // Optional: Prevent stock going negative, or allow based on business rules
            // throw new Error(`Insufficient stock for part ${partId}. Required: ${quantity}, Available: ${previousStock}`);
            logOperation('WARN', 'transaction', { partId, message: 'Stock level went negative' });
        }
        break;
      case 'adjustment':
      case 'initial':
        // For adjustment/initial, the provided quantity IS the new stock level
        newStock = quantity;
        changeQuantity = newStock - previousStock; // Calculate the effective change
        break;
      default:
        throw new Error(`Invalid transaction type: ${transactionType}`);
    }

    // 3. Update the Part's inventory
    // Assuming inventory field exists and has a currentStock subfield
    const updatedPart = await Part.findByIdAndUpdate(
      partId,
      { $set: { 'inventory.currentStock': newStock } }, // Update the nested field
      { new: true, session: session }
    );

    if (!updatedPart) {
        throw new Error(`Failed to update inventory for part ${partId}`);
    }
    logOperation('UPDATE_INVENTORY', 'part', { partId, previousStock, newStock });


    // 4. Create and save the transaction record
    const newTransaction = new Transaction({
      partId: partId,
      transactionType: transactionType,
      quantity: changeQuantity, // Store the calculated change quantity
      previousStock: previousStock,
      newStock: newStock,
      transactionDate: transactionData.transactionDate || new Date(),
      referenceNumber: transactionData.referenceNumber,
      notes: transactionData.notes,
      originalSqlId: transactionData.originalSqlId,
      // schemaVersion is handled by model default
      // createdAt/updatedAt handled by timestamps
    });

    const savedTransaction = await newTransaction.save({ session: session });
    logOperation('SAVE_TRANSACTION', 'transaction', { _id: savedTransaction._id, partId });

    // Commit transaction if we own the session
    if (ownsSession) {
      await session.commitTransaction();
      logOperation('COMMIT_TRANSACTION', 'transaction', { partId });
    }

    return savedTransaction;

  } catch (error: any) {
    // Abort transaction on error if we own the session
    if (ownsSession) {
      await session.abortTransaction();
      logOperation('ABORT_TRANSACTION', 'transaction', { partId, error: error.message });
    }
    logOperation('ERROR', 'record_transaction', { partId, error: error.message });
    handleMongoDBError(error); // Moved inside the catch block
  } finally {
    // End session if we own it
    if (ownsSession) {
      session.endSession();
      logOperation('END_SESSION', 'transaction', { partId });
    }
  }
}

export async function getTransactionHistory(partId: string, options: any = {}) { // partId is the string _id of the Part
  const {
    page = 1,
    limit = 50, // Default to more transactions per page
    sort = { transactionDate: -1 } // Default sort by date descending
  } = options;
  logOperation('GET_TRANSACTION_HISTORY', 'transaction', { partId, page, limit });
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Get transactions for this part using the string partId directly
    const transactions = await Transaction.find({ partId: partId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      // Select fields based on updated Transaction schema
      .select('partId transactionType quantity previousStock newStock transactionDate referenceNumber notes createdAt')
      // Removed populate for user_id as it's not in the schema
      .lean();

     const totalCount = await Transaction.countDocuments({ partId: partId });

     logOperation('SUCCESS', 'get_transaction_history', { partId, count: transactions.length, totalCount });

    return {
        transactions,
        pagination: {
            totalCount,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: page,
            limit
        }
    };
  } catch (error: any) {
    logOperation('ERROR', 'get_transaction_history', { partId, error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return { transactions: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}

// --- Assembly Management Functions ---

// Interface for fetchAssemblies options
interface FetchAssembliesOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
  includeParts?: boolean; // Option to populate parts details
}

// Interface for the structure returned by fetchAssemblies
interface FetchAssembliesResult {
  assemblies: any[]; // Define a more specific type if possible based on Assembly model
  pagination: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

/**
 * Fetches a list of assemblies with pagination, sorting, filtering, and optional part population.
 * @param options - Options for fetching assemblies.
 * @returns An object containing the list of assemblies and pagination info.
 */
export async function fetchAssemblies(options: FetchAssembliesOptions = {}): Promise<FetchAssembliesResult> {
  const {
    page = 1,
    limit = 10, // Default limit
    sort = { updatedAt: -1 }, // Default sort
    filter = {},
    includeParts = false // Default to not populating parts
  } = options;

  logOperation('FETCH_ALL', 'assemblies', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter), includeParts });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    let query = Assembly.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      // Select fields based on the Assembly schema - ensure we get _id and assemblyCode
      .select('_id assemblyCode name description status partsRequired isTopLevel productId parentId notes createdBy createdAt updatedAt')
      .populate({
          path: 'createdBy',
          model: 'User',
          select: 'username fullName email', // Select relevant user fields
          strictPopulate: false // Add this line to make createdBy population optional
      });

    // Conditionally populate partsRequired with complete part information if requested
    if (includeParts) {
      query = query
        .populate({
          path: 'partsRequired.partId',
          model: 'Part',
          // Include all essential part fields for visual analysis
          select: '_id name description technicalSpecs isManufactured reorderLevel status inventory createdAt updatedAt'
        });

      // If there are other part references or component fields that need population, add them here
    }

    const assemblies = await query.lean(); // Execute the query

    const totalCount = await Assembly.countDocuments(filter);

    logOperation('SUCCESS', 'assemblies', { count: assemblies.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });

    return {
      assemblies,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'fetch_assemblies', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return { assemblies: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}


/**
 * Adds a new assembly to the database.
 * @param assemblyData - The data for the new assembly.
 * @returns The newly created assembly document.
 */
export async function addAssembly(assemblyData: any) {
  logOperation('ADD', 'assembly', { name: assemblyData.name });
  try {
    await connectToMongoose();

    // Use the assemblyCode provided in the data
    const assemblyCode = assemblyData.assemblyCode;
    if (!assemblyCode) {
        // This should ideally be caught by API route validation, but double-check
        throw new Error("Assembly code is missing in the provided data for addAssembly service.");
    }
    logOperation('PREPARE_CREATE', 'assembly', { assemblyCode });

    // Process the partsRequired array to ensure proper structure with children
    if (assemblyData.partsRequired && Array.isArray(assemblyData.partsRequired)) {
      logOperation('DEBUG', 'assembly_partsRequired_before', {
        count: assemblyData.partsRequired.length,
        sample: assemblyData.partsRequired.slice(0, 2)
      });

      assemblyData.partsRequired = assemblyData.partsRequired.map((part: any) => {
        // Enhanced debugging
        if (!part.partId) {
          logOperation('WARN', 'assembly_missing_partId', { part });
        }

        // Ensure partId exists and is in the correct format
        let partId = part.partId;
        if (typeof partId === 'object' && partId !== null) {
          if (partId._id) {
            partId = partId._id;
            logOperation('DEBUG', 'assembly_fixed_partId_object', {
              before: JSON.stringify(part.partId),
              after: partId
            });
          } else {
            logOperation('ERROR', 'assembly_invalid_partId', { partId });
          }
        }

        // Use canonical field names
        const formattedPart: any = {
          partId, // Use the fixed partId
          // Use canonical field names with fallbacks to legacy names
          quantityRequired: part.quantityRequired || part.quantity || 1,
          unitOfMeasure: part.unitOfMeasure || part.unit_of_measure || 'ea'
        };

        // Process children recursively if present
        if (part.children && Array.isArray(part.children)) {
          formattedPart.children = processPartsRecursively(part.children);
        }

        return formattedPart;
      });

      logOperation('DEBUG', 'assembly_partsRequired_after', {
        count: assemblyData.partsRequired.length,
        sample: assemblyData.partsRequired.slice(0, 2)
      });
    } else {
      logOperation('WARN', 'assembly_missing_partsRequired', { assemblyData });
      // Create a minimal valid partsRequired array if missing
      assemblyData.partsRequired = [];
    }

    // Map legacy status values to canonical status values if needed
    let statusValue = assemblyData.status || 'active';
    if (statusValue === 'design_complete' || statusValue === 'design_phase') {
      statusValue = 'pending_review';
    }

    // Create the assembly based on the schema, using correct field names
    const newAssembly = new Assembly({
      assemblyCode: assemblyCode, // Use the correct field name from schema
      name: assemblyData.name,
      description: assemblyData.description,
      status: statusValue, // Use canonical status values
      // Removed 'components' as it's not in the schema
      partsRequired: assemblyData.partsRequired, // Use the correct field name from schema
      notes: assemblyData.notes,
      createdBy: assemblyData.createdBy, // Expecting ObjectId string (ensure this is passed correctly)
      // isTopLevel, productId, parentId might need defaults or be passed if required by schema logic
      isTopLevel: assemblyData.isTopLevel ?? false, // Default based on schema
      // productId: assemblyData.productId, // Pass if needed
      // parentId: assemblyData.parentId, // Pass if needed
      // createdAt/updatedAt handled by timestamps
      // schemaVersion handled by default
    });

    // Log the object just before saving
    logOperation('DEBUG', 'assembly_pre_save', { assemblyObject: newAssembly.toObject() });

    const savedAssembly = await newAssembly.save();
    // Log the correct identifier after successful save
    logOperation('SUCCESS', 'assembly_creation', { _id: savedAssembly._id, assemblyCode: savedAssembly.assemblyCode });
    return savedAssembly;
  } catch (error: any) {
    // Log the specific error during creation attempt
    logOperation('ERROR', 'assembly_creation_save', { name: assemblyData.name, error: error.message, stack: error.stack });
    // Get the standardized error object but throw it so the API layer can handle it
    const errorResponse = handleMongoDBError(error);
    // Throw a new error with the specific message and potentially attach the status
    const specificError = new Error(errorResponse.message);
    (specificError as any).status = errorResponse.status; // Attach status if needed by API layer
    throw specificError; // Throw the more specific error
  }
}

/**
 * Fetches a single assembly by its _id or assemblyCode.
 * @param id - The unique identifier (can be either assemblyCode or _id) of the assembly.
 * @param includeParts - Whether to populate part details.
 * @returns The assembly document or null if not found.
 */
export async function getAssembly(id: string, includeParts: boolean = false) {
  logOperation('GET', 'assembly', { id, includeParts });
  try {
    await connectToMongoose();

    // Determine the query based on id format
    let query = {};

    // First try to find by assemblyCode (which is the primary identifier in the route)
    logOperation('GET_ATTEMPT', 'assembly', { assemblyCode: id });
    let assembly = await Assembly.findOne({ assemblyCode: id });

    // If not found by assemblyCode, try by _id if it's a valid ObjectId
    if (!assembly && mongoose.Types.ObjectId.isValid(id)) {
      logOperation('GET_ATTEMPT', 'assembly', { _id: id });
      query = { _id: id };
    } else {
      // Either we found it by assemblyCode or we'll try again with the assemblyCode
      query = { assemblyCode: id };
    }

    // If we already found the assembly in our first attempt, we don't need to query again
    if (!assembly) {
      let queryBuilder = Assembly.findOne(query) // Find by query (either _id or assemblyCode)
        .select('_id assemblyCode name description status partsRequired notes createdBy createdAt updatedAt') // Removed 'components' from selection
        .populate({
            path: 'createdBy',
            model: 'User',
            select: 'username fullName email'
        });

      // Set strictPopulate to false to prevent schema path errors
      assembly = await queryBuilder.lean({ strictPopulate: false });
    }

    // If we found the assembly and need to populate parts, do it in a separate query
    if (assembly && includeParts) {
      // Create a new query to populate the parts with proper nesting
      let populateQuery = Assembly.findOne({ _id: assembly._id })
        .select('_id assemblyCode name description status partsRequired notes createdBy createdAt updatedAt')
        .populate({
            path: 'createdBy',
            model: 'User',
            select: 'username fullName email'
        });

      // Add recursive population for partsRequired and their children
      // Use a recursive function to build the populate configuration
      const buildPartPopulation = (path: string) => ({
        path: `${path}.partId`,
          model: 'Part',
        select: '_id name description category inventory reorder_level technical_specs',
        // Don't populate further to avoid circular references
        options: { strictPopulate: false }
      });

      // Populate parts at top level
      populateQuery = populateQuery.populate(buildPartPopulation('partsRequired'));

      // Add explicit population for children to support the recursive structure
      // This handles one level of nesting - you can add more levels if needed
      populateQuery = populateQuery.populate(buildPartPopulation('partsRequired.children'));
      populateQuery = populateQuery.populate(buildPartPopulation('partsRequired.children.children'));

      // Get the populated assembly
      const populatedAssembly = await populateQuery.lean({ strictPopulate: false });
      if (populatedAssembly) {
        assembly = populatedAssembly;
      }
    }

    if (!assembly) {
      logOperation('NOT_FOUND', 'assembly', { id });
      // Let the API layer handle the 404 response by returning null
      return null;
    }

    logOperation('SUCCESS', 'get_assembly', { id });
    return assembly;
  } catch (error: any) {
    logOperation('ERROR', 'get_assembly', { id, error: error.message });
    handleMongoDBError(error); // Throws standardized error
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing assembly by its assemblyCode or _id.
 * @param id - The unique identifier (can be either assemblyCode or _id) of the assembly to update.
 * @param updateData - An object containing the fields to update.
 * @returns The updated assembly document.
 */
export async function updateAssembly(id: string, updateData: any) {
  logOperation('UPDATE', 'assembly', { id });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...updateData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.assemblyCode; // Don't update the unique identifier
    delete updatePayload.createdAt;
    // updatedAt is handled by timestamps

    // Process any nested partsRequired structure if present
    if (updatePayload.partsRequired && Array.isArray(updatePayload.partsRequired)) {
      // Ensure the children arrays are properly formatted for MongoDB
      updatePayload.partsRequired = updatePayload.partsRequired.map((part: any) => {
        const formattedPart = { ...part };

        // Process children recursively if present
        if (formattedPart.children && Array.isArray(formattedPart.children)) {
          formattedPart.children = processPartsRecursively(formattedPart.children);
        }

        return formattedPart;
      });
    }

    // First try to find by _id (for backward compatibility)
    let query = {};
    try {
      // Check if the id is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(id)) {
        query = { _id: id };
        logOperation('UPDATE_ATTEMPT', 'assembly', { _id: id });
      } else {
        // If not a valid ObjectId, use as assemblyCode
        query = { assemblyCode: id };
        logOperation('UPDATE_ATTEMPT', 'assembly', { assemblyCode: id });
      }
    } catch (error) {
      // If any error in parsing, default to using as assemblyCode
      query = { assemblyCode: id };
      logOperation('UPDATE_ATTEMPT', 'assembly', { assemblyCode: id });
    }

    // Find by query (either _id or assemblyCode) and update
    const updatedAssembly = await Assembly.findOneAndUpdate(
      query,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run schema validators
    ).lean();

    if (!updatedAssembly) {
      logOperation('UPDATE_NOT_FOUND', 'assembly', { id });
      throw new Error(`Assembly with identifier ${id} not found`); // Throw error for API layer
    }

    logOperation('UPDATE_SUCCESS', 'assembly', { id });
    return updatedAssembly;
  } catch (error: any) {
    logOperation('ERROR', 'update_assembly', { id, error: error.message });
    handleMongoDBError(error); // Throws standardized error
    throw error; // Re-throw for API layer
  }
}

/**
 * Helper function to process parts recursively, ensuring proper structure
 */
function processPartsRecursively(parts: any[]): any[] {
  if (!Array.isArray(parts)) return [];

  return parts.map((part: any) => { // Add explicit : any type for 'part'
    // Enhanced processing for part objects with proper error handling
    if (!part || typeof part !== 'object') {
      logOperation('ERROR', 'processPartsRecursively_invalid_part', { part });
      return null; // This will be filtered out below
    }

    // Ensure partId exists and is in the correct format
    let partId = part.partId;
    if (typeof partId === 'object' && partId !== null) {
      if (partId._id) {
        partId = partId._id;
        logOperation('DEBUG', 'processPartsRecursively_fixed_partId', {
          before: JSON.stringify(part.partId),
          after: partId
        });
      } else {
        logOperation('ERROR', 'processPartsRecursively_invalid_partId', { partId });
      }
    }

    // Create processed part with fixed partId and ensured quantity
    const formattedPart = {
      ...part,
      partId, // Use fixed partId
      quantity: part.quantity || 1 // Ensure quantity exists with fallback
    };

    // Process children recursively if present
    if (formattedPart.children && Array.isArray(formattedPart.children)) {
      formattedPart.children = processPartsRecursively(formattedPart.children)
        .filter(Boolean); // Remove any null entries from invalid parts
    }

    return formattedPart;
  }).filter(Boolean); // Remove any null entries from invalid parts
}

/**
 * Deletes an assembly by its _id or assemblyCode.
 * @param id - The unique identifier (can be either assemblyCode or _id) of the assembly to delete.
 * @returns An object indicating success or failure.
 */
export async function deleteAssembly(id: string) {
  logOperation('DELETE', 'assembly', { id });
  try {
    await connectToMongoose();

    // Determine the query based on id format
    let query = {};
    try {
      // Check if the id is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(id)) {
        query = { _id: id };
        logOperation('DELETE_ATTEMPT', 'assembly', { _id: id });
      } else {
        // If not a valid ObjectId, use as assemblyCode
        query = { assemblyCode: id };
        logOperation('DELETE_ATTEMPT', 'assembly', { assemblyCode: id });
      }
    } catch (error) {
      // If any error in parsing, default to using as assemblyCode
      query = { assemblyCode: id };
      logOperation('DELETE_ATTEMPT', 'assembly', { assemblyCode: id });
    }

    // First, verify the assembly exists - Remove .lean() for better type inference
    const existingAssembly = await Assembly.findOne(query).select('_id assemblyCode'); // Removed .lean()

    if (!existingAssembly) {
      logOperation('DELETE_NOT_FOUND', 'assembly', { id });
      throw new Error(`Assembly with identifier ${id} not found`);
    }

    logOperation('DELETE_FOUND', 'assembly', {
      id,
      _id: existingAssembly?._id, // Use optional chaining
      assemblyCode: existingAssembly?.assemblyCode // Use optional chaining
    });

    // Find by query (either _id or assemblyCode) and delete
    const result = await Assembly.findOneAndDelete(query);

    if (!result) {
      logOperation('DELETE_FAILED', 'assembly', { id });
      throw new Error(`Assembly with identifier ${id} could not be deleted`);
    }

    // Verify deletion was successful - Use optional chaining for existingAssembly._id - Remove .lean()
    const verifyDeletion = await Assembly.findOne({ _id: existingAssembly?._id }); // Removed .lean()
    if (verifyDeletion) {
      logOperation('DELETE_VERIFICATION_FAILED', 'assembly', {
        id,
        _id: existingAssembly?._id // Use optional chaining
      });
      throw new Error(`Assembly deletion verification failed - assembly still exists in database`);
    }

    logOperation('DELETE_SUCCESS', 'assembly', {
      id,
      _id: existingAssembly._id,
      assemblyCode: existingAssembly.assemblyCode
    });

    return {
      success: true,
      message: `Assembly ${id} deleted successfully`,
      _id: existingAssembly?._id, // Use optional chaining
      assemblyCode: existingAssembly?.assemblyCode // Use optional chaining
    };
  } catch (error: any) {
    logOperation('ERROR', 'delete_assembly', { id, error: error.message, stack: error.stack });
    handleMongoDBError(error); // Throws standardized error
    throw error; // Re-throw for API layer
  }
}

// --- Work Order Management Functions ---

/**
 * Fetches a list of work orders with pagination, sorting, and filtering.
 * @param options - Options for fetching work orders.
 * @returns An object containing the list of work orders and pagination info.
 */
export async function fetchWorkOrders(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { createdAt: -1 }, // Default sort by creation date descending
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'workorders', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch work orders with pagination, sorting, and populate related data
    const workOrders = await WorkOrder.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate({
        path: 'assignedTo',
        model: 'User',
        select: 'username fullName email'
      })
      .populate({
        path: 'assemblyId',
        model: 'Assembly',
        select: 'assemblyCode name'
      })
      .populate({
        path: 'productId',
        model: 'Product',
        select: 'productCode name'
      })
      // Select fields based on WorkOrder schema
      .select('woNumber assemblyId partIdToManufacture productId quantity status priority assignedTo completedAt createdAt updatedAt')
      .lean();

    // Get total count for pagination
    const totalCount = await WorkOrder.countDocuments(filter);

    logOperation('SUCCESS', 'workorders', { count: workOrders.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });

    return {
      workOrders,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'workorders', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return { workOrders: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}

/**
 * Gets a specific work order by its woNumber.
 * @param woNumber - The unique work order number.
 * @returns The work order document or null if not found.
 */
export async function getWorkOrder(woNumber: string) {
  logOperation('GET', 'workorder', { woNumber });
  try {
    await connectToMongoose();

    // Find work order by woNumber and populate related data
    const workOrder = await WorkOrder.findOne({ woNumber })
      .populate({
        path: 'assignedTo',
        model: 'User',
        select: 'username fullName email'
      })
      .populate({
        path: 'assemblyId',
        model: 'Assembly',
        select: 'assemblyCode name'
      })
      .populate({
        path: 'productId',
        model: 'Product',
        select: 'productCode name'
      })
      // Select fields based on WorkOrder schema
      .select('woNumber assemblyId partIdToManufacture productId quantity status priority assignedTo completedAt createdAt updatedAt')
      .lean();

    if (!workOrder) {
      logOperation('NOT_FOUND', 'workorder', { woNumber });
      return null;
    }

    logOperation('SUCCESS', 'get_workorder', { woNumber });
    return workOrder;
  } catch (error: any) {
    logOperation('ERROR', 'get_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // This function's success response is a single work order or null. No pagination.
    throw new Error(handleMongoDBError(error).message || `Failed to get work order ${woNumber}`);
    // Or: return { success: false, message: handleMongoDBError(error).message, data: null };
  }
}

/**
 * Creates a new work order.
 * @param workOrderData - The data for the new work order.
 * @returns The newly created work order document.
 */
export async function createWorkOrder(workOrderData: any) {
  logOperation('CREATE', 'workorder', { woNumber: workOrderData.woNumber });
  try {
    await connectToMongoose();

    // Generate a work order number if not provided
    const woNumber = workOrderData.woNumber || `WO-${format(new Date(), 'yyyyMMdd')}-${uuidv4().substring(0, 4).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'workorder', { woNumber });

    // Create work order based on the schema
    const newWorkOrder = new WorkOrder({
      woNumber,
      assemblyId: workOrderData.assemblyId || null,
      partIdToManufacture: workOrderData.partIdToManufacture || null,
      productId: workOrderData.productId || null,
      quantity: workOrderData.quantity,
      status: workOrderData.status || 'pending',
      priority: workOrderData.priority || 'medium',
      assignedTo: workOrderData.assignedTo,
      completedAt: workOrderData.completedAt || null,
      // createdAt/updatedAt handled by timestamps
    });

    // Validate that at least one of assemblyId, partIdToManufacture, or productId is provided
    if (!newWorkOrder.assemblyId && !newWorkOrder.partIdToManufacture && !newWorkOrder.productId) {
      throw new Error('At least one of assemblyId, partIdToManufacture, or productId must be provided');
    }

    const savedWorkOrder = await newWorkOrder.save();
    logOperation('SUCCESS', 'workorder_creation', { _id: savedWorkOrder._id, woNumber: savedWorkOrder.woNumber });
    return savedWorkOrder;
  } catch (error: any) {
    logOperation('ERROR', 'workorder_creation', { woNumber: workOrderData.woNumber, error: error.message });
    handleMongoDBError(error);
    // No pagination.
    throw new Error(handleMongoDBError(error).message || 'Failed to create work order.');
    // Or: return { success: false, message: handleMongoDBError(error).message, data: null };
  }
}

/**
 * Updates an existing work order by its woNumber.
 * @param woNumber - The unique work order number.
 * @param workOrderData - The data to update.
 * @returns The updated work order document.
 */
export async function updateWorkOrder(woNumber: string, workOrderData: any) {
  logOperation('UPDATE', 'workorder', { woNumber });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...workOrderData };
    delete updatePayload._id;
    delete updatePayload.woNumber; // Don't update the work order number
    delete updatePayload.createdAt;
    // updatedAt is handled by timestamps

    // If status is being updated to 'completed', set completedAt to current date if not provided
    if (updatePayload.status === 'completed' && !updatePayload.completedAt) {
      updatePayload.completedAt = new Date();
    }

    // If status is being updated to something other than 'completed', clear completedAt
    if (updatePayload.status && updatePayload.status !== 'completed') {
      updatePayload.completedAt = null;
    }

    const workOrder = await WorkOrder.findOneAndUpdate(
      { woNumber },
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!workOrder) {
      logOperation('UPDATE_NOT_FOUND', 'workorder', { woNumber });
      throw new Error(`Work order with number ${woNumber} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'workorder', { woNumber });
    return workOrder;
  } catch (error: any) {
    logOperation('ERROR', 'update_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // No pagination.
    throw new Error(handleMongoDBError(error).message || `Failed to update work order ${woNumber}`);
    // Or: return { success: false, message: handleMongoDBError(error).message, data: null };
  }
}

/**
 * Deletes a work order by its woNumber.
 * @param woNumber - The unique work order number.
 * @returns An object indicating success or failure.
 */
export async function deleteWorkOrder(woNumber: string) {
  logOperation('DELETE', 'workorder', { woNumber });
  try {
    await connectToMongoose();

    // Delete the work order
    const result = await WorkOrder.findOneAndDelete({ woNumber });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'workorder', { woNumber });
      throw new Error(`Work order with number ${woNumber} not found`);
    }

    logOperation('DELETE_SUCCESS', 'workorder', { woNumber });
    return { success: true, message: `Work order ${woNumber} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // No pagination.
    // throw new Error(handleMongoDBError(error).message || `Failed to delete work order ${woNumber}`);
    return { success: false, message: handleMongoDBError(error).message || `Failed to delete work order ${woNumber}` };
  }
}

// Warehouse Management Functions

/**
 * Fetches warehouses with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Warehouses array and pagination info.
 */
export async function fetchWarehouses(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    includeParts = false, // New option to include parts
    partsLimit = 10 // Limit for parts per category, adjust as needed
  } = options;

  logOperation('FETCH_ALL', 'warehouses', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch warehouses with pagination and select fields based on schema
    const warehouses = await Warehouse.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('location_id name location capacity manager contact createdAt updatedAt')
      .lean();

    const totalCount = await Warehouse.countDocuments(filter);

    logOperation('SUCCESS', 'warehouses', {
      count: warehouses.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      warehouses,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'warehouses', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      warehouses: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single warehouse by its location_id.
 * @param locationId - The unique location_id of the warehouse.
 * @returns The warehouse or null if not found.
 */
export async function getWarehouse(locationId: string) {
  logOperation('GET', 'warehouse', { location_id: locationId });
  try {
    await connectToMongoose();

    // Find warehouse by its location_id
    const warehouse = await Warehouse.findOne({ location_id: locationId })
      .select('location_id name location capacity manager contact createdAt updatedAt')
      .lean();

    if (!warehouse) {
      logOperation('NOT_FOUND', 'warehouse', { location_id: locationId });
      return null;
    }

    logOperation('SUCCESS', 'get_warehouse', { location_id: locationId });
    return warehouse;
  } catch (error: any) {
    logOperation('ERROR', 'get_warehouse', { location_id: locationId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new warehouse.
 * @param warehouseData - The data for the new warehouse.
 * @returns The newly created warehouse.
 */
export async function addWarehouse(warehouseData: any) {
  logOperation('ADD', 'warehouse', { name: warehouseData.name });
  try {
    await connectToMongoose();

    // Ensure location_id is provided or generate one
    const locationId = warehouseData.location_id || `WH-${uuidv4().substring(0, 8).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'warehouse', { location_id: locationId });

    // Create warehouse based on the schema
    const newWarehouse = new Warehouse({
      location_id: locationId,
      name: warehouseData.name,
      location: warehouseData.location,
      capacity: warehouseData.capacity,
      manager: warehouseData.manager,
      contact: warehouseData.contact,
      schemaVersion: warehouseData.schemaVersion || 1
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedWarehouse = await newWarehouse.save();
    logOperation('SUCCESS', 'warehouse_creation', {
      _id: savedWarehouse._id,
      location_id: savedWarehouse.location_id
    });

    return savedWarehouse;
  } catch (error: any) {
    logOperation('ERROR', 'warehouse_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing warehouse by its location_id.
 * @param locationId - The unique location_id of the warehouse to update.
 * @param warehouseData - The data to update.
 * @returns The updated warehouse.
 */
export async function updateWarehouse(locationId: string, warehouseData: any) {
  logOperation('UPDATE', 'warehouse', { location_id: locationId });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...warehouseData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.location_id; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const warehouse = await Warehouse.findOneAndUpdate(
      { location_id: locationId }, // Find by the unique location_id
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!warehouse) {
      logOperation('UPDATE_NOT_FOUND', 'warehouse', { location_id: locationId });
      throw new Error(`Warehouse with location ID ${locationId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'warehouse', { location_id: locationId });
    return warehouse;
  } catch (error: any) {
    logOperation('ERROR', 'warehouse', { location_id: locationId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a warehouse by its location_id.
 * @param locationId - The unique location_id of the warehouse to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteWarehouse(locationId: string) {
  logOperation('DELETE', 'warehouse', { location_id: locationId });
  try {
    await connectToMongoose();

    // Delete the warehouse by its location_id
    const result = await Warehouse.findOneAndDelete({ location_id: locationId });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'warehouse', { location_id: locationId });
      throw new Error(`Warehouse with location ID ${locationId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'warehouse', { location_id: locationId });
    return { success: true, message: `Warehouse ${locationId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'warehouse', { location_id: locationId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// Category Management Functions

/**
 * Fetches categories with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Categories array and pagination info.
 */
export async function fetchCategories(options: any = {}) { // Added export
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    includeParts = false, // New option to include parts
    partsLimit = 10 // Limit for parts per category, adjust as needed
  } = options;

  logOperation('FETCH_ALL', 'categories', { // Added includeParts, partsLimit
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch categories with pagination
    let categories;

    if (includeParts) {
      // Use aggregation with $lookup to avoid N+1 queries
      const sortStage: any = {}; // Typed sortStage as any
      Object.entries(sort).forEach(([key, value]) => {
        (sortStage as any)[key] = value; // Use type assertion for indexing
      });

      // Create the aggregation pipeline
      const pipeline = [
        { $match: filter },
        { $sort: sortStage },
        { $skip: skip },
        { $limit: limit },
        // Lookup to get parent category information
        {
          $lookup: {
            from: 'categories', // The collection to join with
            localField: 'parentCategory', // Field from the categories collection
            foreignField: '_id', // Field from the parentCategory collection
            as: 'parentCategoryData' // Output array field
          }
        },
        // Lookup to get parts for each category
        {
          $lookup: {
            from: 'parts', // The collection to join with
            let: { categoryId: '$_id' }, // Variable to use in the pipeline
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$category', '$$categoryId'] }
                }
              },
              { $limit: partsLimit },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  description: 1
                }
              }
            ],
            as: 'parts' // Output array field
          }
        },
        // Unwind and regroup parentCategory to get single object instead of array
        {
          $addFields: {
            parentCategory: {
              $cond: {
                if: { $gt: [{ $size: '$parentCategoryData' }, 0] },
                then: {
                  _id: { $arrayElemAt: ['$parentCategoryData._id', 0] },
                  name: { $arrayElemAt: ['$parentCategoryData.name', 0] }
                },
                else: null
              }
            }
          }
        },
        // Remove the parentCategoryData array
        { $project: { parentCategoryData: 0 } }
      ];

      logOperation('AGGREGATE', 'categories', {
        pipeline: JSON.stringify(pipeline.map(stage => Object.keys(stage)[0]))
      });

      categories = await Category.aggregate(pipeline);
    } else {
      // If parts are not needed, use regular find query with populate
      categories = await Category.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .select('_id name description parentCategory createdAt updatedAt')
        .populate({
          path: 'parentCategory',
          model: 'Category',
          select: '_id name' // Only select essential fields from parent
        })
        .lean();
    }

    const totalCount = await Category.countDocuments(filter);

    logOperation('SUCCESS', 'categories', {
      count: categories.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      categories,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'categories', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      categories: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single category by its ID.
 * @param categoryId - The unique ID of the category.
 * @returns The category or null if not found.
 */
export async function getCategory(categoryId: string) {
  logOperation('GET', 'category', { _id: categoryId });
  try {
    await connectToMongoose();

    // Find category by its ID
    const category = await Category.findById(categoryId)
      .select('_id name description parentCategory createdAt updatedAt')
      .populate({
        path: 'parentCategory',
        model: 'Category',
        select: '_id name' // Only select essential fields from parent
      })
      .lean();

    if (!category) {
      logOperation('NOT_FOUND', 'category', { _id: categoryId });
      return null;
    }

    logOperation('SUCCESS', 'get_category', { _id: categoryId });
    return category;
  } catch (error: any) {
    logOperation('ERROR', 'get_category', { _id: categoryId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new category.
 * @param categoryData - The data for the new category.
 * @returns The newly created category.
 */
export async function addCategory(categoryData: any) {
  logOperation('ADD', 'category', { name: categoryData.name });
  try {
    await connectToMongoose();

    // Create category based on the schema
    const newCategory = new Category({
      name: categoryData.name,
      description: categoryData.description,
      parentCategory: categoryData.parentCategory
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedCategory = await newCategory.save();
    logOperation('SUCCESS', 'category_creation', {
      _id: savedCategory._id,
      name: savedCategory.name
    });

    return savedCategory;
  } catch (error: any) {
    logOperation('ERROR', 'category_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing category by its ID.
 * @param categoryId - The unique ID of the category to update.
 * @param categoryData - The data to update.
 * @returns The updated category.
 */
export async function updateCategory(categoryId: string, categoryData: any) {
  logOperation('UPDATE', 'category', { _id: categoryId });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...categoryData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const category = await Category.findByIdAndUpdate(
      categoryId,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!category) {
      logOperation('UPDATE_NOT_FOUND', 'category', { _id: categoryId });
      throw new Error(`Category with ID ${categoryId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'category', { _id: categoryId });
    return category;
  } catch (error: any) {
    logOperation('ERROR', 'category', { _id: categoryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a category by its ID.
 * @param categoryId - The unique ID of the category to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteCategory(categoryId: string) {
  logOperation('DELETE', 'category', { _id: categoryId });
  try {
    await connectToMongoose();

    // Check if this category is a parent for any other categories
    const childCategories = await Category.findOne({ parentCategory: categoryId });
    if (childCategories) {
      logOperation('DELETE_FAILED', 'category', { _id: categoryId, reason: 'has_children' });
      throw new Error(`Cannot delete category with ID ${categoryId} because it has child categories`);
    }

    // Check if this category is used by any products
    const products = await Product.findOne({ categoryId: categoryId });
    if (products) {
      logOperation('DELETE_FAILED', 'category', { _id: categoryId, reason: 'used_by_products' });
      throw new Error(`Cannot delete category with ID ${categoryId} because it is used by products`);
    }

    // Delete the category
    const result = await Category.findByIdAndDelete(categoryId);

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'category', { _id: categoryId });
      throw new Error(`Category with ID ${categoryId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'category', { _id: categoryId });
    return { success: true, message: `Category ${categoryId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'category', { _id: categoryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// Delivery Management Functions

/**
 * Fetches deliveries with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Deliveries array and pagination info.
 */
export async function fetchDeliveries(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { scheduledDate: -1 }, // Default sort by scheduled date (newest first)
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'deliveries', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch deliveries with pagination
    const deliveries = await Delivery.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id deliveryId referenceType referenceId supplierId status scheduledDate actualDate trackingNumber notes receivedBy createdAt updatedAt')
      .populate([
        {
          path: 'supplierId',
          model: 'Supplier',
          select: 'supplier_id name contactPerson' // Only select essential fields
        },
        {
          path: 'receivedBy',
          model: 'User',
          select: 'username fullName' // Only select essential fields
        }
      ])
      .lean();

    const totalCount = await Delivery.countDocuments(filter);

    logOperation('SUCCESS', 'deliveries', {
      count: deliveries.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      deliveries,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'deliveries', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      deliveries: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single delivery by its deliveryId.
 * @param deliveryId - The unique deliveryId of the delivery.
 * @returns The delivery or null if not found.
 */
export async function getDelivery(deliveryId: string) {
  logOperation('GET', 'delivery', { deliveryId });
  try {
    await connectToMongoose();

    // Find delivery by its deliveryId
    const delivery = await Delivery.findOne({ deliveryId })
      .select('_id deliveryId referenceType referenceId supplierId status scheduledDate actualDate trackingNumber notes receivedBy createdAt updatedAt')
      .populate([
        {
          path: 'supplierId',
          model: 'Supplier',
          select: 'supplier_id name contactPerson' // Only select essential fields
        },
        {
          path: 'receivedBy',
          model: 'User',
          select: 'username fullName' // Only select essential fields
        }
      ])
      .lean();

    if (!delivery) {
      logOperation('NOT_FOUND', 'delivery', { deliveryId });
      return null;
    }

    logOperation('SUCCESS', 'get_delivery', { deliveryId });
    return delivery;
  } catch (error: any) {
    logOperation('ERROR', 'get_delivery', { deliveryId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new delivery.
 * @param deliveryData - The data for the new delivery.
 * @returns The newly created delivery.
 */
export async function addDelivery(deliveryData: any) {
  logOperation('ADD', 'delivery', { deliveryId: deliveryData.deliveryId });
  try {
    await connectToMongoose();

    // Ensure deliveryId is provided or generate one
    const deliveryId = deliveryData.deliveryId || `DEL-${uuidv4().substring(0, 8).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'delivery', { deliveryId });

    // Create delivery based on the schema
    const newDelivery = new Delivery({
      deliveryId,
      referenceType: deliveryData.referenceType,
      referenceId: deliveryData.referenceId,
      supplierId: deliveryData.supplierId,
      status: deliveryData.status,
      scheduledDate: deliveryData.scheduledDate,
      actualDate: deliveryData.actualDate,
      trackingNumber: deliveryData.trackingNumber,
      notes: deliveryData.notes,
      receivedBy: deliveryData.receivedBy
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedDelivery = await newDelivery.save();
    logOperation('SUCCESS', 'delivery_creation', {
      _id: savedDelivery._id,
      deliveryId: savedDelivery.deliveryId
    });

    return savedDelivery;
  } catch (error: any) {
    logOperation('ERROR', 'delivery_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing delivery by its deliveryId.
 * @param deliveryId - The unique deliveryId of the delivery to update.
 * @param deliveryData - The data to update.
 * @returns The updated delivery.
 */
export async function updateDelivery(deliveryId: string, deliveryData: any) {
  logOperation('UPDATE', 'delivery', { deliveryId });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...deliveryData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.deliveryId; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const delivery = await Delivery.findOneAndUpdate(
      { deliveryId }, // Find by the unique deliveryId
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!delivery) {
      logOperation('UPDATE_NOT_FOUND', 'delivery', { deliveryId });
      throw new Error(`Delivery with ID ${deliveryId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'delivery', { deliveryId });
    return delivery;
  } catch (error: any) {
    logOperation('ERROR', 'delivery', { deliveryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a delivery by its deliveryId.
 * @param deliveryId - The unique deliveryId of the delivery to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteDelivery(deliveryId: string) {
  logOperation('DELETE', 'delivery', { deliveryId });
  try {
    await connectToMongoose();

    // Delete the delivery by its deliveryId
    const result = await Delivery.findOneAndDelete({ deliveryId });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'delivery', { deliveryId });
      throw new Error(`Delivery with ID ${deliveryId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'delivery', { deliveryId });
    return { success: true, message: `Delivery ${deliveryId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'delivery', { deliveryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// User Management Functions

/**
 * Fetches users with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Users array and pagination info.
 */
export async function fetchUsers(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { username: 1 }, // Default sort by username
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'users', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch users with pagination
    const users = await User.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id username email fullName role isActive createdAt updatedAt')
      .lean();

    const totalCount = await User.countDocuments(filter);

    logOperation('SUCCESS', 'users', {
      count: users.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      users,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'users', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      users: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single user by its username.
 * @param username - The unique username of the user.
 * @returns The user or null if not found.
 */
export async function getUser(username: string) {
  logOperation('GET', 'user', { username });
  try {
    await connectToMongoose();

    // Find user by its username
    const user = await User.findOne({ username })
      .select('_id username email fullName role isActive createdAt updatedAt')
      .lean();

    if (!user) {
      logOperation('NOT_FOUND', 'user', { username });
      return null;
    }

    logOperation('SUCCESS', 'get_user', { username });
    return user;
  } catch (error: any) {
    logOperation('ERROR', 'get_user', { username, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new user.
 * @param userData - The data for the new user.
 * @returns The newly created user.
 */
export async function addUser(userData: any) {
  logOperation('ADD', 'user', { username: userData.username });
  try {
    await connectToMongoose();

    // Create user based on the schema
    const newUser = new User({
      username: userData.username,
      email: userData.email,
      fullName: userData.fullName,
      role: userData.role || 'worker', // Default role
      passwordHash: userData.passwordHash, // Should be pre-hashed
      isActive: userData.isActive !== undefined ? userData.isActive : true,
      schemaVersion: userData.schemaVersion || 1
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedUser = await newUser.save();
    logOperation('SUCCESS', 'user_creation', {
      _id: savedUser._id,
      username: savedUser.username
    });

    return savedUser;
  } catch (error: any) {
    logOperation('ERROR', 'user_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing user by its username.
 * @param username - The unique username of the user to update.
 * @param userData - The data to update.
 * @returns The updated user.
 */
export async function updateUser(username: string, userData: any) {
  logOperation('UPDATE', 'user', { username });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...userData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.username; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const user = await User.findOneAndUpdate(
      { username }, // Find by the unique username
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!user) {
      logOperation('UPDATE_NOT_FOUND', 'user', { username });
      throw new Error(`User with username ${username} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'user', { username });
    return user;
  } catch (error: any) {
    logOperation('ERROR', 'user', { username, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a user by its username.
 * @param username - The unique username of the user to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteUser(username: string) {
  logOperation('DELETE', 'user', { username });
  try {
    await connectToMongoose();

    // Check if this user is referenced by other collections
    // For example, check if the user is assigned to any work orders
    const workOrders = await WorkOrder.findOne({ assignedTo: { $exists: true } });
    if (workOrders) {
      logOperation('DELETE_FAILED', 'user', { username, reason: 'referenced_by_work_orders' });
      throw new Error(`Cannot delete user with username ${username} because it is referenced by work orders`);
    }

    // Delete the user by its username
    const result = await User.findOneAndDelete({ username });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'user', { username });
      throw new Error(`User with username ${username} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'user', { username });
    return { success: true, message: `User ${username} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'user', { username, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// Batch Management Functions

/**
 * Fetches batches with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Batches array and pagination info.
 */
export async function fetchBatches(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { createdAt: -1 }, // Default sort by creation date (newest first)
    filter = {},
    includeParts = false  // Add the missing parameter with default value
  } = options;

  logOperation('FETCH_ALL', 'batches', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Prepare population options
    const populateOptions = [
      {
        path: 'partId',
        model: 'Part',
        select: '_id name description' // Only select essential fields
      },
      {
        path: 'assemblyId',
        model: 'Assembly',
        select: '_id assemblyCode name' // Only select essential fields
      },
      {
        path: 'workOrderId',
        model: 'WorkOrder',
        select: '_id woNumber status' // Only select essential fields
      }
    ];

    // Fetch batches with pagination
    const batches = await Batch.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced startDate endDate status notes workOrderId createdAt updatedAt')
      .populate(populateOptions)
      .lean();

    const totalCount = await Batch.countDocuments(filter);

    logOperation('SUCCESS', 'batches', {
      count: batches.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      batches,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'batches', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      batches: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single batch by its ID.
 * @param batchId - The unique ID of the batch.
 * @returns The batch or null if not found.
 */
export async function getBatch(batchId: string) {
  logOperation('GET', 'batch', { _id: batchId });
  try {
    await connectToMongoose();

    // Find batch by its ID
    const batch = await Batch.findById(batchId)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced startDate endDate status notes workOrderId createdAt updatedAt')
      .populate([
        {
          path: 'partId',
          model: 'Part',
          select: '_id name description' // Only select essential fields
        },
        {
          path: 'assemblyId',
          model: 'Assembly',
          select: '_id assemblyCode name' // Only select essential fields
        },
        {
          path: 'workOrderId',
          model: 'WorkOrder',
          select: '_id woNumber status' // Only select essential fields
        }
      ])
      .lean();

    if (!batch) {
      logOperation('NOT_FOUND', 'batch', { _id: batchId });
      return null;
    }

    logOperation('SUCCESS', 'get_batch', { _id: batchId });
    return batch;
  } catch (error: any) {
    logOperation('ERROR', 'get_batch', { _id: batchId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new batch.
 * @param batchData - The data for the new batch.
 * @returns The newly created batch.
 */
export async function addBatch(batchData: any) {
  logOperation('ADD', 'batch', { batchCode: batchData.batchCode });
  try {
    await connectToMongoose();

    // Generate a batch code if not provided
    const batchCode = batchData.batchCode || `BATCH-${uuidv4().substring(0, 8).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'batch', { batchCode });

    // Create batch based on the schema
    const newBatch = new Batch({
      batchCode,
      partId: batchData.partId,
      assemblyId: batchData.assemblyId,
      quantityPlanned: batchData.quantityPlanned,
      quantityProduced: batchData.quantityProduced,
      startDate: batchData.startDate || new Date(),
      endDate: batchData.endDate,
      status: batchData.status || 'pending',
      notes: batchData.notes,
      workOrderId: batchData.workOrderId
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedBatch = await newBatch.save();
    logOperation('SUCCESS', 'batch_creation', {
      _id: savedBatch._id,
      batchCode: savedBatch.batchCode
    });

    return savedBatch;
  } catch (error: any) {
    logOperation('ERROR', 'batch_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing batch by its ID.
 * @param batchId - The unique ID of the batch to update.
 * @param batchData - The data to update.
 * @returns The updated batch.
 */
export async function updateBatch(batchId: string, batchData: any) {
  logOperation('UPDATE', 'batch', { _id: batchId });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...batchData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.batchCode; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const batch = await Batch.findByIdAndUpdate(
      batchId,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!batch) {
      logOperation('UPDATE_NOT_FOUND', 'batch', { _id: batchId });
      throw new Error(`Batch with ID ${batchId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'batch', { _id: batchId });
    return batch;
  } catch (error: any) {
    logOperation('ERROR', 'batch', { _id: batchId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a batch by its ID.
 * @param batchId - The unique ID of the batch to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteBatch(batchId: string) {
  logOperation('DELETE', 'batch', { _id: batchId });
  try {
    await connectToMongoose();

    // Delete the batch by its ID
    const result = await Batch.findByIdAndDelete(batchId);

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'batch', { _id: batchId });
      throw new Error(`Batch with ID ${batchId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'batch', { _id: batchId });
    return { success: true, message: `Batch ${batchId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'batch', { _id: batchId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Fetches batches associated with a specific work order.
 * @param woNumber - The unique work order number.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Batches array and pagination info.
 */
export async function getBatchesByWorkOrderId(woNumber: string, options: any = {}) {
  const {
    page = 1,
    limit = 10,
    sort = { createdAt: -1 }, // Default sort by creation date (newest first)
    additionalFilter = {}
  } = options;

  logOperation('GET_BATCHES_BY_WORKORDER', 'batches', { woNumber, page, limit, sort: JSON.stringify(sort) });

  try {
    await connectToMongoose();

    // First, find the work order by woNumber to get its _id
    const workOrder = await WorkOrder.findOne({ woNumber }).select('_id').lean();

    if (!workOrder) {
      logOperation('NOT_FOUND', 'workorder', { woNumber });
      return {
        batches: [],
        pagination: {
          totalCount: 0,
          totalPages: 0,
          currentPage: page,
          limit
        }
      };
    }

    const workOrderId = workOrder._id;
    const skip = (page - 1) * limit;

    // Combine the workOrderId filter with any additional filters
    const filter = {
      workOrderId,
      ...additionalFilter
    };

    // Fetch batches with pagination
    const batches = await Batch.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced startDate endDate status notes workOrderId createdAt updatedAt')
      .populate([
        {
          path: 'partId',
          model: 'Part',
          select: '_id name description' // Only select essential fields
        },
        {
          path: 'assemblyId',
          model: 'Assembly',
          select: '_id assemblyCode name' // Only select essential fields
        },
        {
          path: 'workOrderId',
          model: 'WorkOrder',
          select: '_id woNumber status' // Only select essential fields
        }
      ])
      .lean();

    // Get total count for pagination
    const totalCount = await Batch.countDocuments(filter);

    logOperation('SUCCESS', 'get_batches_by_workorder', {
      woNumber,
      workOrderId,
      count: batches.length,
      totalCount
    });

    return {
      batches,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'get_batches_by_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      batches: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

// Settings Management Functions

/**
 * Fetches settings with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Settings array and pagination info.
 */
export async function fetchSettings(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { key: 1 }, // Default sort by key
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'settings', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch settings with pagination
    const settings = await Setting.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id key value description dataType group lastModifiedBy lastModifiedAt createdAt updatedAt')
      .populate({
        path: 'lastModifiedBy',
        model: 'User',
        select: 'username fullName' // Only select essential fields
      })
      .lean();

    const totalCount = await Setting.countDocuments(filter);

    logOperation('SUCCESS', 'settings', {
      count: settings.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      settings,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'settings', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      settings: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single setting by its key.
 * @param key - The unique key of the setting.
 * @returns The setting or null if not found.
 */
export async function getSetting(key: string) {
  logOperation('GET', 'setting', { key });
  try {
    await connectToMongoose();

    // Find setting by its key
    const setting = await Setting.findOne({ key })
      .select('_id key value description dataType group lastModifiedBy lastModifiedAt createdAt updatedAt')
      .populate({
        path: 'lastModifiedBy',
        model: 'User',
        select: 'username fullName' // Only select essential fields
      })
      .lean();

    if (!setting) {
      logOperation('NOT_FOUND', 'setting', { key });
      return null;
    }

    logOperation('SUCCESS', 'get_setting', { key });
    return setting;
  } catch (error: any) {
    logOperation('ERROR', 'get_setting', { key, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new setting.
 * @param settingData - The data for the new setting.
 * @returns The newly created setting.
 */
export async function addSetting(settingData: any) {
  logOperation('ADD', 'setting', { key: settingData.key });
  try {
    await connectToMongoose();

    // Create setting based on the schema
    const newSetting = new Setting({
      key: settingData.key,
      value: settingData.value,
      description: settingData.description,
      dataType: settingData.dataType,
      group: settingData.group,
      lastModifiedBy: settingData.lastModifiedBy,
      lastModifiedAt: new Date()
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedSetting = await newSetting.save();
    logOperation('SUCCESS', 'setting_creation', {
      _id: savedSetting._id,
      key: savedSetting.key
    });

    return savedSetting;
  } catch (error: any) {
    logOperation('ERROR', 'setting_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing setting by its key.
 * @param key - The unique key of the setting to update.
 * @param settingData - The data to update.
 * @returns The updated setting.
 */
export async function updateSetting(key: string, settingData: any) {
  logOperation('UPDATE', 'setting', { key });
  try {
    await connectToMongoose();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...settingData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.key; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    // Always update lastModifiedAt
    updatePayload.lastModifiedAt = new Date();

    const setting = await Setting.findOneAndUpdate(
      { key }, // Find by the unique key
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!setting) {
      logOperation('UPDATE_NOT_FOUND', 'setting', { key });
      throw new Error(`Setting with key ${key} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'setting', { key });
    return setting;
  } catch (error: any) {
    logOperation('ERROR', 'setting', { key, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a setting by its key.
 * @param key - The unique key of the setting to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteSetting(key: string) {
  logOperation('DELETE', 'setting', { key });
  try {
    await connectToMongoose();

    // Delete the setting by its key
    const result = await Setting.findOneAndDelete({ key });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'setting', { key });
      throw new Error(`Setting with key ${key} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'setting', { key });
    return { success: true, message: `Setting ${key} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'setting', { key, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}
