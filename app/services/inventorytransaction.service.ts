import mongoose, { Types } from 'mongoose';
import { InventoryTransaction, IInventoryTransaction } from '../models/inventorytransaction.model';
import { Inventory } from '../models/inventory.model';
import * as inventoryService from './inventory.service';
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[InventoryTransactionService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[InventoryTransactionService Error]', error);

  // Set Sentry tags for better filtering
  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'inventorytransaction');

  // Determine error type and set appropriate tags
  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  // Check for specific MongoDB error types
  if (error.name === 'ValidationError') {
    // Handle validation errors
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;

    // Set Sentry tags for validation errors
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    // Handle duplicate key errors
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;

    // Set Sentry tags for duplicate key errors
    setTag('error.subtype', 'duplicate_key');
  } else {
    // Handle other errors
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;

    // Set Sentry tags for general database errors
    setTag('error.subtype', 'general');
  }

  // Capture the exception in Sentry
  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  // Return standardized error response
  return { message: errorMessage, status: errorStatus };
};

/**
 * Fetch inventory transactions with pagination, sorting, and filtering options
 * @param options Pagination, sorting, and filtering options
 * @returns Transactions array and pagination information
 */
export async function fetchTransactions(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { transactionDate: -1 },
    filter = {}
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Build the query
    const transactions = await InventoryTransaction.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('partId', 'partNumber name') // Populate related part basic info
      .populate('warehouseId', 'warehouseCode name') // Populate related warehouse basic info
      .populate('userId', 'username') // Populate user who performed the transaction
      .lean();

    // Get total count for pagination
    const totalCount = await InventoryTransaction.countDocuments(filter);

    logOperation('SUCCESS', {
      count: transactions.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch transactions');
  }
}

/**
 * Get a transaction by its ID
 * @param transactionId The ObjectId of the transaction
 * @returns The transaction document
 */
export async function getTransaction(transactionId: string) {
  logOperation('GET_BY_ID', { transactionId });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(transactionId)) {
      throw new Error('Invalid transaction ID format');
    }

    // Find the transaction by ID
    const transaction = await InventoryTransaction.findById(transactionId)
      .populate('partId', 'partNumber name') // Populate related part basic info
      .populate('warehouseId', 'warehouseCode name') // Populate related warehouse basic info
      .populate('userId', 'username') // Populate user who performed the transaction
      .lean();

    if (!transaction) {
      logOperation('NOT_FOUND', { transactionId });
      return null;
    }

    logOperation('SUCCESS', { transactionId });
    return transaction;
  } catch (error: any) {
    logOperation('ERROR', { transactionId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transaction with ID ${transactionId}`);
  }
}

/**
 * Record an inventory transaction and update stock level in one atomic operation
 * @param transactionData The transaction data
 * @returns The created transaction
 */
export async function recordTransaction(transactionData: any) {
  logOperation('RECORD', transactionData);

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate required fields
    if (!transactionData.partId || !transactionData.warehouseId || !transactionData.type || 
        transactionData.quantityChanged === undefined || !transactionData.userId) {
      throw new Error('Missing required fields for transaction');
    }

    // Validate ObjectId formats
    if (!Types.ObjectId.isValid(transactionData.partId) || 
        !Types.ObjectId.isValid(transactionData.warehouseId) || 
        !Types.ObjectId.isValid(transactionData.userId)) {
      throw new Error('Invalid ID format');
    }

    // Start a session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get current inventory to determine stockOnHandBefore
      let inventory = await Inventory.findOne(
        { 
          partId: transactionData.partId, 
          warehouseId: transactionData.warehouseId 
        },
        null,
        { session }
      );

      // If no inventory record exists, create one with 0 quantity
      if (!inventory) {
        inventory = new Inventory({
          partId: transactionData.partId,
          warehouseId: transactionData.warehouseId,
          quantity: 0,
          status: 'out_of_stock'
        });
        await inventory.save({ session });
      }

      // Set stockOnHandBefore
      const stockOnHandBefore = inventory.quantity;
      
      // Calculate stockOnHandAfter
      const stockOnHandAfter = stockOnHandBefore + transactionData.quantityChanged;
      
      // Validate stockOnHandAfter is not negative
      if (stockOnHandAfter < 0) {
        throw new Error('Cannot reduce stock below zero');
      }

      // Create the transaction record
      const newTransaction = new InventoryTransaction({
        ...transactionData,
        stockOnHandBefore,
        stockOnHandAfter,
        transactionDate: transactionData.transactionDate || new Date()
      });

      // Save the transaction
      await newTransaction.save({ session });

      // Update inventory quantity
      inventory.quantity = stockOnHandAfter;
      if (transactionData.quantityChanged > 0) {
        inventory.lastRestockedDate = new Date();
      }
      await inventory.save({ session });

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      logOperation('SUCCESS', { 
        _id: newTransaction._id, 
        partId: newTransaction.partId, 
        warehouseId: newTransaction.warehouseId,
        type: newTransaction.type,
        quantityChanged: newTransaction.quantityChanged
      });

      // Return the newly created transaction with populated fields
      const populatedTransaction = await InventoryTransaction.findById(newTransaction._id)
        .populate('partId', 'partNumber name')
        .populate('warehouseId', 'warehouseCode name')
        .populate('userId', 'username');

      return populatedTransaction?.toObject();
    } catch (error) {
      // Abort transaction on error
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error: any) {
    logOperation('ERROR', { error: error.message, data: transactionData });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to record transaction');
  }
}

/**
 * Get transactions for a specific part
 * @param partId The ObjectId of the part
 * @param options Pagination, sorting options
 * @returns Transactions array and pagination information
 */
export async function getTransactionsByPart(partId: string, options: any = {}) {
  logOperation('GET_BY_PART', { partId, options });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(partId)) {
      throw new Error('Invalid part ID format');
    }

    const {
      page = 1,
      limit = 20,
      sort = { transactionDate: -1 }
    } = options;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find transactions for the part
    const transactions = await InventoryTransaction.find({ partId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('warehouseId', 'warehouseCode name')
      .populate('userId', 'username')
      .lean();

    // Get total count for pagination
    const totalCount = await InventoryTransaction.countDocuments({ partId });

    logOperation('SUCCESS', { 
      partId, 
      count: transactions.length,
      totalCount
    });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', { partId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transactions for part ID ${partId}`);
  }
}

/**
 * Get transactions for a specific warehouse
 * @param warehouseId The ObjectId of the warehouse
 * @param options Pagination, sorting options
 * @returns Transactions array and pagination information
 */
export async function getTransactionsByWarehouse(warehouseId: string, options: any = {}) {
  logOperation('GET_BY_WAREHOUSE', { warehouseId, options });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(warehouseId)) {
      throw new Error('Invalid warehouse ID format');
    }

    const {
      page = 1,
      limit = 20,
      sort = { transactionDate: -1 }
    } = options;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find transactions for the warehouse
    const transactions = await InventoryTransaction.find({ warehouseId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('partId', 'partNumber name')
      .populate('userId', 'username')
      .lean();

    // Get total count for pagination
    const totalCount = await InventoryTransaction.countDocuments({ warehouseId });

    logOperation('SUCCESS', { 
      warehouseId, 
      count: transactions.length,
      totalCount
    });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', { warehouseId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transactions for warehouse ID ${warehouseId}`);
  }
}

/**
 * Get transactions by reference (e.g., PO, WO, SO)
 * @param referenceId The ObjectId of the reference document
 * @param referenceModel The model name of the reference
 * @returns Array of transactions related to the reference
 */
export async function getTransactionsByReference(referenceId: string, referenceModel: string) {
  logOperation('GET_BY_REFERENCE', { referenceId, referenceModel });

  try {
    // Connect using Mongoose for model operations
    await connectToMongoose();

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(referenceId)) {
      throw new Error('Invalid reference ID format');
    }

    // Find transactions for the reference
    const transactions = await InventoryTransaction.find({ 
      referenceId, 
      referenceModel 
    })
      .sort({ transactionDate: -1 })
      .populate('partId', 'partNumber name')
      .populate('warehouseId', 'warehouseCode name')
      .populate('userId', 'username')
      .lean();

    logOperation('SUCCESS', { 
      referenceId, 
      referenceModel, 
      count: transactions.length 
    });

    return transactions;
  } catch (error: any) {
    logOperation('ERROR', { referenceId, referenceModel, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get transactions for reference ID ${referenceId} of type ${referenceModel}`);
  }
} 