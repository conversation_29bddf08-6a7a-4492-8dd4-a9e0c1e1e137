import mongoose, { Types } from 'mongoose';
import { Assembly, IAssembly, IAssemblyPart } from '../models/assembly.model';
import { Part } from '../models/part.model';
import connectToMongoose from '../lib/mongodb';
import { captureException, setTag } from '../lib/sentry-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[AssemblyService][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Error handler for MongoDB errors
 */
export function handleMongoDBError(error: any) {
  console.error('MongoDB Error:', error);

  // Report to Sentry if available
  try {
    setTag('error.type', 'mongodb');
    captureException(error);
  } catch (sentryError) {
    console.error('Failed to report error to Sentry:', sentryError);
  }

  // Handle specific MongoDB errors
  if (error.name === 'MongoServerError') {
    if (error.code === 11000) { // Duplicate key error
      // Extract the field name from the error message
      const fieldName = Object.keys(error.keyPattern)[0];
      const fieldValue = error.keyValue[fieldName];
      
      return {
        type: 'duplicate',
        message: `The ${fieldName} '${fieldValue}' already exists. Please use a unique value.`,
        field: fieldName,
        value: fieldValue,
        code: 409
      };
    }
  }

  if (error.name === 'ValidationError') {
    const validationErrors = Object.keys(error.errors).map(field => ({
      field,
      message: error.errors[field].message
    }));
    
    return {
      type: 'validation',
      message: 'Validation failed',
      errors: validationErrors,
      code: 400
    };
  }

  // Default generic error
  return {
    type: 'database',
    message: error.message || 'An unknown database error occurred',
    code: 500
  };
}

// DTO interfaces for assembly operations
export interface CreateAssemblyDto {
  assemblyCode: string;
  name: string;
  description?: string;
  productId?: string;
  parentId?: string;
  isTopLevel?: boolean;
  partsRequired?: Array<{
    partId: string;
    quantityRequired: number;
    unitOfMeasure: string;
  }>;
  status: string;
  version?: number;
  manufacturingInstructions?: string;
  estimatedBuildTime?: string;
  notes?: string;
  createdBy?: string | null;
  // Legacy fields (backward compatibility)
  assembly_id?: string;
  images?: Array<{ url: string; alt_text?: string }>;
  attributes?: Array<{ name: string; value: string }>;
}

interface UpdateAssemblyDto {
  name?: string;
  description?: string;
  productId?: string;
  parentId?: string;
  isTopLevel?: boolean;
  partsRequired?: Array<{
    partId: string;
    quantityRequired: number;
    unitOfMeasure: string;
  }>;
  status?: string;
  version?: number;
  manufacturingInstructions?: string;
  estimatedBuildTime?: string;
  notes?: string;
  // Legacy fields (backward compatibility)
  images?: Array<{ url: string; alt_text?: string }>;
  attributes?: Array<{ name: string; value: string }>;
}

// Interface for the service
export interface IAssemblyService extends IAssembly {}

/**
 * Fetches assemblies with pagination, sorting, and filtering.
 */
export async function getAllAssemblies(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
  } = options;

  logOperation('FETCH_ALL', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Create the aggregation pipeline
    const pipeline = [
      { $match: filter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const assemblies = await Assembly.aggregate(pipeline);
    const totalCount = await Assembly.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', {
      count: assemblies.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      assemblies,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch assemblies');
  }
}

/**
 * Gets a single assembly by its MongoDB ObjectId.
 */
export async function getAssemblyById(id: string, includeParts: boolean = false): Promise<IAssemblyService | null> {
  logOperation('GET_BY_ID', { id, includeParts });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    let assemblyQuery;
    
    if (includeParts) {
      // Populate part details when requested
      assemblyQuery = Assembly.findById(id)
        .populate({
          path: 'partsRequired.partId',
          model: 'Part',
          select: 'name partNumber description inventory category isAssembly'
        });
    } else {
      assemblyQuery = Assembly.findById(id);
    }
    
    const assembly = await assemblyQuery.lean() as IAssemblyService | null;
    
    if (!assembly) {
      logOperation('GET_BY_ID_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', { id, includeParts });
    return assembly;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get assembly by ID ${id}`);
  }
}

/**
 * Gets a single assembly by its assemblyCode.
 */
export async function getAssemblyByAssemblyCode(assemblyCode: string, includeParts: boolean = false): Promise<IAssemblyService | null> {
  logOperation('GET_BY_ASSEMBLY_CODE', { assemblyCode, includeParts });
  console.log(`[AssemblyService] Looking up assembly by assemblyCode: "${assemblyCode}", includeParts: ${includeParts}`);
  
  try {
    await connectToMongoose();
    // console.log(`[AssemblyService] DB connected. Querying with { assemblyCode: "${assemblyCode}" }`);

    let assembly: IAssemblyService | null = null;
    let queryPathDescription = '';

    // --- Attempt 1: Find by assemblyCode --- 
    queryPathDescription = `assemblyCode: "${assemblyCode}"`;
    let assemblyQuery = Assembly.findOne({ assemblyCode: assemblyCode });
    if (includeParts) {
      console.log(`[AssemblyService] [${queryPathDescription}] Populating partsRequired.partId`);
      assemblyQuery = assemblyQuery.populate({
          path: 'partsRequired.partId',
          model: 'Part',
          select: 'name partNumber description inventory category isAssembly'
        });
    }
    assembly = await assemblyQuery.lean() as IAssemblyService | null;
    // console.log(`[AssemblyService] [${queryPathDescription}] Found assembly (pre-population or no parts):`, assembly ? assembly._id : 'null');

    // --- Attempt 2: Find by assembly_id (fallback for backward compatibility) --- 
    if (!assembly) {
      queryPathDescription = `assembly_id: "${assemblyCode}"`;
      console.log(`[AssemblyService] Not found with assemblyCode, trying ${queryPathDescription}`);
      let fallbackQuery = Assembly.findOne({ assembly_id: assemblyCode });
      if (includeParts) {
        console.log(`[AssemblyService] [${queryPathDescription}] Populating partsRequired.partId`);
        fallbackQuery = fallbackQuery.populate({
            path: 'partsRequired.partId',
            model: 'Part',
            select: 'name partNumber description inventory category isAssembly'
          });
      }
      assembly = await fallbackQuery.lean() as IAssemblyService | null;
      // console.log(`[AssemblyService] [${queryPathDescription}] Found assembly (pre-population or no parts):`, assembly ? assembly._id : 'null');
    }
    
    if (!assembly) {
      logOperation('GET_BY_ASSEMBLY_CODE_NOT_FOUND', { assemblyCode });
      console.log(`[AssemblyService] Assembly with code/ID "${assemblyCode}" not found after all attempts.`);
      return null;
    }
    
    // console.log(`[AssemblyService] Final assembly object for "${assemblyCode}":`, JSON.stringify(assembly, null, 2));
    logOperation('GET_BY_ASSEMBLY_CODE_SUCCESS', { assemblyCode, id: assembly._id, includeParts });
    return assembly;

  } catch (error: any) {
    logOperation('GET_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, error: error.message, stack: error.stack });
    console.error(`[AssemblyService] Error fetching assembly by code "${assemblyCode}":`, error);
    // Let the centralized Sentry error handler in the API route or middleware catch this
    // Re-throwing the original error or a new error with context
    const errDetails = handleMongoDBError(error); // This function already reports to Sentry
    throw new Error(errDetails.message || `Failed to get assembly by code ${assemblyCode}. Original error: ${error.message}`);
  }
}

// For backward compatibility, alias the function
export const getAssemblyByAssemblyId = getAssemblyByAssemblyCode;

/**
 * Creates a new assembly
 */
export async function createAssembly(assemblyData: CreateAssemblyDto): Promise<IAssemblyService> {
  logOperation('CREATE', { 
    assemblyCode: assemblyData.assemblyCode,
    name: assemblyData.name,
    partsRequiredCount: assemblyData.partsRequired?.length || 0,
    createdBy: assemblyData.createdBy
  });
  
  try {
    await connectToMongoose();

    // Validate assembly code format (if specific format is required)
    const assemblyCodePattern = /^[A-Za-z0-9\-\.]{3,}$/;
    if (!assemblyCodePattern.test(assemblyData.assemblyCode)) {
      throw new Error('Invalid assembly code format. Use alphanumeric characters, hyphens and dots (minimum 3 characters).');
    }

    // Check for existing assembly with the same code
    const existingAssembly = await Assembly.findOne({ 
      $or: [
        { assemblyCode: assemblyData.assemblyCode },
        { assembly_id: assemblyData.assemblyCode }
      ]
    });
    
    if (existingAssembly) {
      logOperation('CREATE_DUPLICATE_CODE', { assemblyCode: assemblyData.assemblyCode });
      throw new Error(`Assembly with code ${assemblyData.assemblyCode} already exists`);
    }

    // Validate parts if provided
    if (assemblyData.partsRequired && assemblyData.partsRequired.length > 0) {
      for (const partItem of assemblyData.partsRequired) {
        // Validate part exists
        if (!Types.ObjectId.isValid(partItem.partId)) {
          throw new Error(`Invalid part ID format for part: ${partItem.partId}`);
        }
        
        const partExists = await Part.findById(partItem.partId);
        if (!partExists) {
          throw new Error(`Referenced part with ID ${partItem.partId} not found`);
        }
        
        // Validate quantity
        if (partItem.quantityRequired <= 0 || !Number.isInteger(partItem.quantityRequired)) {
          throw new Error(`Invalid quantity for part ${partItem.partId}. Must be a positive integer.`);
        }
      }
    }

    // Check for parent assembly reference if provided
    if (assemblyData.parentId) {
      if (!Types.ObjectId.isValid(assemblyData.parentId)) {
        throw new Error('Invalid parent assembly ID format');
      }
      
      const parentExists = await Assembly.findById(assemblyData.parentId);
      if (!parentExists) {
        throw new Error(`Referenced parent assembly with ID ${assemblyData.parentId} not found`);
      }
    }

    // Check for product reference if provided
    if (assemblyData.productId) {
      if (!Types.ObjectId.isValid(assemblyData.productId)) {
        throw new Error('Invalid product ID format');
      }
      
      // Assuming there's a Product model - would need to verify the product exists
      // const productExists = await Product.findById(assemblyData.productId);
      // if (!productExists) {
      //   throw new Error(`Referenced product with ID ${assemblyData.productId} not found`);
      // }
    }

    // Handle legacy fields if needed
    if (assemblyData.assembly_id && !assemblyData.assemblyCode) {
      assemblyData.assemblyCode = assemblyData.assembly_id;
    }

    // Create the new assembly document
    const newAssembly = new Assembly({
      assemblyCode: assemblyData.assemblyCode,
      name: assemblyData.name,
      description: assemblyData.description,
      productId: assemblyData.productId ? new Types.ObjectId(assemblyData.productId) : null,
      parentId: assemblyData.parentId ? new Types.ObjectId(assemblyData.parentId) : null,
      isTopLevel: assemblyData.isTopLevel ?? false,
      partsRequired: assemblyData.partsRequired ? 
        assemblyData.partsRequired.map(part => {
          // Handle legacy format conversion if needed
          const quantityRequired = part.quantityRequired;
          const unitOfMeasure = part.unitOfMeasure || 'ea';
          
          return {
            partId: new Types.ObjectId(part.partId),
            quantityRequired: quantityRequired,
            unitOfMeasure: unitOfMeasure
          };
        }) : [],
      status: assemblyData.status,
      version: assemblyData.version || 1,
      manufacturingInstructions: assemblyData.manufacturingInstructions,
      estimatedBuildTime: assemblyData.estimatedBuildTime,
      notes: assemblyData.notes,
      createdBy: assemblyData.createdBy ? new Types.ObjectId(assemblyData.createdBy) : null,
      images: assemblyData.images,
      attributes: assemblyData.attributes
    });

    let savedAssembly = await newAssembly.save();
    
    // Populate createdBy field before returning
    if (savedAssembly.createdBy) {
      savedAssembly = await savedAssembly.populate('createdBy');
    }

    logOperation('CREATE_SUCCESS', {
      _id: savedAssembly._id,
      assemblyCode: savedAssembly.assemblyCode
    });

    return savedAssembly.toObject() as IAssemblyService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create assembly');
  }
}

/**
 * Updates an existing assembly by its MongoDB ObjectId.
 */
export async function updateAssembly(id: string, updateData: UpdateAssemblyDto): Promise<IAssemblyService | null> {
  logOperation('UPDATE', { id, updateFields: Object.keys(updateData) });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    // Validate the update data
    // Cannot update assemblyCode (primary identifier)
    if ('assemblyCode' in updateData) {
      throw new Error('Cannot update assemblyCode field');
    }

    // Prepare the update payload
    const updatePayload: any = {
      ...updateData
    };
    
    // Handle ObjectId references
    if (updateData.productId) {
      if (!Types.ObjectId.isValid(updateData.productId)) {
        throw new Error('Invalid product ID format');
      }
      updatePayload.productId = new Types.ObjectId(updateData.productId);
    }
    
    if (updateData.parentId) {
      if (!Types.ObjectId.isValid(updateData.parentId)) {
        throw new Error('Invalid parent assembly ID format');
      }
      updatePayload.parentId = new Types.ObjectId(updateData.parentId);
    }
    
    // Handle partsRequired array transformation
    if (updateData.partsRequired) {
      // Validate parts
      for (const partItem of updateData.partsRequired) {
        if (!Types.ObjectId.isValid(partItem.partId)) {
          throw new Error(`Invalid part ID format for part: ${partItem.partId}`);
        }
        
        const partExists = await Part.findById(partItem.partId);
        if (!partExists) {
          throw new Error(`Referenced part with ID ${partItem.partId} not found`);
        }
        
        if (partItem.quantityRequired <= 0 || !Number.isInteger(partItem.quantityRequired)) {
          throw new Error(`Invalid quantity for part ${partItem.partId}. Must be a positive integer.`);
        }
      }
      
      // Transform partIds to ObjectIds
      updatePayload.partsRequired = updateData.partsRequired.map(part => ({
        partId: new Types.ObjectId(part.partId),
        quantityRequired: part.quantityRequired,
        unitOfMeasure: part.unitOfMeasure
      }));
    }

    const assembly = await Assembly.findByIdAndUpdate(
      id,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean() as IAssemblyService | null;

    if (!assembly) {
      logOperation('UPDATE_NOT_FOUND', { id });
      return null;
    }
    
    logOperation('UPDATE_SUCCESS', { id });
    return assembly;
  } catch (error: any) {
    logOperation('UPDATE_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update assembly with id ${id}`);
  }
}

/**
 * Updates an existing assembly by its assemblyCode.
 */
export async function updateAssemblyByAssemblyCode(assemblyCode: string, updateData: UpdateAssemblyDto): Promise<IAssemblyService | null> {
  logOperation('UPDATE_BY_ASSEMBLY_CODE', { assemblyCode, data: updateData });

  try {
    await connectToMongoose();

    // Prevent assemblyCode update through this method
    if ('assemblyCode' in updateData) {
      throw new Error('Cannot update assemblyCode field');
    }

    // Prepare the update payload
    const updatePayload: any = {
      ...updateData
    };
    
    // Handle ObjectId references
    if (updateData.productId) {
      if (!Types.ObjectId.isValid(updateData.productId)) {
        throw new Error('Invalid product ID format');
      }
      updatePayload.productId = new Types.ObjectId(updateData.productId);
    }
    
    if (updateData.parentId) {
      if (!Types.ObjectId.isValid(updateData.parentId)) {
        throw new Error('Invalid parent assembly ID format');
      }
      updatePayload.parentId = new Types.ObjectId(updateData.parentId);
    }
    
    // Handle partsRequired array transformation
    if (updateData.partsRequired) {
      // Validate parts
      for (const partItem of updateData.partsRequired) {
        if (!Types.ObjectId.isValid(partItem.partId)) {
          throw new Error(`Invalid part ID format for part: ${partItem.partId}`);
        }
        
        const partExists = await Part.findById(partItem.partId);
        if (!partExists) {
          throw new Error(`Referenced part with ID ${partItem.partId} not found`);
        }
        
        if (partItem.quantityRequired <= 0 || !Number.isInteger(partItem.quantityRequired)) {
          throw new Error(`Invalid quantity for part ${partItem.partId}. Must be a positive integer.`);
        }
      }
      
      // Transform partIds to ObjectIds
      updatePayload.partsRequired = updateData.partsRequired.map(part => ({
        partId: new Types.ObjectId(part.partId),
        quantityRequired: part.quantityRequired,
        unitOfMeasure: part.unitOfMeasure
      }));
    }
    
    const assembly = await Assembly.findOneAndUpdate(
      { assemblyCode: assemblyCode }, // First try with new field name
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean() as IAssemblyService | null;

    if (!assembly) {
      // Try with legacy field name for backward compatibility
      const legacyAssembly = await Assembly.findOneAndUpdate(
        { assembly_id: assemblyCode },
        { $set: updatePayload },
        { new: true, runValidators: true } 
      ).lean() as IAssemblyService | null;

      if (!legacyAssembly) {
        logOperation('UPDATE_BY_ASSEMBLY_CODE_NOT_FOUND', { assemblyCode });
        return null;
      }
      
      return legacyAssembly;
    }
    
    logOperation('UPDATE_BY_ASSEMBLY_CODE_SUCCESS', { assemblyCode });
    return assembly;
  } catch (error: any) {
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update assembly with assemblyCode ${assemblyCode}`);
  }
}

// For backward compatibility
export const updateAssemblyByAssemblyId = updateAssemblyByAssemblyCode;

/**
 * Deletes an assembly by its MongoDB ObjectId.
 */
export async function deleteAssembly(id: string): Promise<boolean> {
  logOperation('DELETE', { id });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_INVALID_FORMAT', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    // Check if assembly is referenced by another assembly
    const isReferenced = await Assembly.findOne({ parentId: id });
    if (isReferenced) {
      logOperation('DELETE_REFERENCED', { id, referencedBy: isReferenced._id });
      throw new Error(`Cannot delete assembly: it is used as a parent by assembly ${isReferenced.name} (${isReferenced.assemblyCode})`);
    }

    const result = await Assembly.deleteOne({ _id: id });
    if (result.deletedCount === 0) {
      logOperation('DELETE_NOT_FOUND', { id });
      return false;
    }
    
    logOperation('DELETE_SUCCESS', { id, deletedCount: result.deletedCount });
    return true;
  } catch (error: any) {
    logOperation('DELETE_ERROR', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete assembly with id ${id}`);
  }
}

/**
 * Deletes an assembly by its assemblyCode.
 */
export async function deleteAssemblyByAssemblyCode(assemblyCode: string): Promise<boolean> {
  logOperation('DELETE_BY_ASSEMBLY_CODE', { assemblyCode });
  
  try {
    await connectToMongoose();

    // Find the assembly first to check references
    const assembly = await Assembly.findOne({ 
      $or: [
        { assemblyCode: assemblyCode },
        { assembly_id: assemblyCode }
      ]
    });
    
    if (!assembly) {
      logOperation('DELETE_BY_ASSEMBLY_CODE_NOT_FOUND', { assemblyCode });
      return false;
    }
    
    // Check if assembly is referenced by another assembly
    const isReferenced = await Assembly.findOne({ parentId: assembly._id });
    if (isReferenced) {
      logOperation('DELETE_BY_ASSEMBLY_CODE_REFERENCED', { 
        assemblyCode, 
        referencedBy: isReferenced.assemblyCode || isReferenced.assembly_id 
      });
      throw new Error(`Cannot delete assembly: it is used as a parent by assembly ${isReferenced.name} (${isReferenced.assemblyCode || isReferenced.assembly_id})`);
    }

    const result = await Assembly.deleteOne({ _id: assembly._id });
    
    logOperation('DELETE_BY_ASSEMBLY_CODE_SUCCESS', { assemblyCode, deletedCount: result.deletedCount });
    return result.deletedCount > 0;
  } catch (error: any) {
    logOperation('DELETE_BY_ASSEMBLY_CODE_ERROR', { assemblyCode, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete assembly with assemblyCode ${assemblyCode}`);
  }
}

// For backward compatibility
export const deleteAssemblyByAssemblyId = deleteAssemblyByAssemblyCode;

/**
 * Searches assemblies based on a text query string.
 */
export async function searchAssemblies(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { assemblyCode: new RegExp(query, 'i') },
        { assembly_id: new RegExp(query, 'i') }, // For backward compatibility
        { description: new RegExp(query, 'i') }
      ];
      
      // If query matches notes field
      if ('notes' in searchFilter) {
        searchFilter.$or.push({ notes: new RegExp(query, 'i') });
      }
    }

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', { query, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search assemblies');
  }
}

/**
 * Gets assemblies by status with pagination.
 */
export async function getAssembliesByStatus(status: string, options: any = {}) {
  logOperation('GET_BY_STATUS', { status });
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Combine status filter with additional filters
    const searchFilter = { 
      ...filter,
      status
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_STATUS_SUCCESS', { status, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('GET_BY_STATUS_ERROR', { status, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get assemblies with status ${status}`);
  }
}