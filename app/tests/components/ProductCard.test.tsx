import React from 'react';
import { render, screen } from '@testing-library/react';
import ProductCard from '@/app/components/features/ProductCard';
import { ThemeProvider } from '@/app/context/ThemeContext';

// Mock the ThemeContext
jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

describe('ProductCard', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    description: 'This is a test product',
    currentStock: 15,
    reorderLevel: 10,
    category: 'Test Category',
    onHandValue: 1500,
    demand: 'High' as const,
  };

  const setup = (props = {}) => {
    return render(
      <ThemeProvider>
        <ProductCard
          product={mockProduct}
          {...props}
        />
      </ThemeProvider>
    );
  };

  it('renders product name correctly', () => {
    setup();
    expect(screen.getByText('Test Product')).toBeInTheDocument();
  });

  it('renders product description correctly', () => {
    setup();
    expect(screen.getByText('This is a test product')).toBeInTheDocument();
  });

  it('renders stock status correctly when in stock', () => {
    setup();
    expect(screen.getByText('In Stock')).toBeInTheDocument();
  });

  it('renders low stock status when stock is below reorder level', () => {
    setup({
      product: {
        ...mockProduct,
        currentStock: 5
      }
    });
    expect(screen.getByText('Low Stock')).toBeInTheDocument();
  });

  it('renders out of stock status when stock is zero', () => {
    setup({
      product: {
        ...mockProduct,
        currentStock: 0
      }
    });
    expect(screen.getByText('Out of Stock')).toBeInTheDocument();
  });

  it('renders demand label correctly', () => {
    setup();
    expect(screen.getByText('High Demand')).toBeInTheDocument();
  });

  it('renders category correctly', () => {
    setup();
    expect(screen.getByText('Test Category')).toBeInTheDocument();
  });

  it('renders stock quantity correctly', () => {
    setup();
    expect(screen.getByText('15 units')).toBeInTheDocument();
  });

  it('renders value correctly', () => {
    setup();
    expect(screen.getByText('$1,500')).toBeInTheDocument();
  });

  it('renders placeholder correctly', () => {
    setup({
      isPlaceholder: true,
      product: mockProduct
    });
    expect(screen.getByText('Add New Product')).toBeInTheDocument();
  });
});