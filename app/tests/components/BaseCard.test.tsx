import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import BaseCard from '@/app/components/ui/BaseCard';
import { ThemeProvider } from '@/app/context/ThemeContext';

// Mock the ThemeContext
jest.mock('@/app/context/ThemeContext', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

describe('BaseCard', () => {
  const setup = (props = {}) => {
    return render(
      <ThemeProvider>
        <BaseCard {...props}>
          <div>Card Content</div>
        </BaseCard>
      </ThemeProvider>
    );
  };

  it('renders children correctly', () => {
    setup();
    expect(screen.getByText('Card Content')).toBeInTheDocument();
  });

  it('renders title and subtitle correctly', () => {
    setup({ title: 'Test Title', subtitle: 'Test Subtitle' });
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
  });

  it('calls onClick when card is clicked', () => {
    const handleClick = jest.fn();
    setup({ onClick: handleClick });

    fireEvent.click(screen.getByText('Card Content').closest('div')!);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('renders view details text and calls onViewDetails', () => {
    const handleViewDetails = jest.fn();
    setup({ onViewDetails: handleViewDetails, viewDetailsText: 'See More' });

    const viewDetailsLink = screen.getByText('See More');
    expect(viewDetailsLink).toBeInTheDocument();

    fireEvent.click(viewDetailsLink);
    expect(handleViewDetails).toHaveBeenCalledTimes(1);
  });

  it('applies the correct color classes', () => {
    const { container } = setup({ color: 'green', title: 'Test Title' });

    // The component should have green color classes applied somewhere
    // This is a simplified test; in a real test you might want to check specific elements
    expect(container.innerHTML).toContain('text-green-600');
  });

  it('renders with featured styles when isFeatured is true', () => {
    const { container } = setup({ isFeatured: true, color: 'blue' });

    // Check if the featured border class is applied
    expect(container.innerHTML).toContain('border-2');
    expect(container.innerHTML).toContain('border-blue-500/30');
  });
});