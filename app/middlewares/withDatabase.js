import { NextResponse } from 'next/server';
import connectToDatabase, { 
  getConnectionState, 
  forceReconnect 
} from '@/app/lib/mongodb';
import { logError } from '@/app/services/logging';

/**
 * @typedef {import('next/server').NextRequest} NextRequest
 * @typedef {import('next/server').NextResponse} NextResponse
 */

/**
 * Middleware to ensure database connection is established before handling API requests.
 * This prevents the "Cannot call insertOne() before initial connection is complete" error
 * by ensuring the connection is fully established before proceeding.
 * 
 * @param {(request: NextRequest, ...args: any[]) => Promise<NextResponse>} handler - The API route handler function
 * @returns {(request: NextRequest, ...args: any[]) => Promise<NextResponse>} - The wrapped handler function with database connection assurance
 */
export default function withDatabase(handler) {
  return async (request, ...args) => {
    try {
      // Check current connection state
      const connectionState = getConnectionState();
      
      // If connection is in ERROR state, try to reconnect
      if (connectionState.state === 'error') {
        console.log('[Middleware] Database connection in error state, reconnecting...');
        await forceReconnect();
      } else if (connectionState.state !== 'connected') {
        // If not connected, connect to database
        console.log('[Middleware] Establishing database connection...');
        await connectToDatabase();
      }
      
      // At this point, database connection should be established
      // Proceed with the original handler
      return handler(request, ...args);
    } catch (error) {
      // Log the error
      await logError(
        'database', 
        'Database connection error in API middleware', 
        error
      );
      
      // Return a friendly error response
      // Ensure the error response also matches the Promise<NextResponse> type
      return NextResponse.json(
        { 
          success: false, 
          error: 'Database connection error. Please try again later.' 
        },
        { status: 503, headers: { 'Content-Type': 'application/json' } }
      );
    }
  };
}