import { NextRequest, NextResponse } from 'next/server';
import { setTag } from '@/app/lib/sentry-utils';
import { logApiPerformance } from '@/app/services/logging';

// Thresholds in milliseconds
const SLOW_THRESHOLD = 1000; // 1 second
const VERY_SLOW_THRESHOLD = 3000; // 3 seconds

/**
 * Middleware that adds performance monitoring to API routes
 * @param handler - The API route handler
 * @param routePath - The API route path (for performance logging and tagging)
 * @returns A wrapped handler with performance monitoring
 */
export default function withPerformanceMonitoring(
  handler: (request: NextRequest) => Promise<NextResponse>,
  routePath: string
) {
  return async function performanceHandler(request: NextRequest) {
    const startTime = Date.now();
    
    // Execute the original handler
    const response = await handler(request);
    
    // Calculate request duration
    const duration = Date.now() - startTime;
    
    // Set performance tags
    setTag('performance.route', routePath);
    setTag('performance.duration', duration.toString());
    
    // Add performance classification
    let performanceCategory = 'normal';
    if (duration > VERY_SLOW_THRESHOLD) {
      performanceCategory = 'very-slow';
      setTag('performance.category', 'very-slow');
    } else if (duration > SLOW_THRESHOLD) {
      performanceCategory = 'slow';
      setTag('performance.category', 'slow');
    } else {
      setTag('performance.category', 'normal');
    }
    
    // Log performance data
    await logApiPerformance(routePath, duration, performanceCategory, {
      method: request.method,
      query: Object.fromEntries(new URL(request.url).searchParams.entries())
    });
    
    // Add performance header to response
    response.headers.set('X-Response-Time', `${duration}ms`);
    
    return response;
  };
} 