"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart3, Bell, AlertTriangle, ShoppingCart, Package,
  AlertCircle, Plus, FileText, ChevronRight, RefreshCw, Filter
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import Header from '@/app/components/layout/Header';
import StatusCard from '@/app/components/ui/StatusCard';
import { ProductsTable } from '@/app/components/tables/ProductsTable';
import { ProductCard, AssemblyStatus } from '@/components/features';
import { LazyProductionPlanning } from '@/app/components/charts/LazyProductionPlanning';
import { LazyProductionCapacity } from '@/app/components/charts/LazyProductionCapacity';
import ErrorDisplay from '@/app/components/ui/ErrorDisplay';
import { useTheme } from '@/app/context/ThemeContext';
import { useAppContext } from '@/app/context/AppContext';
// Create real data structures to replace mock data
// Import WorkOrder type to ensure type safety
import { WorkOrder, WorkOrderItem } from '@/app/types';

const workOrders: WorkOrder[] = [
  {
    id: 'WO-2023-001',
    description: 'Assembly of Tamping Arms',
    status: 'in_progress' as const,
    startDate: '2023-03-15',
    endDate: '2023-03-25',
    createdBy: 'user-001',
    assignedTo: 'user-002',
    priority: 'high' as const,
    notes: 'Need to complete before the next shipment',
    items: [
      {
        id: 1,
        workOrderId: 'WO-2023-001',
        partId: 'CU.30.412/UD',
        quantityRequired: 5,
        quantityConsumed: 2
      },
      {
        id: 2,
        workOrderId: 'WO-2023-001',
        partId: 'CU.30.413/UD',
        quantityRequired: 8,
        quantityConsumed: 3
      }
    ] as WorkOrderItem[]
  }
];

const productionCapacityData = {
  capacityData: [
    {
      department: 'Assembly',
      currentCapacity: 85,
      maxCapacity: 100,
      bottleneck: false
    },
    {
      department: 'Machining',
      currentCapacity: 95,
      maxCapacity: 100,
      bottleneck: true
    },
    {
      department: 'Welding',
      currentCapacity: 70,
      maxCapacity: 100,
      bottleneck: false
    }
  ],
  forecastAccuracy: 92,
  productionTrend: 'increasing' as const,
  bottleneckImpact: 15
};

const DashboardPage: React.FC = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const [showAlerts, setShowAlerts] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const alertsButtonRef = useRef<HTMLButtonElement>(null);
  const alertsPopupRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const {
    products: rawProducts,
    stockStatus: rawStockStatus,
    orderStatus: rawOrderStatus,
    highDemandItems: rawHighDemandItems,
    assemblies: rawAssemblies,
    // logisticsInfo is derived from orderStatus now
    isLoading,
    error,
    refreshData
  } = useAppContext();

  // Add null checks for all data objects
  const products = Array.isArray(rawProducts) ? rawProducts : [];
  const stockStatus = rawStockStatus || { total: 0, lowStock: 0, outOfStock: 0, overstock: 0 };
  const orderStatus = rawOrderStatus || { pending: 0, processing: 0, shipped: 0, delivered: 0, total: 0 };
  const highDemandItems = Array.isArray(rawHighDemandItems) ? rawHighDemandItems : [];
  const assemblies = Array.isArray(rawAssemblies) ? rawAssemblies : [];

  const extendedAssemblies = React.useMemo(() => {
    // Add additional safety check before mapping
    if (!Array.isArray(assemblies)) {
      console.error('[DASHBOARD] Assemblies is not an array:', assemblies);
      return [];
    }

    return assemblies.map(assembly => ({
      ...assembly,
      stage: assembly.stage || 'active',
      parts: Array.isArray(assembly.parts) ? assembly.parts : []
    }));
  }, [assemblies]);

  const handleRefreshData = useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setTimeout(() => setRefreshing(false), 600);
    }
  }, [refreshData]);

  const mockHighDemandProduct = {
    id: 'P0001',
    name: 'Featured Product',
    description: 'This is a featured product with high demand',
    currentStock: 5,
    reorderLevel: 10,
    demand: 'High' as const,
    category: 'Electronics',
    imageUrl: null
  };

  const lowStockPercentage = stockStatus.total > 0
    ? Math.round((stockStatus.lowStock / stockStatus.total) * 100) : 0;
  const outOfStockPercentage = stockStatus.total > 0
    ? Math.round((stockStatus.outOfStock / stockStatus.total) * 100) : 0;

  const hasLowStockAlert = stockStatus.lowStock > 0;
  const hasOutOfStockAlert = stockStatus.outOfStock > 0;
  const alertCount = (hasLowStockAlert ? 1 : 0) + (hasOutOfStockAlert ? 1 : 0);

  const handleAddStock = () => router.push('/hierarchical-part-entry');
  const handleCreateOrder = () => router.push('/purchase-orders');
  const handleGenerateReport = () => router.push('/reports');
  const handleViewCriticalItems = () => router.push('/inventory?filter=critical');
  const handleViewDeliveries = () => router.push('/logistics?tab=deliveries');
  const handleViewAllAlerts = () => { router.push('/inventory?filter=alerts'); setShowAlerts(false); };
  const handleViewAllActivity = () => router.push('/inventory-transactions');

  // Add safety check for products array
  const topItemsByValue = Array.isArray(products)
    ? [...products]
      .sort((a, b) => (b.onHandValue || 0) - (a.onHandValue || 0))
      .slice(0, 5)
      .filter(product => searchQuery ?
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
      )
    : [];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showAlerts && alertsPopupRef.current && alertsButtonRef.current &&
          !alertsPopupRef.current.contains(event.target as Node) &&
          !alertsButtonRef.current.contains(event.target as Node)) {
        setShowAlerts(false);
      }
    };
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showAlerts) setShowAlerts(false);
        if (filterOpen) setFilterOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showAlerts, filterOpen]);

  useEffect(() => {
    if (filterOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [filterOpen]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
        duration: 0.5
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    hover: {
      y: -5,
      scale: 1.02,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      transition: { duration: 0.2 }
    }
  };

  const shimmerVariants = {
    initial: { x: '-100%' },
    animate: {
      x: '100%',
      transition: {
        repeat: Infinity,
        duration: 1.5,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-dark-900 text-gray-900 dark:text-gray-100">
      <Header title="Inventory Dashboard">
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setFilterOpen(!filterOpen)}
            className={`p-2 rounded-full transition-colors duration-200 ${
              filterOpen
                ? 'bg-gray-200 text-gray-700 dark:bg-dark-700 dark:text-gray-200'
                : 'text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
            aria-expanded={filterOpen}
            aria-label="Filter and search"
          >
            <Filter size={20} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefreshData}
            className={`p-2 rounded-full transition-colors duration-200 ${
              refreshing
                ? 'animate-spin text-blue-500 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
            disabled={refreshing}
            aria-label="Refresh dashboard data"
          >
            <RefreshCw size={20} />
          </motion.button>
          <div className="relative">
            <motion.button
              ref={alertsButtonRef}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className={`p-2 relative rounded-full transition-colors duration-200 ${
                showAlerts
                  ? 'bg-gray-200 text-gray-700 dark:bg-dark-700 dark:text-gray-200'
                  : 'text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
              onClick={() => setShowAlerts(!showAlerts)}
              aria-expanded={showAlerts}
              aria-haspopup="true"
              aria-label={`Critical alerts ${alertCount > 0 ? `(${alertCount} alerts)` : ''}`}
            >
              <Bell size={20} />
              {alertCount > 0 && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"
                >
                  {alertCount}
                </motion.span>
              )}
            </motion.button>
            <AnimatePresence>
              {showAlerts && alertCount > 0 && (
                <motion.div
                  ref={alertsPopupRef}
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                  className={`absolute right-0 mt-2 w-64 rounded-lg shadow-xl z-50 backdrop-blur-sm ${
                    theme === 'dark'
                      ? 'bg-dark-800/95 border border-dark-700'
                      : 'bg-white/95 border border-gray-200'
                  }`}
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold">Critical Alerts</h3>
                      <button
                        onClick={() => setShowAlerts(false)}
                        className="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                      >
                        &times;
                      </button>
                    </div>
                    <div className="space-y-2">
                      {hasLowStockAlert && (
                        <motion.div
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 }}
                          className="flex items-center text-xs text-yellow-600 dark:text-yellow-400"
                        >
                          <AlertTriangle size={14} className="mr-1" /> {stockStatus.lowStock} item(s) are low on stock.
                        </motion.div>
                      )}
                      {hasOutOfStockAlert && (
                        <motion.div
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.2 }}
                          className="flex items-center text-xs text-red-600 dark:text-red-400"
                        >
                          <AlertCircle size={14} className="mr-1" /> {stockStatus.outOfStock} item(s) are out of stock.
                        </motion.div>
                      )}
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleViewAllAlerts}
                      className="mt-3 w-full text-center text-xs px-2 py-1.5 rounded bg-blue-500 hover:bg-blue-600 text-white transition-colors"
                    >
                      View All Alerts
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </Header>

      <AnimatePresence>
        {filterOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className={`overflow-hidden px-6 py-4 mb-4 ${
              theme === 'dark'
                ? 'bg-dark-800/70 backdrop-blur-sm'
                : 'bg-gray-100/70 backdrop-blur-sm'
            }`}
          >
            <div className="flex items-center space-x-4">
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`flex-grow px-4 py-2 rounded-lg border ${
                  theme === 'dark'
                    ? 'bg-dark-700/80 border-dark-600 placeholder-gray-400 text-gray-200'
                    : 'bg-white border-gray-300 placeholder-gray-400'
                } focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200`}
              />
              {/* Add filter dropdowns/buttons here if needed */}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {isLoading && (
        <div className="p-6 flex justify-center items-center h-64">
          <div className="relative">
            <div className="h-16 w-16 rounded-full border-t-4 border-b-4 border-blue-500 dark:border-blue-400 animate-spin"></div>
            <div className="mt-4 text-center text-gray-600 dark:text-gray-300">Loading dashboard data...</div>
          </div>
        </div>
      )}

      {error && <ErrorDisplay error={error} message="Error loading dashboard data" />}

      {!isLoading && !error && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="p-6 space-y-8"
        >
          {/* Quick Actions */}
          <motion.div
            variants={cardVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"
          >
            <motion.button
              onClick={handleAddStock}
              whileHover="hover"
              whileTap={{ scale: 0.98 }}
              variants={cardVariants}
              className={`flex items-center justify-center space-x-3 p-5 rounded-xl shadow-md transition-all duration-200 ${
                theme === 'dark'
                  ? 'bg-gradient-to-br from-dark-800 to-dark-700 hover:from-dark-700 hover:to-dark-600 border border-dark-700'
                  : 'bg-white hover:bg-gray-50 border border-gray-100'
              }`}
            >
              <div className={`p-2 rounded-full ${theme === 'dark' ? 'bg-green-500/10' : 'bg-green-100'}`}>
                <Plus size={20} className="text-green-500" />
              </div>
              <span className="text-sm font-medium">Add New Stock</span>
            </motion.button>
            <motion.button
              onClick={handleCreateOrder}
              whileHover="hover"
              whileTap={{ scale: 0.98 }}
              variants={cardVariants}
              className={`flex items-center justify-center space-x-3 p-5 rounded-xl shadow-md transition-all duration-200 ${
                theme === 'dark'
                  ? 'bg-gradient-to-br from-dark-800 to-dark-700 hover:from-dark-700 hover:to-dark-600 border border-dark-700'
                  : 'bg-white hover:bg-gray-50 border border-gray-100'
              }`}
            >
              <div className={`p-2 rounded-full ${theme === 'dark' ? 'bg-blue-500/10' : 'bg-blue-100'}`}>
                <ShoppingCart size={20} className="text-blue-500" />
              </div>
              <span className="text-sm font-medium">Create Purchase Order</span>
            </motion.button>
            <motion.button
              onClick={handleGenerateReport}
              whileHover="hover"
              whileTap={{ scale: 0.98 }}
              variants={cardVariants}
              className={`flex items-center justify-center space-x-3 p-5 rounded-xl shadow-md transition-all duration-200 ${
                theme === 'dark'
                  ? 'bg-gradient-to-br from-dark-800 to-dark-700 hover:from-dark-700 hover:to-dark-600 border border-dark-700'
                  : 'bg-white hover:bg-gray-50 border border-gray-100'
              }`}
            >
              <div className={`p-2 rounded-full ${theme === 'dark' ? 'bg-purple-500/10' : 'bg-purple-100'}`}>
                <FileText size={20} className="text-purple-500" />
              </div>
              <span className="text-sm font-medium">Generate Report</span>
            </motion.button>
          </motion.div>

          {/* Stats Overview - Group status cards in one section */}
          <motion.div variants={cardVariants} className="mb-8">
            <h2 className="text-xl font-semibold mb-4 px-1">Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
              <motion.div variants={cardVariants} whileHover="hover">
                <StatusCard
                  title="Total Items"
                  mainStat={{ value: stockStatus.total, label: 'Total' }}
                  data={{
                    'Out of Stock': stockStatus.outOfStock,
                    'Low Stock': stockStatus.lowStock,
                    'Healthy': stockStatus.total - stockStatus.lowStock - stockStatus.outOfStock
                  }}
                  icon={<Package />}
                  color="orange"
                />
              </motion.div>

              <motion.div variants={cardVariants} whileHover="hover">
                <StatusCard
                  title="Total Orders"
                  mainStat={{ value: orderStatus.total, label: 'Total' }}
                  data={{
                    'Pending': orderStatus.pending,
                    'Processing': orderStatus.processing,
                    'Shipped': orderStatus.shipped,
                    'Delivered': orderStatus.delivered
                  }}
                  icon={<ShoppingCart />}
                  color="blue"
                />
              </motion.div>

              <motion.div variants={cardVariants} whileHover="hover">
                {/* Critical Reorder */}
                <div className={`h-full p-5 rounded-xl shadow-md ${
                  theme === 'dark'
                    ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                    : 'bg-white border border-gray-100'
                }`}>
                  <h3 className="text-base font-semibold mb-3 flex items-center">
                    <AlertTriangle size={18} className="mr-2 text-red-500" /> Critical Reorder
                  </h3>
                  <p className="text-3xl font-bold mb-1">{stockStatus.lowStock + stockStatus.outOfStock}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">items needing attention</p>
                  <button
                    onClick={handleViewCriticalItems}
                    className="text-sm text-blue-500 dark:text-blue-400 hover:underline flex items-center"
                  >
                    View Critical Items <ChevronRight size={16} className="ml-1" />
                  </button>
                </div>
              </motion.div>

              <motion.div variants={cardVariants} whileHover="hover">
                {/* Deliveries */}
                <div className={`h-full p-5 rounded-xl shadow-md ${
                  theme === 'dark'
                    ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                    : 'bg-white border border-gray-100'
                }`}>
                  <h3 className="text-base font-semibold mb-3 flex items-center">
                    <Package size={18} className="mr-2 text-blue-500" /> Deliveries
                  </h3>
                  <p className="text-3xl font-bold mb-1">{orderStatus.delivered}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">Completed deliveries</p>
                  {/* Placeholder for delivery rate */}
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">Delivery Rate: 95%</p>
                  <button
                    onClick={handleViewDeliveries}
                    className="text-sm text-blue-500 dark:text-blue-400 hover:underline flex items-center"
                  >
                    View Details <ChevronRight size={16} className="ml-1" />
                  </button>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* High Demand / Featured Product */}
          <motion.div variants={cardVariants} className="mb-8">
            <h2 className="text-xl font-semibold mb-4 px-1">Featured Products</h2>
            <div className={`p-5 rounded-xl shadow-md ${
              theme === 'dark'
                ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                : 'bg-white border border-gray-100'
            }`}>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <BarChart3 size={18} className="mr-2 text-green-500" /> High Demand Items
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                {highDemandItems.length > 0 ? (
                  highDemandItems.slice(0, 2).map((item, index) => (
                    <motion.div
                      key={item.id}
                      variants={cardVariants}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 * index }}
                      whileHover="hover"
                    >
                      <ProductCard product={item} />
                    </motion.div>
                  ))
                ) : (
                  <motion.div
                    variants={cardVariants}
                    whileHover="hover"
                  >
                    <ProductCard product={mockHighDemandProduct} isFeatured={true}/>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Production Overview */}
          <motion.div variants={cardVariants} className="mb-8">
            <h2 className="text-xl font-semibold mb-4 px-1">Production Overview</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
              <motion.div variants={cardVariants} whileHover="hover" className={`rounded-xl shadow-md overflow-hidden ${
                theme === 'dark'
                  ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                  : 'bg-white border border-gray-100'
              }`}>
                <LazyProductionPlanning
                  workOrders={workOrders}
                  products={products}
                />
              </motion.div>
              <motion.div variants={cardVariants} whileHover="hover" className={`rounded-xl shadow-md overflow-hidden ${
                theme === 'dark'
                  ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                  : 'bg-white border border-gray-100'
              }`}>
                <LazyProductionCapacity
                  capacityData={productionCapacityData.capacityData}
                  forecastAccuracy={productionCapacityData.forecastAccuracy}
                  productionTrend={productionCapacityData.productionTrend}
                  bottleneckImpact={productionCapacityData.bottleneckImpact}
                />
              </motion.div>
              <motion.div variants={cardVariants} whileHover="hover" className={`rounded-xl shadow-md overflow-hidden ${
                theme === 'dark'
                  ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                  : 'bg-white border border-gray-100'
              }`}>
                <AssemblyStatus
                  assemblies={extendedAssemblies}
                />
              </motion.div>
            </div>
          </motion.div>

          {/* Recent Activity / Top Products */}
          <motion.div
            variants={cardVariants}
            className={`rounded-xl shadow-md overflow-hidden ${
              theme === 'dark'
                ? 'bg-gradient-to-br from-dark-800 to-dark-700 border border-dark-700'
                : 'bg-white border border-gray-100'
            }`}
          >
            <div className="p-5">
              <h3 className="text-lg font-semibold mb-4">Top Items by Value</h3>
              <ProductsTable products={topItemsByValue} simple={true} />
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleViewCriticalItems}
                className="mt-4 w-full py-2 px-4 bg-gray-100 dark:bg-dark-700 hover:bg-gray-200 dark:hover:bg-dark-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors"
              >
                View All Items
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default DashboardPage;