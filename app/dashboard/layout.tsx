import React from 'react';
import MainLayoutServer from '../(main)/MainLayoutServer';
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Dashboard - Inventory Management System",
  description: "Overview of inventory status, orders, and key metrics",
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MainLayoutServer>
      {children}
    </MainLayoutServer>
  );
}