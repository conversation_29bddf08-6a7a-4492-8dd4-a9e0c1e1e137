/**
 * Theme Variables CSS
 * This file defines all theme variables for consistent styling across the application
 */

:root {
  /* Base colors */
  --primary-yellow: #FFEB3B;
  --primary-orange: #FF9800;
  --primary-black: #212121;
  --primary-blue: #1274F3;
  --primary-pink: #EC3A76;
  --primary-mint: #4BFFB2;

  /* Light theme variables */
  --L-bg-primary: #FFFFFF;
  --L-bg-secondary: #F9FAFB;
  --L-bg-tertiary: #F3F4F6;
  --L-text-primary: #111827;
  --L-text-secondary: #4B5563;
  --L-text-tertiary: #6B7280;
  --L-border-primary: #E5E7EB;
  --L-border-secondary: #D1D5DB;
  --L-accent-primary: #1274F3;
  --L-accent-secondary: #3B82F6;
  --L-hover: #F3F4F6;
  --L-focus-ring: rgba(59, 130, 246, 0.5);
  
  /* Status colors - Light */
  --L-success: #10B981;
  --L-success-bg: #D1FAE5;
  --L-warning: #F59E0B;
  --L-warning-bg: #FEF3C7;
  --L-error: #EF4444;
  --L-error-bg: #FEE2E2;
  --L-info: #3B82F6;
  --L-info-bg: #DBEAFE;

  /* Dark theme variables */
  --T-bg-primary: #1E1E1E;
  --T-bg-sidebar: #2D2D2D;
  --T-bg-card: #333333;
  --T-text-primary: #F0F0F0;
  --T-text-secondary: #A0A0A0;
  --T-text-headings: #FFFFFF;
  --T-accent-primary: #FFFFFF;
  --T-accent-active: #E0E0E0;
  --T-border-color: #444444;
  --T-border-subtle: #383838;
  --T-focus-ring: rgba(224, 224, 224, 0.4);
  --T-hover-overlay: rgba(255, 255, 255, 0.05);
  --T-hover: #3E3E3E;
  
  /* Status colors - Dark */
  --T-success: #4BFFB2;
  --T-success-bg: rgba(75, 255, 178, 0.2);
  --T-warning: #FBC02D;
  --T-warning-bg: rgba(251, 192, 45, 0.2);
  --T-error: #EC3A76;
  --T-error-bg: rgba(236, 58, 118, 0.2);
  --T-info: #E0E0E0;
  --T-info-bg: rgba(224, 224, 224, 0.2);
}

/* Light theme (default) */
:root {
  --bg-primary: var(--L-bg-primary);
  --bg-secondary: var(--L-bg-secondary);
  --bg-tertiary: var(--L-bg-tertiary);
  --text-primary: var(--L-text-primary);
  --text-secondary: var(--L-text-secondary);
  --text-tertiary: var(--L-text-tertiary);
  --border-primary: var(--L-border-primary);
  --border-secondary: var(--L-border-secondary);
  --accent-primary: var(--L-accent-primary);
  --accent-secondary: var(--L-accent-secondary);
  --hover: var(--L-hover);
  --focus-ring: var(--L-focus-ring);
  
  /* Status colors */
  --success: var(--L-success);
  --success-bg: var(--L-success-bg);
  --warning: var(--L-warning);
  --warning-bg: var(--L-warning-bg);
  --error: var(--L-error);
  --error-bg: var(--L-error-bg);
  --info: var(--L-info);
  --info-bg: var(--L-info-bg);
}

/* Dark theme */
.dark {
  --bg-primary: var(--T-bg-primary);
  --bg-secondary: var(--T-bg-sidebar);
  --bg-tertiary: var(--T-bg-card);
  --text-primary: var(--T-text-primary);
  --text-secondary: var(--T-text-secondary);
  --text-tertiary: var(--T-text-secondary);
  --border-primary: var(--T-border-color);
  --border-secondary: var(--T-border-subtle);
  --accent-primary: var(--T-accent-primary);
  --accent-secondary: var(--T-accent-active);
  --hover: var(--T-hover);
  --focus-ring: var(--T-focus-ring);
  
  /* Status colors */
  --success: var(--T-success);
  --success-bg: var(--T-success-bg);
  --warning: var(--T-warning);
  --warning-bg: var(--T-warning-bg);
  --error: var(--T-error);
  --error-bg: var(--T-error-bg);
  --info: var(--T-info);
  --info-bg: var(--T-info-bg);
}

/* Utility classes for theme-aware styling */
.bg-theme-primary {
  background-color: var(--bg-primary);
}

.bg-theme-secondary {
  background-color: var(--bg-secondary);
}

.bg-theme-tertiary {
  background-color: var(--bg-tertiary);
}

.text-theme-primary {
  color: var(--text-primary);
}

.text-theme-secondary {
  color: var(--text-secondary);
}

.text-theme-tertiary {
  color: var(--text-tertiary);
}

.border-theme-primary {
  border-color: var(--border-primary);
}

.border-theme-secondary {
  border-color: var(--border-secondary);
}

.accent-theme-primary {
  color: var(--accent-primary);
}

.accent-theme-secondary {
  color: var(--accent-secondary);
}

.hover\:bg-theme-hover:hover {
  background-color: var(--hover);
}

.focus\:ring-theme-focus:focus {
  --tw-ring-color: var(--focus-ring);
}

/* Status utility classes */
.text-theme-success {
  color: var(--success);
}

.bg-theme-success {
  background-color: var(--success);
}

.bg-theme-success-light {
  background-color: var(--success-bg);
}

.text-theme-warning {
  color: var(--warning);
}

.bg-theme-warning {
  background-color: var(--warning);
}

.bg-theme-warning-light {
  background-color: var(--warning-bg);
}

.text-theme-error {
  color: var(--error);
}

.bg-theme-error {
  background-color: var(--error);
}

.bg-theme-error-light {
  background-color: var(--error-bg);
}

.text-theme-info {
  color: var(--info);
}

.bg-theme-info {
  background-color: var(--info);
}

.bg-theme-info-light {
  background-color: var(--info-bg);
}
