# MongoDB Schema Refactoring Checklist

This checklist outlines the necessary steps and code changes to align the current IMS codebase with the updated MongoDB schema. It covers models, services, API endpoints, database migration, testing, and documentation.

## I. Server-Side Changes

### A. Models (`app/models/`)

For each model, ensure the following:
- Fields match the new schema (additions, removals, renames, type changes).
- Validation rules are updated (e.g., `required`, `unique`, `enum`, `minlength`, `maxlength`, custom validators).
- Indexes are updated (e.g., new unique indexes, compound indexes).
- Timestamps (`createdAt`, `updatedAt`) are consistently handled.

1.  **Part Model (`part.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key, unique MongoDB identifier).
    -   [x] Field `partNumber`: Type `String`, `unique`, `required` (Unique business identifier for the part, indexed).
    -   [x] Field `name`: Type `String`, `required`.
    -   [x] Field `description`: Type `String | Null`.
    -   [x] Field `categoryId`: Type `ObjectId | Null`, `ref: 'categories._id'`.
    -   [x] Field `unitOfMeasure`: Type `String`, `required` (e.g., "pcs", "kg", "meter").
    -   [x] Field `cost`: Type `Decimal128`, `required`. (Now `costPrice`: `Number`, aligned with target)
    -   [x] Field `preferredSupplierId`: Type `ObjectId | Null`, `ref: 'suppliers._id'`. (Now `supplierId`, aligned with target)
    -   [x] Field `technicalSpecifications`: Type `String | Null`. (Now `technical_specs`, aligned with target)
    -   [x] Field `isManufactured`: Type `Boolean`, `default: false`. (Now `is_manufactured`, aligned with target)
    -   [x] Field `reorderPoint`: Type `Int32 | Null`. (Now `reorder_level`, aligned with target)
    -   [x] Field `status`: Type `String`, enum (`active`, `obsolete`, `in_development`, `pending_approval`), `required`. (Enums maintained, aligned with target structure)
    -   [x] Field `dimensions`: Object (`length`: `Decimal128`, `width`: `Decimal128`, `height`: `Decimal128`, `unit`: `String` (e.g., "mm", "in")). (Removed as per target schema)
    -   [x] Field `weight`: Object (`value`: `Decimal128`, `unit`: `String` (e.g., "kg", "lb")). (Removed as per target schema)
    -   [x] Field `material`: Type `String | Null`. (Removed as per target schema)
    -   [x] Field `revision`: Type `String | Null`. (Removed as per target schema)
    -   [x] Field `customFields`: Array of Objects (`fieldName`: `String`, `fieldValue`: `String`). (Removed as per target schema)
    -   [x] Field `imageURLs`: Array of `String`. (Removed as per target schema)
    -   [x] Field `documents`: Array of Objects (`documentName`: `String`, `url`: `String`, `uploadedAt`: `Date`). (Removed as per target schema)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all changed/added fields (e.g., `required`, `unique`, enums, types). (Completed in new model)
    -   [x] Ensure indexes on `partNumber` (unique) and potentially `name`, `categoryId`, `status`. (Completed, including text index)
    -   [x] Remove deprecated fields if any persist from old schema not covered by renames/updates. (Completed, new `inventory` object added)

2.  **Warehouse Model (`warehouse.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `warehouseCode`: Type `String`, `unique`, `required` (Unique business identifier). (Now `location_id`, aligned with target)
    -   [x] Field `name`: Type `String`, `required`.
    -   [x] Field `location`: Type `String` (Descriptive location details).
    -   [x] Field `capacity`: Type `Int32` (Storage capacity).
    -   [x] Field `manager`: Type `String` (Name of the manager responsible).
    -   [x] Field `contact`: Type `String` (Contact information for the warehouse).
    -   [x] Field `isBinTracked`: Type `Boolean`.
    -   [x] Field `createdAt`: Type `Date`. (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date`. (Handled by Mongoose timestamps)
    -   [x] Update validation rules. (Completed in new model)

3.  **Supplier Model (`supplier.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `supplierCode`: Type `String`, `unique`, `required` (Unique business identifier). (Now `supplier_id`, aligned with target)
    -   [x] Field `name`: Type `String`, `required`.
    -   [x] Field `contactName`: Type `String | Null`. (Now `contactPerson: String`, aligned with target)
    -   [x] Field `contactEmail`: Type `String | Null`, `unique` (if provided). (Now `email: String`, unique, aligned with target)
    -   [x] Field `contactPhone`: Type `String | Null`. (Now `phone: String`, aligned with target)
    -   [x] Field `address`: Object with: (Now `address: String`, aligned with target)
        -   [x] `street`: Type `String`.
        -   [x] `city`: Type `String`.
        -   [x] `state`: Type `String`.
        -   [x] `postalCode`: Type `String`.
        -   [x] `country`: Type `String`.
    -   [x] Field `rating`: Type `Int32 | Null` (e.g., 1-5 stars). (Now `rating: Number | null`, aligned with target)
    -   [x] Field `paymentTerms`: Type `String | Null`. (Now `payment_terms`, aligned with target)
    -   [x] Field `notes`: Type `String | Null`. (Removed as per target schema, `specialty` and `delivery_terms` added)
    -   [x] Field `status`: Type `String`, enum (`active`, `inactive`, `preferred`), `required`. (Now `is_active: Boolean`, aligned with target)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model)
    -   [x] Ensure indexes on `supplierCode` (unique) and potentially `name`, `status`. (Indexes updated for `supplier_id` and `is_active`)

4.  **Purchase Order Model (`purchaseorder.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `poNumber`: Type `String`, `unique`, `required`. (Now `po_number`, aligned with target)
    -   [x] Field `supplierId`: Type `ObjectId`, `ref: 'suppliers._id'`, `required`. (Now `supplier_id`, aligned with target)
    -   [x] Field `orderDate`: Type `Date`, `default: Date.now`, `required`. (Now `order_date`, aligned with target)
    -   [x] Field `expectedDeliveryDate`: Type `Date | Null`. (Now `expected_delivery_date`, aligned with target)
    -   [x] Field `items`: Array of Objects (`partId`: `ObjectId`, `ref: 'parts._id'`, `description`: `String`, `quantity`: `Int32`, `unitPrice`: `Decimal128`, `lineTotal`: `Decimal128`, `receivedQuantity`: `Int32`, `default: 0`). (Structure updated to `item_id`, `item_type`, `quantity_ordered`, `unit_price`, `total_price`, etc. as per target)
    -   [x] Field `totalAmount`: Type `Decimal128`, `required`. (Now `total_amount`, calculation logic updated, aligned with target which also has `sub_total`, `tax_amount`, `shipping_cost`)
    -   [x] Field `status`: Type `String`, enum (`draft`, `pending_approval`, `approved`, `ordered`, `partially_received`, `fully_received`, `cancelled`, `closed`), `required`. (Enum values aligned with target)
    -   [x] Field `shippingAddress`: Object (copied from Supplier or specified) with `street`, `city`, `state`, `postalCode`, `country`. (Now `shipping_address: String | Null`, aligned with target)
    -   [x] Field `billingAddress`: Object (copied from Supplier or specified) with `street`, `city`, `state`, `postalCode`, `country`. (Now `billing_address: String | Null`, aligned with target)
    -   [x] Field `termsAndConditions`: Type `String | Null`. (Now `terms_and_conditions`, aligned with target)
    -   [x] Field `notes`: Type `String | Null`. (Aligned with target)
    -   [x] Field `createdBy`: Type `ObjectId`, `ref: 'users._id'`, `required`. (Now `created_by`, aligned with target)
    -   [x] Field `approvedBy`: Type `ObjectId | Null`, `ref: 'users._id'`. (Now `approved_by`, aligned with target)
    -   [x] Field `approvalDate`: Type `Date | Null`. (Now `approval_date`, aligned with target)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model, including new fields: `actual_delivery_date`, `sub_total`, `tax_amount`, `shipping_cost`, `payment_terms`, `attachments`, `history`, `tags`)
    -   [x] Ensure indexes on `poNumber` (unique), `supplierId`, `status`, `orderDate`. (Indexes updated for `po_number`, `supplier_id`, `status`, `order_date`, `items.item_id`, `tags`)

5.  **Product Model (`product.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `productCode`: Type `String`, `unique`, `required`. (Now `product_id`, aligned with target)
    -   [x] Field `name`: Type `String`, `required`. (Aligned)
    -   [x] Field `description`: Type `String | Null`. (Aligned)
    -   [x] Field `categoryId`: Type `ObjectId | Null`, `ref: 'categories._id'`. (Now `category_id`, aligned)
    -   [x] Field `sellingPrice`: Type `Decimal128`, `required`. (Now `price: Number`, aligned with target)
    -   [x] Field `status`: Type `String`, enum (`active`, `discontinued`, `in_development`, `pending_approval`), `required`. (Enum values updated to `active`, `inactive`, `draft`, `archived` as per target)
    -   [x] Field `billOfMaterials`: Array of Objects (`componentId`: `ObjectId` (ref: `parts._id` or `assemblies._id`), `componentType`: `String` (enum: `Part`, `Assembly`), `quantity`: `Int32`, `unitOfMeasure`: `String`). (Component fields renamed to `item_id`, `item_type`, `unit_of_measure` as per target)
    -   [x] Field `imageURLs`: Array of `String`. (Replaced with `images: Array<Object>` with `url`, `alt_text` as per target)
    -   [x] Field `tags`: Array of `String`. (Now `tags?: string[] | null`, aligned)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model, including new fields: `cost`, `sku`, `barcode`, `dimensions`, `weight`, `attributes`)
    -   [x] Ensure indexes on `productCode` (unique) and potentially `name`, `categoryId`, `status`. (Indexes updated for `product_id`, `sku`, `barcode`, `name`, `category_id`, `status`, `tags`)

6.  **Category Model (`category.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `name`: Type `String`, `unique`, `required`.
    -   [x] Field `description`: Type `String | Null`.
    -   [x] Field `parentCategoryId`: Type `ObjectId | Null`, `ref: 'categories._id'` (for subcategories). (Now `parentCategory`, aligned with target)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model)
    -   [x] Ensure index on `name` (unique) and `parentCategoryId`. (Index on `parentCategory` now, aligned with target)

7.  **Inventory Transactions Model (`inventorytransaction.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `partId`: Type `ObjectId`, `ref: 'parts._id'`, `required`.
    -   [x] Field `warehouseId`: Type `ObjectId`, `ref: 'warehouses._id'`, `required`.
    -   [x] Field `type`: Type `String`, enum (`purchase_receipt`, `production_output`, `production_consumption`, `sales_shipment`, `stock_adjustment`, `internal_transfer_out`, `internal_transfer_in`), `required`. (Now `transactionType` with schema values)
    -   [x] Field `quantityChanged`: Type `Int32`, `required` (positive for stock in, negative for stock out). (Now `quantity`)
    -   [x] Field `stockOnHandBefore`: Type `Int32`, `required`. (Now `previousStock`)
    -   [x] Field `stockOnHandAfter`: Type `Int32`, `required`. (Now `newStock`)
    -   [x] Field `transactionDate`: Type `Date`, `default: Date.now`, `required`.
    -   [x] Field `referenceId`: Type `ObjectId | Null` (e.g., PO, WO, SO, Adjustment ID). (Now `referenceNumber: String | Null`)
    -   [x] Field `referenceModel`: Type `String | Null` (e.g., `PurchaseOrder`, `WorkOrder`, `SalesOrder`, `StockAdjustment`). (Now `referenceType: String | Null`)
    -   [x] Field `userId`: Type `ObjectId`, `ref: 'users._id'`, `required` (User performing the transaction).
    -   [x] Field `notes`: Type `String | Null`.
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed)
    -   [x] Ensure indexes on `partId`, `warehouseId`, `transactionDate`, `type`, `referenceId`. (Completed, adapted to new field names)

8.  **Work Order Model (`workorder.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `woNumber`: Type `String`, `unique`, `required`.
    -   [x] Field `assemblyId`: Type `ObjectId | Null`, `ref: 'assemblies._id'` (if producing an assembly).
    -   [x] Field `partIdToManufacture`: Type `ObjectId | Null`, `ref: 'parts._id'` (if manufacturing a specific part).
    -   [x] Field `productId`: Type `ObjectId | Null`, `ref: 'products._id'` (if the WO is to produce a saleable product).
    -   [x] Field `quantity`: Type `Int32`, `required`.
    -   [x] Field `status`: Type `String`, enum (`planned`, `released`, `in_progress`, `completed`, `on_hold`, `cancelled`, `closed`), `required`. (Enum updated)
    -   [x] Field `priority`: Type `String`, enum (`low`, `medium`, `high`, `urgent`), `default: 'medium'`.
    -   [x] Field `dueDate`: Type `Date`, `required`.
    -   [x] Field `startDate`: Type `Date | Null`.
    -   [x] Field `completedAt`: Type `Date | Null`.
    -   [x] Field `assignedToUserId`: Type `ObjectId | Null`, `ref: 'users._id'`. (Renamed to `assignedTo`)
    -   [x] Field `notes`: Type `String | Null`.
    -   [x] Field `sourceDemand`: Type `String | Null` (e.g., "SalesOrder:SO2025-105", "StockReplenishment", "Project:XYZ").
    -   [x] Field `billOfMaterialsSnapshot`: Array of Objects (`partId`: `ObjectId`, `quantityRequired`: `Int32`, `unitOfMeasure`: `String`). (Kept as is)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Remove deprecated fields (e.g., `quantityToProduce`, `expectedCompletionDate`, `actualCompletionDate`). (Assumed covered by current fieldset)
    -   [x] Update validation rules for all fields. (Completed, kept existing BOM validation)
    -   [x] Ensure indexes on `woNumber` (unique), `status`, `dueDate`, `priority`, `assignedToUserId`. (Completed, adapted to new field name `assignedTo`)

9.  **Shipment Model (`shipment.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `shipmentNumber`: Type `String`, `unique`, `required`.
    -   [x] Field `salesOrderId`: Type `ObjectId`, `ref: 'salesorders._id'`, `required`.
    -   [x] Field `shipmentDate`: Type `Date`, `required`.
    -   [x] Field `carrierName`: Type `String | Null`.
    -   [x] Field `trackingNumber`: Type `String | Null`.
    -   [x] Field `status`: Type `String`, enum (`pending`, `processing`, `shipped`, `in_transit`, `delivered`, `cancelled`, `failed_delivery`), `required`. (Enum updated)
    -   [x] Field `estimatedDeliveryDate`: Type `Date | Null`.
    -   [x] Field `actualDeliveryDate`: Type `Date | Null`.
    -   [x] Field `itemsShipped`: Array of Objects (`salesOrderItemId`: `ObjectId` (ref to item in SO), `productId`: `ObjectId`, `ref: 'products._id'`, `quantityShipped`: `Int32`, `notes`: `String | Null`). (Structure aligned)
    -   [x] Field `shippingAddress`: Object (copied from Sales Order or specified) with `street`, `city`, `state`, `postalCode`, `country`. (Kept structured object)
    -   [x] Field `notes`: Type `String | Null`.
    -   [x] Field `shippedByUserId`: Type `ObjectId`, `ref: 'users._id'`.
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed)
    -   [x] Ensure indexes on `shipmentNumber` (unique), `salesOrderId`, `status`, `shipmentDate`. (Completed)

10. **Quality Check Model (`qualitycheck.model.ts`)**
    -   [ ] **Note: Corresponding collection missing in target schema (`@database_schema_updated.md`). Marked as pending.**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `qcNumber`: Type `String`, `unique`, `required`.
    -   [x] Field `referenceType`: Type `String`, enum (`Part`, `Assembly`, `Product`, `WorkOrder`, `PurchaseOrderReceipt`), `required`. (Enum updated)
    -   [x] Field `referenceId`: Type `ObjectId`, `required` (Refers to `parts._id`, `assemblies._id`, etc.).
    -   [x] Field `checkDate`: Type `Date`, `default: Date.now`, `required`.
    -   [x] Field `inspectorId`: Type `ObjectId`, `ref: 'users._id'`, `required`.
    -   [x] Field `status`: Type `String`, enum (`pending`, `pass`, `fail`, `rework_required`, `reworked_pass`, `reworked_fail`), `required`. (Enum updated)
    -   [x] Field `results`: Array of Objects (`checkName`: `String`, `specification`: `String`, `actualValue`: `String`, `result`: `String` (enum: `pass`, `fail`, `not_applicable`), `notes`: `String | Null`). (Structure updated)
    -   [x] Field `overallNotes`: Type `String | Null`.
    -   [x] Field `attachments`: Array of Objects (`fileName`: `String`, `url`: `String`, `uploadedAt`: `Date`). (Structure updated)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed)
    -   [x] Ensure indexes on `qcNumber` (unique), `referenceId`, `referenceType`, `status`, `checkDate`. (Completed)

11. **User Model (`user.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `username`: Type `String`, `unique`, `required`.
    -   [x] Field `passwordHash`: Type `String`, `required`. (Now `password_hash`, aligned with target)
    -   [x] Field `email`: Type `String`, `unique`, `required`.
    -   [x] Field `firstName`: Type `String | Null`. (Now `first_name: String`, required, aligned with target)
    -   [x] Field `lastName`: Type `String | Null`. (Now `last_name: String`, required, aligned with target)
    -   [x] Field `roles`: Array of `String`, `required` (e.g., `admin`, `manager`, `staff`).
    -   [x] Field `employeeId`: Type `String | Null`, `unique` (if provided). (Removed, `user_id` added as per target)
    -   [x] Field `department`: Type `String | Null`.
    -   [x] Field `jobTitle`: Type `String | Null`. (Now `job_title`, aligned with target)
    -   [x] Field `contactInfo`: Object (`phone`: `String | Null`, `extension`: `String | Null`). (Replaced with `phone_number: String | Null` as per target)
    -   [x] Field `isActive`: Type `Boolean`, `default: true`. (Now `is_active`, aligned with target)
    -   [x] Field `lastLogin`: Type `Date | Null`. (Now `last_login_date`, aligned with target)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model)
    -   [x] Ensure indexes on `username` (unique), `email` (unique), `employeeId` (unique, if applicable). (Indexes updated for `user_id`, `username`, `email`, `is_active`)

12. **Customer Model (`customer.model.ts`)**
    -   [ ] **Note: Corresponding collection missing in target schema (`@database_schema_updated.md`), though `deliveries` collection references `customerId`. Marked as pending.**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `customerCode`: Type `String`, `unique`, `required`. (Now `customer_id`, aligned with target)
    -   [x] Field `name`: Type `String`, `required`.
    -   [x] Field `contactInfo`: Object with: (Now direct fields `contact_person`, `email`, `phone` as per target)
        -   [x] `primaryContactName`: Type `String | Null`. (Now `contact_person`, aligned with target)
        -   [x] `email`: Type `String | Null`, `unique` (if provided). (Now `email: String`, unique, required, aligned with target)
        -   [x] `phone`: Type `String | Null`. (Aligned with target)
        -   [x] `department`: Type `String | Null`. (Removed, not in target contact details)
    -   [x] Field `billingAddress`: Object with `street`: `String`, `city`: `String`, `state`: `String`, `postalCode`: `String`, `country`: `String`, `required`. (Now `address: String` for primary/billing, aligned with target)
    -   [x] Field `shippingAddresses`: Array of Objects (`addressName`: `String`, `street`: `String`, `city`: `String`, `state`: `String`, `postalCode`: `String`, `country`: `String`, `isDefault`: `Boolean`). (Field names like `state_province` aligned with target)
    -   [x] Field `paymentTerms`: Type `String | Null`. (Now `payment_terms`, aligned with target)
    -   [x] Field `creditLimit`: Type `Decimal128 | Null`. (Now `credit_limit: Number | Null`, aligned with target)
    -   [x] Field `notes`: Type `String | Null`. (Aligned with target, `tax_id` also added)
    -   [x] Field `status`: Type `String`, enum (`active`, `inactive`, `on_hold`), `required`, `default: 'active'`. (Now `account_status`, aligned with target)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model)
    -   [x] Ensure indexes on `customerCode` (unique), `name`, `status`. (Indexes updated for `customer_id`, `email`, `account_status`)

13. **Batches Model (`batch.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `batchCode`: Type `String`, `unique`, `required`.
    -   [x] Field `workOrderId`: Type `ObjectId`, `ref: 'workorders._id'`, `required`.
    -   [x] Field `partId`: Type `ObjectId | Null`, `ref: 'parts._id'` (if batch is for a part).
    -   [x] Field `assemblyId`: Type `ObjectId | Null`, `ref: 'assemblies._id'` (if batch is for an assembly).
    -   [x] Field `quantityPlanned`: Type `Int32`, `required`.
    -   [x] Field `quantityProduced`: Type `Int32`, `default: 0`.
    -   [x] Field `quantityScrapped`: Type `Int32`, `default: 0`.
    -   [x] Field `startDate`: Type `Date | Null`.
    -   [x] Field `endDate`: Type `Date | Null`.
    -   [x] Field `status`: Type `String`, enum (`pending`, `in_progress`, `completed`, `paused`, `cancelled`), `required`. (Enum updated)
    -   [x] Field `notes`: Type `String | Null`.
    -   [x] Field `assignedMachine`: Type `String | Null`.
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed)
    -   [x] Ensure indexes on `batchCode` (unique), `workOrderId`, `status`. (Completed)

14. **Inventory Model (`inventory.model.ts`)** (Corresponds to `inventory_levels` collection)
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `item_id`: Type `ObjectId`, `refPath: 'item_type'`, `required`, `indexed`. (Replaces `partId`)
    -   [x] Field `item_type`: Type `String`, enum: [`Part`, `Assembly`, `Product`], `required`, `indexed`.
    -   [x] Field `warehouse_id`: Type `ObjectId`, `ref: 'Warehouse'`, `required`, `indexed`. (Replaces `warehouseId`)
    -   [x] Field `quantity_on_hand`: Type `Number`, `required`, `default: 0`, `min: 0`. (Replaces `quantity`)
    -   [x] Field `quantity_allocated`: Type `Number`, `default: 0`, `min: 0`.
    -   [x] Field `quantity_available`: Virtual (`quantity_on_hand` - `quantity_allocated`).
    -   [x] Field `location_in_warehouse`: Type `String | Null`. (Replaces `locationInWarehouse`)
    -   [x] Field `reorder_level`: Type `Number | Null`, `min: 0`. (Replaces `minStockLevel`)
    -   [x] Field `safety_stock_level`: Type `Number | Null`, `min: 0`.
    -   [x] Field `maximum_stock_level`: Type `Number | Null`, `min: 0`. (Replaces `maxStockLevel`)
    -   [x] Field `average_daily_usage`: Type `Decimal128 | Null`.
    -   [x] Field `abc_classification`: Type `String | Null`.
    -   [x] Field `last_stock_update`: Type `Date`, `default: Date.now`. (Replaces `lastRestockedDate`)
    -   [x] Field `notes`: Type `String | Null`.
    -   [x] Field `createdAt`: Type `Date` (Handled by Mongoose timestamps).
    -   [x] Field `updatedAt`: Type `Date` (Handled by Mongoose timestamps).
    -   [x] Update validation rules for all fields. (Completed in new model)
    -   [x] Create compound unique index on (`item_id`, `item_type`, `warehouse_id`). (Completed)
    -   [x] Ensure index on `last_stock_update`. (Completed)
    -   [x] Remove deprecated fields (e.g., old `status` enum). (Completed)

15. **Assembly Model (`assembly.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `assemblyCode`: Type `String`, `unique`, `required`. (Now `assembly_id`, aligned with target)
    -   [x] Field `name`: Type `String`, `required`. (Aligned)
    -   [x] Field `description`: Type `String | Null`. (Aligned)
    -   [x] Field `version`: Type `String`, `required`. (Now optional, aligned with target)
    -   [x] Field `status`: Type `String`, enum (`in_development`, `active`, `obsolete`, `pending_approval`), `required`. (Enum values updated to `active`, `inactive`, `prototype`, `obsolete` as per target)
    -   [x] Field `components`: Array of Objects (`partId`: `ObjectId`, `ref: 'parts._id'`, `quantity`: `Int32`, `unitOfMeasure`: `String`). (Structure updated: `item_id`, `item_type`, `quantity`, `unit_of_measure` as per target)
    -   [x] Field `manufacturingInstructionsURL`: Type `String | Null`. (Removed, `notes` can be used, or `attributes`)
    -   [x] Field `estimatedProductionTimeHours`: Type `Decimal128 | Null`. (Now `manufacturing_lead_time: Number | null` in days, aligned)
    -   [x] Field `imageURLs`: Array of `String`. (Replaced with `images: Array<Object>` as per target)
    -   [x] Field `customFields`: Array of Objects (`fieldName`: `String`, `fieldValue`: `String`). (Replaced with `attributes: Array<Object>` as per target)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model, including new fields: `type`, `cost`, `notes`)
    -   [x] Ensure indexes on `assemblyCode` (unique), `name`, `status`. (Indexes updated for `assembly_id`, `name`, `status`)

16. **Sales Order Model (`salesorder.model.ts`)**
    -   [ ] **Note: Corresponding collection missing in target schema (`@database_schema_updated.md`). Marked as pending.**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `soNumber`: Type `String`, `unique`, `required`. (Now `so_number`, aligned with target)
    -   [x] Field `customerId`: Type `ObjectId`, `ref: 'customers._id'`, `required`. (Now `customer_id`, aligned with target)
    -   [x] Field `orderDate`: Type `Date`, `default: Date.now`, `required`. (Now `order_date`, aligned with target)
    -   [x] Field `status`: Type `String`, enum (`draft`, `pending_approval`, `approved`, `processing`, `partially_shipped`, `fully_shipped`, `completed`, `cancelled`, `on_hold`), `required`. (Enum values aligned with target)
    -   [x] Field `items`: Array of Objects (`productId`: `ObjectId`, `ref: 'products._id'`, `quantityOrdered`: `Int32`, `unitPrice`: `Decimal128`, `discount`: `Decimal128 | Null`, `lineTotal`: `Decimal128`, `notes`: `String | Null`). (Structure updated to `item_id`, `item_type`, `quantity_ordered`, `unit_price`, `discount_percentage`, `discount_amount`, `tax_rate_percentage`, `tax_amount_item`, `total_price`, etc. as per target)
    -   [x] Field `subTotal`: Type `Decimal128`, `required`. (Now `sub_total`, calculation logic updated, aligned with target)
    -   [x] Field `taxAmount`: Type `Decimal128`, `default: 0`. (Now `tax_total`, aligned with target)
    -   [x] Field `shippingCost`: Type `Decimal128`, `default: 0`. (Now `shipping_cost`, aligned with target)
    -   [x] Field `totalAmount`: Type `Decimal128`, `required`. (Now `total_amount`, calculation logic updated, aligned with target)
    -   [x] Field `shippingAddress`: Object (copied from Customer or specified) with `street`, `city`, `state`, `postalCode`, `country`. (Now `shipping_address_id` and `shipping_address_details`, aligned with target)
    -   [x] Field `billingAddress`: Object (copied from Customer or specified) with `street`, `city`, `state`, `postalCode`, `country`. (Now `billing_address_details`, aligned with target)
    -   [x] Field `notes`: Type `String | Null`. (Aligned with target)
    -   [x] Field `salesRepId`: Type `ObjectId | Null`, `ref: 'users._id'`. (Now `sales_rep_id`, aligned with target)
    -   [x] Field `expectedDeliveryDate`: Type `Date | Null`. (Now `expected_shipment_date`, aligned with target, `actual_shipment_date` also added)
    -   [x] Field `paymentStatus`: Type `String`, enum (`pending`, `partially_paid`, `fully_paid`, `refunded`), `default: 'pending'`. (Enum values aligned, `payment_method` and `transaction_id` added)
    -   [x] Field `createdAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Field `updatedAt`: Type `Date` (timestamp). (Handled by Mongoose timestamps)
    -   [x] Update validation rules for all fields. (Completed in new model, including new fields `attachments`, `history`, `tags`)
    -   [x] Ensure indexes on `soNumber` (unique), `customerId`, `status`, `orderDate`. (Indexes added for `so_number`, `customer_id`, `status`, `order_date`, `sales_rep_id`, `payment_status`, `tags`)

17. **Settings Model (`setting.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `key`: Type `String`, `unique`, `required`.
    -   [x] Field `value`: Type `String`, `required`. (Type changed)
    -   [x] Field `description`: Type `String`.
    -   [x] Field `dataType`: Type `String`, enum (`string`, `integer`, `boolean`, `json`). (Added)
    -   [x] Field `group`: Type `String`. (Made required)
    -   [x] Field `lastModifiedBy`: Type `ObjectId`, `ref: 'User'`, `required`. (Added)
    -   [x] Field `lastModifiedAt`: Type `Date`, `required`. (Added, Mongoose updatedAt disabled)
    -   [x] Field `isSystemEditableOnly`: Type `Boolean`. (Added)
    -   [x] Create index on `key` field. (Completed)
    -   [x] Update validation rules. (Completed)

18. **System Logs Model (`systemlog.model.ts`)**
    -   [x] Field `_id`: Type `ObjectId` (Primary Key).
    -   [x] Field `timestamp`: Type `Date`, `required`. (Handled by Mongoose `createdAt: 'timestamp'`)
    -   [x] Field `eventType`: Type `String`, `required`. (Added)
    -   [x] Field `level`: Type `String`, enum (`INFO`, `WARN`, `ERROR`, `DEBUG`, `FATAL`). (Enum values uppercased)
    -   [x] Field `message`: Type `String`, `required`.
    -   [x] Field `source`: Type `String`, `required`. (Renamed from `serviceContext`)
    -   [x] Field `details`: Type `Object`.
    -   [x] Field `userId`: Type `ObjectId | Null`, `ref: 'User'`.
    -   [x] Field `correlationId`: Type `String | Null`. (Added)
    -   [x] Create indexes on `timestamp`, `eventType`, and `level` fields. (Completed, also indexed `source`, `userId`, `correlationId`)
    -   [x] Update validation rules. (Completed)

### B. Services (`app/services/`)

For each service corresponding to a model or business logic area:
-   [ ] Update data access logic to use new field names and types.
-   [ ] Adapt business logic to handle new/changed fields and relationships.
-   [ ] Modify Data Transfer Objects (DTOs) if used.
-   [ ] Ensure error handling is robust for schema changes.

1.  **Part Service (`part.service.ts`)**
2.  **Warehouse Service (`warehouse.service.ts`)**
3.  **Supplier Service (`supplier.service.ts`)**
4.  **Category Service (`category.service.ts`)**
5.  **Product Service (`product.service.ts`)**
6.  **Assembly Service (`assembly.service.ts`)**
7.  **Inventory Service (`inventory.service.ts`)** (manages `inventory` collection for stock levels)
8.  **Inventory Transaction Service (`inventorytransaction.service.ts`)** (manages `inventorytransactions` collection)
9.  **Purchase Order Service (`purchaseorder.service.ts`)**
10. **Sales Order Service (`salesorder.service.ts`)**
11. **Work Order Service (`workorder.service.ts`)**
12. **Batches Service (`batch.service.ts`)**
13. **Shipment Service (`shipment.service.ts`)**
14. **Quality Check Service (`qualitycheck.service.ts`)**
15. **Customer Service (`customer.service.ts`)**
16. **User Service / Auth Service (`user.service.ts`, `auth.service.ts`)**
17. **Settings Service (`setting.service.ts`)** (if applicable for CRUD operations)

### C. API Endpoints (`app/controllers/` or `app/routes/`)

For each API endpoint:
-   [ ] Update request payloads (validation, structure).
-   [ ] Update response payloads (structure, field names).
-   [ ] Adjust controller logic to interact with updated services.
-   [ ] Ensure API versioning strategy is considered if breaking changes are significant.

1.  **Part Endpoints (`part.controller.ts`)**
2.  **Warehouse Endpoints (`warehouse.controller.ts`)**
3.  **Supplier Endpoints (`supplier.controller.ts`)**
4.  **Category Endpoints (`category.controller.ts`)**
5.  **Product Endpoints (`product.controller.ts`)**
6.  **Assembly Endpoints (`assembly.controller.ts`)**
7.  **Inventory Endpoints (`inventory.controller.ts`)** (for `inventory` collection stock levels)
8.  **Inventory Transaction Endpoints (`inventorytransaction.controller.ts`)** (for `inventorytransactions` collection)
9.  **Purchase Order Endpoints (`purchaseorder.controller.ts`)**
10. **Sales Order Endpoints (`salesorder.controller.ts`)**
11. **Work Order Endpoints (`workorder.controller.ts`)**
12. **Batches Endpoints (`batch.controller.ts`)**
13. **Shipment Endpoints (`shipment.controller.ts`)**
14. **Quality Check Endpoints (`qualitycheck.controller.ts`)**
15. **Customer Endpoints (`customer.controller.ts`)**
16. **User/Auth Endpoints (`user.controller.ts`, `auth.controller.ts`)**
17. **Settings Endpoints (`setting.controller.ts`)** (if applicable for CRUD operations)

## II. Client-Side Changes (If Applicable)

-   [ ] **Data Fetching Logic:** Update API calls to match new endpoint structures and payloads.
-   [ ] **UI Components:** Adapt components to display new/changed fields, handle new data structures.
-   [ ] **Forms & Input Validation:** Modify forms for new fields, update client-side validation.
-   [ ] **State Management:** Update client-side state (e.g., Redux, Zustand, Context API) to reflect schema changes.

## IV. Testing

### A. Unit Tests

-   [ ] Update tests for models (validation, methods).
-   [ ] Update tests for services (business logic, data transformation).
-   [ ] Update tests for controllers/API handlers (request/response handling).

### B. Integration Tests

-   [ ] Update tests for API endpoints (payloads, responses, status codes).
-   [ ] Update tests for cross-service interactions.
-   [ ] Test data integrity after simulated migrations.

### C. End-to-End (E2E) Tests

-   [ ] Update E2E scenarios to reflect UI changes and new data flows.
-   [ ] Verify critical user workflows with the new schema.

## V. Utilities & Libraries

-   [ ] Identify and update any utility functions, shared libraries, or reporting tools that consume or produce data based on the old schema.

## VI. Documentation

-   [ ] **API Documentation:** Update Swagger/OpenAPI specifications.
-   [ ] **Internal Design Documents:** Reflect schema changes in system architecture and data model diagrams.
-   [ ] **User Guides/Manuals:** Update if UI/UX is impacted or new features are introduced due to schema changes.
-   [ ] **Developer Onboarding Docs:** Ensure new schema is documented for new team members.

## VII. Rollback Plan

-   [ ] **Strategy Definition:**
    -   [ ] Option 1: Database snapshot restore (requires downtime).
    -   [ ] Option 2: Revert migration scripts (if reversible transformations were used).
    -   [ ] Option 3: Shadow-write to old schema during transition (complex, allows zero-downtime rollback of application code).
-   [ ] **Code Reversion:** Have previous application code version ready for deployment.
-   [ ] **Data Reversion Scripts:** Prepare scripts to undo data transformations if possible (e.g., `$rename` back, `$unset` new fields).
-   [ ] **Communication Plan:** How to communicate rollback to stakeholders/users.
-   [ ] **Trigger Conditions:** Define what conditions would trigger a rollback.

## VIII. Open Questions / Assumptions

-   [ ] Confirmation: `User` and `Customer` collections are now explicitly part of the schema and checklist.
-   [ ] Question: Specific GeoJSON library/usage for `warehouse.location.coordinates`? (e.g., `mongoose-geojson-schema` or native MongoDB operators for indexing and querying).
-   [ ] Question: Are there any existing data anomalies that need to be cleaned up before/during migration (e.g., inconsistent enums, missing required fields in old data that are now required)?
-   [ ] Question: Performance implications of new indexes or complex queries on large datasets? Plan for load testing critical queries.
-   [ ] Question: Strategy for validating `referenceModel` in `InventoryTransactions` (e.g., strict enum based on possible source collections, dynamic check against collection names)?
-   [ ] Question: How will the `billOfMaterialsSnapshot` in `WorkOrderModel` be populated and maintained? Is it a deep copy at Work Order creation, and how are updates handled if the source BOM changes?
-   [ ] Question: Clarify if `Settings Model` requires dedicated service/controller for user-facing management, or if it's primarily for internal configuration.

This checklist provides a comprehensive guide. Adjust and add specific tasks based on the unique complexities of the Trend IMS application.



