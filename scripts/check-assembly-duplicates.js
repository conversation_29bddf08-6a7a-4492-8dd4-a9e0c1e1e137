/**
 * <PERSON><PERSON><PERSON> to check for duplicate assemblyCode values in the database
 * Also checks for legacy assembly_id duplicates for backward compatibility
 * Run with: node scripts/check-assembly-duplicates.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
async function connectToDatabase() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Define Assembly schema using canonical field names
const assemblySchema = new mongoose.Schema({
  assemblyCode: { type: String, required: true, unique: true, index: true },
  name: { type: String, required: true },
  description: String,
  status: {
    type: String,
    enum: ['active', 'pending_review', 'in_production', 'obsolete'],
    default: 'active'
  },
  partsRequired: [{
    partId: { type: String, required: true },
    quantityRequired: { type: Number, required: true, min: 1 },
    unitOfMeasure: { type: String, default: 'ea' }
  }],
  // Legacy fields for backward compatibility
  assembly_id: { type: String, index: true, sparse: true },
  assembly_stage: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Create Assembly model
const Assembly = mongoose.model('Assembly', assemblySchema);

// Check for duplicate assemblyCode values
async function checkDuplicateAssemblyIds() {
  try {
    console.log('Checking for duplicate assemblyCode values...');

    // Find duplicate assemblyCode values using aggregation
    const duplicates = await Assembly.aggregate([
      { $group: { _id: "$assemblyCode", count: { $sum: 1 }, ids: { $push: "$_id" } } },
      { $match: { count: { $gt: 1 } } },
      { $sort: { count: -1 } }
    ]);

    if (duplicates.length === 0) {
      console.log('No duplicate assemblyCode values found.');
    } else {
      console.log(`Found ${duplicates.length} duplicate assemblyCode values:`);

      // Print details of each duplicate
      for (const dup of duplicates) {
        console.log(`\nAssembly Code: ${dup._id} (${dup.count} occurrences)`);

        // Get full details of each duplicate assembly
        const assemblies = await Assembly.find({ assemblyCode: dup._id }).lean();

        assemblies.forEach((assembly, index) => {
          console.log(`\n  Duplicate #${index + 1}:`);
          console.log(`  MongoDB _id: ${assembly._id}`);
          console.log(`  Name: ${assembly.name}`);
          console.log(`  Created: ${assembly.createdAt}`);
          console.log(`  Parts count: ${assembly.partsRequired?.length || 0}`);
        });
      }
    }

    // Also check for legacy assembly_id duplicates for backward compatibility
    console.log('\nChecking for duplicate legacy assembly_id values...');

    // Find duplicate assembly_id values using aggregation
    const legacyDuplicates = await Assembly.aggregate([
      { $match: { assembly_id: { $exists: true } } }, // Only check documents with assembly_id field
      { $group: { _id: "$assembly_id", count: { $sum: 1 }, ids: { $push: "$_id" } } },
      { $match: { count: { $gt: 1 } } },
      { $sort: { count: -1 } }
    ]);

    if (legacyDuplicates.length === 0) {
      console.log('No duplicate legacy assembly_id values found.');
    } else {
      console.log(`Found ${legacyDuplicates.length} duplicate legacy assembly_id values:`);

      // Print details of each duplicate
      for (const dup of legacyDuplicates) {
        console.log(`\nLegacy Assembly ID: ${dup._id} (${dup.count} occurrences)`);

        // Get full details of each duplicate assembly
        const assemblies = await Assembly.find({ assembly_id: dup._id }).lean();

        assemblies.forEach((assembly, index) => {
          console.log(`\n  Duplicate #${index + 1}:`);
          console.log(`  MongoDB _id: ${assembly._id}`);
          console.log(`  Name: ${assembly.name}`);
          console.log(`  Created: ${assembly.createdAt}`);
          console.log(`  Parts count: ${assembly.partsRequired?.length || 0}`);
        });
      }
    }
  } catch (error) {
    console.error('Error checking for duplicates:', error);
  }
}

// Check index status
async function checkIndexes() {
  try {
    console.log('\nChecking indexes on Assembly collection...');
    const indexes = await Assembly.collection.indexes();

    console.log('Indexes:');
    indexes.forEach(index => {
      console.log(`- ${JSON.stringify(index.key)} (name: ${index.name}, unique: ${!!index.unique})`);
    });

    // Check if assemblyCode has a unique index
    const hasUniqueAssemblyCodeIndex = indexes.some(
      index => index.key.assemblyCode === 1 && index.unique === true
    );

    if (hasUniqueAssemblyCodeIndex) {
      console.log('\nThe assemblyCode field has a unique index as expected.');
    } else {
      console.log('\nWARNING: The assemblyCode field does not have a unique index!');
      console.log('This could be causing the duplicate ID issues.');
    }

    // Also check for legacy assembly_id index
    const hasAssemblyIdIndex = indexes.some(
      index => index.key.assembly_id === 1
    );

    if (hasAssemblyIdIndex) {
      console.log('\nThe legacy assembly_id field has an index (for backward compatibility).');
    } else {
      console.log('\nNOTE: The legacy assembly_id field does not have an index.');
      console.log('This is fine if you have fully migrated to using assemblyCode.');
    }
  } catch (error) {
    console.error('Error checking indexes:', error);
  }
}

// Main function
async function main() {
  await connectToDatabase();
  await checkDuplicateAssemblyIds();
  await checkIndexes();

  // Disconnect from MongoDB
  await mongoose.disconnect();
  console.log('\nDisconnected from MongoDB');
}

// Run the script
main().catch(console.error);
