// Script to update inventory data by linking parts to suppliers and ensuring correct inventory structure
require('dotenv').config({ path: '.env.local' });
const { MongoClient, ObjectId } = require('mongodb');

async function updateInventoryData() {
  // Get MongoDB URI from environment variable
  const uri = process.env.MONGODB_URI;
  const dbName = process.env.MONGODB_DB_NAME || 'Trend_IMS';
  
  if (!uri) {
    console.error('Error: MONGODB_URI environment variable is not defined');
    console.error('Please set the MONGODB_URI in your .env.local file');
    process.exit(1);
  }

  console.log('Connecting to MongoDB...');
  console.log('URI:', uri ? uri.replace(/:([^@]+)@/, ':****@') : 'undefined');
  console.log('Database:', dbName);
  
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db(dbName);
    console.log('Successfully connected to MongoDB');
    
    // 1. First, check existing suppliers
    const suppliers = await db.collection('suppliers').find({}).toArray();
    console.log(`Found ${suppliers.length} suppliers in the database`);
    
    // If no suppliers exist, create some
    let supplierIds = [];
    if (suppliers.length === 0) {
      console.log('No suppliers found. Creating sample suppliers...');
      
      const sampleSuppliers = [
        {
          supplier_id: `SUP-${new ObjectId().toString().substring(0, 8)}`,
          name: 'Acme Parts Inc.',
          contactPerson: 'John Doe',
          email: '<EMAIL>',
          phone: '************',
          address: '123 Main St, Anytown, USA',
          specialty: ['Mechanical', 'Hydraulic'],
          rating: 4.5,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          supplier_id: `SUP-${new ObjectId().toString().substring(0, 8)}`,
          name: 'TechSupply Co.',
          contactPerson: 'Jane Smith',
          email: '<EMAIL>',
          phone: '************',
          address: '456 Tech Blvd, Innovation City, USA',
          specialty: ['Electronic', 'Automation'],
          rating: 4.8,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          supplier_id: `SUP-${new ObjectId().toString().substring(0, 8)}`,
          name: 'Global Manufacturing Ltd.',
          contactPerson: 'Robert Johnson',
          email: '<EMAIL>',
          phone: '************',
          address: '789 Industrial Way, Factory Town, USA',
          specialty: ['Fabrication', 'Assembly'],
          rating: 4.2,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      const result = await db.collection('suppliers').insertMany(sampleSuppliers);
      console.log(`${result.insertedCount} suppliers created successfully`);
      
      // Get the newly created supplier IDs
      supplierIds = Object.values(result.insertedIds);
    } else {
      // Use existing supplier IDs
      supplierIds = suppliers.map(s => s._id);
    }
    
    // 2. Update parts with supplier_id references and ensure inventory.current_stock is set
    const parts = await db.collection('parts').find({}).toArray();
    console.log(`Found ${parts.length} parts in the database`);
    
    let updatedCount = 0;
    
    for (const part of parts) {
      // Randomly assign a supplier to each part
      const randomSupplierIndex = Math.floor(Math.random() * supplierIds.length);
      const supplierId = supplierIds[randomSupplierIndex];
      
      // Ensure inventory structure exists and has current_stock set
      let inventory = part.inventory || {};
      if (!inventory.current_stock && inventory.current_stock !== 0) {
        inventory.current_stock = Math.floor(Math.random() * 100) + 1; // Random stock between 1-100
      }
      
      // Update the part with supplier_id and inventory
      const updateResult = await db.collection('parts').updateOne(
        { _id: part._id },
        { 
          $set: { 
            supplier_id: supplierId,
            inventory: inventory,
            updatedAt: new Date()
          } 
        }
      );
      
      if (updateResult.modifiedCount > 0) {
        updatedCount++;
      }
    }
    
    console.log(`Updated ${updatedCount} parts with supplier references and inventory data`);
    
    // 3. Verify the updates
    const verifyParts = await db.collection('parts')
      .find({ supplier_id: { $exists: true } })
      .limit(3)
      .toArray();
    
    console.log('\nSample updated parts:');
    verifyParts.forEach(part => {
      console.log(`- Part: ${part.name}, ID: ${part._id}`);
      console.log(`  Supplier ID: ${part.supplier_id}`);
      console.log(`  Current Stock: ${part.inventory?.current_stock || 0}`);
      console.log('');
    });
    
    console.log('Inventory data update completed successfully!');
    
  } catch (error) {
    console.error('Error updating inventory data:', error);
  } finally {
    await client.close();
    console.log('Database connection closed');
  }
}

// Execute the function
updateInventoryData().catch(console.error); 