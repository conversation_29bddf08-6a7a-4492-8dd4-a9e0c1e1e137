/**
 * Migration Script: Convert Part category fields from String to ObjectId
 * 
 * This script updates all existing parts in the database that have a category field
 * as a string to use ObjectId references instead.
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectToMongoDB = async () => {
  try {
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/trend_ims';
    console.log(`Connecting to MongoDB at ${uri}...`);
    await mongoose.connect(uri);
    console.log('Connected to MongoDB successfully');
    return mongoose.connection;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
};

// Define minimal schemas for migration
const CategorySchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true }
});

const Category = mongoose.models.Category || mongoose.model('Category', CategorySchema);

const PartSchema = new mongoose.Schema({
  _id: { type: String, required: true },
  name: { type: String, required: true },
  category: { type: mongoose.Schema.Types.Mixed } // Allow any type for migration
});

const Part = mongoose.models.Part || mongoose.model('Part', PartSchema);

// Migration function
const migrateCategoryIds = async () => {
  try {
    // Find all parts with string category values
    const parts = await Part.find({
      category: { $exists: true, $ne: null, $not: { $type: 7 } } // Type 7 is ObjectId
    });

    console.log(`Found ${parts.length} parts with string category values`);

    // Get all categories
    const categories = await Category.find({});
    console.log(`Found ${categories.length} categories`);

    // Create a map of category names to IDs for quick lookup
    const categoryMap = new Map();
    categories.forEach(category => {
      categoryMap.set(category.name, category._id);
      categoryMap.set(category._id.toString(), category._id);
    });

    // Update each part
    let updatedCount = 0;
    let skippedCount = 0;

    for (const part of parts) {
      // Skip if category is already an ObjectId
      if (part.category instanceof mongoose.Types.ObjectId) {
        skippedCount++;
        continue;
      }

      const categoryValue = part.category.toString();
      const categoryId = categoryMap.get(categoryValue);

      if (categoryId) {
        // Update the part with the ObjectId
        await Part.updateOne(
          { _id: part._id },
          { $set: { category: categoryId } }
        );
        updatedCount++;
        console.log(`Updated part ${part._id} (${part.name}): category ${categoryValue} -> ${categoryId}`);
      } else {
        console.warn(`Warning: Could not find category for part ${part._id} (${part.name}): ${categoryValue}`);
        skippedCount++;
      }
    }

    console.log(`Migration complete: ${updatedCount} parts updated, ${skippedCount} parts skipped`);
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

// Run the migration
const runMigration = async () => {
  const connection = await connectToMongoDB();
  try {
    await migrateCategoryIds();
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

runMigration();