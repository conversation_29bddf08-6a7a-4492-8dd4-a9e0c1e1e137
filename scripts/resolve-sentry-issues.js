#!/usr/bin/env node

/**
 * Trend_IMS - Sentry Issue Resolver
 * 
 * This script resolves Sentry issues by their ID using the Sentry API.
 * 
 * Usage:
 *   node scripts/resolve-sentry-issues.js
 */

const https = require('https');
const readline = require('readline');

// Configuration
const ORG_SLUG = 'trendtech-innovations';
const TOKEN = process.env.SENTRY_AUTH_TOKEN; // Will be prompted if not set

// List of issues to resolve (from Sentry_Issues_TaskList.md)
const issues = [
  { id: 'IMS-TEJ-E', status: 'resolved', comment: 'Fixed by adding main_assembly_id field to Product schema' },
  { id: 'IMS-TEJ-F', status: 'resolved', comment: 'Fixed with structured error handling and input validation' },
  { id: 'IMS-TEJ-G', status: 'resolved', comment: 'Fixed by improving MongoDB connection handling' }
];

// Create readline interface for prompting
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Makes a request to the Sentry API
 * @param {string} method - HTTP method
 * @param {string} path - API path
 * @param {object} data - Request data
 * @param {string} token - Auth token
 * @returns {Promise<object>} Response data
 */
function makeRequest(method, path, data, token) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'us.sentry.io',
      port: 443,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      }
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(JSON.stringify(data));
    }

    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            if (responseData) {
              resolve(JSON.parse(responseData));
            } else {
              resolve({ success: true });
            }
          } else {
            reject(new Error(`HTTP Error ${res.statusCode}: ${responseData}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

/**
 * Resolves a Sentry issue
 * @param {string} issueId - Sentry issue ID
 * @param {string} status - New status
 * @param {string} comment - Comment for the resolution
 * @param {string} token - Auth token
 * @returns {Promise<object>} Response data
 */
async function resolveIssue(issueId, status, comment, token) {
  const path = `/api/0/organizations/${ORG_SLUG}/issues/${issueId}/`;
  
  const data = {
    status,
    statusDetails: {}
  };
  
  if (comment) {
    // Add a comment if provided
    try {
      await makeRequest('POST', `${path}comments/`, { text: comment }, token);
      console.log(`Added comment to issue ${issueId}`);
    } catch (error) {
      console.warn(`Warning: Failed to add comment to issue ${issueId}: ${error.message}`);
    }
  }
  
  // Update the issue status
  return makeRequest('PUT', path, data, token);
}

/**
 * Main function
 */
async function main() {
  // Check for auth token
  let authToken = TOKEN;
  
  if (!authToken) {
    authToken = await new Promise((resolve) => {
      rl.question('Please enter your Sentry auth token: ', (answer) => {
        resolve(answer.trim());
      });
    });
  }
  
  console.log(`Resolving ${issues.length} Sentry issues for organization: ${ORG_SLUG}`);
  
  for (const issue of issues) {
    try {
      console.log(`Resolving issue ${issue.id} as "${issue.status}"...`);
      const result = await resolveIssue(issue.id, issue.status, issue.comment, authToken);
      console.log(`✅ Successfully resolved issue ${issue.id}`);
    } catch (error) {
      console.error(`❌ Failed to resolve issue ${issue.id}: ${error.message}`);
    }
  }
  
  rl.close();
  console.log('Done!');
}

// Run the script
main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
}); 