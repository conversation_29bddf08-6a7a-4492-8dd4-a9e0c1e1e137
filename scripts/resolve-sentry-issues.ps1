# PowerShell script to run Sentry issue resolver

# Define script location
$scriptPath = Join-Path $PSScriptRoot "resolve-sentry-issues.js"

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "Using Node.js $nodeVersion"
} catch {
    Write-Error "Node.js is not installed or not in PATH. Please install Node.js before running this script."
    exit 1
}

# Check if the Sentry auth token is provided as an argument or environment variable
$sentryToken = $env:SENTRY_AUTH_TOKEN

if (-not $sentryToken) {
    # Prompt for token if not set
    $sentryToken = Read-Host -Prompt "Please enter your Sentry auth token"
    
    if (-not $sentryToken) {
        Write-Error "No Sentry auth token provided. Exiting."
        exit 1
    }
    
    # Set for the current session only
    $env:SENTRY_AUTH_TOKEN = $sentryToken
}

# Run the Node.js script
try {
    Write-Host "Running Sentry issue resolver script..."
    node $scriptPath
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Script execution failed with exit code $LASTEXITCODE"
        exit $LASTEXITCODE
    }
    
    Write-Host "Script completed successfully." -ForegroundColor Green
} catch {
    Write-Error "Error executing script: $_"
    exit 1
} 