/**
 * Enhanced UI Testing Script
 * 
 * This script tests the enhanced UI components implemented with Magic UI
 * using Puppeteer to inspect components and check for console errors.
 */

import { runUITest } from '../app/utils/ui-testing';
import { uiEnhancementConfig } from '../app/config/ui-enhancement-plan';
import fs from 'fs/promises';
import path from 'path';

/**
 * Main function to run the UI tests
 */
async function main() {
  console.log('Starting Enhanced UI Testing...');
  
  // URL to test - use the local development server
  const url = 'http://localhost:3000/assemblies';
  
  console.log(`Testing URL: ${url}`);
  console.log('Make sure your development server is running!');
  
  try {
    // Run the UI test
    console.log('Running UI tests...');
    const report = await runUITest(url, uiEnhancementConfig);
    
    // Save the report to a file
    const reportDir = path.join(process.cwd(), 'reports');
    await fs.mkdir(reportDir, { recursive: true });
    
    const reportPath = path.join(reportDir, `ui-test-report-${new Date().toISOString().replace(/[:.]/g, '-')}.md`);
    await fs.writeFile(reportPath, report);
    
    console.log(`UI test completed successfully!`);
    console.log(`Report saved to: ${reportPath}`);
    console.log('\nReport Summary:');
    console.log(report.split('\n').slice(0, 10).join('\n') + '\n...');
  } catch (error) {
    console.error('Error running UI test:', error);
  }
}

// Run the main function
main().catch(console.error);

/**
 * To run this script:
 * 1. Start the development server: npm run dev
 * 2. In a separate terminal, run: npx ts-node scripts/test-enhanced-ui.ts
 */