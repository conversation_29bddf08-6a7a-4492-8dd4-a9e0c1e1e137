/**
 * <PERSON><PERSON><PERSON> to assign categories to parts
 * 
 * This script updates parts in the database to assign them to categories.
 * Run this script with Node.js to fix the issue where no parts are showing up in categories.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' }); // Ensure .env.local is loaded

// MongoDB connection string from environment variables
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/trend_ims';

// Connect to MongoDB
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

// Define simplified schemas for this script
const categorySchema = new mongoose.Schema({
  name: String,
  description: String
});

const partSchema = new mongoose.Schema({
  _id: String, // Reverted to use _id as the identifier
  name: String,
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }
});

// Create models
const Category = mongoose.model('Category', categorySchema);
const Part = mongoose.model('Part', partSchema);

// Sample category-part mappings (customize as needed)
const categoryMappings = [
  { categoryName: 'Bearings', partIdPattern: /bearing/i },
  { categoryName: 'Bushings', partIdPattern: /bush|bushing/i },
  { categoryName: 'Cylinder Assemblies', partIdPattern: /cylinder assembly|piston assembly/i },
  { categoryName: 'Fasteners', partIdPattern: /bolt|screw|nut|pin|key|fastener|stud|rivet|clip|washer/i },
  { categoryName: 'Hydraulics', partIdPattern: /hydraulic|cylinder(?! assembly)/i }, // Exclude "cylinder assembly"
  { categoryName: 'Mechanical Components', partIdPattern: /flange|arm|wheel|cover|rod|shaft|plate|bracket|lever|gear|pulley|spring|fitting/i },
  { categoryName: 'Seals & Rings', partIdPattern: /seal|ring|o-ring|gasket/i },
  // Note: 'Assemblies' category might be for top-level assemblies, not individual parts.
  // 'Tampion Limits' is too specific to guess a general pattern and might require manual assignment or specific data.
];

async function assignCategories() {
  try {
    // Get all categories
    const categories = await Category.find().lean();
    console.log(`Found ${categories.length} categories`);
    
    if (categories.length === 0) {
      console.log('No categories found. Please create categories first.');
      process.exit(0);
    }
    
    // Get all parts
    const parts = await Part.find().lean();
    console.log(`Found ${parts.length} parts`);
    
    if (parts.length === 0) {
      console.log('No parts found.');
      process.exit(0);
    }
    
    // Create a map of category names to IDs
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.name] = category._id;
    });
    
    // Count of updated parts
    let updatedCount = 0;
    
    // Assign categories based on mappings
    for (const part of parts) {
      // Skip parts that already have a category
      if (part.category) {
        console.log(`Part ${part._id} already has a category assigned.`); // Reverted to use _id for logging
        continue;
      }
      
      // Find a matching category based on part name or ID
      let matchedCategory = null;
      
      for (const mapping of categoryMappings) {
        // Match against _id or name
        if (mapping.partIdPattern.test(part._id) || mapping.partIdPattern.test(part.name)) { // Reverted to use _id for matching
          matchedCategory = categoryMap[mapping.categoryName];
          break;
        }
      }
      
      // If no match found, skip this part
      if (!matchedCategory) {
        console.log(`No category match found for part ${part._id} (${part.name})`); // Reverted to use _id for logging
        continue;
      }
      
      // Update the part with the matched category
      await Part.updateOne(
        { _id: part._id }, // Query by _id
        { $set: { category: matchedCategory } }
      );
      
      console.log(`Assigned category to part ${part._id} (${part.name})`); // Reverted to use _id for logging
      updatedCount++;
    }
    
    console.log(`Updated ${updatedCount} parts with categories`);
  } catch (error) {
    console.error('Error assigning categories:', error);
  } finally {
    // Close the MongoDB connection
    mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
assignCategories();