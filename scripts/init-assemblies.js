// <PERSON><PERSON>t to initialize sample assemblies in the database
// <PERSON><PERSON>t to initialize sample assemblies in the database
// Uses actual models now
const mongoose = require('mongoose');
require('dotenv').config();
const path = require('path');

// Adjust path to import models correctly from the script's location
const modelsPath = path.resolve(__dirname, '../app/models'); // Go up one level from scripts/ to project root, then into app/models
const { Assembly, Part } = require(modelsPath); // Import actual models

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://test:<EMAIL>/Trend_IMS';

// Removed outdated local schema definitions

async function main() {
  let connection; // Define connection variable outside try block
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    // Use createConnection for better handling if needed elsewhere, or stick with connect
    connection = await mongoose.createConnection(MONGODB_URI).asPromise();
    console.log('Connected to MongoDB');

    // Use the connection for models
    const PartModel = connection.model('Part', Part.schema);
    const AssemblyModel = connection.model('Assembly', Assembly.schema);

    // Check if there are any parts in the database using the actual model
    const partsCount = await PartModel.countDocuments();
    console.log(`Found ${partsCount} parts in the database`);

    if (partsCount === 0) {
      console.log('No parts found in the database. Please add parts first.');
      if (connection) await connection.close();
      process.exit(1);
    }

    // Get some parts to use in assemblies using the actual model and selecting the String _id
    const parts = await PartModel.find().limit(10).select('_id name').lean(); // Use lean for plain objects
    console.log(`Retrieved ${parts.length} parts for use in assemblies`);
    if (parts.length < 3) { // Need at least 3 parts for the sample data below
        console.error(`Error: Need at least 3 parts in the DB for sample data, found only ${parts.length}.`);
        if (connection) await connection.close();
        process.exit(1);
    }


    // Check if there are already assemblies in the database using the actual model
    const assembliesCount = await AssemblyModel.countDocuments();
    console.log(`Found ${assembliesCount} existing assemblies in the database`);

    if (assembliesCount > 0) {
      const proceed = await promptUser('Assemblies already exist. Do you want to add more? (y/n): ');
      if (proceed.toLowerCase() !== 'y') {
        console.log('Operation cancelled by user');
        process.exit(0);
      }
    }

    // Create sample assemblies using the NEW schema structure
    const assemblies = [
      {
        assemblyCode: 'ASM-001', // Use assemblyCode
        name: 'Tamping Arm Assembly',
        productId: null, // Assuming no specific product link for this sample
        parentId: null, // Assuming top-level
        isTopLevel: true,
        status: 'active', // Add required status field
        partsRequired: [ // Rename 'parts' to 'partsRequired'
          // Use the String _id from the fetched parts
          { partId: parts[0]._id.toString(), quantityRequired: 1 },
          { partId: parts[1]._id.toString(), quantityRequired: 2 },
          { partId: parts[2]._id.toString(), quantityRequired: 4 }
        ]
        // Removed description, assembly_stage, assembly_id
      },
      {
        assemblyCode: 'ASM-002',
        name: 'Heating Element Sub-Assembly', // Name updated
        productId: null,
        parentId: null, // Or link to a parent if applicable
        isTopLevel: false, // Example of a sub-assembly
        status: 'active',
        partsRequired: [
          { partId: parts[3 % parts.length]._id.toString(), quantityRequired: 1 }, // Use modulo for safety if fewer parts fetched
          { partId: parts[4 % parts.length]._id.toString(), quantityRequired: 1 }
        ]
      },
      {
        assemblyCode: 'ASM-003',
        name: 'Control Panel Assembly',
        productId: null,
        parentId: null,
        isTopLevel: true,
        status: 'active',
        partsRequired: [
          { partId: parts[5 % parts.length]._id.toString(), quantityRequired: 1 },
          { partId: parts[6 % parts.length]._id.toString(), quantityRequired: 5 },
          { partId: parts[7 % parts.length]._id.toString(), quantityRequired: 1 }
        ]
      }
    ];

    console.log('Creating sample assemblies...');

    // Insert assemblies one by one using the actual model
    for (const assemblyData of assemblies) {
      try {
        // Check if assembly already exists using assemblyCode
        const existingAssembly = await AssemblyModel.findOne({ assemblyCode: assemblyData.assemblyCode });

        if (existingAssembly) {
          console.log(`Assembly ${assemblyData.assemblyCode} already exists, skipping`);
          continue;
        }

        // Create new assembly using the actual model
        const newAssembly = new AssemblyModel(assemblyData);
        await newAssembly.save();
        console.log(`Created assembly: ${assemblyData.name} (${assemblyData.assemblyCode})`);
      } catch (error) {
        // Provide more context on validation errors
        if (error.name === 'ValidationError') {
            console.error(`Validation Error creating assembly ${assemblyData.assemblyCode}:`, JSON.stringify(error.errors, null, 2));
        } else {
            console.error(`Error creating assembly ${assemblyData.assemblyCode}:`, error.message);
        }
      }
    }

    console.log('Sample assembly creation process finished.');
  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    // Close the connection
    if (connection) {
        await connection.close();
        console.log('MongoDB connection closed');
    }
    process.exit(0); // Ensure script exits
  }
}

// Helper function to prompt user for input
function promptUser(question) {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise(resolve => {
    readline.question(question, answer => {
      readline.close();
      resolve(answer);
    });
  });
}

// Run the main function
main();
