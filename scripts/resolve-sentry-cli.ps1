# PowerShell script to resolve Sentry issues using the Sentry CLI

# Sentry Configuration
$SENTRY_ORG = "trendtech-innovations"
$SENTRY_AUTH_TOKEN = $env:SENTRY_AUTH_TOKEN
$SENTRY_URL = "https://us.sentry.io/"

# Check if Sentry CLI is installed
function Check-SentryCLI {
    try {
        $sentry_version = & sentry-cli --version
        Write-Host "Using Sentry CLI: $sentry_version"
        return $true
    } catch {
        Write-Host "Sentry CLI not found. Would you like to install it? (Y/N)" -ForegroundColor Yellow
        $install = Read-Host
        
        if ($install -eq "Y" -or $install -eq "y") {
            try {
                Write-Host "Installing Sentry CLI via npm..."
                & npm install -g @sentry/cli
                return Check-SentryCLI
            } catch {
                Write-Error "Failed to install Sentry CLI. Please install it manually: npm install -g @sentry/cli"
                return $false
            }
        } else {
            Write-Host "Please install Sentry CLI manually: npm install -g @sentry/cli"
            return $false
        }
    }
}

# Configure Sentry CLI
function Configure-SentryCLI {
    # Set environment variables for Sentry CLI
    $env:SENTRY_ORG = $SENTRY_ORG
    $env:SENTRY_URL = $SENTRY_URL
    
    # Check if auth token is provided
    if (-not $SENTRY_AUTH_TOKEN) {
        Write-Host "No Sentry auth token found in environment. Please enter it now:"
        $env:SENTRY_AUTH_TOKEN = Read-Host -Prompt "Sentry Auth Token"
    } else {
        $env:SENTRY_AUTH_TOKEN = $SENTRY_AUTH_TOKEN
    }
    
    # Verify auth configuration
    try {
        & sentry-cli info
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to authenticate with Sentry. Check your token and organization."
            return $false
        }
    } catch {
        Write-Error "Error verifying Sentry configuration: $_"
        return $false
    }
    
    return $true
}

# Resolve an issue
function Resolve-Issue {
    param (
        [string]$issueId,
        [string]$comment
    )
    
    Write-Host "Resolving issue $issueId..." -ForegroundColor Cyan
    
    # Add comment if provided
    if ($comment) {
        try {
            Write-Host "Adding comment to issue $issueId..."
            & sentry-cli issues update $issueId --comment "$comment"
            
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Failed to add comment. Continuing with status update..."
            }
        } catch {
            Write-Warning "Error adding comment: $_"
        }
    }
    
    # Update issue status to resolved
    try {
        & sentry-cli issues update $issueId --resolved
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Successfully resolved issue $issueId" -ForegroundColor Green
            return $true
        } else {
            Write-Error "Failed to resolve issue $issueId"
            return $false
        }
    } catch {
        Write-Error "Error resolving issue $issueId: $_"
        return $false
    }
}

# Main execution
function Main {
    # Issues to resolve (from Sentry_Issues_TaskList.md)
    $issues = @(
        @{id="IMS-TEJ-E"; comment="Fixed by adding main_assembly_id field to Product schema"},
        @{id="IMS-TEJ-F"; comment="Fixed with structured error handling and input validation"},
        @{id="IMS-TEJ-G"; comment="Fixed by improving MongoDB connection handling"}
    )
    
    # Check for Sentry CLI
    if (-not (Check-SentryCLI)) {
        return
    }
    
    # Configure Sentry CLI
    if (-not (Configure-SentryCLI)) {
        return
    }
    
    # Resolve each issue
    $issueCount = $issues.Count
    Write-Host "Resolving $issueCount Sentry issues for organization: $SENTRY_ORG" -ForegroundColor Cyan
    
    $successCount = 0
    
    foreach ($issue in $issues) {
        $result = Resolve-Issue -issueId $issue.id -comment $issue.comment
        if ($result) {
            $successCount++
        }
    }
    
    # Summary
    Write-Host "Resolution complete: $successCount/$issueCount issues resolved successfully" -ForegroundColor Cyan
}

# Run main function
Main 