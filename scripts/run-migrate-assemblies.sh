#!/bin/bash

# Make sure we're in the project root directory
cd "$(dirname "$0")/.."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js to run this script."
    exit 1
fi

# Check if the script exists
if [ ! -f "./scripts/migrate-assemblies.js" ]; then
    echo "migrate-assemblies.js script not found in the scripts directory."
    exit 1
fi

# Run the migration script
echo "Running assembly migration script..."
node ./scripts/migrate-assemblies.js

# Check if the script executed successfully
if [ $? -eq 0 ]; then
    echo "Assembly migration completed successfully."
else
    echo "Assembly migration failed."
    exit 1
fi

echo "Done."
