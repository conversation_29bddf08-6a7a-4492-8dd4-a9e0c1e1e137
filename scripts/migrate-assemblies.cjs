// Script to migrate assemblies from old schema to new schema
// <PERSON>ript to migrate assemblies from old schema to the actual new schema
const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });
const path = require('path');

// Adjust path to import the actual Assembly model
const modelsPath = path.resolve(__dirname, '../app/models');
const { Assembly } = require(modelsPath); // Import the actual Assembly model

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://test:<EMAIL>/Trend_IMS';

// Removed local schema definitions

async function migrateAssemblies() {
  let connection;
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    // Use createConnection for potentially better resource management if needed
    connection = await mongoose.createConnection(MONGODB_URI).asPromise();
    console.log('Connected to MongoDB');

    // Use the actual Assembly model associated with the connection
    const AssemblyModel = connection.model('Assembly', Assembly.schema);

    // Fetch assemblies that might still have the old structure.
    // We fetch using the current model but select fields that might exist in the old structure.
    console.log('Fetching assemblies for potential migration...');
    // Fetch documents that likely haven't been migrated (e.g., missing 'assemblyCode' or having 'assembly_id')
    const assembliesToMigrate = await AssemblyModel.find({
      $or: [
        { assemblyCode: { $exists: false } }, // Find docs missing the new field
        { assembly_id: { $exists: true } }   // Or docs still having the old field
      ]
    }).lean(); // Use lean to get plain JS objects

    console.log(`Found ${assembliesToMigrate.length} assemblies potentially needing migration.`);

    if (assembliesToMigrate.length === 0) {
      console.log('No assemblies found requiring migration based on criteria.');
      return; // Exit if nothing to migrate
    }

    let migratedCount = 0;
    let errorCount = 0;

    // Migrate each assembly
    for (const oldDoc of assembliesToMigrate) {
      const assemblyIdentifier = oldDoc.assembly_id || oldDoc.name || oldDoc._id.toString();
      console.log(`Attempting migration for assembly: ${assemblyIdentifier}`);

      try {
        // --- Construct the update operations ---
        const setUpdate = {};
        const unsetUpdate = {};

        // 1. Rename assembly_id to assemblyCode
        if (oldDoc.assembly_id) {
          setUpdate.assemblyCode = oldDoc.assembly_id;
          unsetUpdate.assembly_id = ""; // Mark old field for removal
        } else if (!oldDoc.assemblyCode) {
           // Handle case where assembly_id is missing but assemblyCode is also missing
           console.warn(`Assembly ${assemblyIdentifier} missing both assembly_id and assemblyCode. Skipping code update.`);
        }

        // 2. Add new required fields with defaults
        setUpdate.isTopLevel = typeof oldDoc.isTopLevel === 'boolean' ? oldDoc.isTopLevel : true; // Default to true if missing
        setUpdate.status = oldDoc.status || 'active'; // Default to 'active' if missing

        // 3. Add new optional fields with defaults
        setUpdate.productId = oldDoc.productId || null;
        setUpdate.parentId = oldDoc.parentId || null;

        // 4. Rename and transform 'parts' to 'partsRequired'
        if (Array.isArray(oldDoc.parts)) {
          setUpdate.partsRequired = oldDoc.parts.map(oldPart => {
            // Ensure partId is treated as String (as per Part schema _id)
            const partIdString = oldPart.part_id ? oldPart.part_id.toString() : null;
            if (!partIdString) {
                console.warn(`Missing or invalid part_id in parts array for assembly ${assemblyIdentifier}`);
                // Decide how to handle: skip part, throw error, etc. Here we skip.
                return null; // Filter out invalid parts later
            }
            return {
              partId: partIdString, // Use the string ID
              quantityRequired: oldPart.quantity_required || 1 // Use old field name, default to 1
            };
          }).filter(part => part !== null); // Remove parts that couldn't be mapped

          unsetUpdate.parts = ""; // Mark old array for removal
        } else if (!oldDoc.partsRequired) {
            // If 'parts' is missing and 'partsRequired' is also missing, initialize as empty array
            setUpdate.partsRequired = [];
        }


        // 5. Unset other deprecated fields if they exist
        if (oldDoc.description !== undefined) unsetUpdate.description = "";
        if (oldDoc.assembly_stage !== undefined) unsetUpdate.assembly_stage = "";
        if (oldDoc.assembly_code !== undefined && oldDoc.assembly_code !== setUpdate.assemblyCode) {
             // Only unset assembly_code if it exists and is different from the new assemblyCode
             unsetUpdate.assembly_code = "";
        }


        // --- Execute the update ---
        if (Object.keys(setUpdate).length > 0 || Object.keys(unsetUpdate).length > 0) {
          console.log(`Updating ${assemblyIdentifier}: $set: ${JSON.stringify(setUpdate)}, $unset: ${JSON.stringify(unsetUpdate)}`);
          const result = await AssemblyModel.updateOne(
            { _id: oldDoc._id },
            {
              $set: setUpdate,
              $unset: unsetUpdate
            }
          );

          if (result.modifiedCount > 0) {
            console.log(`Successfully migrated assembly: ${assemblyIdentifier}`);
            migratedCount++;
          } else if (result.matchedCount > 0) {
             console.log(`Assembly ${assemblyIdentifier} matched but no modifications needed (already up-to-date?).`);
          } else {
             console.warn(`Assembly ${assemblyIdentifier} not found during update operation.`);
          }
        } else {
           console.log(`No updates needed for assembly: ${assemblyIdentifier}`);
        }

      } catch (error) {
        console.error(`Error migrating assembly ${assemblyIdentifier}:`, error.message);
        errorCount++;
      }
    }

    console.log(`Migration process finished. Successfully migrated: ${migratedCount}, Errors: ${errorCount}`);

  } catch (error) {
    console.error('Fatal error during migration setup or execution:', error);
  } finally {
    // Close the connection
    if (connection) {
      await connection.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the migration
migrateAssemblies();
