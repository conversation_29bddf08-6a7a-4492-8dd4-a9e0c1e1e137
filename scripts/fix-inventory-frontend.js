// Script to diagnose and fix inventory data not being displayed in the frontend
// Run this script to debug API responses and update the frontend adapter if needed
require('dotenv').config({ path: '.env.local' });
const { MongoClient } = require('mongodb');
const fetch = require('node-fetch');

async function diagnoseFrontendIssue() {
  console.log('Starting inventory frontend diagnostic...');
  
  // Get MongoDB URI from environment variable
  const uri = process.env.MONGODB_URI;
  const dbName = process.env.MONGODB_DB_NAME || 'Trend_IMS';
  
  if (!uri) {
    console.error('Error: MONGODB_URI environment variable is not defined');
    console.error('Please set the MONGODB_URI in your .env.local file');
    process.exit(1);
  }
  
  const client = new MongoClient(uri);
  
  try {
    // 1. First check the raw database data
    await client.connect();
    const db = client.db(dbName);
    console.log('Successfully connected to MongoDB');
    
    // Check if parts exist in the database
    const partsCount = await db.collection('parts').countDocuments();
    console.log(`Found ${partsCount} parts in the database`);
    
    if (partsCount === 0) {
      console.error('No parts found in the database! Please add some parts first.');
      return;
    }
    
    // Get a sample part to see its structure
    const samplePart = await db.collection('parts').findOne({});
    console.log('\nSample part document structure:');
    console.log(JSON.stringify(samplePart, null, 2));
    
    // Check if supplier_id exists in any parts
    const partsWithSuppliers = await db.collection('parts').countDocuments({
      supplier_id: { $exists: true, $ne: null }
    });
    console.log(`\nFound ${partsWithSuppliers} parts with supplier_id references`);
    
    // Check if inventory.current_stock exists in any parts
    const partsWithStock = await db.collection('parts').countDocuments({
      'inventory.current_stock': { $exists: true }
    });
    console.log(`Found ${partsWithStock} parts with inventory.current_stock`);
    
    // 2. Then check the API response
    console.log('\nMaking test request to the Parts API...');
    try {
      const apiUrl = 'http://localhost:5174/api/parts?page=1&limit=20';
      console.log(`Fetching from: ${apiUrl}`);
      
      const response = await fetch(apiUrl);
      const apiData = await response.json();
      
      console.log(`API Response Code: ${apiData.code}`);
      console.log(`API Response Parts Count: ${apiData.data?.length || 0}`);
      
      if (!apiData.data || apiData.data.length === 0) {
        console.error('API returned no parts! Checking direct database connection...');
        
        // Try fetching directly from the database
        const directParts = await db.collection('parts')
          .find({})
          .limit(5)
          .toArray();
          
        console.log(`\nDirectly found ${directParts.length} parts in the database`);
        if (directParts.length > 0) {
          console.log('Database contains parts but API returns none - API route issue!');
          console.log('Check the /api/parts route implementation.');
        }
      } else {
        // Check the sample API response
        console.log('\nSample API response part structure:');
        console.log(JSON.stringify(apiData.data[0], null, 2));
        
        // Check if supplier name and inventory is present in the response
        const hasSupplierName = apiData.data.some(part => part.supplierManufacturer && part.supplierManufacturer !== '—');
        const hasStock = apiData.data.some(part => part.currentStock > 0);
        
        console.log(`\nAPI response includes supplier names: ${hasSupplierName}`);
        console.log(`API response includes stock values: ${hasStock}`);
        
        if (!hasSupplierName || !hasStock) {
          console.log('\nPROBLEM IDENTIFIED: The data is in the database but not correctly transformed in the API response.');
          console.log('Check the transformDocumentToResponse function in the API route.');
          console.log('Also ensure that the frontend adapts the API response correctly in adaptProductsForTable function.');
        }
      }
    } catch (apiError) {
      console.error('Error making API request:', apiError.message);
      console.log('Make sure the development server is running on port 5174.');
    }
    
    // 3. Report possible issues and solutions
    console.log('\n=== DIAGNOSIS SUMMARY ===');
    if (partsWithSuppliers < partsCount) {
      console.log('⚠️ Some parts are missing supplier references. Run the update-inventory-data.js script.');
    }
    
    if (partsWithStock < partsCount) {
      console.log('⚠️ Some parts are missing inventory.current_stock. Run the update-inventory-data.js script.');
    }
    
    console.log('\nPossible solutions:');
    console.log('1. Ensure frontend is correctly calling the API and handling the response');
    console.log('2. Check browser console for any JavaScript errors');
    console.log('3. Verify the adaptProductsForTable function in inventory/page.tsx handles supplier_name and inventory.current_stock correctly');
    console.log('4. Restart the development server with npm run dev');
    
  } catch (error) {
    console.error('Error diagnosing frontend issue:', error);
  } finally {
    await client.close();
    console.log('\nDiagnostic completed. Database connection closed.');
  }
}

// Execute the function
diagnoseFrontendIssue().catch(console.error); 