name: Staging Deployment

on:
  pull_request:
    branches: [main, development]
    paths-ignore:
      - '**.md'
      - '.github/**'
      - '!.github/workflows/staging.yml'

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    name: Deploy to Vercel Staging
    env:
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '19'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install Vercel CLI
        run: pnpm add -g vercel@latest

      - name: Build project
        run: pnpm build

      - name: Deploy to Vercel Staging
        id: deploy
        run: |
          DEPLOYMENT_URL=$(vercel deploy --token=${{ secrets.VERCEL_TOKEN }} --prod --scope=${{ secrets.VERCEL_ORG_ID }} --yes --build-env NEXT_PUBLIC_ENVIRONMENT=staging)
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV

      - name: Comment on PR
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { issue: { number: issue_number }, repo: { owner, repo } } = context;
            github.rest.issues.createComment({
              issue_number,
              owner,
              repo,
              body: `✅ Staging deployment complete! Preview URL: ${process.env.DEPLOYMENT_URL}`
            });

      - name: Create deployment status
        id: deployment-status
        run: |
          echo "::set-output name=url::$DEPLOYMENT_URL"
          echo "::set-output name=success::true"

      - name: Update migration audit
        run: |
          echo "Updating deployment status for PR #${{ github.event.pull_request.number }}"
          echo "Page: ${{ github.event.pull_request.title }}"
          echo "Deployment URL: $DEPLOYMENT_URL"
          # This step would be expanded to actually update the migration-audit-report.json
          # with evidence of staging deployment for the specific page(s) being modified