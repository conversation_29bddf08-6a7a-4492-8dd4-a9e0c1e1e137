# Codebase Structure

```
.gitignore
.sentrycliignore
assemblies.html
capture-console-errors.js
check-env.cjs
create-env.cjs
database_schema
diagnose-mongodb.cjs
eslint.config.js
init-mongodb.cjs
instrumentation-client.ts
instrumentation.ts
jest.config.js
jest.setup.js
migration-audit-report.json
next.config.mjs
package-lock.json
package.json
postcss.config.js
README.md
REQUIREMENTS.md
sentry.edge.config.ts
sentry.server.config.ts
tailwind.config.js
<details><summary>__mocks__/ - Mocks for testing</summary>
__mocks__/ - Mocks for testing
</details>
:start_line:31
-------
<details><summary>__tests__/ - Tests</summary>
__tests__/ - Tests
</details>
__tests__/extractDatabaseCollections.js
__tests__/api/error-handling.test.ts
__tests__/api/parts.integration.test.ts
__tests__/api/analytics/route.test.ts
__tests__/api/assemblies/route.test.ts
__tests__/api/assemblies/[id]/route.test.ts
__tests__/api/batch-tracking/route.test.ts
__tests__/api/batches/inventory.test.ts
__tests__/api/batches/logs.test.ts
__tests__/api/categories/route.test.ts
__tests__/api/hierarchical-builder/route.test.ts
__tests__/api/hierarchical-part-entry/route.test.ts
__tests__/api/inventory/route.test.ts
__tests__/api/inventory-transactions/route.test.ts
__tests__/api/logistics/route.test.ts
__tests__/api/parts/route.test.ts
__tests__/api/parts/[id]/route.test.ts
__tests__/api/parts/search/route.test.ts
__tests__/api/product-import/route.test.ts
__tests__/api/products/route.test.ts
__tests__/api/products/[id]/route.test.ts
__tests__/api/purchase-orders/route.test.ts
__tests__/api/purchase-orders/[id]/route.test.ts
__tests__/api/reports/route.test.ts
__tests__/api/settings/route.test.ts
__tests__/api/suppliers/route.test.ts
__tests__/api/suppliers/[id]/route.test.ts
__tests__/api/user-management/route.test.ts
__tests__/api/warehouses/[id]/route.test.ts
__tests__/api/work-orders/route.test.ts
__tests__/api/work-orders/[id]/route.test.ts
__tests__/app/work-orders/page.test.tsx
__tests__/components/accessibility/buttons.test.tsx
__tests__/components/accessibility/components.test.tsx
__tests__/components/accessibility/forms.test.tsx
__tests__/components/accessibility/tables.test.tsx
__tests__/components/actions/RefreshDataButton.test.tsx
__tests__/components/forms/EnhancedPartForm.test.tsx
__tests__/components/forms/WorkOrderForm.test.tsx
__tests__/components/status/AssemblyStatusBadge.test.tsx
__tests__/components/status/PartsCountBadge.test.tsx
__tests__/components/tables/AssembliesTableClient.test.tsx
__tests__/components/tables/WorkOrdersTable.test.tsx
__tests__/contexts/AssembliesContext.test.tsx
__tests__/contexts/AssemblyFormContext.test.tsx
__tests__/hooks/accessibility.test.tsx
__tests__/models/assembly.model.test.ts
__tests__/models/batch.model.test.ts
__tests__/models/batchLog.model.test.ts
__tests__/models/category.model.test.ts
__tests__/models/delivery.model.test.ts
__tests__/models/part.model.test.ts
__tests__/models/purchaseOrder.model.test.ts
__tests__/models/settings.model.test.ts
__tests__/models/supplier.model.test.ts
__tests__/models/systemLog.model.test.ts
__tests__/models/transaction.model.test.ts
__tests__/models/warehouse.model.test.ts
__tests__/models/workOrder.model.test.ts
__tests__/services/mongodb.integration.test.ts
:start_line:93
-------
<details><summary>app/ - Main application directory</summary>
app/ - Main application directory
</details>
app/global-error.tsx
app/providers.tsx
app/components/accessibility/AccessibleButton.tsx
app/components/accessibility/AccessibleCheckbox.tsx
app/components/accessibility/AccessibleDataTable.tsx
app/components/accessibility/AccessibleFormField.tsx
app/components/accessibility/AccessibleIconButton.tsx
app/components/accessibility/AccessibleLink.tsx
app/components/accessibility/AccessibleSelect.tsx
app/components/accessibility/AccessibleTable.tsx
app/components/accessibility/AccessibleTableHeader.tsx
app/components/accessibility/AccessibleTablePagination.tsx
app/components/accessibility/AccessibleTextarea.tsx
app/components/accessibility/SkipLink.tsx
app/components/accessibility/VisuallyHidden.tsx
app/components/actions/DeleteAssemblyAction.tsx
app/components/actions/DuplicateAssemblyAction.tsx
app/components/actions/QuickEditAssemblyAction.tsx
app/components/actions/RefreshDataButton.tsx
app/components/assemblies/RecentAssemblies.tsx
app/components/controls/AutoRefreshControl.tsx
app/components/demos/DebounceDemo.tsx
app/components/demos/EnhancedTableDemo.tsx
app/components/details/BatchDetailsView.tsx
app/components/details/index.ts
app/components/dialogs/ConfirmationDialog.tsx
app/components/examples/AccessibleButtonsExample.tsx
app/components/examples/AccessibleFormsExample.tsx
app/components/examples/AccessibleTablesExample.tsx
app/components/inventory/BatchInventoryView.tsx
app/components/inventory/index.ts
app/components/inventory/filters/types.ts
app/components/layout/EnhancedBackground.tsx
app/components/layout/Header.test.tsx
app/components/layout/Header.tsx
app/components/layout/HeaderRightControls.tsx
app/components/layout/Sidebar.tsx
app/components/layout/Container/Container.tsx
app/components/layout/Container/ContainerClient.tsx
app/components/layout/Container/index.ts
app/components/layout/Container/types.ts
app/components/layout/Footer/Footer.tsx
app/components/layout/Footer/FooterClient.tsx
app/components/layout/Footer/index.ts
app/components/layout/Footer/types.ts
app/components/layout/Header/Header.tsx
app/components/layout/Header/HeaderClient.tsx
app/components/layout/Header/index.ts
app/components/layout/Header/types.ts
app/components/layout/Header/HeaderRightControls/HeaderRightControls.tsx
app/components/layout/Header/HeaderRightControls/HeaderRightControlsClient.tsx
app/components/layout/Header/HeaderRightControls/index.ts
app/components/layout/Sidebar/index.ts
app/components/layout/Sidebar/Sidebar.tsx
app/components/layout/Sidebar/SidebarClient.tsx
app/components/layout/Sidebar/types.ts
app/components/logs/BatchLogsView.tsx
app/components/logs/index.ts
app/components/navigation/NewAssemblyButton.tsx
app/components/search/PartSearch.tsx
app/components/search/PartSearchSimple.tsx
app/contexts/AssembliesContext.tsx
app/contexts/AssemblyFormContext.tsx
:start_line:157
-------
<details><summary>components/ - Reusable components</summary>
components/ - Reusable components
</details>
components/features/AssemblyStatus.tsx
components/features/index.ts
components/features/ProductCard.tsx
components/ui/button.tsx
components/ui/card.tsx
components/ui/date-picker.tsx
components/ui/ErrorBoundary.tsx
components/ui/popover.tsx
components/ui/separator.tsx
components/ui/ErrorDisplay/ErrorDisplay.tsx
components/ui/ErrorDisplay/ErrorDisplayClient.tsx
components/ui/ErrorDisplay/index.ts
components/ui/ErrorDisplay/types.ts
:start_line:171
-------
<details><summary>coverage/ - Test coverage reports</summary>
coverage/ - Test coverage reports
</details>
:start_line:174
-------
<details><summary>docs/ - Documentation</summary>
docs/ - Documentation
</details>
:start_line:177
-------
<details><summary>lib/ - Utility libraries</summary>
lib/ - Utility libraries
</details>
lib/utils.ts
lib/services/mongodb.d.ts
:start_line:180
-------
<details><summary>logs/ - Application logs</summary>
logs/ - Application logs
</details>
:start_line:183
-------
<details><summary>public/ - Public assets</summary>
public/ - Public assets
</details>
public/config.js
public/env-check.html
public/sample-product.jpg
:start_line:186
-------
<details><summary>scripts/ - Utility scripts</summary>
scripts/ - Utility scripts
</details>
scripts/assign-categories.js
scripts/check-assembly-duplicates.js
scripts/init-assemblies.js
scripts/migrate-assemblies.cjs
scripts/migrate-category-ids.js
scripts/resolve-sentry-cli.ps1
scripts/resolve-sentry-issues.js
scripts/resolve-sentry-issues.ps1
scripts/run-init-assemblies.sh
scripts/run-migrate-assemblies.sh
scripts/test-enhanced-ui.ts
:start_line:189
-------
<details><summary>sentry-mcp/ - Sentry MCP related files</summary>
sentry-mcp/ - Sentry MCP related files
</details>
:start_line:192
-------
<details><summary>task_list_testing/ - Task list testing files</summary>
task_list_testing/ - Task list testing files
</details>
task_list_testing/database_schema_old.md
task_list_testing/database_schema_updated.md
:start_line:195
-------
<details><summary>tasks/ - Task related files</summary>
tasks/ - Task related files
</details>