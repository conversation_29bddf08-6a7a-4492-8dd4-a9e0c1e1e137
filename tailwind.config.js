/** @type {import('tailwindcss').Config} */
export default {
  content: ['./app/**/*.{js,ts,jsx,tsx}', './components/**/*.{js,ts,jsx,tsx}', './index.html'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        yellow: {
          300: '#FFEB3B',
          400: '#FDD835',
          500: '#FBC02D',
        },
        orange: {
          300: '#FFB74D',
          400: '#FFA726',
          500: '#FF9800',
        },
        gray: {
          750: '#2D3748',
          850: '#1A202C',
        },
        dark: {
          900: 'var(--T-bg-primary, #1E1E1E)',
          800: 'var(--T-bg-sidebar, #2D2D2D)',
          700: 'var(--T-bg-card, #333333)',
          600: 'var(--dark-hover, #3E3E3E)',
          '900/80': 'rgba(30, 30, 30, 0.8)',
          '800/80': 'rgba(45, 45, 45, 0.8)',
          '800/60': 'rgba(45, 45, 45, 0.6)',
          '700/60': 'rgba(51, 51, 51, 0.6)',
          '700/40': 'rgba(51, 51, 51, 0.4)',
          'focus-ring': 'var(--T-focus-ring, rgba(224, 224, 224, 0.4))',
          'hover-overlay': 'var(--T-hover-overlay, rgba(255, 255, 255, 0.05))',
        },
        'focus-ring': 'var(--T-focus-ring, rgba(224, 224, 224, 0.4))',
        'hover-overlay': 'var(--T-hover-overlay, rgba(255, 255, 255, 0.05))',
        'accent-primary': 'var(--T-accent-primary, #FFFFFF)',
        'accent-active': 'var(--T-accent-active, #E0E0E0)',
        mint: {
          300: '#ACFFE0',
          400: '#7CFFC9',
          500: '#4BFFB2',
        },
        primary: {
          blue: '#E0E0E0',
          pink: '#EC3A76',
          'blue/20': 'rgba(224, 224, 224, 0.2)',
          'blue/10': 'rgba(224, 224, 224, 0.1)',
          'pink/20': 'rgba(236, 58, 118, 0.2)',
          'pink/10': 'rgba(236, 58, 118, 0.1)',
        }
      },
      textColor: {
        dark: {
          'text-primary': 'var(--T-text-primary, #F0F0F0)',
          'text-secondary': 'var(--T-text-secondary, #A0A0A0)',
          'text-headings': 'var(--T-text-headings, #FFFFFF)',
        },
        'text-primary': 'var(--T-text-primary, #F0F0F0)',
        'text-secondary': 'var(--T-text-secondary, #A0A0A0)',
        'text-headings': 'var(--T-text-headings, #FFFFFF)',
      },
      backgroundColor: {
        dark: {
          'bg': 'var(--T-bg-primary, #1E1E1E)',
          'card': 'var(--T-bg-card, #333333)',
          'element': 'var(--T-bg-sidebar, #2D2D2D)',
          'hover': 'var(--dark-hover, #3E3E3E)',
        },
        'bg': 'var(--T-bg-primary, #1E1E1E)',
        'card': 'var(--T-bg-card, #333333)',
        'sidebar': 'var(--T-bg-sidebar, #2D2D2D)',
        'hover': 'var(--dark-hover, #3E3E3E)',
      },
      borderColor: {
        dark: {
          'border': 'var(--T-border-color, #444444)',
          'border-subtle': 'var(--T-border-subtle, #383838)',
        },
        'border': 'var(--T-border-color, #444444)',
        'border-subtle': 'var(--T-border-subtle, #383838)',
        DEFAULT: 'var(--T-border-subtle, #e5e7eb)',
        white: 'var(--T-border-subtle, #e5e7eb)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-out forwards',
        'slide-up': 'slideUp 0.5s ease-out forwards',
        'slide-down': 'slideDown 0.5s ease-out forwards',
        'slide-left': 'slideLeft 0.5s ease-out forwards',
        'slide-right': 'slideRight 0.5s ease-out forwards',
        'float': 'float 6s ease-in-out infinite',
        'shimmer-slide': 'shimmer-slide calc(var(--speed)*0.5) infinite linear',
        'spin-around': 'spin-around calc(var(--speed)*1) infinite linear',
        'rippling': 'rippling 600ms linear forwards',
        'border': 'border 3s ease infinite',
        'border-beam': 'border-beam 3s ease infinite',
        'spin-slow': 'spin-slow 4s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'border-beam': {
          '0%, 100%': { opacity: '0.8' },
          '50%': { opacity: '0.4' },
        },
        'spin-slow': {
          to: {
            transform: 'rotate(1turn)',
          },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
        },
        'shimmer-slide': {
          to: {
            transform: 'translateY(-100cqh)',
          },
        },
        'spin-around': {
          to: {
            transform: 'rotate(1turn)',
          },
        },
        'rippling': {
          '0%': {
            transform: 'scale(0)',
            opacity: '0.5',
          },
          '100%': {
            transform: 'scale(6)',
            opacity: '0',
          },
        },
        'border': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideLeft: {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideRight: {
          '0%': { transform: 'translateX(-20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
      backgroundImage: {
        'gradient-dark': 'linear-gradient(to bottom right, var(--T-bg-sidebar, #2D2D2D), var(--T-bg-card, #333333))',
        'gradient-dark-reverse': 'linear-gradient(to bottom right, var(--T-bg-card, #333333), var(--T-bg-sidebar, #2D2D2D))',
      },
      backdropBlur: {
        xs: '2px',
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
        '2xl': '24px',
      },
    },
  },
  plugins: [],
};