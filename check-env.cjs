// check-env.cjs
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config();

const envPath = path.resolve(process.cwd(), '.env');

// Check if .env file exists
if (!fs.existsSync(envPath)) {
  console.error('Error: .env file not found.');
  console.error('Please run "npm run setup-env" to create the .env file first.');
  process.exit(1);
}

// Required environment variables
const requiredVars = [
  'MONGODB_URI',
  'MONGODB_DB',
  'PORT',
  'NODE_ENV'
];

// Optional but recommended variables
const recommendedVars = [
  'NEXTAUTH_URL',
  'NEXTAUTH_SECRET',
  'NEXT_PUBLIC_APP_URL'
];

// Sentry-related variables (optional)
const sentryVars = [
  'NEXT_PUBLIC_SENTRY_DSN',
  'SENTRY_AUTH_TOKEN'
];

// Check required variables
const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('Error: The following required environment variables are missing:');
  missingVars.forEach(varName => console.error(`  - ${varName}`));
  console.error('\nPlease update your .env file with these variables.');
  process.exit(1);
}

// Check MongoDB URI format
const mongodbUri = process.env.MONGODB_URI;
if (mongodbUri && !mongodbUri.startsWith('mongodb')) {
  console.error('Error: Invalid MONGODB_URI format. It should start with "mongodb://" or "mongodb+srv://"');
  process.exit(1);
}

// Check recommended variables
const missingRecommended = recommendedVars.filter(varName => !process.env[varName]);
if (missingRecommended.length > 0) {
  console.warn('Warning: The following recommended environment variables are missing:');
  missingRecommended.forEach(varName => console.warn(`  - ${varName}`));
  console.warn('\nYour application may not function correctly without them.');
}

// Check Sentry configuration
const hasAnySentryVar = sentryVars.some(varName => !!process.env[varName]);
if (hasAnySentryVar) {
  const missingSentryVars = sentryVars.filter(varName => !process.env[varName]);
  if (missingSentryVars.length > 0) {
    console.warn('Warning: Some Sentry-related variables are defined but others are missing:');
    missingSentryVars.forEach(varName => console.warn(`  - ${varName}`));
    console.warn('\nThis might result in incomplete Sentry configuration.');
  }
}

console.log('Environment check passed. All required variables are present.');